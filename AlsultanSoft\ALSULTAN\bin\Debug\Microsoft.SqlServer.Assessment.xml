<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.SqlServer.Assessment</name>
    </assembly>
    <members>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentRequest2.Checks">
            <summary>
            Gets all checks to be run against the Target.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentSuccess.Message">
            <summary>
            Gets a <see cref="T:System.String"/> containing information message, recommendation, or error message ro this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.Message"/> is usually displayed to the end user.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentSuccess.Check">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> which gave this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentSuccess.TargetPath">
            <summary>
            Gets the path to the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentSuccess.TargetType">
            <summary>
            Gets the type of the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentSuccess.Timestamp">
            <summary>
            Gets timestamp of the issue
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.IItemChange`1">
            <summary>
            Instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.IItemChange`1"/> represents a modification to an item
            or a property in an object of type <typeparamref name="TTarget"/>.
            </summary>
            <typeparam name="TTarget">Type of object to be modified by this <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.IItemChange`1"/>.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.IItemChange`1.ApplyTo(`0)">
            <summary>
            Applies the modification to the <paramref name="target"/>.
            </summary>
            <param name="target">Target object for this modification.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="target"/> is <see langword="null"/></exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.IMultipleChangeAccessor`2">
            <summary>
            Implementations of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.IMultipleChangeAccessor`2"/> get multiple objects
            of type <typeparamref name="TValue"/> from a <typeparamref name="TTarget"/> instance.
            </summary>
            <typeparam name="TTarget">Type of object providing data.</typeparam>
            <typeparam name="TValue">Type of objects obtained by this accessor.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.IMultipleChangeAccessor`2.GetValues(`0)">
            <summary>
            Gets objects of type <typeparamref name="TValue"/> from <paramref name="target"/>.
            </summary>
            <param name="target">Ths source object.</param>
            <returns>Returns objects of type <typeparamref name="TValue"/> from <paramref name="target"/> if any.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.InPlaceTransform`1">
            <summary>
            Represents a change of state for an instance of <typeparamref name="TTarget"/>.
            </summary>
            <typeparam name="TTarget">Type of object ot be transformed.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.InPlaceTransform`1.ApplyTo(`0)">
            <summary>
            Applies the change to <paramref name="target"/>.
            </summary>
            <param name="target">The object to be changed.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange`2">
            <summary>
            Multiple item change represents a modification to multiple instances of <typeparamref name="TValue"/>
            obtained from and object of type <typeparamref name="TTarget"/>.
            </summary>
            <typeparam name="TTarget">Type of object source.</typeparam>
            <typeparam name="TValue">Type of objects which will be modified.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange`2.#ctor(Microsoft.SqlServer.Management.Assessment.Configuration.Customization.IMultipleChangeAccessor{`0,`1},Microsoft.SqlServer.Management.Assessment.Configuration.Customization.InPlaceTransform{`1})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange`2"/>
            with given <paramref name="accessor"/> and <paramref name="transform"/>.
            </summary>
            <param name="accessor">Gets objects from a target object.</param>
            <param name="transform">Transforms objects obtained by <paramref name="accessor"/>.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange`2.Accessor">
            <summary>
            Gets accessor for objects to be transformed by <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange`2.Transform"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange`2.Transform">
            <summary>
            Gets transform modifying objects obtained by <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange`2.Accessor"/>.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange`2.ApplyTo(`0)">
            <summary>
            Applies the modification to the <paramref name="target"/>.
            </summary>
            <param name="target">Target object for this modification.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="target"/> is <see langword="null"/></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange`2.Equals(Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange{`0,`1})">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange`2.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <returns>true if the specified object  is equal to the current object; otherwise, false.</returns>
            <param name="obj">The object to compare with the current object. </param>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Customization.MultipleItemChange`2.GetHashCode">
            <summary>Serves as the default hash function. </summary>
            <returns>A hash code for the current object.</returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.IRuleset">
            <summary>
            Ruleset contains assessment rules.
            Rules define check selection for given target.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.IRuleset.Name">
            <summary>
            Get or sets the ruleset name.
            The name lets the user see where a check came from.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.IRuleset.Version">
            <summary>
            Get or sets ruleset version.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.IRuleset.GetSuspects(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,System.Collections.Generic.HashSet{System.String},System.Collections.Generic.HashSet{System.String})">
            <summary>
            For given <paramref name="target"/> finds rules adding, replacing, or modifying
            rules with given <paramref name="ids"/> or <paramref name="tags"/>.
            Adds IDs of rules found to <paramref name="ids"/> collection.
            </summary>
            <param name="target">Target object.</param>
            <param name="ids">Rule IDs.</param>
            <param name="tags">Rule tags.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.IRuleset.GetChecks(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.Checks.ICheck},System.Collections.Generic.HashSet{System.String},System.Collections.Generic.HashSet{System.String})">
            <summary>
            For given target constructs new checks or modifies <paramref name="baseChecks"/>
            according to <paramref name="target"/>, <paramref name="ids"/>, and <paramref name="tags"/>.
            </summary>
            <param name="target"></param>
            <param name="baseChecks"></param>
            <param name="ids"></param>
            <param name="tags"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.CheckModificationConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.CheckModificationConverter.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.Configuration.Customization.CheckRule,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.CheckModificationConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.Configuration.Customization.CheckRule,System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.GrouperConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.GrouperConverter.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.Data.Morphs.Grouper,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.GrouperConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.Data.Morphs.Grouper,System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlProbeQueryPropertyConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlProbeQueryPropertyConverter.CanConvert(System.Type)">
            <summary>
            Determines whether this instance can convert the specified object type.
            </summary>
            <param name="objectType">Type of the object.</param>
            <returns>
                 <c>true</c> if this instance can convert the specified object type; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlProbeQueryPropertyConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,System.Object,Newtonsoft.Json.JsonSerializer)">
            <summary>Reads the JSON representation of the object.</summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader" /> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
            <exception cref="T:Newtonsoft.Json.JsonReaderException"><paramref name="reader" /> is not valid JSON.</exception>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException">unsupported expression</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlProbeQueryPropertyConverter.WriteJson(Newtonsoft.Json.JsonWriter,System.Object,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
            <exception cref="T:Newtonsoft.Json.JsonSerializationException">Converter cannot write specified value to JSON.</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.AggregateFuncConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.AggregateFuncConverter.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.Data.IAggregateFunc,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.AggregateFuncConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.Data.IAggregateFunc,System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.AssessmentMessageConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.AssessmentMessageConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.ParameterizedText,System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.AssessmentMessageConverter.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.ParameterizedText,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ConditionConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ConditionConverter.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.Expressions.Condition,Newtonsoft.Json.JsonSerializer)">
            <summary>Writes the JSON representation of the object.</summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter" /> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ConditionConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.Expressions.Condition,System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>Reads the JSON representation of the object.</summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader" /> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ExpressionConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ExpressionConverter.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.Expressions.Expression,Newtonsoft.Json.JsonSerializer)">
            <summary>Writes the JSON representation of the object.</summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter" /> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ExpressionConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.Expressions.Expression,System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>Reads the JSON representation of the object.</summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader" /> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ExtendedRegexConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ExtendedRegexConverter.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.Configuration.IPattern{System.String},Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ExtendedRegexConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.Configuration.IPattern{System.String},System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.JsonConverter`1">
            <summary>
            Converts an object to and from JSON.
            </summary>
            <typeparam name="T">The object type to convert.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.JsonConverter`1.WriteJson(Newtonsoft.Json.JsonWriter,System.Object,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
            <exception cref="T:Newtonsoft.Json.JsonSerializationException">Converter cannot write specified value to JSON.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.JsonConverter`1.WriteJson(Newtonsoft.Json.JsonWriter,`0,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.JsonConverter`1.ReadJson(Newtonsoft.Json.JsonReader,System.Type,System.Object,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
            <exception cref="T:Newtonsoft.Json.JsonSerializationException">Converter cannot read JSON with the specified existing value..</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.JsonConverter`1.ReadJson(Newtonsoft.Json.JsonReader,System.Type,`0,System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.JsonConverter`1.CanConvert(System.Type)">
            <summary>
            Determines whether this instance can convert the specified object type.
            </summary>
            <param name="objectType">Type of the object.</param>
            <returns>
                 <c>true</c> if this instance can convert the specified object type; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ProbeCollectionConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ProbeCollectionConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection,System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ProbeCollectionConverter.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ProbeConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ProbeConverter.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.Probes.IProbe,Newtonsoft.Json.JsonSerializer)">
            <summary>Writes the JSON representation of the object.</summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter" /> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ProbeConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.Probes.IProbe,System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>Reads the JSON representation of the object.</summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader" /> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
            <exception cref="T:Newtonsoft.Json.JsonReaderException"><paramref name="reader" /> is not valid JSON.</exception>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException">Condition.</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ProbeReferenceConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ProbeReferenceConverter.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.ProbeReferenceConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference,System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.DataMorphConverter.CanWrite">
            <summary>
            Gets a value indicating whether this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON.
            </summary>
            <value><c>true</c> if this <see cref="T:Newtonsoft.Json.JsonConverter" /> can write JSON; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.DataMorphConverter.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.IDataMorph,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.DataMorphConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.IDataMorph,System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter`1">
            <summary>
            This class converts <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> to/from JSON.
            </summary>
            <typeparam name="T">Any <see cref="T:System.IComparable`1"/> type as domain for range.</typeparam>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter`1.ParsingPattern">
            <summary>
            Represents range string syntax.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter`1.#ctor(Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter{`0}.ParserDelegate)">
            <summary>
            Initializes a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter`1"/> instance with specified <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter`1.ParserDelegate"/>.
            </summary>
            <param name="parser">A <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter`1.ParserDelegate"/> converting domain value of type T from a string.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter`1.ParserDelegate">
            <summary>
            Delegate for methods converting domain value of type T from a string.
            </summary>
            <param name="input">Input <see cref="T:System.String"/> to be converted to T.</param>
            <param name="result">Output variable of type T filled with converted value.</param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter`1.Parser">
            <summary>
            Gets a <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter`1.ParserDelegate"/> passed to the constructor.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter`1.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.Configuration.Range{`0},Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the range.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeConverter`1.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.Configuration.Range{`0},System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the range.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The range.</returns>
            <exception cref="T:Newtonsoft.Json.JsonSerializationException">JSON cannot be parsed as a<see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/>.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeListConverter`1.WriteJson(Newtonsoft.Json.JsonWriter,Microsoft.SqlServer.Management.Assessment.Configuration.RangeList{`0},Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.RangeListConverter`1.ReadJson(Newtonsoft.Json.JsonReader,System.Type,Microsoft.SqlServer.Management.Assessment.Configuration.RangeList{`0},System.Boolean,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read. If there is no existing value then <c>null</c> will be used.</param>
            <param name="hasExistingValue">The existing value has a value.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException">
            <summary>
            The exception that is thrown when an error occurs
            during serialization or deserialization of Engine configuration.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.#ctor">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the Newtonsoft.Json.JsonSerializationException class.
            </summary>
            <param name="info">The System.Runtime.Serialization.SerializationInfo that holds
            the serialized object data about the exception being </param>
            <param name="context">The System.Runtime.Serialization.StreamingContext that contains
            contextual information about the source or destination.</param>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name
            is <see langword="null"/> or <see cref="P:System.Exception.HResult"/> is zero (0).</exception>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info"/> is <see langword="null"/>,</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.#ctor(System.String)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/> class.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/> class.
            </summary>
            <param name="message">The message that describes the error.</param>
            <param name="innerException">The exception that is the cause of the current exception.
            If the innerException parameter is not a null reference (Nothing in Visual Basic),
            the current exception is raised in a catch block that handles the inner exception.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.#ctor(System.String,System.String,System.Int32,System.Int32,System.Exception)">
            <summary>
            Initializes  new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/> with JSON location details.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="path">The path to the JSON where the error occurred.</param>
            <param name="lineNumber">The line number indicating where the error occurred.</param>
            <param name="linePosition">The line position indicating where the error occurred.</param>
            <param name="innerException">The exception that is the cause of the current exception, or <c>null</c> if no inner exception is specified.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.Create(Newtonsoft.Json.JsonReader,System.String,System.Exception)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/> specifying current JSON location.
            </summary>
            <param name="reader"></param>
            <param name="message"></param>
            <param name="innerException"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.Create(System.String,Newtonsoft.Json.IJsonLineInfo,System.String,System.Exception)">
            <summary>
            This helper method creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/> object populated with JSON location path,
            as well as line info, and line position data.
            </summary>
            <param name="path">JSON path to location where the error was encountered.</param>
            <param name="lineInfo"><see cref="T:Newtonsoft.Json.IJsonLineInfo"/> pointing to stream position where the error was encountered.</param>
            <param name="message">Error message</param>
            <param name="innerException">Inner <see cref="T:System.Exception"/> providing more details.</param>
            <returns>Returns a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.Create(Newtonsoft.Json.JsonReader,System.String,System.Object,System.Exception)">
            <summary>
            This helper method creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/> object populated with data from JSON reader.
            </summary>
            <param name="reader"><see cref="T:Newtonsoft.Json.JsonReader"/> providing path and line info.</param>
            <param name="messageTemplate">A <see cref="T:System.String"/> containing a template used to build exception message.</param>
            <param name="obj1">The only argument for <paramref name="messageTemplate"/> used to build exception message.</param>
            <param name="innerException">Inner <see cref="T:System.Exception"/> if any.</param>
            <returns>Returns a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.Create(System.String,Newtonsoft.Json.IJsonLineInfo,System.String,System.Object,System.Exception)">
            <summary>
            This helper method creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/> object populated with JSON location path,
            as well as line info, and line position data.
            </summary>
            <param name="path">JSON path to location where the error was encountered.</param>
            <param name="lineInfo"><see cref="T:Newtonsoft.Json.IJsonLineInfo"/> pointing to stream position where the error was encountered.</param>
            <param name="messageTemplate">A <see cref="T:System.String"/> containing a template used to build exception message.</param>
            <param name="obj1">The only argument for <paramref name="messageTemplate"/> used to build exception message.</param>
            <param name="innerException">Inner <see cref="T:System.Exception"/> providing more details.</param>
            <returns>Returns a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.Create(Newtonsoft.Json.JsonReader,System.String,System.Object,System.Object,System.Exception)">
            <summary>
            This helper method creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/> object populated with data from JSON reader.
            </summary>
            <param name="reader"><see cref="T:Newtonsoft.Json.JsonReader"/> providing path and line info.</param>
            <param name="messageTemplate">A <see cref="T:System.String"/> containing a template used to build exception message.</param>
            <param name="obj1">The first argument for <paramref name="messageTemplate"/> used to build exception message.</param>
            <param name="obj2">The second argument for <paramref name="messageTemplate"/> used to build exception message.</param>
            <param name="innerException">Inner <see cref="T:System.Exception"/> if any.</param>
            <returns>Returns a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.Create(System.String,Newtonsoft.Json.IJsonLineInfo,System.String,System.Object,System.Object,System.Exception)">
            <summary>
            This helper method creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/> object populated with JSON location path,
            as well as line info, and line position data.
            </summary>
            <param name="path">JSON path to location where the error was encountered.</param>
            <param name="lineInfo"><see cref="T:Newtonsoft.Json.IJsonLineInfo"/> pointing to stream position where the error was encountered.</param>
            <param name="messageTemplate">A <see cref="T:System.String"/> containing a template used to build exception message.</param>
            <param name="obj1">The first argument for <paramref name="messageTemplate"/> used to build exception message.</param>
            <param name="obj2">The second argument for <paramref name="messageTemplate"/> used to build exception message.</param>
            <param name="innerException">Inner <see cref="T:System.Exception"/> providing more details.</param>
            <returns>Returns a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException.Create(Newtonsoft.Json.JsonReader,System.String,System.Object,System.Object,System.Object,System.Exception)">
            <summary>
            This helper method creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/> object populated with data from JSON reader.
            </summary>
            <param name="reader"><see cref="T:Newtonsoft.Json.JsonReader"/> providing path and line info.</param>
            <param name="messageTemplate">A <see cref="T:System.String"/> containing a template used to build exception message.</param>
            <param name="obj1">The first argument for <paramref name="messageTemplate"/> used to build exception message.</param>
            <param name="obj2">The second argument for <paramref name="messageTemplate"/> used to build exception message.</param>
            <param name="obj3">The third argument for <paramref name="messageTemplate"/> used to build exception message.</param>
            <param name="innerException">Inner <see cref="T:System.Exception"/> if any.</param>
            <returns>Returns a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SerializationUtil.CheckTokenType(Newtonsoft.Json.JsonReader,Newtonsoft.Json.JsonToken[])">
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException">Condition.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SerializationUtil.CheckNull(System.Object,Newtonsoft.Json.JsonReader,System.String)">
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException">Condition.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SerializationUtil.CheckNull(System.Object,Newtonsoft.Json.JsonReader,System.String,System.Object)">
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException">Condition.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SerializationUtil.CheckNull(System.Object,Newtonsoft.Json.JsonReader,System.String,System.Object,System.Object)">
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Json.SqlAssessmentJsonException">Condition.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternList`1.GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary>
            <returns>An enumerator that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternList`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary>
            <returns>An <see cref="T:System.Collections.IEnumerator"></see> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset">
            <summary>
            Ruleset contains assessment rules and probes.
            Rules define check selection process for given target.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset.#ctor">
            <summary>
            Initializes an empty instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset.Name">
            <summary>
            Get or sets the ruleset name.
            The name lets the user see where a check came from.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset.Version">
            <summary>
            Get or sets ruleset version.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset.SchemaVersion">
            <summary>
            Gets or sets ruleset schema version.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset.SchemaVersion"/> is useful for enabling compatibility to rulesets
            defined for previous versions of SQL Assessment engine.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset.Probes">
            <summary>
            Gets collection of all probes.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset.GetSuspects(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,System.Collections.Generic.HashSet{System.String},System.Collections.Generic.HashSet{System.String})">
            <summary>
            For given <paramref name="target"/> finds rules adding, replacing, or modifying
            rules with given <paramref name="ids"/> or <paramref name="tags"/>.
            Adds IDs of rules found to <paramref name="ids"/> collection.
            </summary>
            <param name="target">Target object.</param>
            <param name="ids">Rule IDs.</param>
            <param name="tags">Rule tags.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset.GetChecks(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.Checks.ICheck},System.Collections.Generic.HashSet{System.String},System.Collections.Generic.HashSet{System.String})">
            <summary>
            For given <paramref name="target"/> constructs new checks or modifies <paramref name="baseChecks"/>
            according to <paramref name="target"/>, <paramref name="ids"/>, and <paramref name="tags"/>.
            </summary>
            <param name="target"></param>
            <param name="baseChecks"></param>
            <param name="ids"></param>
            <param name="tags"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.AssessmentWarning">
            <summary>
            Warning is an assessment result describing not the target state but the process of assessment.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.AssessmentWarning.#ctor(Microsoft.SqlServer.Management.Assessment.Checks.Check,Microsoft.SqlServer.Management.Assessment.SqlObjectType,System.String,System.String)">
            <summary>
            Initializes a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.AssessmentWarning"/> with given target and <paramref name="message"/>.
            </summary>
            <param name="check">The check which produced this <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.AssessmentWarning"/>.</param>
            <param name="targetType">Type o target object.</param>
            <param name="targetPath">Path identifying the target object</param>
            <param name="message">A message <see cref="T:System.String"/> displayed to the user.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.AssessmentWarning.Message">
            <summary>
            Gets a <see cref="T:System.String"/> containing information message, recommendation, or error message ro this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.Message"/> is usually displayed to the end user.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.AssessmentWarning.Check">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> which gave this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.AssessmentWarning.TargetPath">
            <summary>
            Gets the path to the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.AssessmentWarning.TargetType">
            <summary>
            Gets the type of the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.AssessmentWarning.Timestamp">
            <summary>
            Gets timestamp of the issue
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig">
            <summary>
            Represents an <see cref="T:Microsoft.SqlServer.Management.Assessment.Engine"/> configurations. Contains all checks and probes.
            Configuration may be deserialized from JSON.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.CloneConnection">
            <summary>
            Gets or sets a value that indicates whether the <see cref="T:System.Data.Common.DbConnection"/> should be cloned while running assessment requests.
            </summary>
            <value>
            <c>true</c> if <see cref="T:System.Data.Common.DbConnection"/> should be cloned; otherwise <c>false</c>. <c>true</c> is the default.
            </value>
            <remarks>
            If <see cref="T:System.Data.Common.DbConnection"/> implementation does not support <see cref="T:System.ICloneable"/> this property is ignored.
            </remarks>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.MaxCheckResults">
            <summary>
            Gets or sets a value that indicates maximum number of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/> per check to return.
            </summary>
            <value>
            The number of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/> per check to return.
            </value>
            <remarks>
            Assessment process yields check results until <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.MaxCheckResults"/> results have been yielded or there are no more results.
            If <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.MaxCheckResults"/> is less than or equal to zero, it is ignored.
            </remarks>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.ShouldReportSuccess">
            <summary>
            Gets or sets a value indicating whether assessment engine should produce
            <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentSuccess"/> instance for every passed check.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.Rulesets">
            <summary>
            Gets collection of all loaded factories.
            Rulesets are identified by name.
            Multiple versions for every factory are supported.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.DefaultRuleset">
            <summary>
            Gets default factory.
            Always present.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.CurrentRulesets">
            <summary>
            Get current factory combination.
            Only those factories are invoked to get checks for any target.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.Version">
            <summary>
            Gets or sets configuration version.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.Name">
            <summary>
            Gets or sets rule set name.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.AddRuleset(Microsoft.SqlServer.Management.Assessment.Configuration.IRuleset,System.String)">
            <summary>
            Adds a factory to <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.Rulesets"/> according to its name (or alias) and version.
            </summary>
            <param name="item">A factory to load.</param>
            <param name="alias">An optional name replacement. For example, default factory is aliased with the empty string.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.GetChecks(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator)">
            <summary>
            Finds all checks for given <paramref name="target"/>.
            </summary>
            <param name="target">Target object info.</param>
            <returns>Returns all checks for given <paramref name="target"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.GetChecks(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Finds all checks for given <paramref name="target"/>,
            that match <paramref name="checkIdsOrTags"/>.
            </summary>
            <param name="target">Target object info.</param>
            <param name="checkIdsOrTags">Ids or tags selecting checks to run.</param>
            <returns>Returns all checks for given <paramref name="target"/>,
            that match <paramref name="checkIdsOrTags"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig.GetProbeImplementation(System.String,Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator)">
            <summary>
            Finds the most specific implementation of a probe <paramref name="probeId"/> for <paramref name="target"/>
            </summary>
            <param name="probeId">Identifier of the probe to look for.</param>
            <param name="target">Target object info.</param>
            <returns>Returns the most specific implementation of a probe <paramref name="probeId"/> for <paramref name="target"/>.</returns>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.ProbeNotFoundException">No implementation of probe <paramref name="probeId"/> for <paramref name="target"/>.</exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.IQuasiCheck">
            <summary>
            Quasicheck is an object substituted for an <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> in special cases.
            Usually quasicheck produces <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentWarning"/> or <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentError"/>.
            Quasicheck is not listed by Get-SqlAssessmentItem cmdlet, but the result of <see cref="M:Microsoft.SqlServer.Management.Assessment.Configuration.IQuasiCheck.GetMessage(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator)"/>
            is displayed as verbose output.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.IQuasiCheck.GetMessage(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator)">
            <summary>
            Builds a message to be displayed or the user instead of normal check.
            </summary>
            <param name="target">Target object.</param>
            <returns>Returns a message to be displayed or the user instead of normal check.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition">
            <summary>
            SQL Server edition.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition.None">
            <summary>
            Edition unknown.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition.PersonalOrDesktopEngine">
            <summary>
            Personal or desktop edition.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition.Standard">
            <summary>
            Standard edition.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition.Enterprise">
            <summary>
            Enterprise or developer edition.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition.Express">
            <summary>
            Express edition.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition.AzureDatabase">
            <summary>
            Azure SQL Database.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition.DataWarehouse">
            <summary>
            Azure Data Warehouse.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition.StretchDatabase">
            <summary>
            Azure Stretch Database.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition.ManagedInstance">
            <summary>
            Azure SQL Server Managed Instance.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition.Azure">
            <summary>
            Any Azure edition of SQL Server.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition.SqlServer">
            <summary>
            Any on-premises edition of SQL Server.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.IPattern`1">
            <summary>
            Generic pattern for an arbitrary type <typeparamref name="T"/>.
            For example, <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/> is a pattern for <see cref="T:System.Version"/>.
            Regular expression is pattern for <see cref="T:System.String"/> (see <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RegexPattern"/>).
            </summary>
            <typeparam name="T">Type of value to be matched.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.IPattern`1.IsMatch(`0)">
            <summary>
            Checks if <paramref name="value"/> matches this <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.IPattern`1"/>.
            </summary>
            <param name="value">Value to be matched.</param>
            <returns>Returns <see langword="true"/> if <paramref name="value"/> matches this
            <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.IPattern`1"/>, <see langword="false"/> otherwise.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2">
            <summary>
            An ordered collection of <typeparamref name="TValue"/>s associated to <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.IPattern`1"/>.
            <see cref="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.get_Item(`0)"/> returns the first <typeparamref name="TValue"/> whose
            pattern matches given <typeparamref name="TKey"/> value.
            </summary>
            <typeparam name="TKey">Type of keys.</typeparam>
            <typeparam name="TValue">Type of values stored in the collection.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.#ctor(System.Func{`1,Microsoft.SqlServer.Management.Assessment.Configuration.IPattern{`0}})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2"/>
            with a delegate returning <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.IPattern`1"/> for given <typeparamref name="TValue"/>.
            </summary>
            <param name="getPattern">
            Delegate returning <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.IPattern`1"/> for given <typeparamref name="TValue"/>.
            </param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.Count">
            <summary>Gets the number of elements contained in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</summary>
            <returns>The number of elements contained in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.IsReadOnly">
            <summary>Gets a value indicating whether the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" /> is read-only.</summary>
            <returns>true if the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" /> is read-only; otherwise, false.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.Item(`0)">
            <summary>
            Finds the first matching item in this collection.
            </summary>
            <param name="key">The key.</param>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">No pattern matches given <paramref name="key"/>.</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.Item(System.Int32)">
            <summary>Gets or sets the element at the specified index.</summary>
            <returns>The element at the specified index.</returns>
            <param name="index">The zero-based index of the element to get or set.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index" /> is not a valid index in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</exception>
            <exception cref="T:System.NotSupportedException">The property is set and the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" /> is read-only.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary>
            <returns>An enumerator that can be used to iterate through the collection.</returns>
            <filterpriority>1</filterpriority>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary>
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.Add(`1)">
            <summary>Adds an item to the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</summary>
            <param name="item">The object to add to the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</param>
            <exception cref="T:System.NotSupportedException">The <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" /> is read-only.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.AddRange(System.Collections.Generic.IEnumerable{`1})">
            <summary>
            Adds the elements of the specified collection to the end of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2"/>.
            </summary>
            <param name="collection">
            The collection whose elements should be added to the end of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2"/>.
            </param>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="collection" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`1})">
            <summary>
            Inserts the elements of a collection into the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" /> at the specified index.
            </summary>
            <param name="index">The zero-based index at which the new elements should be inserted.</param>
            <param name="collection">
            The collection whose elements should be inserted into the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.
            The collection itself cannot be <see langword="null" />, but it can contain elements that are <see langword="null" />,
            if type <typeparamref name="TValue" /> is a reference type.</param>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="collection" /> is <see langword="null" />.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index" /> is less than 0.-or-
            <paramref name="index" /> is greater than <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.Count" />.
            </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.Clear">
            <summary>Removes all items from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.Contains(`1)">
            <summary>Determines whether the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" /> contains a specific value.</summary>
            <returns>true if <paramref name="item" /> is found in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />; otherwise, false.</returns>
            <param name="item">The object to locate in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.CopyTo(`1[],System.Int32)">
            <summary>Copies the elements of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" /> to an <see cref="T:System.Array" />,
            starting at a particular <see cref="T:System.Array" /> index.</summary>
            <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements
            copied from <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="array" /> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="arrayIndex" /> is less than 0.</exception>
            <exception cref="T:System.ArgumentException">
            The number of elements in the source <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" /> is greater than
            the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.
            </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.Remove(`1)">
            <summary>Removes the first occurrence of a specific object from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</summary>
            <returns>true if <paramref name="item" /> was successfully removed from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />;
            otherwise, false. This method also returns false if <paramref name="item" /> is not found in the original
            <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</returns>
            <param name="item">The object to remove from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.IndexOf(`1)">
            <summary>Determines the index of a specific item in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</summary>
            <returns>The index of <paramref name="item" /> if found in the list; otherwise, -1.</returns>
            <param name="item">The object to locate in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.Insert(System.Int32,`1)">
            <summary>Inserts an item to the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" /> at the specified index.</summary>
            <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
            <param name="item">The object to insert into the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index" /> is not a valid index in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.RemoveAt(System.Int32)">
            <summary>Removes the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" /> item at the specified index.</summary>
            <param name="index">The zero-based index of the item to remove.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index" /> is not a valid index in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.TagItem(`1)">
            <summary>
            When overriden performs some manipulations on every <typeparamref name="TValue"/> before it
            is added to the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2"/>.
            </summary>
            <param name="value">The new value.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.CreateRecord(`1)">
            <summary>
            Creates a pair of patten and <paramref name="value"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>Returns a pair of pattern an <paramref name="value"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2.TryGetValue(`0,`1@)">
            <summary>
            Attempts to get the value associated with the specified key from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2" />.
            </summary>
            <param name="key">The key of the value to get.</param>
            <param name="value">When this method returns, contains the object from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2"/>
            that has the specified key, or the default value of the type if the operation failed.</param>
            <returns>true if the key was found in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.PatternDictionary`2"/>; otherwise, false.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection">
            <summary>
            Collection of probes maps probe family names to probe families.
            Use <see cref="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.TryGetImplementation(System.String,Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,Microsoft.SqlServer.Management.Assessment.Probes.IProbe@)"/> method to find an implementation for a probe applicable to given SQL Server object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Count">
            <summary>Gets the number of elements contained in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</summary>
            <returns>The number of elements contained in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</returns>
            <exception cref="T:System.OverflowException">The dictionary already contains the maximum number of elements (<see cref="F:System.Int32.MaxValue"></see>).</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.IsReadOnly">
            <summary>Gets a value indicating whether the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see> is read-only.</summary>
            <returns>true if the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see> is read-only; otherwise, false.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Keys">
            <summary>Gets an <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection" />
            containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
            <returns>A <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection" />
            containing the keys of the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Values">
            <summary>
            Gets a <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            <returns>
            A <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see> containing the values in the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Item(System.String)">
            <summary>Gets or sets the element with the specified key.</summary>
            <param name="key">The key of the element to get or set.</param>
            <returns>The element with the specified key.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="key">key</paramref> is null.</exception>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">The property is retrieved and <paramref name="key">key</paramref> is not found.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.TryGetImplementation(System.String,Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,Microsoft.SqlServer.Management.Assessment.Probes.IProbe@)">
            <summary>
            Finds the first probe implementation with given name and applicable to given SQL Server object.
            </summary>
            <param name="name">Probe name.</param>
            <param name="target">Target SQL Server object.</param>
            <param name="value">When this method returns, contains the object from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection" />
            that has the specified key, or the default value of the type if the operation failed.</param>
            <returns><see langword="true" /> if an implementation was found in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection" />
            for specified <paramref name="name"/> and <paramref name="target"/>; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.System#Collections#Generic#ICollection{Microsoft#SqlServer#Management#Assessment#Probes#ProbeFamily}#Add(Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily)">
            <summary>Adds an item to the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</summary>
            <param name="newItem">The object to add to the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="newItem"/> is <see langword="null"/></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Add(System.Collections.Generic.KeyValuePair{System.String,Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily})">
            <summary>Adds an item to the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</summary>
            <param name="item">The object to add to the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Clear">
            <summary>Removes all items from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection" />.</summary>
            <exception cref="T:System.NotSupportedException">The <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection" /> is read-only. </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Contains(System.Collections.Generic.KeyValuePair{System.String,Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily})">
            <summary>Determines whether the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see> contains a specific value.</summary>
            <param name="item">The object to locate in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</param>
            <returns>true if <paramref name="item">item</paramref> is found in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.CopyTo(System.Collections.Generic.KeyValuePair{System.String,Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily}[],System.Int32)">
            <summary>Copies the elements of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see> to an <see cref="T:System.Array"></see>, starting at a particular <see cref="T:System.Array"></see> index.</summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"></see> that is the destination of the elements copied from <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>. The <see cref="T:System.Array"></see> must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="array">array</paramref> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="arrayIndex">arrayIndex</paramref> is less than 0.</exception>
            <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see> is greater than the available space from <paramref name="arrayIndex">arrayIndex</paramref> to the end of the destination <paramref name="array">array</paramref>.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Remove(System.Collections.Generic.KeyValuePair{System.String,Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily})">
            <summary>Removes the first occurrence of a specific object from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</summary>
            <param name="item">The object to remove from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</param>
            <returns>true if <paramref name="item">item</paramref> was successfully removed from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>; otherwise, false. This method also returns false if <paramref name="item">item</paramref> is not found in the original <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</returns>
            <exception cref="T:System.NotSupportedException">The <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see> is read-only.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Contains(Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily)">
            <summary>Determines whether the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see> contains a specific value.</summary>
            <param name="item">The object to locate in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</param>
            <returns>true if <paramref name="item">item</paramref> is found in the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.CopyTo(Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily[],System.Int32)">
            <summary>Copies the elements of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see> to an <see cref="T:System.Array"></see>, starting at a particular <see cref="T:System.Array"></see> index.</summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"></see> that is the destination of the elements copied from <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>. The <see cref="T:System.Array"></see> must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="array">array</paramref> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="arrayIndex">arrayIndex</paramref> is less than 0.</exception>
            <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see> is greater than the available space from <paramref name="arrayIndex">arrayIndex</paramref> to the end of the destination <paramref name="array">array</paramref>.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Remove(Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily)">
            <summary>Removes the first occurrence of a specific object from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</summary>
            <param name="item">The object to remove from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</param>
            <returns>true if <paramref name="item">item</paramref> was successfully removed from the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>; otherwise, false. This method also returns false if <paramref name="item">item</paramref> is not found in the original <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see>.</returns>
            <exception cref="T:System.NotSupportedException">The <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection"></see> is read-only.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Add(System.String,Microsoft.SqlServer.Management.Assessment.Probes.IProbe)">
            <summary>
            Adds a new probe implementation for given name.
            </summary>
            <param name="name">Probe name.</param>
            <param name="newItem">A new implementation for probe <paramref name="name"/>.</param>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="name"/> of <paramref name="newItem"/> are <see langword="null"/>.
            </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{System#String,Microsoft#SqlServer#Management#Assessment#Probes#ProbeFamily}}#GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary>
            <returns>An enumerator that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary>
            <returns>An enumerator that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary>
            <returns>An <see cref="T:System.Collections.IEnumerator"></see> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Add(System.String,Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily)">
            <summary>Adds an element with the provided key and value to the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
            <param name="key">The object to use as the key of the element to add.</param>
            <param name="value">The object to use as the value of the element to add.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="key">key</paramref> is null.</exception>
            <exception cref="T:System.ArgumentException">An element with the same key already exists in the <see cref="T:System.Collections.Generic.IDictionary`2"></see>.</exception>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.IDictionary`2"></see> is read-only.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.ContainsKey(System.String)">
            <summary>Determines whether the <see cref="T:System.Collections.Generic.IDictionary`2"></see> contains an element with the specified key.</summary>
            <param name="key">The key to locate in the <see cref="T:System.Collections.Generic.IDictionary`2"></see>.</param>
            <returns>true if the <see cref="T:System.Collections.Generic.IDictionary`2"></see> contains an element with the key; otherwise, false.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="key">key</paramref> is null.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.Remove(System.String)">
            <summary>Removes the element with the specified key from the <see cref="T:System.Collections.Generic.IDictionary`2"></see>.</summary>
            <param name="key">The key of the element to remove.</param>
            <returns>true if the element is successfully removed; otherwise, false.  This method also returns false if <paramref name="key">key</paramref> was not found in the original <see cref="T:System.Collections.Generic.IDictionary`2"></see>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="key">key</paramref> is null.</exception>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.IDictionary`2"></see> is read-only.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.ProbeCollection.TryGetValue(System.String,Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily@)">
            <summary>Gets the value associated with the specified key.</summary>
            <param name="key">The key whose value to get.</param>
            <param name="value">When this method returns, the value associated with the specified key, if the key is found; otherwise, the default value for the type of the value parameter. This parameter is passed uninitialized.</param>
            <returns>true if the object that implements <see cref="T:System.Collections.Generic.IDictionary`2"></see> contains an element with the specified key; otherwise, false.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="key">key</paramref> is null.</exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1">
            <summary>
            A range of values of ordered type <typeparamref name="T"/>. <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> is a <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.IPattern`1"/>
            and <see cref="M:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.IsMatch(`0)"/> returns true for values inside the range between <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.Left"/> and <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.Right"/>.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.IncludesLeft"/> ans <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.IncludesRight"/> properties define if the range is inclusive.
            </summary>
            <typeparam name="T">An ordered nullable type.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.#ctor">
            <summary>
            Initializes an empty instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> matching nothing.
            The range includes both boundaries.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.#ctor(`0)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> which matches <paramref name="singleValue"/> only.
            The range includes both boundaries.
            </summary>
            <param name="singleValue">The only value that this range will contain.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.#ctor(`0,`0,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> with boundaries <paramref name="left"/> and <paramref name="right"/>.
            The range include both boundaries by default but this behaviour may be altered with optional parameters
            <paramref name="includesLeft"/> nad <paramref name="includesRight"/>.
            </summary>
            <param name="left">Left boundary for the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/>.</param>
            <param name="right">Right boundary for the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/>.</param>
            <param name="includesLeft"> Determines if the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> includes its left boundary.</param>
            <param name="includesRight">Determines if the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> includes its right boundary.</param>
            <exception cref="T:System.ArgumentException">Singular range does not include its bound(s).</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.Left">
            <summary>
            Gets the left boundary of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.Right">
            <summary>
            Gets the right boundary of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.IncludesLeft">
            <summary>
            Gets a value indicating if the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> includes its left boundary.
            </summary>
            <returns>Returns true if the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> includes its left boundary. Otherwise returns false.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.IncludesRight">
            <summary>
            Gets a value indicating of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> includes its right boundary.
            </summary>
            <returns>Returns true if the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> includes its right boundary. Otherwise returns false.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.IsEmpty">
            <summary>
            Gets a value indicating if the range is empty. Empty range does not match any value.
            </summary>
            <returns>Returns true if the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> is empty. Otherwise returns false.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.IsSingular">
            <summary>
            Gets a value indicating if the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> contains exactly one value.
            In this case <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.Left"/> equals <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.Right"/>
            and <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.IncludesRight"/> and <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.IncludesLeft"/> are true.
            </summary>
            <returns>Returns true if the range contains exactly one value. Otherwise returns false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <returns>true if the specified object  is equal to the current object; otherwise, false.</returns>
            <param name="obj">The object to compare with the current object. </param>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.Equals(Microsoft.SqlServer.Management.Assessment.Configuration.Range{`0})">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <returns>true if the specified object  is equal to the current object; otherwise, false.</returns>
            <param name="other">The object to compare with the current object. </param>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.GetHashCode">
            <summary>Serves as the default hash function. </summary>
            <returns>A hash code for the current object.</returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.IsMatch(`0)">
            <summary>
            Checks if <paramref name="value"/> falls within the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/>.
            </summary>
            <param name="value">Value to be matched.</param>
            <returns>Returns <see langword="true"/> if <paramref name="value"/> falls within the
            <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/>, <see langword="false"/> otherwise.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1">
            <summary>
            A set of zero or more ranges of ordered type <typeparamref name="T"/>.
            <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/> as an <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.IPattern`1"/> matches any value that falls within
            any of its ranges.
            </summary>
            <typeparam name="T">An ordered nullable type.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1.#ctor">
            <summary>
            Initializes an empty instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/> matching nothing.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1.#ctor(System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.Configuration.Range{`0}})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/> wth copies of given <paramref name="ranges"/>.
            </summary>
            <param name="ranges">Ranges to be copied into new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/>.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1.#ctor(Microsoft.SqlServer.Management.Assessment.Configuration.Range{`0},Microsoft.SqlServer.Management.Assessment.Configuration.Range{`0}[])">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/> wth copies of given ranges.
            </summary>
            <param name="head">The first <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> to be copied into new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/>.</param>
            <param name="rest">The rest of ranges to be copied into new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/>.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="rest"/> is <see langword="null"/></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1.#ctor(`0)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/> with single value. The <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/>
            will contain only one singular <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> matching the <paramref name="singleValue"/>.
            </summary>
            <param name="singleValue">the single value to be matched by the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/>.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1.#ctor(`0,`0,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/> with boundaries <paramref name="left"/> and <paramref name="right"/>.
            The range include both boundaries by default but this behaviour may be altered with optional parameters
            <paramref name="includesLeft"/> nad <paramref name="includesRight"/>. The <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/> will contain only
            one <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Range`1"/> with given properties.
            </summary>
            <param name="left">Left boundary for the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/>.</param>
            <param name="right">Right boundary for the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/>.</param>
            <param name="includesLeft"> Determines if the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/> includes its left boundary.</param>
            <param name="includesRight">Determines if the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/> includes its right boundary.</param>
            <exception cref="T:System.ArgumentException">Singular range does not include its bound(s).</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1.Ranges">
            <summary>
            Gets the list of ranges comprising the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1.IsEmpty">
            <summary>
            Gets the value indicating that the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/>  doesn't contain any object.
            This means either <see cref="P:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1.Ranges"/> is empty or every its element is an empty range.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1.IsMatch(`0)">
            <summary>
            Checks if <paramref name="value"/> matches this <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.IPattern`1"/>.
            </summary>
            <param name="value">Value to be matched.</param>
            <returns>
            Returns <see langword="true"/> if <paramref name="value"/> falls inside any range
            comprising the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.RangeList`1"/>, <see langword="false"/> otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern">
            <summary>
            SqlObjectLocatorPattern specifies pattern for SQL Server objects. <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern"/>s are used
            to select appropriate checks and probes for given targets.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.#ctor(Microsoft.SqlServer.Management.Assessment.Configuration.RangeList{System.Version},Microsoft.SqlServer.Management.Assessment.Configuration.IPattern{System.String},Microsoft.SqlServer.Management.Assessment.SqlObjectType,Microsoft.SqlServer.Management.Assessment.Configuration.IPattern{System.String},Microsoft.SqlServer.Management.Assessment.Configuration.SqlEngineEdition,Microsoft.SqlServer.Management.Assessment.Configuration.IPattern{System.String},Microsoft.SqlServer.Management.Assessment.Configuration.IPattern{System.String})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern"/>
            with given patterns for components of SQL Server object locator.
            </summary>
            <param name="version">A pattern for version of SQL Server hosting matching object.</param>
            <param name="platform">A pattern for platform code hosting matching object.</param>
            <param name="objectType">A patterns for matching SQL Server object type.</param>
            <param name="namePattern">A pattern for matching object name.</param>
            <param name="engineEdition">A pattern matching SQL Server edition.</param>
            <param name="serverNamePattern">A pattern matching name of target server.</param>
            <param name="machineTypePattern">A pattern matching host machine type (physical, Hyper-V, Azure).</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.Version">
            <summary>
            Gets pattern for version of SQL Server hosting matching object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.Platform">
            <summary>
            Gets pattern for platform code hosting matching object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.ObjectType">
            <summary>
            Gets pattern for matching SQL Server object type.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.NamePattern">
            <summary>
            Gets pattern for matching object name.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.ServerNamePattern">
            <summary>
            Gets pattern for matching names of target servers.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.MachineTypePattern">
            <summary>
            Gets pattern for matching virtual machine hosting target servers.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.EngineEdition">
            <summary>
            Gets pattern for matching SQL Server edition.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.IsMatch(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator)">
            <summary>
            Checks if <paramref name="value"/> matches the <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern"/>.
            </summary>
            <param name="value">Value to be matched.</param>
            <returns>
            Returns <see langword="true"/> if <paramref name="value"/> matches the
            <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern"/>, <see langword="false"/> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.Equals(Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <param name="other">An object to compare with this object.</param>
            <returns>true if the current object is equal to the <paramref name="other">other</paramref> parameter; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns>true if the specified object  is equal to the current object; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.SqlObjectLocatorPattern.GetHashCode">
            <summary>Serves as the default hash function.</summary>
            <returns>A hash code for the current object.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Configuration.WarningCheck">
            <summary>
            WarningCheck produces <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentWarning"/>(s) instead of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentNote"/>(s).
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.WarningCheck.#ctor(System.String)">
            <summary>
            Initializes a new warning check with <paramref name="message"/>.
            </summary>
            <param name="message">Message displayed to the user as warning.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Configuration.WarningCheck.GetMessage(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator)">
            <summary>
            Builds a message to be displayed or the user instead of normal check.
            </summary>
            <param name="target">Target object.</param>
            <returns>Returns a message to be displayed or the user instead of normal check.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Data.Morphs.DefaultValueMorph.BenefitsFromCache">
            <summary>
            Gets a value indicating if this object implement a cache.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Data.Morphs.DefaultValueMorph.ApplyTo(Microsoft.SqlServer.Management.Assessment.IDataView,System.Boolean)">
            <summary>
            Applies the transformation to the <paramref name="source"/> data.
            Result caching may be disabled with <paramref name="useCache"/> = <c>false</c>.
            </summary>
            <param name="source">An <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataView"/> of the data to be transformed.</param>
            <param name="useCache">Indicates if cache should be created and used.
            <c>true</c> saves time for some <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataMorph"/>s,
            while <c>false</c> saves memory.</param>
            <returns>Returns a new <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataView"/> of transformed data.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Data.Morphs.ToStringMorph.ApplyTo(Microsoft.SqlServer.Management.Assessment.IDataView,System.Boolean)">
            <summary>
            Applies the transformation to the <paramref name="source" /> data.
            Result caching may be disabled with <paramref name="useCache" /> = <c>false</c>.
            </summary>
            <param name="source">
            An <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataView" />
            of the data to be transformed.
            </param>
            <param name="useCache">
            Indicates if cache should be created and used.
            <c>true</c> saves time for some <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataMorph" />s,
            while <c>false</c> saves memory.
            </param>
            <returns>
            Returns a new <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataView" />
            of transformed data.
            </returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Data.Morphs.ToStringMorph.BenefitsFromCache">
            <summary>
            Gets a value indicating if this object implement a cache.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Expressions.INamedVariableConsumer">
            <summary>
            An implementer of this interface requests values of named variables.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.INamedVariableConsumer.GetNames">
            <summary>
            Gets all names that may be accessed by this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.INamedVariableConsumer"/>.
            </summary>
            <returns>Returns an enumeration of variable names.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.IntervalExpression.Copy">
            <summary>
            Copy operation specific to derived class. Overrides must return instances of the same class as this object.
            </summary>
            <returns>An instance of the same class as this object.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.SequentialCompositeExpression.#ctor(System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.Expressions.Expression})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression"/> with a sequence of child items.
            </summary>
            <param name="source">A sequence of child <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>s.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="source"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.SequentialCompositeExpression.#ctor">
            <summary>
            Initializes a new instance os <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression"/> with no child expressions.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.SequentialCompositeExpression.EvaluateCore(Microsoft.SqlServer.Management.Assessment.IEvaluationContext)">
            <summary>
            Calculates <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression"/>'s value.
            </summary>
            <param name="data">Data used to get calculation arguments and parameter values.</param>
            <returns>Returns calculated <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/> value.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="data"/> is <see langword="null"/></exception>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException">
            This <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/> was called recursively.
            </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.SequentialCompositeExpression.EvaluateCore(System.Collections.Generic.IEnumerable{System.Object})">
            <summary>
            In derived classes defines the operation for this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>.
            </summary>
            <param name="args">An sequence of results of <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>s from Children in the same order.</param>
            <returns>Returns an object which is the result of the operation.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.ArrayExpression.Equals(Microsoft.SqlServer.Management.Assessment.Expressions.ArrayExpression)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <param name="other">An object to compare with this object.</param>
            <returns>true if the current object is equal to the <paramref name="other">other</paramref> parameter; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.ArrayExpression.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns>true if the specified object  is equal to the current object; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.ArrayExpression.GetHashCode">
            <summary>Serves as the default hash function.</summary>
            <returns>A hash code for the current object.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression">
            <summary>
            <para>Abstract CompositeExpression is base class for expressions performing their operations on sets of other expressions.
            Derived classes define the operation, allowed types and number of child expressions, and return type.</para>
            <para>Add o remove child expressions with <see cref="P:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression.Children"/> property.</para>
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression.#ctor(System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.Expressions.Expression})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression"/> with a sequence of child items.
            </summary>
            <param name="source">A sequence of child <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>s.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="source"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression.#ctor">
            <summary>
            Initializes a new instance os <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression"/> with no child expressions.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression.Children">
            <summary>
            Gets an ordered collection of child expressions.
            This <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression"/> will apply its operation on their results.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression.Equals(Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <param name="other">An object to compare with this object.</param>
            <returns>true if the current object is equal to the <paramref name="other">other</paramref> parameter; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns>true if the specified object  is equal to the current object; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression.GetHashCode">
            <summary>Serves as the default hash function.</summary>
            <returns>A hash code for the current object.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.CompositeExpression.Copy">
            <summary>
            Copy operation specific to derived class. Overrides must return instances of the same class as this object.
            </summary>
            <returns>An instance of the same class as this object.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.ColumnExpression.Equals(Microsoft.SqlServer.Management.Assessment.Expressions.ColumnExpression)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <param name="other">An object to compare with this object.</param>
            <returns>true if the current object is equal to the <paramref name="other">other</paramref> parameter; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.ColumnExpression.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns>true if the specified object  is equal to the current object; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.ColumnExpression.GetHashCode">
            <summary>Serves as the default hash function.</summary>
            <returns>A hash code for the current object.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Expressions.Condition">
            <summary>
            Defines condition for a check which must be satisfied when the object conforms to a guideline.
            Every check issues an <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentNote"/> for every guideline violation.
            <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Condition"/> forwards Evaluation requests to its <see cref="P:Microsoft.SqlServer.Management.Assessment.Expressions.Condition.Implementation"/> <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Condition.#ctor(Microsoft.SqlServer.Management.Assessment.Expressions.Expression)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Condition"/> with given implementation.
            </summary>
            <param name="implementation">
            This <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Condition"/> will forward evaluation requests to this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>.
            </param>
            <exception cref="T:System.ArgumentNullException"><paramref name="implementation"/> is <see langword="null"/>.</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Expressions.Condition.Implementation">
            <summary>
            Gets <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/> implementing this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Condition"/>.
            </summary>
            <returns>
            Returns <see langword="true"/> when the object conforms the guideline. <see langword="false"/> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Condition.Equals(Microsoft.SqlServer.Management.Assessment.Expressions.Condition)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <param name="other">An object to compare with this object.</param>
            <returns>true if the current object is equal to the <paramref name="other">other</paramref> parameter; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Condition.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns>true if the specified object  is equal to the current object; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Condition.GetHashCode">
            <summary>Serves as the default hash function.</summary>
            <returns>A hash code for the current object.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Condition.EvaluateCore(Microsoft.SqlServer.Management.Assessment.IEvaluationContext)">
            <summary>
            Calculates <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Condition"/> value.
            The call is forwarded to <see cref="P:Microsoft.SqlServer.Management.Assessment.Expressions.Condition.Implementation"/> and the result is cast to <see cref="T:System.Boolean"/>.
            </summary>
            <param name="data">Data used to get calculation arguments and parameter values.</param>
            <returns>Returns calculated <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/> value.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="data"/> is <see langword="null"/></exception>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException">
            This <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/> was called recursively.
            </exception>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException">
            Value returned by <see cref="P:Microsoft.SqlServer.Management.Assessment.Expressions.Condition.Implementation"/> cannot be cast to <see cref="T:System.Boolean"/>.
            </exception>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException">
            <see cref="T:System.String"/> returned by <see cref="P:Microsoft.SqlServer.Management.Assessment.Expressions.Condition.Implementation"/> cannot be converted to <see cref="T:System.Boolean"/>.
            </exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression">
            <summary>
            Expressions comprise definitions for check conditions and parameters.
            Parameter values and conditions are <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>s themselves.
            Constant values are represented by <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> class.
            Other <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>s represent logical, comparison, or other operations.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Expressions.Expression.VariablePrefix">
            <summary>
            This <see cref="T:System.Char"/> is used as prefix to use JSON strings as parameters.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Expression.op_Implicit(System.Int32)~Microsoft.SqlServer.Management.Assessment.Expressions.Expression">
            <summary>
            Creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/>.
            </summary>
            <param name="value">Value for the literal.</param>
            <returns>Returns a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Expression.op_Implicit(System.Decimal)~Microsoft.SqlServer.Management.Assessment.Expressions.Expression">
            <summary>
            Creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/>.
            </summary>
            <param name="value">Value for the literal.</param>
            <returns>Returns a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Expression.op_Implicit(System.String)~Microsoft.SqlServer.Management.Assessment.Expressions.Expression">
            <summary>
            Creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/>.
            </summary>
            <param name="value">Value for the literal.</param>
            <returns>Returns a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Expression.op_Implicit(System.DateTime)~Microsoft.SqlServer.Management.Assessment.Expressions.Expression">
            <summary>
            Creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/>.
            </summary>
            <param name="value">Value for the literal.</param>
            <returns>Returns a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Expression.op_Implicit(System.Boolean)~Microsoft.SqlServer.Management.Assessment.Expressions.Expression">
            <summary>
            Creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/>.
            </summary>
            <param name="value">Value for the literal.</param>
            <returns>Returns a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Expression.Evaluate(Microsoft.SqlServer.Management.Assessment.IEvaluationContext)">
            <summary>
            Calculates <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/> value.
            </summary>
            <param name="data">Data used to get calculation arguments and parameter values.</param>
            <returns>Returns calculated <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/> value.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="data"/> is <see langword="null"/></exception>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException">
            This <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/> was called recursively.
            </exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Expressions.ExpressionByReferenceComparer">
            <summary>
            Compares expressions by reference only.
            </summary>
            <remarks>
            By default expressions are compared by value.
            For example, two <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.ColumnExpression"/>s are equal if they refer to the same column name.
            </remarks>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.ExpressionByReferenceComparer.Equals(Microsoft.SqlServer.Management.Assessment.Expressions.Expression,Microsoft.SqlServer.Management.Assessment.Expressions.Expression)">
            <summary>Determines whether the specified objects are equal.</summary>
            <param name="x">The first object of type T to compare.</param>
            <param name="y">The second object of type T to compare.</param>
            <returns>true if the specified objects are equal; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.ExpressionByReferenceComparer.GetHashCode(Microsoft.SqlServer.Management.Assessment.Expressions.Expression)">
            <summary>Returns a hash code for the specified object.</summary>
            <param name="obj">The <see cref="T:System.Object"></see> for which a hash code is to be returned.</param>
            <returns>A hash code for the specified object.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1">
             <summary>
             Represents  literal expression storing constant value of type <typeparamref name="T"/>.
             </summary>
             <example>
             For the following <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>s <see cref="M:Microsoft.SqlServer.Management.Assessment.Expressions.Expression.Evaluate(Microsoft.SqlServer.Management.Assessment.IEvaluationContext)"/> always returns <see langword="false"/>.
             <code>
             var expr1 = new AndExpression(new Expression[]      // true &amp;&amp; false
                 {
                     new Literal&lt;bool&gt;(true),
                     new Literal&lt;bool&gt;(false)
                 });
            
             var r1 = expr1.Evaluate(...) // r1 is always false
             var expr2 = new LessExpression(new Expression[]     // 42 &lt; 12
                 {
                     new Literal&lt;decimal&gt;(42),
                     new Literal&lt;decimal&gt;(12)
                 });
             var r2 = expr2.Evaluate(...) // r2 is always false
             </code>
             </example>
             <typeparam name="T">Type of stored literal value.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1.#ctor(`0)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> class.
            </summary>
            <param name="value">The value stored in this instance.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1.Value">
            <summary>
            Gets the value which will be returned by <see cref="M:Microsoft.SqlServer.Management.Assessment.Expressions.Expression.Evaluate(Microsoft.SqlServer.Management.Assessment.IEvaluationContext)"/> method of this instance.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1.EvaluateCore(Microsoft.SqlServer.Management.Assessment.IEvaluationContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1.CompareTo(Microsoft.SqlServer.Management.Assessment.Expressions.Literal{`0})">
            <summary>
            Compares this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> to other object.
            Returns 0 if equal, -1 if this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> is less than <paramref name="other"/>,
            1 if this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> is greater than <paramref name="other"/>
            </summary>
            <param name="other">Object to compare.</param>
            <returns>
            Returns 0 if equal, -1 if this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> is less than <paramref name="other"/>,
            1 if this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> is greater than <paramref name="other"/>
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1.CompareTo(System.Object)">
            <summary>
            Compares this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> to other object.
            Returns 0 if equal, -1 if this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> is less than <paramref name="obj"/>,
            1 if this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> is greater than <paramref name="obj"/>
            </summary>
            <param name="obj">Object to compare.</param>
            <returns>
            Returns 0 if equal, -1 if this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> is less than <paramref name="obj"/>,
            1 if this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> is greater than <paramref name="obj"/>
            </returns>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException">
            <paramref name="obj"/> is not <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/>.
            </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns>true if the specified object  is equal to the current object; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1.Equals(Microsoft.SqlServer.Management.Assessment.Expressions.Literal{`0})">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Expressions.NullLiteralExpression">
            <summary>
            A literal <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/> returning <see langword="null"/>.
            It's a singleton to avoid unnecessary memory allocations.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.NullLiteralExpression.#ctor">
            <summary>
            Constructor is private to implement singleton pattern.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Expressions.NullLiteralExpression.Instance">
            <summary>
            The only <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.NullLiteralExpression"/> instance implementing singleton.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.NullLiteralExpression.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.NullLiteralExpression.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Expressions.NullLiteralExpression.EvaluateCore(Microsoft.SqlServer.Management.Assessment.IEvaluationContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.IAssessmentRequest">
            <summary>
            Represents a single request for assessment.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IAssessmentRequest.Checks">
            <summary>
            Gets all checks to be run against the Target.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IAssessmentRequest.EvaluationContext">
            <summary>
            Gets the outer scope for this assessment task.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.IAssessmentSuccess">
            <summary>
            Represents successfully passed check.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.ITargetedObject">
            <summary>
            An interface for any object that has a target
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ITargetedObject.Target">
            <summary>
            Gets description of the object to be assessed.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Logics.ILogicsProvider">
            <summary>
            A general <see langword="interface"/> for objects implementing assessment process.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Logics.ILogicsProvider.GetAssessmentResults(Microsoft.SqlServer.Management.Assessment.IAssessmentRequest,System.Data.Common.DbConnection,Microsoft.SqlServer.Management.Assessment.Configuration.EngineConfig)">
            <summary>
            Runs assessment.
            </summary>
            <param name="request">An assessment request describing tarted and check.</param>
            <param name="connection">An open connection to target server.</param>
            <param name="configuration">Engine configuration.</param>
            <returns>Returns zero or more assessment results.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.CollectionUtil.SplitWith``1(System.Collections.Generic.HashSet{``0},System.Collections.Generic.HashSet{``0})">
            <summary>
            For two sets finds intersection, and trims each argument with another one.
            </summary>
            <typeparam name="T">Type of HasSet's element.</typeparam>
            <param name="left">The left argument. Will be trimmed by <paramref name="right"/>.</param>
            <param name="right">The right argument. Will be trimmed by <paramref name="left"/></param>
            <returns>Returns intersection of <paramref name="left"/> and <paramref name="right"/>.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Tools.IAzGraphTool">
            <summary>
            An interface for communicating to Azure services.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Tools.IAzGraphTool.Query(System.String,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            This method invokes a Kusto query with parameters to get data from Azure.
            </summary>
            <param name="query">A Kusto query to invoke.</param>
            <param name="parameters">Query parameters.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>Returns an <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataView"/> containing data returned from Azure.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Tools.SingleFieldMeasurement.TryGetData(System.String,System.Object@)">
            <summary>
            Obtains data from this <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataRow" /> by name.
            Returns <c>true</c> if data was registered for this <paramref name="column" />, and <c>false</c> otherwise.
            </summary>
            <param name="column">Column name.</param>
            <param name="value">Data if any. Initialized with null otherwise.</param>
            <returns>
            Returns <c>true</c> if data was registered for this <paramref name="column" />, and <c>false</c> otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Tools.ITool">
            <summary>
            A channel of communication to the target server.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Tools.ITool.GetDependencies">
            <summary>
            Lists characteristics required for this channel.
            </summary>
            <returns>Returns a <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/> required needed by this <see cref="T:Microsoft.SqlServer.Management.Assessment.Tools.ITool"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Tools.ITool.IsAvailable(System.Threading.CancellationToken)">
            <summary>
            Determines whether the specified run space is available.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>Returns <c>true</c> when the channel is ready to use. <c>false</c> otherwise.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Tools.IInventory">
            <summary>
            A DI container for channels.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Tools.IInventory.GetTools(Microsoft.SqlServer.Management.Assessment.RunSpace,System.Type)">
            <summary>
            Obtains all channels of type <paramref name="channelType"/>
            connected to the target of the <paramref name="runSpace"/>.
            </summary>
            <param name="runSpace"></param>
            <param name="channelType">Tpe of channel to return.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Tools.TwoFieldMeasurement.TryGetData(System.String,System.Object@)">
            <summary>
            Obtains data from this <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataRow" /> by name.
            Returns <c>true</c> if data was registered for this <paramref name="column" />, and <c>false</c> otherwise.
            </summary>
            <param name="column">Column name.</param>
            <param name="value">Data if any. Initialized with null otherwise.</param>
            <returns>
            Returns <c>true</c> if data was registered for this <paramref name="column" />, and <c>false</c> otherwise.
            </returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.AzStorageProbe.StorageDisk.IsPoolDisk">
            <summary>
            Get or sets a value indicating if this disk
            is a phycal disk comprising a storage pool.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.AzStorageProbe.StorageDisk.ColumnCount">
             <summary>
             Gets or sets virtual disk column count
             which is the number of physical disks
             used for the virtual one.
             Including this <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.AzStorageProbe.Disk"/>.
            
             Should be 1, if this <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.AzStorageProbe.Disk"/>
             is not a part of a storage pool.
             </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.AzStorageProbe.StorageDisk.PoolDiskCount">
             <summary>
             Gets or sets the number of physical disks
             comprising storage pool.
             Includes this <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.AzStorageProbe.Disk"/>.
            
             Should be 1, if this <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.AzStorageProbe.Disk"/>
             is not a part of a storage pool.
             </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.Characteristics.SysConfigurationsProvider.GetDisplayName(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Collections.Generic.HashSet{System.String})">
            <summary>
            Returns a <see cref="T:System.String"/> describing a requirement corresponding to given parameters.
            Used for generating a warning on insufficient permissions.
            </summary>
            <param name="key">The key.</param>
            <param name="arguments">Key arguments.</param>
            <param name="specification">The specification.</param>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Probes.Characteristics.ChannelPresenceProvider.StaticPattern">
            <summary>
            The static pattern is a global constant.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.Characteristics.ChannelPresenceProvider.GetDisplayName(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Collections.Generic.HashSet{System.String})">
            <summary>
            Returns a <see cref="T:System.String"/> describing a requirement corresponding to given parameters.
            Used for generating a warning on insufficient permissions.
            </summary>
            <param name="key">The key.</param>
            <param name="arguments">Key arguments.</param>
            <param name="specification">The specification.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.Characteristics.FeatureCharacteristicProvider.GetDisplayName(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Collections.Generic.HashSet{System.String})">
            <summary>
            Returns a <see cref="T:System.String"/> describing a requirement corresponding to given parameters.
            Used for generating a warning on insufficient permissions.
            </summary>
            <param name="key">The key.</param>
            <param name="arguments">Key arguments.</param>
            <param name="specification">The specification.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.Characteristics.PermissionsCharacteristicProvider.GetDisplayName(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Collections.Generic.HashSet{System.String})">
            <summary>
            Returns a <see cref="T:System.String"/> describing a requirement corresponding to given parameters.
            Used for generating a warning on insufficient permissions.
            </summary>
            <param name="key">The key.</param>
            <param name="arguments">Key arguments.</param>
            <param name="specification">The specification.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.Characteristics.RoleCharacteristicProvider.GetDisplayName(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Collections.Generic.HashSet{System.String})">
            <summary>
            Returns a <see cref="T:System.String"/> describing a requirement corresponding to given parameters.
            Used for generating a warning on insufficient permissions.
            </summary>
            <param name="key">The key.</param>
            <param name="arguments">Key arguments.</param>
            <param name="specification">The specification.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Probes.ICharacteristicCollection">
            <summary>
            Interface for a collection of desired or actual target's characteristics.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.ICharacteristicCollection.Add(System.String,System.String)">
            <summary>
            Adds a value to a given attribute
            </summary>
            <param name="key">Attribute name</param>
            <param name="value">A value to be added to the attribute's characteristic</param>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.Probes.DbaProbe.NameParameterName">
            <summary>
            SQL command parameter for target object name.
            Used as @name in SQL expressions.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Probes.IProbe">
            <summary>
            The minimal interface implemented by every probe.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.IProbe.Target">
            <summary>
            Gets a locator patterns specifying SQL Server objects supported by the probe.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.IProbe.FamilyName">
            <summary>
            Gets or sets probe family name. Checks refer to probes by this <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.IProbe.Morph">
            <summary>
            Gets or sets transformation applied to probe output on every call.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.IProbe.Subject">
            <summary>
            Gets the collection of characteristics of a reasonable target.
            <c>null</c> is equivalent to an empty collection.
            </summary>
            <remarks>
            The probe will not be run for targets not having all of the characteristics.
            </remarks>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.IProbe.Requirements">
            <summary>
            Gets the collection of target's characteristics which must be present
            for this <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.IProbe"/> to be run.
            </summary>
            <remarks>
            An exception will be thrown if the <see cref="P:Microsoft.SqlServer.Management.Assessment.Probes.IProbe.Requirements"/> were not met.
            </remarks>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.IProbe.ToolTypes">
            <summary>
            Gets the types of channels used by this <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.IProbe"/>.
            </summary>
            <value>
            The channel types used by this <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.IProbe"/>.
            </value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.IProbe.GetDataAsync(Microsoft.SqlServer.Management.Assessment.RunSpace,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object})">
            <summary>
            Implements the main probe logic obtaining information.
            </summary>
            <param name="runSpace"></param>
            <param name="parameters"></param>
            <returns>
            Returns zero or more <see cref="T:Microsoft.SqlServer.Management.Assessment.Measurement"/>s containing annotated data.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.ICharacteristicProvider.GetDisplayName(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Collections.Generic.HashSet{System.String})">
            <summary>
            Returns a <see cref="T:System.String"/> describing a requirement corresponding to given parameters.
            Used for generating a warning on insufficient permissions.
            </summary>
            <param name="key">The key.</param>
            <param name="arguments">Key arguments.</param>
            <param name="specification">The specification.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Probes.ProbeRequirementsException">
            <summary>
            This exception is thrown when a probe cannot be used
            due to insufficient user rights or absent transports or services.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.ProbeRequirementsException.MissingDependencies">
            <summary>
            Gets or sets the collection of requirements which were not met.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.ProbeRequirementsException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.ProbeRequirementsException"/> class.
            </summary>
            <param name="message">A message describing the exception.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.ProbeRequirementsException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.ProbeRequirementsException"/> class.
            with <paramref name="innerException"/>.
            </summary>
            <param name="message">A message describing the exception.</param>
            <param name="innerException">An exception wrapped by this one.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.ProbeRequirementsException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.ProbeRequirementsException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.ProbeRequirementsException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.ProbeRequirementsException"/> class.
            </summary>
            <param name="info">The System.Runtime.Serialization.SerializationInfo that holds
            the serialized object data about the exception being </param>
            <param name="context">The System.Runtime.Serialization.StreamingContext that contains
            contextual information about the source or destination.</param>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name
            is <see langword="null"/> or <see cref="P:System.Exception.HResult"/> is zero (0).</exception>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info"/> is <see langword="null"/>,</exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily">
            <summary>
            Named ordered collection of probe implementations.
            An implementation may be selected by an <see cref="T:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator"/>.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily"/>.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily"/> class with given name.
            </summary>
            <param name="name">The family name.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily.Name">
            <summary>
            Gets or sets the probe family name.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily.TagItem(Microsoft.SqlServer.Management.Assessment.Probes.IProbe)">
            <summary>
            When overriden performs some manipulations on every <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.IProbe"/> before it
            is added to the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.ProbeFamily"/>.
            </summary>
            <param name="value">The new value.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection">
            <summary>
            Contains desired or actual target's characteristics.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.Empty">
            <summary>
            Gets the empty characteristic collection.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.Total">
            <summary>
            Gets the total number of all characteristics.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.#ctor">
            <summary>
            Initializes an empty characteristics collection.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.#ctor(Microsoft.SqlServer.Management.Assessment.Probes.ICharacteristicCollection)">
            <summary>
            Copy constructor.
            </summary>
            <param name="other">Another collection to copy.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Serialization support constructor.
            </summary>
            <param name="serializationInfo"></param>
            <param name="streamingContext"></param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.#ctor(System.String,System.String[])">
            <summary>
            Initializes <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/>
            with given provider <paramref name="key"/>
            and its <paramref name="specification"/>.
            </summary>
            <param name="key">Provider key</param>
            <param name="specification">Specification interpreted by the provider.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.#ctor(System.String,System.Collections.Generic.HashSet{System.String})">
            <summary>
            Initializes <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/>
            with given provider <paramref name="key"/>
            and its <paramref name="specification"/>.
            </summary>
            <param name="key">Provider key</param>
            <param name="specification">Specification interpreted by the provider.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.UnionWith(Microsoft.SqlServer.Management.Assessment.Probes.ICharacteristicCollection)">
            <summary>
            Adds characteristics from <paramref name="other"/> collection
            if they were not present in this instance.
            </summary>
            <param name="other">A characteristic collection to fuse into this instance.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.IsSupersetOf(Microsoft.SqlServer.Management.Assessment.Probes.ICharacteristicCollection)">
            <summary>
            Tests if this characteristics collection represents a superset of <paramref name="other"/>.
            </summary>
            <param name="other">A characteristic collection to compare</param>
            <returns>
            Returns <c>true</c> when this instance represents a superset of <paramref name="other"/>,
            <c>false</c> otherwise
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.Union(System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.Probes.ICharacteristicCollection})">
            <summary>
            Creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/> containing
            the union of all the other <paramref name="collections"/>.
            </summary>
            <param name="collections"><see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/>s to unite.</param>
            <returns>
            Returns an empty <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/>
            when <paramref name="collections"/> is <c>null</c> or empty,
            or all <paramref name="collections"/> are empty.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.Union(Microsoft.SqlServer.Management.Assessment.Probes.ICharacteristicCollection[])">
            <summary>
            Creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/> containing
            the union of all the other <paramref name="collections"/>.
            </summary>
            <param name="collections"><see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/>s to unite.</param>
            <returns>
            Returns an empty <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/>
            when <paramref name="collections"/> is <c>null</c> or empty,
            or all <paramref name="collections"/> are empty.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.ExceptWith(Microsoft.SqlServer.Management.Assessment.Probes.ICharacteristicCollection)">
            <summary>
            Removes all items presented in the <paramref name="other"/> <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/>.
            </summary>
            <param name="other">Contains items to remove from this <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/>.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.Add(System.String,System.String)">
            <summary>
            Adds a value to a given attribute
            </summary>
            <param name="key">Attribute name</param>
            <param name="value">A value to be added to the attribute's characteristic</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection.Clone">
            <summary>
            Creates a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/> that is a copy of the current instance.
            </summary>
            <returns>
            A new <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.CharacteristicCollection"/> that is a copy of this instance.
            </returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException">
            <summary>
            Base class for exceptions specific to SQL Assessment API probes.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException"/> class.
            </summary>
            <param name="info">The System.Runtime.Serialization.SerializationInfo that holds
            the serialized object data about the exception being </param>
            <param name="context">The System.Runtime.Serialization.StreamingContext that contains
            contextual information about the source or destination.</param>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name
            is <see langword="null"/> or <see cref="P:System.Exception.HResult"/> is zero (0).</exception>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info"/> is <see langword="null"/>,</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException"/> class
            with specified error <paramref name="message"/>.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException"/> class
            with specified error <paramref name="message"/> template.
            Replaces the format item or items in the message template with <paramref name="arg"/>.
            </summary>
            <param name="message">
            The error message template that explains the reason for the exception.
            </param>
            <param name="arg">The object to format.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException"/> class with specified
            error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">
            The exception that is the cause of the current exception,
            or a null reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified.
            </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException.#ctor(System.Exception,System.String,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.Probes.SqlAssessmentProbeException"/> class with specified
            error message and a reference to the inner exception that is the cause of this exception.
            Replaces the format item or items in the message template with <paramref name="args"/>.
            </summary>
            <param name="message">
            The error message template that explains the reason for the exception.
            </param>
            <param name="innerException">
            The exception that is the cause of the current exception,
            or a null reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified.
            </param>
            <param name="args">The object array that contains one or more objects to format.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.MachineType">
            <summary>
            Indicates whether SQL Server is running in a virtualized environment.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.MachineType.Physical">
            <summary>
            SQL Server is not running inside a virtual machine.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.MachineType.AzureVm">
            <summary>
            SQL Server is running inside an Azure virtual machine.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.MachineType.Hypervisor">
            <summary>
            SQL Server is running inside a virtual machine hosted by an operating system running hypervisor (a host operating system that employs hardware-assisted virtualization).
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.MachineType.Other">
            <summary>
            SQL Server is running inside a virtual machine hosted by an operating system that does not employ hardware assistant.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ProbeRequirementsError.#ctor(Microsoft.SqlServer.Management.Assessment.Checks.ICheck,System.String,Microsoft.SqlServer.Management.Assessment.SqlObjectType,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeError"/> with given values when no exception occured.
            </summary>
            <param name="check">The check returning this <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeError"/>.</param>
            <param name="message">A <see cref="T:System.String"/> message explaining the issue to the user.</param>
            <param name="targetType">Type of target object.</param>
            <param name="targetPath">Path to the target object.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ProbeRequirementsError.#ctor(Microsoft.SqlServer.Management.Assessment.Checks.ICheck,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Exception}},Microsoft.SqlServer.Management.Assessment.SqlObjectType,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeError"/> with exceptions were caught.
            </summary>
            <param name="check">The check returning this <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeError"/>.</param>
            <param name="message">A <see cref="T:System.String"/> message explaining the issue to the user.</param>
            <param name="innerExceptions">A dictionary mapping probe names to exceptions.</param>
            <param name="targetType">Type of target object.</param>
            <param name="targetPath">Path to the target object.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.RequirementsWarning.Message">
            <summary>
            Gets a <see cref="T:System.String"/> containing information message, recommendation, or error message ro this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.Message"/> is usually displayed to the end user.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.RequirementsWarning.Check">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> which gave this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.RequirementsWarning.TargetPath">
            <summary>
            Gets the path to the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.RequirementsWarning.TargetType">
            <summary>
            Gets the type of the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.RequirementsWarning.Timestamp">
            <summary>
            Gets timestamp of the issue
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SpecGroupList.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary>
            <returns>An <see cref="T:System.Collections.IEnumerator"></see> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.DataSources.IAzureInstanceMetadataSource">
            <summary>
            A data source for direct communication to Azure Instance Metadata Service.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DataSources.IAzureInstanceMetadataSource.QueryData(System.Uri,System.Threading.CancellationToken)">
            <summary>
            This method queries data from Azure Instance Metadata Service.
            </summary>
            <param name="endpoint">An endpoint to query.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>Returns an <see cref="T:System.String"/> containing data returned from Azure Instance Metadata Service.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.DataSources.IAzureResourceGraphDataSource">
            <summary>
            A data source for direct communication to Azure services.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DataSources.IAzureResourceGraphDataSource.QueryData(System.String,System.Threading.CancellationToken)">
            <summary>
            This method invokes a Kusto query to get data from Azure.
            </summary>
            <param name="query">A Kusto query to invoke</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>Returns an <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataView"/> containing data returned from Azure</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.DataSources.IRegistryDataSource">
            <summary>
            A data source for direct Registry communication.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DataSources.IRegistryDataSource.EnumKey(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Enumerates subkeys for a registry key.
            </summary>
            <param name="hive">A registry hive, that contains the <paramref name="key"/>.</param>
            <param name="key">A registry key, that contains subkeys to be enumerated.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>Returns a <see cref="T:System.Collections.Generic.IList`1"/> containing all names of subkeys for the specified <paramref name="key"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DataSources.IRegistryDataSource.ReadValue(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Retrieves the value associated with the name.
            </summary>
            <param name="hive">A registry hive, that contains the <paramref name="key"/>.</param>
            <param name="key">A registry key, that contains the named value to be read.</param>
            <param name="name">The name of the value to retrieve.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>Returns a <see cref="T:System.Collections.Generic.IList`1"/> containing the value associated with the specified <paramref name="name"/>.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.DataSources.IDataSource">
            <summary>
            A data source for direct communication to the target server.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DataSources.IDataSource.IsAvailable(System.Threading.CancellationToken)">
            <summary>
            Determines whether the data source is available.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>Returns <c>true</c> when the data source is ready to use. <c>false</c> otherwise.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.DataSources.IManagementDataSource">
            <summary>
            A data source for direct WMI communication.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DataSources.IManagementDataSource.QueryData(System.String,System.String,System.Collections.Generic.IReadOnlyDictionary{System.String,System.String},System.Threading.CancellationToken)">
            <summary>
            Runs a WMI query
            </summary>
            <param name="namespaceName">The namespace name to carry out the operation.</param>
            <param name="query">The query expression to be carried out.</param>
            <param name="methods">Information about methods to call.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>Returns a <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataView"/> containing data that match the specified <paramref name="query"/>.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.DataSources.IDataSourceCollection">
            <summary>
            A simple collection of data sources for direct communication to the target server.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DataSources.IDataSourceCollection.AddRange(System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.DataSources.IDataSource})">
            <summary>
            Adds the elements of the specified collection to the end of the <see cref="T:Microsoft.SqlServer.Management.Assessment.DataSources.IDataSourceCollection"/>.
            </summary>
            <param name="collection">The collection whose elements should be added to the end of the <see cref="T:Microsoft.SqlServer.Management.Assessment.DataSources.IDataSourceCollection"/>.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.ConcurrentSet`1">
            <summary>
            A concurrent implementation of set of <typeparamref name="TItem"/>s.
            </summary>
            <typeparam name="TItem">Type of elements.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ConcurrentSet`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
            <summary>
            Initializes a new empty concurrent set using given <paramref name="comparer"/>.
            </summary>
            <param name="comparer">
            An <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> to be used to compare elements.
            </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ConcurrentSet`1.Add(`0)">
            <summary>
            Add a new element to the concurrent set if it was not present.
            </summary>
            <param name="item">A <typeparamref name="TItem"/> to be added.</param>
            <returns>
            Returns <see langword="true"/> if <paramref name="item"/> was not found,
            <see langword="false"/> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ConcurrentSet`1.Contains(`0)">
            <summary>
            Detects if this <see cref="T:Microsoft.SqlServer.Management.Assessment.ConcurrentSet`1"/> contains <paramref name="item"/>.
            </summary>
            <param name="item">An object to look for.</param>
            <returns>
            Returns <see langword="true"/> if this <see cref="T:Microsoft.SqlServer.Management.Assessment.ConcurrentSet`1"/>
            contains <paramref name="item"/>, <see langword="false"/> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ConcurrentSet`1.Remove(`0)">
            <summary>
            Removes <paramref name="item"/> from this <see cref="T:Microsoft.SqlServer.Management.Assessment.ConcurrentSet`1"/>.
            </summary>
            <param name="item">A <typeparamref name="TItem"/> to remove.</param>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.ParameterizedText.VariablePattern">
            <summary>
            This <see cref="T:System.Text.RegularExpressions.Regex"/> pattern is used to detect variable references in recommendation text.
            </summary>
            <remarks>
            Any reference to a variable is replaced in output <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentNote"/>
            by the variable's value if present.
            </remarks>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ParameterizedText.GetNames">
            <summary>
            Gets all names that may be accessed by this <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.INamedVariableConsumer"/>.
            </summary>
            <returns>Returns an enumeration of variable names.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.CheckError">
            <summary>
            <p>CheckError contains information on an error occured while evaluating a check.</p>
            <p>Use <see cref="P:Microsoft.SqlServer.Management.Assessment.CheckError.Message"/> property to get error description. If exceptions were thrown,
            they are stored in <see cref="P:Microsoft.SqlServer.Management.Assessment.CheckError.InnerExceptions"/> collection.</p>
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.CheckError.#ctor(Microsoft.SqlServer.Management.Assessment.Checks.ICheck,System.String,Microsoft.SqlServer.Management.Assessment.SqlObjectType,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.CheckError"/> with given values. This constructor is used when no exception occured.
            </summary>
            <param name="check">The check which caused the error.</param>
            <param name="message">The message <see cref="T:System.String"/> describing the error to the end user.</param>
            <param name="targetType">Type of the target object which was checked when the error occured.</param>
            <param name="targetPath">Path to the target object which was checked when the error occured.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.CheckError.#ctor(Microsoft.SqlServer.Management.Assessment.Checks.ICheck,System.String,System.Exception,Microsoft.SqlServer.Management.Assessment.SqlObjectType,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.CheckError"/> when an exception was caught.
            </summary>
            <param name="check">The check which caused the error.</param>
            <param name="message">The message <see cref="T:System.String"/> describing the error to the end user.</param>
            <param name="innerException"><see cref="T:System.Exception"/> that was the reason for this <see cref="T:Microsoft.SqlServer.Management.Assessment.CheckError"/>.</param>
            <param name="targetType">Type of the target object which was checked when the error occured.</param>
            <param name="targetPath">Path to the target object which was checked when the error occured.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.CheckError.Check">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> which gave this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.CheckError.TargetPath">
            <summary>
            Gets the path to the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.CheckError.TargetType">
            <summary>
            Gets the type of the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.CheckError.Message">
            <summary>
            Gets a <see cref="T:System.String"/> containing information message, recommendation, or error message ro this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.CheckError.Message"/> is usually displayed to the end user.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.CheckError.InnerExceptions">
            <summary>
            Gets a read-only collection of all exceptions occured during attempt to perform a check.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.CheckError.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.CheckError.Timestamp">
            <summary>
            Gets timestamp of the issue
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Checks.Check">
            <summary>
            Simple conditional check.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Description">
            <summary>
            Gets or sets description for the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> explaining the reason
            for <see cref="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Condition"/> to be satisfied.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.DisplayName">
            <summary>
            Gets or sets human readable display name for the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Id">
            <summary>
            Gets or sets unique name for the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>.
            </summary>
            <exception cref="T:System.ArgumentException" accessor="set">
            <list type="bullet">
                <item>
                    <description>
            Value is an empty string.
                    </description>
                </item>
                <item>
                    <description>
            Value is a white space string.
                    </description>
                </item>
                <item>
                    <description>
            Value ends with white space.
                    </description>
                </item>
                <item>
                    <description>
            Value starts with white space.
                    </description>
                </item>
            </list>
            </exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.HelpLink">
            <summary>
            Gets or sets a link to a document providing detailed explanation
            for the issues detected by the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Probes">
            <summary>
            Gets a list of probe references required to obtain server data for this <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Enabled">
            <summary>
            Get or sets a flag enabling the check.
            When a <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> is disabled it is ignored and not recommendations are returned.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Level">
            <summary>
            Gets or sets recommendation level.
            Informational or even moderate recommendations may be ignored by the user.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Tags">
            <summary>
            Gets a set of tags associated with this <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>.
            Checks are grouped by tags.  For example, <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>s detecting
            issues with backup state usually have "Backup" tag.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Message">
            <summary>
            Gets or sets recommendation text displayed to the user.
            The text recommends possible future actions to mitigate detected issue.
            </summary>
            <exception cref="T:System.ArgumentNullException" accessor="set"><paramref name="value"/> is <see langword="null"/></exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Condition">
            <summary>
            Gets or sets a condition tht must be satisfied by any target object.
            <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentNote"/>s are issued when <see cref="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Condition"/> was not satisfied.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Target">
            <summary>
            Gets or sets a pattern filtering target <see cref="T:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator"/>s.
            Only matching targets are checked for issues by this <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.OriginName">
            <summary>
            Gets the name of the rule set where the check was originally defined.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.OriginVersion">
            <summary>
            Gets version of the rule set where the check was originally defined.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Parameters">
            <summary>
            Gets a map for <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>'s parameter values associated to parameter names.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Logics">
            <summary>
            Gets a logics provider supporting this check.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Checks.Check.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Checks.Check.GetRecommendations(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,Microsoft.SqlServer.Management.Assessment.PermutationContext)">
            <summary>
            This method uses server <paramref name="data" /> to detect possible issues
            and returns <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentNote"/>s if any user action was useful.
            </summary>
            <param name="target"><see cref="T:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator"/> specifying target object.</param>
            <param name="data">Sever data obtained by <see cref="P:Microsoft.SqlServer.Management.Assessment.Checks.Check.Probes"/>.</param>
            <returns>
            Returns a sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentNote"/>s for detected issues.
            The sequence may be empty.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="target"/> or <paramref name="data"/> is <see langword="null"/>.
            </exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck">
            <summary>
            All checks supported by <see cref="T:Microsoft.SqlServer.Management.Assessment.Engine"/> implement this interface.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ICheck.Description">
            <summary>
            Gets or sets description for the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> explaining the reason
            for <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Condition"/> to be satisfied.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ICheck.DisplayName">
            <summary>
            Gets or sets human readable display name for the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ICheck.Enabled">
            <summary>
            Get or sets a flag enabling the check.
            When a <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> is disabled it is ignored and not recommendations are returned.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ICheck.HelpLink">
            <summary>
            Gets or sets a link to a document providing detailed explanation
            for the issues detected by the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ICheck.Level">
            <summary>
            Gets or sets recommendation level.
            Informational or even moderate recommendations may be ignored by the user.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ICheck.Id">
            <summary>
            Gets or sets unique name for the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ICheck.Tags">
            <summary>
            Gets a set of tags associated with this <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>.
            Checks are grouped by tags.  For example, <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>s detecting
            issues with backup state usually have "Backup" tag.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ICheck.Target">
            <summary>
            Gets or sets a pattern filtering target <see cref="T:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator"/>s.
            Only matching targets are checked for issues by this <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ICheck.OriginName">
            <summary>
            Gets the name of the rule set where the check was originally defined.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ICheck.OriginVersion">
            <summary>
            Gets version of the rule set where the check was originally defined.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ICheck.Logics">
            <summary>
            Gets a logics provider supporting this check.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference">
            <summary>
            Probe reference is used in check definitions to define probes required for the check.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of type <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference"/>.
            </summary>
            <param name="probeName">Probe name used to find probe implementation.</param>
            <param name="probeNameAlias">Probe name alias is used to refer to the probe in expressions.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,Microsoft.SqlServer.Management.Assessment.Expressions.Expression},System.String,Microsoft.SqlServer.Management.Assessment.IDataMorph)">
            <summary>
            Initializes a new instance of type <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference"/>
            with specified parameters.
            </summary>
            <param name="probeName">Probe name used to find probe implementation.</param>
            <param name="parameters">The dictionary mapping parameter names to their values.</param>
            <param name="probeNameAlias">Probe name alias is used to refer to the probe in expressions.</param>
            <param name="dataMorph">Optional data transformation applied to the probe's results
            before checking condition.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference.ProbeName">
            <summary>
            Gets probe name used to find probe implementations.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference.ProbeNameAlias">
            <summary>
            Gets probe name alias used in local expressions.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference.Morph">
            <summary>
            Get or sets a transformation applied to the probe's output.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference.Equals(Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <param name="other">The object to compare with the current object.</param>
            <returns>
            <see langword="true" /> if the specified object  is equal to the current object; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns>
            <see langword="true" /> if the specified object  is equal to the current object; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Checks.ProbeReference.GetHashCode">
            <summary>Serves as the default hash function.</summary>
            <returns>A hash code for the current object.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext">
            <summary>
            An evaluation context contained in a bigger outer scope represented by BaseContext.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext.#ctor(System.String,Microsoft.SqlServer.Management.Assessment.IEvaluationContext)">
            <summary>
            Initializes a new empty <see cref="T:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext"/>
            with given <paramref name="baseContext"/>.
            </summary>
            <param name="name">Context name.</param>
            <param name="baseContext">Base context representing outer scope.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext.#ctor(System.String)">
            <summary>
            Initializes an outermost <see cref="T:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext"/>.
            </summary>
            <param name="name">Context name.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext.BaseContext">
            <summary>
            Gets an <see cref="T:Microsoft.SqlServer.Management.Assessment.IEvaluationContext"/> representing outer scope.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext.Name">
            <summary>
            Gets the name of this <see cref="T:Microsoft.SqlServer.Management.Assessment.IEvaluationContext"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext.CallStack">
            <summary>
            Gets the set of <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>s comprising the outer scope.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext.TryGetData(System.String,System.Object@)">
            <summary>
            Obtains data from this <see cref="T:Microsoft.SqlServer.Management.Assessment.IDataRow"/> by name.
            Returns <c>true</c> if data was registered for this <paramref name="column"/>, and <c>false</c> otherwise.
            </summary>
            <param name="column">Column name.</param>
            <param name="value">Data if any. Initialized with <see langword="null"/> otherwise.</param>
            <returns>
            Returns <see langword="true"/> if data was registered for this <paramref name="column"/>,
            and <see langword="false"/> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext.TryGetOwnData(System.String,System.Object@)">
            <summary>
            Looks for data in this context only.
            Does not look outside.
            </summary>
            <param name="column">Column name.</param>
            <param name="value">Data if any. Initialized with <see langword="null"/> otherwise.</param>
            <returns>
            Returns <see langword="true"/> if data was registered for this <paramref name="column"/>,
            and <see langword="false"/> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext.TryGetDataCore(System.String,System.Object@)">
            <summary>
            Actual implementation of data retrieval for this context.
            </summary>
            <param name="column">Column name.</param>
            <param name="value">Data if any. Initialized with <see langword="null"/> otherwise.</param>
            <returns>
            Returns <see langword="true"/> if data was registered for this <paramref name="column"/>,
            and <see langword="false"/> otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Engine">
            <summary>
            Engine schedules and runs checks. Use GetAssessmentResults* methods to run checks
            or GetAssessmentItems to get checks for specific target.
            Every instance has its own set of checks and probes represented by <see cref="P:Microsoft.SqlServer.Management.Assessment.Engine.Configuration"/> property.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.#ctor">
            <summary>
            Initializes a new <see cref="T:Microsoft.SqlServer.Management.Assessment.Engine"/> instance with default configuration.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Engine.Configuration">
            <summary>
            Gets current configuration for this <see cref="T:Microsoft.SqlServer.Management.Assessment.Engine"/>.
            Contains all checks and probes for this instance.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Engine.Scheduler">
            <summary>
            Gets <see cref="T:Microsoft.SqlServer.Management.Assessment.Scheduler"/> for this instance.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.Engine.Scheduler"/> concurrently runs and synchronizes probes and checks.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetServerResultsList(System.Data.Common.DbConnection)">
            <summary>
            Asynchronously runs all checks applicable to SQL Server represented by <paramref name="connection"/>
            and returns all recommendations and errors if any.
            </summary>
            <param name="connection">
            Connection to a SQL server to be checked.
            </param>
            <returns>Returns possibly empty sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>s produced by checks.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="connection"/> is <see langword="null"/></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetServerResultsList(System.Data.Common.DbConnection,System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.DataSources.IDataSource})">
            <summary>
            Asynchronously runs all checks applicable to SQL Server represented by <paramref name="connection"/>
            and returns all recommendations and errors if any.
            </summary>
            <param name="connection">
            Connection to a SQL server to be checked.
            </param>
            <param name="sources">
            Any sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.DataSources.IDataSource"/> for communication to the target server.
            </param>
            <returns>Returns possibly empty sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>s produced by checks.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="connection"/> is <see langword="null"/></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetDatabaseResultsList(System.Data.Common.DbConnection,System.String)">
            <summary>
            Asynchronously runs all checks applicable to SQL Server <paramref name="databaseName"/>
            and returns all recommendations and errors if any.
            </summary>
            <param name="connection">
            Connection to a SQL server.
            </param>
            <param name="databaseName">
            SQL server database to be checked.
            </param>
            <returns>Returns possibly empty sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>s produced by checks.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="connection"/> is <see langword="null"/></exception>
            <exception cref="T:System.ArgumentException"><paramref name="databaseName"/> is not valid database name.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetDatabaseResultsList(System.Data.Common.DbConnection,System.String,System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.DataSources.IDataSource})">
            <summary>
            Asynchronously runs all checks applicable to SQL Server <paramref name="databaseName"/>
            and returns all recommendations and errors if any.
            </summary>
            <param name="connection">
            Connection to a SQL server.
            </param>
            <param name="databaseName">
            SQL server database to be checked.
            </param>
            <param name="sources">
            Any sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.DataSources.IDataSource"/> for communication to the target server.
            </param>
            <returns>Returns possibly empty sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>s produced by checks.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="connection"/> is <see langword="null"/></exception>
            <exception cref="T:System.ArgumentException"><paramref name="databaseName"/> is not valid database name.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetAssessmentResultsList(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator)">
            <summary>
            Asynchronously runs all checks applicable to <paramref name="target"/>
            and returns all recommendations and errors if any.
            </summary>
            <param name="target">
            Target object locator points to a SQL server object to be checked.
            </param>
            <returns>Returns possibly empty sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>s produced by checks.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="target"/> is <see langword="null"/></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetAssessmentResultsList(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,Microsoft.SqlServer.Management.Assessment.SeverityLevel)">
            <summary>
            Asynchronously runs all checks applicable to <paramref name="target"/>
            and returns all recommendations and errors if any.
            </summary>
            <param name="target">
            Target object locator points to a SQL server object to be checked.
            </param>
            <param name="minLevel">Minimum severity level for performed checks.</param>
            <returns>Returns possibly empty sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>s produced by checks.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="target"/> is <see langword="null"/></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetAssessmentResultsList(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Asynchronously runs checks selected by id or tag
            and returns all recommendations and errors if any.
            Only checks applicable to <paramref name="target"/> are selected.
            </summary>
            <param name="target">
            Target object locator points to a SQL server object to be checked.
            </param>
            <param name="checkNamesOrTags">Any sequence of <see cref="T:System.String"/> ids or tags for checks.</param>
            <returns>Returns possibly empty sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>s produced by checks.</returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="target"/> or <paramref name="checkNamesOrTags"/> are <see langword="null"/>
            </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetAssessmentResultsList(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,System.Collections.Generic.IEnumerable{System.String},Microsoft.SqlServer.Management.Assessment.SeverityLevel)">
            <summary>
            Asynchronously runs checks selected by id or tag
            and returns all recommendations and errors if any.
            Only checks applicable to <paramref name="target"/> are selected.
            </summary>
            <param name="target">
            Target object locator points to a SQL server object to be checked.
            </param>
            <param name="checkNamesOrTags">Any sequence of <see cref="T:System.String"/> ids or tags for checks.</param>
            <param name="minLevel">Minimum severity level for performed checks.</param>
            <returns>Returns possibly empty sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>s produced by checks.</returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="target"/> or <paramref name="checkNamesOrTags"/> are <see langword="null"/>
            </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetAssessmentResultsList(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.Checks.ICheck})">
            <summary>
            Asynchronously runs applicable checks from <paramref name="checks"/>
            and returns all recommendations and errors if any.
            </summary>
            <param name="target">
            Target object locator points to a SQL server object to be checked.
            </param>
            <param name="checks">Any sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.Check"/>s to be tried.</param>
            <returns>Returns possibly empty sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>s produced by checks.</returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="target"/> or <paramref name="checks"/> are <see langword="null"/>
            </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetServerChecks(System.Data.Common.DbConnection)">
            <summary>
            Finds all checks applicable to SQL Server represented by <paramref name="connection"/>.
            </summary>
            <param name="connection">
            Connection to a SQL server to be checked.
            </param>
            <returns>Returns possibly empty sequence of all <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>s applicable to SQL Server represented by <paramref name="connection"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="connection"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetServerChecks(System.Data.Common.DbConnection,System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.DataSources.IDataSource})">
            <summary>
            Finds all checks applicable to SQL Server represented by <paramref name="connection"/>.
            </summary>
            <param name="connection">
            Connection to a SQL server to be checked.
            </param>
            <param name="sources">
            Any sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.DataSources.IDataSource"/> for communication to the target server.
            </param>
            <returns>Returns possibly empty sequence of all <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>s applicable to SQL Server represented by <paramref name="connection"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="connection"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetDatabaseChecks(System.Data.Common.DbConnection,System.String)">
            <summary>
            Finds all checks applicable to SQL Server <paramref name="databaseName"/>.
            </summary>
            <param name="connection">
            Connection to a SQL server to be checked.
            </param>
            <param name="databaseName">
            SQL server database to be checked.
            </param>
            <returns>Returns possibly empty sequence of all <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>s applicable to SQL Server <paramref name="databaseName"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="connection"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="databaseName"/> is not valid database name.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetDatabaseChecks(System.Data.Common.DbConnection,System.String,System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.DataSources.IDataSource})">
            <summary>
            Finds all checks applicable to SQL Server <paramref name="databaseName"/>.
            </summary>
            <param name="connection">
            Connection to a SQL server to be checked.
            </param>
            <param name="databaseName">
            SQL server database to be checked.
            </param>
            <param name="sources">
            Any sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.DataSources.IDataSource"/> for communication to the target server.
            </param>
            <returns>Returns possibly empty sequence of all <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>s applicable to SQL Server <paramref name="databaseName"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="connection"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="databaseName"/> is not valid database name.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetChecks(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator)">
            <summary>
            Finds all checks applicable to <paramref name="target"/>.
            </summary>
            <param name="target">
            Target object locator points to a SQL server object to be checked.
            </param>
            <returns>Returns possibly empty sequence of all <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>s applicable to <paramref name="target"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="target"/> is <see langword="null"/></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetChecksAsync(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator)">
            <summary>
            Finds all checks applicable to <paramref name="target"/> asynchronously.
            </summary>
            <param name="target">
            Target object locator points to a SQL server object to be checked.
            </param>
            <returns>Returns possibly empty sequence of all <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>s applicable to <paramref name="target"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="target"/> is <see langword="null"/></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetChecks(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,System.String[])">
            <summary>
            Finds checks applicable checks by id or tag.
            </summary>
            <param name="target">
            Target object locator points to a SQL server object to be checked.
            </param>
            <param name="checkNamesOrTags">Any sequence of <see cref="T:System.String"/> ids or tags for checks.</param>
            <returns>Returns possibly empty sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>s with corresponding tags or ids and applicable to <paramref name="target"/>.</returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="target"/> or <paramref name="checkNamesOrTags"/> are <see langword="null"/>
            </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetChecksAsync(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator,System.String[])">
            <summary>
            Finds checks applicable checks by id or tag asynchronously.
            </summary>
            <param name="target">
            Target object locator points to a SQL server object to be checked.
            </param>
            <param name="checkNamesOrTags">Any sequence of <see cref="T:System.String"/> ids or tags for checks.</param>
            <returns>Returns possibly empty sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>s with corresponding tags or ids and applicable to <paramref name="target"/>.</returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="target"/> or <paramref name="checkNamesOrTags"/> are <see langword="null"/>
            </exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.PushRuleFactoryJson(System.IO.Stream,System.String)">
            <summary>
            Deserializes and pushes a new factory onto the top of current path.
            </summary>
            <param name="inputStream">A <see cref="T:System.IO.Stream"/> containing JSON for the new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset"/>.</param>
            <param name="alias">Optional alias to use instead of the factory's' name.'</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.PushRuleFactoryJson(System.IO.TextReader,System.String)">
            <summary>
            Deserializes and pushes a new factory onto the top of current path.
            </summary>
            <param name="textReader">A <see cref="T:System.IO.TextReader"/> giving JSON for the new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset"/>.</param>
            <param name="alias">Optional alias to use instead of the factory's' name.'</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.PushRuleFactoryJson(Newtonsoft.Json.JsonTextReader,System.String)">
            <summary>
            Deserializes and pushes a new factory onto the top of current path.
            </summary>
            <param name="reader">A <see cref="T:Newtonsoft.Json.JsonTextReader"/> giving JSON for the new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset"/>.</param>
            <param name="alias">Optional alias to use instead of the factory's' name.'</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.PushRuleFactory(Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset,System.String)">
            <summary>
            Pushes a new factory onto the top of current path.
            </summary>
            <param name="factory">The new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset"/>.</param>
            <param name="alias">Optional alias to use instead of the factory's' name.'</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.LoadRuleFactoryJson(System.IO.Stream)">
            <summary>
            Deserializes a new factory.
            </summary>
            <param name="inputStream">A <see cref="T:System.IO.Stream"/> containing JSON for the new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset"/>.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.LoadRuleFactoryJson(System.IO.TextReader)">
            <summary>
            Deserializes a new factory.
            </summary>
            <param name="textReader">A <see cref="T:System.IO.TextReader"/> containing JSON for the new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset"/>.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.LoadRuleFactoryJson(Newtonsoft.Json.JsonTextReader)">
            <summary>
            Deserializes a new factory.
            </summary>
            <param name="reader">A <see cref="T:Newtonsoft.Json.JsonTextReader"/> giving JSON for the new <see cref="T:Microsoft.SqlServer.Management.Assessment.Configuration.Ruleset"/>.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.OpenConnection(System.Data.Common.DbConnection)">
            <summary>
            Opens a <see cref="T:System.Data.Common.DbConnection"/>.
            </summary>
            <param name="connection">A <see cref="T:System.Data.Common.DbConnection"/> to be opened.</param>
            <returns>Returns an opened <see cref="T:System.Data.Common.DbConnection"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetImplementedChecks(System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Assessment.Checks.ICheck},System.Collections.Generic.ICollection{Microsoft.SqlServer.Management.Assessment.Checks.ICheck})">
            <summary>
            Separates <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.Check"/> instances from other <paramref name="input"/> objects implementing <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>.
            Other objects will be added to <paramref name="unimplemented"/> collection.
            </summary>
            <param name="input">Input sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/>s.</param>
            <param name="unimplemented">Objects other than <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.Check"/> instances will be added to this collection.</param>
            <returns>Returns objects of type <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.Check"/> from <paramref name="input"/> if any.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetDefaultConfigurationStream">
            <summary>
            Creates a <see cref="T:System.IO.StreamReader"/> for reading default configuration.
            </summary>
            <returns>Returns a <see cref="T:System.IO.StreamReader"/> providing default configuration data.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.LoadJsonConfig">
            <summary>
            Loads default configuration.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.GetConnection(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator)">
            <summary>
            Finds <see cref="T:System.Data.Common.DbConnection"/> for <paramref name="target"/>
            </summary>
            <param name="target">Target SQL object to connect.</param>
            <returns>Returns a new or existing <see cref="T:System.Data.Common.DbConnection"/> to access <paramref name="target"/>'s data.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Engine.ProcessAssessmentRequest(Microsoft.SqlServer.Management.Assessment.AssessmentRequest2)">
            <summary>
            Runs probes and evaluates checks for <paramref name="request"/>.
            </summary>
            <param name="request">An <see cref="T:Microsoft.SqlServer.Management.Assessment.AssessmentRequest"/> containing checks to be evaluated and parameter values.</param>
            <returns>Returns a sequence of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>s for all checks in <paramref name="request"/>.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.EvaluationContext`1">
            <summary>
            An evaluation context implementation providing data of type <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">Type of data values.</typeparam>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.EvaluationContext`1.#ctor(System.String)">
            <summary>
            Initializes a new evaluation context with given <paramref name="name"/>.
            </summary>
            <param name="name">The name for the context.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.EvaluationContext`1.#ctor(System.String,System.Collections.Generic.IDictionary{System.String,`0})">
            <summary>
            Initializes a new evaluation context with given <paramref name="name"/>.
            Adds some initial <paramref name="data"/> to this context.
            </summary>
            <param name="name">The name for the context.</param>
            <param name="data">Data to be added to the new context on initialization.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.EvaluationContext`1.#ctor(System.String,Microsoft.SqlServer.Management.Assessment.IEvaluationContext)">
            <summary>
            Initializes a new empty <see cref="T:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext"/>
            with given <paramref name="baseContext"/>.
            </summary>
            <param name="name">Context name.</param>
            <param name="baseContext">Base context representing outer scope.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.EvaluationContext`1.#ctor(System.String,Microsoft.SqlServer.Management.Assessment.IEvaluationContext,System.Collections.Generic.IDictionary{System.String,`0})">
            <summary>
            Initializes a new empty <see cref="T:Microsoft.SqlServer.Management.Assessment.DerivedEvaluationContext"/>
            with given <paramref name="baseContext"/>.
            Adds some initial <paramref name="data"/> to this context.
            </summary>
            <param name="name">Context name.</param>
            <param name="baseContext">Base context representing outer scope.</param>
            <param name="data">Data to be added to the new context on initialization.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.EvaluationContext`1.Data">
            <summary>
            Gets an associative container storing named data for this context.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.EvaluationContext`1.CallStack">
            <summary>
            Gets the set of <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>s comprising the outer scope.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.EvaluationContext`1.TryGetDataCore(System.String,System.Object@)">
            <summary>
            Actual implementation of data retrieval for this context.
            </summary>
            <param name="column">Column name.</param>
            <param name="value">Data if any. Initialized with <see langword="null"/> otherwise.</param>
            <returns>
            Returns <see langword="true"/> if data was registered for this <paramref name="column"/>,
            and <see langword="false"/> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.EvaluationContext`1.ToObject(`0)">
            <summary>
            Converts <paramref name="value"/> to <see cref="T:System.Object"/>.
            </summary>
            <param name="value">A value to convert.</param>
            <returns>Returns an <see cref="T:System.Object"/> equivalent to <paramref name="value"/>.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.FatalError">
            <summary>
            FatalError is raised when no checks can be run against given target.
            For example, the database is inaccessible due to user's permissions.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.FatalError.Message">
            <summary>
            Gets a <see cref="T:System.String"/> containing information message, recommendation, or error message ro this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.Message"/> is usually displayed to the end user.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.FatalError.Check">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> which gave this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.FatalError.TargetPath">
            <summary>
            Gets the path to the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.FatalError.TargetType">
            <summary>
            Gets the type of the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.FatalError.InnerExceptions">
            <summary>
            Gets a read-only collection of all exceptions occured during attempt to perform a check.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.FatalError.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.FatalError.Timestamp">
            <summary>
            Gets timestamp of the issue
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.IAssessmentWarning">
            <summary>
            Warning is an assessment result describing not the target state but the process of assessment.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.IAssessmentError">
            <summary>
            <p>An instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentError"/> is returned by a check when it cannot be completed.</p>
            <p>If exceptions were thrown, they are available in <see cref="P:Microsoft.SqlServer.Management.Assessment.IAssessmentError.InnerExceptions"/> collection.</p>
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IAssessmentError.InnerExceptions">
            <summary>
            Gets a read-only collection of all exceptions occured during attempt to perform a check.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.IAssessmentNote">
            <summary>
            Describes an issue detected by a check.
            An instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentNote"/> is an expected result for a check.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IAssessmentNote.HelpLink">
            <summary>
            Gets a <see cref="T:System.String"/> containing a link to an article explaining the issue in detail.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IAssessmentNote.Severity">
            <summary>
            Gets severity level of the issue.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult">
            <summary>
            IAssessmentResult represents result of a single check.
            It may be an <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentNote"/> or an <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentError"/> in case of check cannot be completed.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.Message">
            <summary>
            Gets a <see cref="T:System.String"/> containing information message, recommendation, or error message ro this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.Message"/> is usually displayed to the end user.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.Check">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> which gave this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.TargetPath">
            <summary>
            Gets the path to the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.TargetType">
            <summary>
            Gets the type of the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.Timestamp">
            <summary>
            Gets timestamp of the issue
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.IEvaluationContext">
            <summary>
            Outer scope for check calculations.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IEvaluationContext.Name">
            <summary>
            Gets the name of this <see cref="T:Microsoft.SqlServer.Management.Assessment.IEvaluationContext"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.IEvaluationContext.CallStack">
            <summary>
            Gets the set of <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Expression"/>s comprising the outer scope.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator">
            <summary>
            ISqlObjectLocator identifies a SQL server objects and carries some useful information about this object.
            Use <see cref="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.Connection"/> to connect to the SQL Server hosting the object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.Platform">
            <summary>
            <para>Gets or sets code for platform hosting the SQL Server.</para>
            <para> Supported values: Windows, Linux.</para>
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.Connection">
            <summary>
            Gets a <see cref="T:System.Data.Common.DbConnection"/> to be used to access the object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.Name">
            <summary>
            Gets or sets the object's name.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.Type">
            <summary>
            Gets the object's type tag.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.Version">
            <summary>
            Gets or sets version of SQL Server hosting the object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.Urn">
            <summary>
            Gets or sets a <see cref="T:System.String"/> uniquely identifying the object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.EngineEdition">
            <summary>
            Gets or sets SQL Server edition of the instance.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.ServerName">
            <summary>
            Gets or sets the name of the target server.
            For server instances returns the same value as <see cref="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.Name"/>.
            </summary>
            <value>
            The name of the target server.
            </value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.MachineType">
            <summary>
            Gets or sets type of virtual machine hosting the target instance.
            It's <see cref="F:System.String.Empty"/> for a physical machine.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator.DataSources">
            <summary>
            A collection of data sources for direct communication to the target server.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.ProbeError">
            <summary>
            <p>ProbeError represents exceptions occured while an object was probed. Inaccessible object or server is a typical example.</p>
            <p><see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeError"/> is reported while a check is executed. If more than one probe threw exception for this check,
            every exception is registered in <see cref="P:Microsoft.SqlServer.Management.Assessment.ProbeError.InnerProbeExceptions"/> dictionary.</p>
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ProbeError.#ctor(Microsoft.SqlServer.Management.Assessment.Checks.ICheck,System.String,Microsoft.SqlServer.Management.Assessment.SqlObjectType,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeError"/> with given values when no exception occured.
            </summary>
            <param name="check">The check returning this <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeError"/>.</param>
            <param name="message">A <see cref="T:System.String"/> message explaining the issue to the user.</param>
            <param name="targetType">Type of target object.</param>
            <param name="targetPath">Path to the target object.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ProbeError.#ctor(Microsoft.SqlServer.Management.Assessment.Checks.ICheck,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Exception}},Microsoft.SqlServer.Management.Assessment.SqlObjectType,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeError"/> with exceptions were caught.
            </summary>
            <param name="check">The check returning this <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeError"/>.</param>
            <param name="message">A <see cref="T:System.String"/> message explaining the issue to the user.</param>
            <param name="innerExceptions">A dictionary mapping probe names to exceptions.</param>
            <param name="targetType">Type of target object.</param>
            <param name="targetPath">Path to the target object.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ProbeError.Message">
            <summary>
            Gets a <see cref="T:System.String"/> containing information message, recommendation, or error message ro this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.Message"/> is usually displayed to the end user.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ProbeError.InnerProbeExceptions">
            <summary>
            Gets a read-only dictionary mapping probe names to exceptions thrown by corresponding probes.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ProbeError.Check">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> which gave this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ProbeError.TargetPath">
            <summary>
            Gets the path to the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ProbeError.TargetType">
            <summary>
            Gets the type of the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ProbeError.InnerExceptions">
            <summary>
            Gets a read-only collection of all exceptions occured during attempt to perform a check.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ProbeError.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.ProbeError.Timestamp">
            <summary>
            Gets timestamp of the issue
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorEmtyString">
            <summary>
              Looks up a localized string similar to Empty string is not allowed..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorCheckTargetChange">
            <summary>
              Looks up a localized string similar to Check target replacement is not allowed.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorInvalidCheckId">
            <summary>
              Looks up a localized string similar to Valid check ID required.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorEvalArity">
            <summary>
              Looks up a localized string similar to {0} parameters required, but {1} present..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorEvalMissingData">
            <summary>
              Looks up a localized string similar to Missing data item &quot;{0}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorEvalOverflow">
            <summary>
              Looks up a localized string similar to Value does not fit type {0}..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorEvalStringFormat">
            <summary>
              Looks up a localized string similar to String &apos;{0}&apos; cannot be converted to type {1}..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorEvalTypeMismatch">
            <summary>
              Looks up a localized string similar to Value &apos;{0}&apos; of type {1} cannot be converted to type {2}..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorIndexOutOfRange">
            <summary>
              Looks up a localized string similar to Index out of range..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorJsonMissingProperty">
            <summary>
              Looks up a localized string similar to Property &quot;{0}&quot; was not found..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorJsonOperatorInstantiation">
            <summary>
              Looks up a localized string similar to Operator &quot;{0}&quot; instantiation error..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorJsonReader">
            <summary>
              Looks up a localized string similar to JSON reading error..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorJsonSerializationNotSupported">
            <summary>
              Looks up a localized string similar to JSON serialization for {0} not supported..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorJsonSingularRangeInclusive">
            <summary>
              Looks up a localized string similar to SIngular range must include both left and right boundaries. Use single value without any parenthesis for singular ranges: &quot;{0}&quot; instead of &quot;{1}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorJsonUnexpectedEndOfDocument">
            <summary>
              Looks up a localized string similar to Unecpected end of JSON document..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorJsonUnexpectedToken">
            <summary>
              Looks up a localized string similar to Unexpected token &quot;{0}&quot; in {1}..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorJsonUnsupportedOperation">
            <summary>
              Looks up a localized string similar to Unsupported operation &quot;{0}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorJsonWrongToken">
            <summary>
              Looks up a localized string similar to Encountered {0}, {1} was expected..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorJsonWrongValue">
            <summary>
              Looks up a localized string similar to Wrong value &quot;{0}&quot; for {1}..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorValueEndsWithWhiteSpace">
            <summary>
              Looks up a localized string similar to Value ends with white space..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorValueIsAWhiteSpaceString">
            <summary>
              Looks up a localized string similar to Value is a white space string..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorValueIsAnEmptyString">
            <summary>
              Looks up a localized string similar to Value is an empty string..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorValueStartsWithWhiteSpace">
            <summary>
              Looks up a localized string similar to Value starts with white space..
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.Properties.Resources.ErrorEvalWrongProbeParameter">
            <summary>
              Looks up a localized string similar to Probe {0} does not support parameter {1}..
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.AssessmentNote">
            <summary>
            Trivial implementation for <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentNote"/>.
            Describes an issue detected by a check.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.AssessmentNote.#ctor(Microsoft.SqlServer.Management.Assessment.Checks.ICheck,System.String,System.String,Microsoft.SqlServer.Management.Assessment.SqlObjectType,System.String,Microsoft.SqlServer.Management.Assessment.SeverityLevel)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.AssessmentNote"/> class.
            </summary>
            <param name="check">The check giving this recommendation.</param>
            <param name="message">A message <see cref="T:System.String"/> containing information for the end user.</param>
            <param name="targetPath">Path to the target object.</param>
            <param name="targetType">Type of the target object.</param>
            <param name="helpLink">A <see cref="T:System.String"/> containing URL of an article explaining the issue.</param>
            <param name="severity">Severity level of the issue.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentNote.Message">
            <summary>
            Gets a <see cref="T:System.String"/> containing information message, recommendation, or error message ro this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.IAssessmentResult.Message"/> is usually displayed to the end user.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentNote.Check">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> which gave this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentNote.TargetPath">
            <summary>
            Gets the path to the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentNote.TargetType">
            <summary>
            Gets the type of the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentNote.HelpLink">
            <summary>
            Returns a <see cref="T:System.String"/> containing a link to an article explaining the issue in detail.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentNote.Severity">
            <summary>
            Gets severity level of the issue.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.AssessmentNote.Timestamp">
            <summary>
            Gets timestamp of the issue
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.RunSpace">
            <summary>
            Represents a request processing environment.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.RunSpace.Target">
            <summary>
            Gets the assessment target object.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.RunSpace.GetTool``1">
            <summary>
            Obtains an available channel of type T connected to the target.
            </summary>
            <typeparam name="T">Type of channel to return.</typeparam>
            <returns>The channel connected to the target, or null if no channel is available.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.SeverityLevel">
            <summary>
            Severity lever for Assessment note.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.SeverityLevel.Information">
            <summary>
            No action required.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.SeverityLevel.Low">
            <summary>
            No action is needed, but better to take into consideration.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.SeverityLevel.Medium">
            <summary>
            An action is needed in some usage scenarios which cannot be detected automatically.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.SeverityLevel.High">
            <summary>
            An action is required.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.ProbeNotFoundException">
            <summary>
            <para>ProbeNotFoundException is thrown when an error occurs
            during selecting appropriate probe for check according to its <see cref="T:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator"/> target.</para>
            <para>This usually indicates an incorrect target parameters specified for a check.</para>
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ProbeNotFoundException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeNotFoundException" /> class.</summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ProbeNotFoundException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeNotFoundException" /> class with specified error message.</summary>
            <param name="message">The message that describes the error. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ProbeNotFoundException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeNotFoundException" /> class with specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.ProbeNotFoundException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.ProbeNotFoundException" /> class with serialized data.</summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination. </param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is <see langword="null" />. </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name is <see langword="null" /> or <see cref="P:System.Exception.HResult" /> is zero (0). </exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentProbeParameterException">
            <summary>
            SqlAssessmentProbeParameterException is thrown when a check refers to a probe parameter that is not supported by the probe.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.SqlAssessmentProbeParameterException.ParameterName">
            <summary>
            Gets the name of parameter that caused the exception
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentProbeParameterException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException" /> class.</summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentProbeParameterException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentProbeParameterException" /> class
            with specified error message.</summary>
            <param name="message">The message that describes the error. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentProbeParameterException.#ctor(Microsoft.SqlServer.Management.Assessment.Probes.IProbe,System.String)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException" /> class with specified .</summary>
            <param name="probe">The probe that does not support parameter <paramref name="parameterName"/>. </param>
            <param name="parameterName">Absent parameter name</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentProbeParameterException.#ctor(System.String,Microsoft.SqlServer.Management.Assessment.Probes.IProbe,System.String)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException" /> class with specified error message.</summary>
            <param name="message">Error message.</param>
            <param name="probe">The probe. </param>
            <param name="parameterName">The absent or malfunctioning parameter name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentProbeParameterException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentProbeParameterException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException" /> class
            with specified error <paramref name="message"/> and <paramref name="parameterName"/>.
            </summary>
            <param name="message">Error message details</param>
            <param name="parameterName">Parameter name</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentProbeParameterException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException" /> class with serialized data.</summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination. </param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is <see langword="null" />. </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name is <see langword="null" /> or <see cref="P:System.Exception.HResult" /> is zero (0). </exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator">
            <summary>
            SqlObjectLocator identifies a SQL server objects and carries some useful information about this object.
            Use <see cref="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.Connection"/> to connect to the SQL Server hosting the object.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator"/> type.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.Name">
            <summary>
            Gets the object's name.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.Type">
            <summary>
            Gets the object's type tag.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.Version">
            <summary>
            Gets version of SQL Server hosting the object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.Urn">
            <summary>
            Gets a <see cref="T:System.String"/> uniquely identifying the object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.EngineEdition">
            <summary>
            Gets SQL Server edition of the instance.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.ServerName">
            <summary>
            Gets or sets string identifying server hosting this SQL object.
            When <see langword="this"/> is a SQL server instance,
            <see cref="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.ServerName"/> equals to <see cref="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.Name"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.Connection">
            <summary>
            Gets a <see cref="T:System.Data.Common.DbConnection"/> to be used to access the object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.Platform">
            <summary>
            <para>Gets code for platform hosting the SQL Server.</para>
            <para> Supported values: Windows, Linux.</para>
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.MachineType">
            <summary>
            Gets type of virtual machine hosting the target instance.
            It's <see cref="F:System.String.Empty"/> for a physical machine.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.DataSources">
            <summary>
            A list of data sources for direct communication to the target server.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlObjectLocator.ToString">
            <summary>
            Return string representation
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.SqlObjectType">
            <summary>
            SQL Server object type tag.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.SqlObjectType.None">
            <summary>
            Object type not specified.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.SqlObjectType.Server">
            <summary>
            SQL Server instance.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.SqlObjectType.Database">
            <summary>
            SQL Server database.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.SqlObjectType.FileGroup">
            <summary>
            SQL Server file group.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Assessment.SqlObjectType.AvailabilityGroup">
            <summary>
            SQL Server high availability group
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentArityException">
            <summary>
            SqlAssessmentArityException is thrown when an operation gets too many or not enough arguments.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentArityException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentArityException" /> class.</summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentArityException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentArityException" /> class with a specified error message.</summary>
            <param name="message">The message that describes the error. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentArityException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentArityException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentArityException.#ctor(System.Int32,System.Int32)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentArityException" /> class with expected and actual argument counts.</summary>
            <param name="expectedArity">Expected number of arguments. </param>
            <param name="actualArgumentsCount">Actual (incorrect) number of arguments. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentArityException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentArityException" /> class with serialized data.</summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination. </param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is <see langword="null" />. </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name is <see langword="null" /> or <see cref="P:System.Exception.HResult" /> is zero (0). </exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException">
            <summary>
            <para>SqlAssessmentEvaluationException is thrown when an error occurs
            during evaluation of check expression against a <see cref="T:Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator"/> target.</para>
            <para>This usually indicates an invalid operation or invalid data coming from probes or parameters.</para>
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException" /> class.</summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException" /> class with specified error message.</summary>
            <param name="message">The message that describes the error. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException" /> class with specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException" /> class with serialized data.</summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination. </param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is <see langword="null" />. </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name is <see langword="null" /> or <see cref="P:System.Exception.HResult" /> is zero (0). </exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException">
            <summary>
            SqlAssessmentFormatException is thrown when a string representation has incorrect format to be parsed as given type.
            For example, a string "ten" was encountered while a number was expected.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException" /> class.</summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException" /> class with a specified error message.</summary>
            <param name="message">The message that describes the error. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException.#ctor(System.Type,System.String)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException" /> class with expected <paramref name="type"/> and incorrect string <paramref name="value"/>.</summary>
            <param name="type">Expected type of <paramref name="value"/>.</param>
            <param name="value">Actual <see cref="T:System.String"/> which could not be converted to an object of <paramref name="type"/>.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException.#ctor(System.Type,System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException" /> class
            with expected <paramref name="type"/>, incorrect string <paramref name="value"/>, and corresponding inner exception.</summary>
            <param name="type">Expected type of <paramref name="value"/>.</param>
            <param name="value">Actual <see cref="T:System.String"/> which could not be converted to an object of <paramref name="type"/>.</param>
            <param name="innerException">Inner <see cref="T:System.Exception"/> providing more details.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException" /> class with serialized data.</summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination. </param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is <see langword="null" />. </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name is <see langword="null" /> or <see cref="P:System.Exception.HResult" /> is zero (0). </exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentMissingDataException">
            <summary>
            SqlAssessmentMissingDataException is thrown when some data was expected from probes or parameters
            but was not present at the time of evaluation.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentMissingDataException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentMissingDataException" /> class.</summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentMissingDataException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentMissingDataException" /> class with missing data item name.</summary>
            <param name="dataItemName">The name of missing data item. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentMissingDataException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentMissingDataException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentMissingDataException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentMissingDataException" /> class with serialized data.</summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination. </param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is <see langword="null" />. </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name is <see langword="null" /> or <see cref="P:System.Exception.HResult" /> is zero (0). </exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException">
            <summary>
            SqlAssessmentOverflowException is thrown when incoming or computed value exceeds limits set by expected type.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException" /> class.</summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException" /> class with a specified error message.</summary>
            <param name="message">The message that describes the error. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException.#ctor(System.Type)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException" /> class with expected value <paramref name="type"/>.</summary>
            <param name="type">The expected <see cref="T:System.Type"/> of object.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException.#ctor(System.Type,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="type">The expected <see cref="T:System.Type"/> of object. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentEvaluationException" /> class with serialized data.</summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination. </param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is <see langword="null" />. </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name is <see langword="null" /> or <see cref="P:System.Exception.HResult" /> is zero (0). </exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException">
            <summary>
            SqlAssessmentTypeMismatchException is thrown when an incoming value cannot be converted to expected type.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException" /> class.</summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException" /> class with a specified error message.</summary>
            <param name="message">The message that describes the error. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified. </param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException.#ctor(System.Type,System.Type,System.Object)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException" /> class with a expected type, actual type, and the value.</summary>
            <param name="expected">Expected <see cref="T:System.Type"/>.</param>
            <param name="actual">Actual <see cref="T:System.Type"/>.</param>
            <param name="value">Actual object.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException.#ctor(System.Type,System.Type,System.Object,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException" /> class with a expected type, actual type, and the value.</summary>
            <param name="expected">Expected <see cref="T:System.Type"/>.</param>
            <param name="actual">Actual <see cref="T:System.Type"/>.</param>
            <param name="value">Actual object.</param>
            <param name="innerException">Inner <see cref="T:System.Exception"/> providing more details.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException" /> class with serialized data.</summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown. </param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination. </param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is <see langword="null" />. </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name is <see langword="null" /> or <see cref="P:System.Exception.HResult" /> is zero (0). </exception>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Assessment.NoChecksError">
            <summary>
            <p>No check warning returned in case of using platform that's not supported yet.</p>
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.NoChecksError.#ctor(Microsoft.SqlServer.Management.Assessment.ISqlObjectLocator)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.SqlServer.Management.Assessment.NoChecksError"/> with given values.
            </summary>
            <param name="target">Target object which was checked when the warning occured.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.NoChecksError.TargetPath">
            <summary>
            Gets the path to the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.NoChecksError.TargetType">
            <summary>
            Gets the type of the checked object.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.NoChecksError.Message">
            <summary>
            Gets a <see cref="T:System.String"/> containing information message, recommendation to this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            <see cref="P:Microsoft.SqlServer.Management.Assessment.NoChecksError.Message"/> is usually displayed to the end user.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.NoChecksError.Check">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.Assessment.Checks.ICheck"/> which gave this <see cref="T:Microsoft.SqlServer.Management.Assessment.IAssessmentResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.NoChecksError.HelpLink">
            <summary>
            Returns a <see cref="T:System.String"/> containing a link to an article explaining the issue in detail.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.NoChecksError.Severity">
            <summary>
            Gets severity level of te issue.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.NoChecksError.InnerExceptions">
            <summary>
            Gets a read-only collection of all exceptions occured during attempt to perform a check.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.NoChecksError.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Assessment.NoChecksError.Timestamp">
            <summary>
            Gets timestamp of the issue
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Utils.Normalize``1(System.Object)">
            <summary>
            Converts <paramref name="value"/>'s type
            </summary>
            <typeparam name="T">Type to convert to</typeparam>
            <param name="value">Value to convert</param>
            <returns>Returns converted value</returns>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException" />
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException" />
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException" />
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Utils.Normalize(System.Object,System.Type)">
            <summary>
            Converts <paramref name="value"/>'s type
            </summary>
            <param name="type">Type to convert to</param>
            <param name="value">Value to convert</param>
            <returns>Returns converted value</returns>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException" />
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException" />
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException" />
        </member>
        <member name="M:Microsoft.SqlServer.Management.Assessment.Utils.ToLiteral(System.Object)">
            <summary>
            Creates appropriate <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> expression for <paramref name="value"/>.
            </summary>
            <param name="value">Value to convert.</param>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentOverflowException">
            <paramref name="value"/> cannot be used for a <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> expression.
            </exception>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentFormatException">
            <paramref name="value"/> cannot be used for a <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> expression.
            </exception>
            <exception cref="T:Microsoft.SqlServer.Management.Assessment.SqlAssessmentTypeMismatchException">
            <paramref name="value"/> cannot be used for a <see cref="T:Microsoft.SqlServer.Management.Assessment.Expressions.Literal`1"/> expression.
            </exception>
        </member>
    </members>
</doc>
