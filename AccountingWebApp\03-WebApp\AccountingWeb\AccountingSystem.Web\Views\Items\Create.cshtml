@model AccountingSystem.Models.Item

@{
    ViewData["Title"] = "إنشاء صنف جديد";
}

<h1>@ViewData["Title"]</h1>

<h4>بيانات الصنف</h4>
<hr />
<div class="row">
    <div class="col-md-8">
        <form asp-action="Create" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label asp-for="ItemNo" class="control-label">رقم الصنف</label>
                    <input asp-for="ItemNo" class="form-control" />
                    <span asp-validation-for="ItemNo" class="text-danger"></span>
                </div>
                <div class="form-group col-md-6">
                    <label asp-for="Barcode" class="control-label">الباركود</label>
                    <input asp-for="Barcode" class="form-control" />
                    <span asp-validation-for="Barcode" class="text-danger"></span>
                </div>
            </div>

            <div class="form-group">
                <label asp-for="ItemDescription" class="control-label">الوصف العربي</label>
                <input asp-for="ItemDescription" class="form-control" />
                <span asp-validation-for="ItemDescription" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="ItemDescription2" class="control-label">الوصف الإنجليزي</label>
                <input asp-for="ItemDescription2" class="form-control" />
                <span asp-validation-for="ItemDescription2" class="text-danger"></span>
            </div>

            <div class="form-row">
                <div class="form-group col-md-6">
                    <label asp-for="UnitPurchasePrice" class="control-label">سعر الشراء</label>
                    <input asp-for="UnitPurchasePrice" class="form-control" />
                    <span asp-validation-for="UnitPurchasePrice" class="text-danger"></span>
                </div>
                <div class="form-group col-md-6">
                    <label asp-for="UnitSalesPrice" class="control-label">سعر البيع</label>
                    <input asp-for="UnitSalesPrice" class="form-control" />
                    <span asp-validation-for="UnitSalesPrice" class="text-danger"></span>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label asp-for="UofM" class="control-label"></label>
                        <select asp-for="UofM" class="form-control" asp-items="new SelectList(ViewBag.UofMs)"></select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label asp-for="Tax_Percent" class="control-label"></label>
                         <select asp-for="Tax_Percent" class="form-control" asp-items="new SelectList(ViewBag.Taxes)"></select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label asp-for="ItemType" class="control-label"></label>
                        <select asp-for="ItemType" class="form-control" asp-items="new SelectList(ViewBag.ItemTypes)"></select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label asp-for="CategoryId" class="control-label"></label>
                        <select asp-for="CategoryId" class="form-control" asp-items='new SelectList(ViewBag.Categories, "Id", "Name")'></select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label asp-for="Brand" class="control-label"></label>
                        <select asp-for="Brand" class="form-control" asp-items="new SelectList(ViewBag.Brands)"></select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label asp-for="Shop" class="control-label"></label>
                        <select asp-for="Shop" class="form-control" asp-items='new SelectList(ViewBag.Shops, "SN", "StoreName")'></select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label asp-for="Notes" class="control-label"></label>
                <textarea asp-for="Notes" class="form-control"></textarea>
                <span asp-validation-for="Notes" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Photo" class="control-label"></label>
                <input type="file" name="photoFile" class="form-control" />
            </div>

            <div class="form-group form-check">
                <label class="form-check-label">
                    <input class="form-check-input" asp-for="EnableSN" /> @Html.DisplayNameFor(model => model.EnableSN)
                </label>
            </div>
            <div class="form-group form-check">
                <label class="form-check-label">
                    <input class="form-check-input" asp-for="NegativeEnable" /> @Html.DisplayNameFor(model => model.NegativeEnable)
                </label>
            </div>
            <input type="hidden" asp-for="Status" value="1" />

            <div class="form-group">
                <input type="submit" value="إنشاء" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">العودة إلى القائمة</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 