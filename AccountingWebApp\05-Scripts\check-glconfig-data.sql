-- Check existing data in tblGLConfig table
-- Run this script to see what's currently in the database

-- Check if table exists
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tblGLConfig]') AND type in (N'U'))
BEGIN
    PRINT 'Table tblGLConfig exists!'
    
    -- Count total records
    DECLARE @RecordCount INT
    SELECT @RecordCount = COUNT(*) FROM [dbo].[tblGLConfig]
    PRINT 'Total records in tblGLConfig: ' + CAST(@RecordCount AS VARCHAR(10))
    
    -- Show all records
    SELECT 
        [ConfigID],
        [EntryReferenceModule],
        [AccountNo],
        [Description],
        [IsActive]
    FROM [dbo].[tblGLConfig]
    ORDER BY [ConfigID]
    
    -- Show table structure
    PRINT 'Table structure:'
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'tblGLConfig'
    ORDER BY ORDINAL_POSITION
END
ELSE
BEGIN
    PRINT 'Table tblGLConfig does NOT exist!'
END 