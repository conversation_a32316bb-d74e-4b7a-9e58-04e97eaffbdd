<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.SqlServer.Dmf</name>
    </assembly>
    <members>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Condition">
            <summary>
            This is the non-generated part of the Condition class.
            </summary>
            <summary>
            This is a non-generated part of Condition class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.#ctor">
            <summary>
            Default constructor used for deserialization. VSTS 55852.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore,System.String)">
            <summary>
            
            </summary>
            <param name="parent"></param>
            <param name="name"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.GetSupportedEvaluationMode">
            <summary>
            Aggregated evaluation mode supported by condition's expressions facets
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.GetSupportedEvaluationModeOnDomain(System.String)">
            <summary>
            Aggregated evaluation mode supported by condition's expressions facets
            </summary>
            <param name="domain">The target domain for the evaluation</param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.ExpressionNode">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.IsEnumerable">
            <summary>
            Returns true if the condition can be used as a target set level filter condition, otherwise false.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.Expression">
            <summary>
            Since this is the actual property which needs storage and the above one is a helper property, this one needs to exist
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.HasScript">
            <summary>
            Returns true if the condition contains an expression that runs a dynamic script, which
            is potentially dangerous.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.InitializeUIPropertyState">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.UpdateUIPropertyState">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Create">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.ScriptCreate">
            <summary>
            Scripts creation of the object
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.PostCreate(System.Object)">
            <summary>
            Perform post-create action
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Alter">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.AlterNoValidation">
            <summary>
            Skpis validation to allow overriding Condition with already validated one (Import scenario)
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.ScriptAlter">
            <summary>
            Scripts all changes on the object
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Drop">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.ScriptDrop">
            <summary>
            Scripts deletion of the object
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Rename(System.String)">
            <summary>
            Renames the object on the server.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcRenamable#Rename(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Renames the object on the server.
            </summary>
            <param name="key"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Discover(Microsoft.SqlServer.Management.Sdk.Sfc.ISfcDependencyDiscoveryObjectSink)">
            <summary>
            
            </summary>
            <param name="sink"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.EnumDependentPolicies">
            <summary>
            Returns a collection of Policy objects that are dependent
            on this Condition.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Condition.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.Condition.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.#ctor(Microsoft.SqlServer.Management.Dmf.Condition.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Dmf.Condition.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.Condition.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.Condition.Key,Microsoft.SqlServer.Management.Dmf.Condition.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Dmf.Condition.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.Condition.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.Condition.Key,Microsoft.SqlServer.Management.Dmf.Condition.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.Parent">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.IdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.SetName(System.String)">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.Description">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.CreateDate">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.CreatedBy">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.DateModified">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.ModifiedBy">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.Facet">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.NameConditionType">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.ObjectName">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Condition.IsSystemObject">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.Condition.facetProperties">
            <summary>
            Hashtable to cache Facet Properties for Validation purposes
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.Condition.validatedFacet">
            <summary>
            last validated Facet 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.RefreshFacetProperties">
            <summary>
            Maintains Facet Properties cache for Validation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Validate(System.String,System.Boolean,Microsoft.SqlServer.Management.Sdk.Sfc.ValidationState)">
            <summary>
            master validation method
            </summary>
            <param name="validationMode"></param>
            <param name="throwOnFirst"></param>
            <param name="validationState"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Validate(System.String)">
            <summary>
            Policy validation
            Unlike ISfcValidate.Validate, this method will throw the first exception it encounters
            If execution of this method doesn't produce any exceptions, validation passed
            </summary>
            <param name="mode"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcValidate#Validate(System.String,System.Object[])">
            <summary>
            ISfcValidate implementation for Policy
            </summary>
            <param name="validationMethod"></param>
            <param name="arguments"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.ValidateDeserialized(Microsoft.SqlServer.Management.Dmf.Policy)">
            <summary>
            Validate condition altered as a part of policy deserialization process
            </summary>
            <param name="policy">policy being deserialized</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Condition.ValidateReferencesDeserialized(Microsoft.SqlServer.Management.Dmf.Policy)">
            <summary>
            The following function is validating the way that this condition is used by
            all the DMF objects that reference it.
            For example overwritten deserialized conditions can change their Facet 
            as long as their referenced only by deserialized Policy and OS.
            </summary>
            <param name="policy"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.TargetEvaluation">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetEvaluation.Target">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetEvaluation.Result">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ConditionCollection">
            <summary>
            This is the collection for Conditions.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConditionCollection.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConditionCollection.Item(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConditionCollection.Contains(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConditionCollection.GetElementFactoryImpl">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ConditionExtender">
            <summary>
            Decorator for the Condition object. Used add additional properties to the base Condition object
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ConditionExtender.facetsCollection">
            <summary>
            Collection of all facets in Policy store
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ConditionExtender.facets">
            <summary>
            Collection of facets exposed.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConditionExtender.#ctor">
            <summary>
            default ctor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConditionExtender.#ctor(Microsoft.SqlServer.Management.Dmf.Condition)">
            <summary>
            ctor. Takes parent Condition object to aggregate on
            </summary>
            <param name="condition"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConditionExtender.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore,System.String)">
            <summary>
            ctor. Create a new Condition object and aggregates on it.
            </summary>
            <param name="policyStore"></param>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConditionExtender.ExpressionNode">
            <summary>
            Translates parent's string 'Expression' property into
            Expression node property
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConditionExtender.FacetInfo">
            <summary>
            Translates parent's 'Facet' string property into FacedInfo object property
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConditionExtender.Facets">
            <summary>
            Provides a list of available facets
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConditionExtender.DependentPolicies">
            <summary>
            provides a list of dependant policies
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConditionExtender.FacetsCollection">
            <summary>
            since PolicyStore.Facets re-creates collection on every call, we have to cache it here
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConditionExtender.RootFacets">
            <summary>
            Expose collection of facets that are attributed as root facets
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.EvaluationId">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.ServerInstance">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Exception">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Parent">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.EvaluationDetails">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Discover(Microsoft.SqlServer.Management.Sdk.Sfc.ISfcDependencyDiscoveryObjectSink)">
            <summary>
            
            </summary>
            <param name="sink"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.IdentityKey">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Result">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.id">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.#ctor(Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.#ctor(System.Int64)">
            <summary>
            
            </summary>
            <param name="id"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.ID">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key,Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key,Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistoryCollection">
            <summary>
            This is the collection for ConnectionEvaluationHistorys.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistoryCollection.#ctor(Microsoft.SqlServer.Management.Dmf.EvaluationHistory)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistoryCollection.Item(System.Int32)">
            <summary>
            
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistoryCollection.Contains(System.Int32)">
            <summary>
            
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistoryCollection.GetElementFactoryImpl">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.EvaluationDetail">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.#ctor(Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory)">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.HistoryId">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.EvaluationDate">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Exception">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.ResultDetail">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.TargetQueryExpression">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Parent">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.IdentityKey">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Result">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.id">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.#ctor(Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.#ctor(System.Int64)">
            <summary>
            
            </summary>
            <param name="id"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.ID">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key,Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key,Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetail.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.EvaluationDetailCollection">
            <summary>
            This is the collection for EvaluationDetails.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetailCollection.#ctor(Microsoft.SqlServer.Management.Dmf.ConnectionEvaluationHistory)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationDetailCollection.Item(System.Int32)">
            <summary>
            
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetailCollection.Contains(System.Int32)">
            <summary>
            
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationDetailCollection.GetElementFactoryImpl">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.OperatorType">
            <summary>
            Operator Types
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.NONE">
            <summary>
            Default type to use when the type should be initialized before it is known
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.AND">
            <summary>
            Logical AND
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.OR">
            <summary>
            Logical OR
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.EQ">
            <summary>
            Equals 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.NE">
            <summary>
            Not equals 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.LT">
            <summary>
            Less than
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.GT">
            <summary>
            Greater than
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.LE">
            <summary>
            Less or equal to
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.GE">
            <summary>
            Greater of equal to 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.IN">
            <summary>
            Equals to one of list items
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.LIKE">
            <summary>
            Matches a string pattern
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.NOT_IN">
            <summary>
            Reversed IN
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.NOT_LIKE">
            <summary>
            Reversed LIKE
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.BEQ">
            <summary>
            Bitwise equals
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.OperatorType.BNE">
            <summary>
            Bitwise not equals
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.TypeClass">
            <summary>
            Enum used for grouping System.Types into larger classes
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TypeClass.Unsupported">
            <summary>
            Not supported type - functionally equivalent to null
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TypeClass.Numeric">
            <summary>
            Numeric types - includes integer, floating point, and fixed point
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TypeClass.String">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TypeClass.Bool">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TypeClass.DateTime">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TypeClass.Guid">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TypeClass.Array">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TypeClass.Variant">
            <summary>
            Unknown return type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TypeClass.VarArgs">
            <summary>
            Variable number of parameters
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TypeClass.BitmappedEnum">
             <summary>
            
             A bitmapped enumeration
             </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.EvaluationFactory">
            <summary>
            Static Factory class producing Evaluators
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationFactory.#ctor">
            <summary>
            private constructor to prevent construction, as this object is not intended to be instanciated
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationFactory.IsTypeSupported(System.Type)">
            <summary>
            Indicates whether supplied Type is supported by ExpressionTree
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationFactory.IsTypeSupportedForConstant(System.Type)">
            <summary>
            Indiciate whether supplied Type can be used to construct a Constant
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationFactory.SupportedOperators(System.Type)">
            <summary>
            Returns a list of operators supported for the given type
            Empty list if evaluation for the type is not supported
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationFactory.SupportedOperators(Microsoft.SqlServer.Management.Dmf.TypeClass)">
            <summary>
            Returns a list of operators supported for the given TypeClass
            Throws if unxepected class requested
            </summary>
            <param name="typeClass"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationFactory.LikeToRegex(System.String)">
            <summary>
            Converts Like pattern to equivalent Regex pattern
            </summary>
            <param name="likePattern">Like pattern</param>
            <returns>Regex pattern</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.EvaluatorDBNull">
            <summary>
            Evaluates an expression that has a DBNull value on one of its sides (or both).
            DBNull always evaluates to false regardless of the operand on the other side
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.EvaluatorBitmappedEnum">
            <summary>
            Evaluates an expression with an enumerator
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluatorBitmappedEnum.Evaluate(Microsoft.SqlServer.Management.Dmf.OperatorType)">
            <summary>
            The evaluator asserts that at least the right operand is a bitmapped enum.
            The evaluation is done as follows:
                BEQ: left AND right == right
                BNE: left AND right != right
            </summary>
            <param name="opType">The operations type. This has to be == or !=.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.EvaluatorArray">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluatorArray.Left">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluatorArray.Right">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluatorArray.InObject">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluatorArray.Evaluator">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluatorArray.ArraysEqual">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.EvaluationHistory">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.#ctor(Microsoft.SqlServer.Management.Dmf.Policy)">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.PolicyName">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.StartDate">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.EndDate">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Exception">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Parent">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.ConnectionEvaluationHistories">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Discover(Microsoft.SqlServer.Management.Sdk.Sfc.ISfcDependencyDiscoveryObjectSink)">
            <summary>
            
            </summary>
            <param name="sink"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.IdentityKey">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Result">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.id">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.#ctor(Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.#ctor(System.Int64)">
            <summary>
            
            </summary>
            <param name="id"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.ID">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key,Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key,Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistory.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.EvaluationHistoryCollection">
            <summary>
            This is the collection for EvaluationHistory.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistoryCollection.#ctor(Microsoft.SqlServer.Management.Dmf.Policy)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.EvaluationHistoryCollection.Item(System.Int64)">
            <summary>
            
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistoryCollection.Contains(System.Int64)">
            <summary>
            
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.EvaluationHistoryCollection.GetElementFactoryImpl">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.NameConditionType">
            <summary>
            Enumerates operators used in Name Conditions (expression: @Name operator 'object_name')
            None used for all other expressions
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.NameConditionType.None">
            
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.NameConditionType.Equal">
            
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.NameConditionType.Like">
            
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.NameConditionType.NotEqual">
            
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.NameConditionType.NotLike">
            
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ExpressionNodeType">
            <summary>
            Types of nodes in Expression Tree
            These are used for identification, not for creation of nodes
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeType.Base">
            <summary>
            Base Type - for initialization only
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeType.Constant">
            <summary>
            Constant
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeType.Attribute">
            <summary>
            Attribute - Management Facet property
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeType.Operator">
            <summary>
            Operator - predefined boolean function with 2 arguments
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeType.Function">
            <summary>
            Function
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeType.Group">
            <summary>
            Group - node enclosed in parentheses
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ConfigurationItem">
            <summary>
            Class representing configuration items - actions needed 
            to configure a target to meet a certain condition.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ConfigurationItem.#ctor(System.String,System.Object)">
            <summary>
            
            </summary>
            <param name="property"></param>
            <param name="desiredValue"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConfigurationItem.Property">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ConfigurationItem.DesiredValue">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ExpressionNode">
            <summary>
            Base node class, cannot be instantiated
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNode.nodeType">
            <summary>
            Type of the Node used for identification when called through base class
            (An alternative to GetType())
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNode.LastEvaluationResult">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNode.Type">
            <summary>
            Type of the Node (read-only)
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.SetNodeType(Microsoft.SqlServer.Management.Dmf.ExpressionNodeType)">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNode.Tag">
            <summary>
            Node's Tag 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNode.TypeClass">
            <summary>
            Node's TypeClass
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.SetTypeClass(Microsoft.SqlServer.Management.Dmf.TypeClass)">
            <summary>
            
            </summary>
            <param name="value"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNode.NameConditionType">
            <summary>
            If condition has the following format {@Name operator 'object_name'}, returns type of operator
            otherwise returns None
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.SetNameConditionType(Microsoft.SqlServer.Management.Dmf.NameConditionType)">
            <summary>
            
            </summary>
            <param name="value"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ObjectName">
            <summary>
            Name of object in NameCondition
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.SetObjectName(System.String)">
            <summary>
            
            </summary>
            <param name="value"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNode.HasScript">
            <summary>
            Returns true if this ExpressionNode's evaluation will run a dynamic script, which is
            potentially dangerous.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.SetHasScript(System.Boolean)">
            <summary>
            
            </summary>
            <param name="value"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNode.FilterNodeCompatible">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.SetFilterNodeCompatible(System.Boolean)">
            <summary>
            
            </summary>
            <param name="value"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.SetProperties">
            <summary>
            Sets class properties (HasScript, NameConditionType, ...)
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.DeepClone">
            <summary>
            Creates a deep clone of the current expresion.
            The evaluation results are copied by reference, not by value.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ResetResult">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.Evaluate(Microsoft.SqlServer.Management.Facets.FacetEvaluationContext)">
            <summary>
            Evaluates the node (tree) using supplied Management Facet context
            </summary>
            <param name="context">Management Facet context</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.Evaluate(Microsoft.SqlServer.Management.Facets.FacetEvaluationContext,System.Boolean)">
            <summary>
            Evaluates the node (tree) using supplied Management Facet context
            </summary>
            <param name="context">Management Facet context</param>
            <param name="checkSqlScriptAsProxy"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.DoEvaluate(Microsoft.SqlServer.Management.Facets.FacetEvaluationContext,System.Boolean)">
            <summary>
            Does actual evaluatioin
            </summary>
            <param name="context">Management Facet context</param>
            <param name="checkSqlScriptAsProxy"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.AnalyzeForConfiguration(System.Collections.Generic.List{Microsoft.SqlServer.Management.Dmf.ConfigurationItem})">
            <summary>
            
            </summary>
            <param name="configurationList"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.GetResult">
            <summary>
            Result of the latest Evaluation
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.GetResultString">
            <summary>
            Result of the latest Evaluation as a String
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.EnumChildren">
            <summary>
            Enumerates Children of the Node
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.EnumAttributes">
            <summary>
            Enumerates Attributes in the tree
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.DoEnumAttributes(System.Collections.Generic.List{System.String})">
            <summary>
            Actual tree enumerator
            </summary>
            <param name="list"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.EnumAttributeOperatorPairs">
            <summary>
            Enumerates Attributes in the tree along with thier Operators
            It ignores Attributes that are not direct children of Operators
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.DoEnumAttributeOperatorPairs(System.Collections.Generic.List{Microsoft.SqlServer.Management.Dmf.AttributeOperatorPair})">
            <summary>
            Actual tree enumerator
            </summary>
            <param name="list"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ToString">
            <summary>
            Represents Expression as a string in T-SQL like syntax
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ToStringForDisplay">
            <summary>
            A special method to display some simple node in the UI in simplified form,
            which cannot always be parsed back
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.GetHashCode">
            <summary>
            Overriden to support overriden Equals
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.Equals(System.Object)">
            <summary>
            Overriden Equals to support value comparison
            Inheritants implement EqualProperties method for type sepcific comparison
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.EqualProperties(System.Object)">
            <summary>
            virtual method for descendants to implement type specific comparison
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.Serialize(System.Xml.XmlWriter)">
            <summary>
            Base serialization routine - creates start and end elements
            calls to virtual method SerializeProperties to output properties of particular node objectTypeName
            </summary>
            <param name="xmlWriter"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.SerializeProperties(System.Xml.XmlWriter,System.Boolean)">
            <summary>
            Virtual method for children classes to serialize thier properties
            </summary>
            <param name="xw"></param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.SerializeResult(System.Xml.XmlWriter)">
            <summary>
            Includes Last Result into serialization output
            </summary>
            <param name="xw"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.DeserializeResult(System.Xml.XmlReader)">
            <summary>
            Deserializes Last Result 
            </summary>
            <param name="xr"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.DeserializeProperties(System.Xml.XmlReader,System.Boolean)">
            <summary>
            Virtual method for children classes to deserialize thier properties
            </summary>
            <param name="xr">XmlReader - must ignore whitespaces</param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ReadEndElement(System.Xml.XmlReader)">
            <summary>
            Reads and verifies instance specific end node element
            </summary>
            <param name="xr"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ReadWithCheck(System.Xml.XmlReader,System.Xml.XmlNodeType,System.String)">
            <summary>
            Reads the next xml node and verifies it has expected type and name (if supplied)
            </summary>
            <param name="xr"></param>
            <param name="nodeType">expected node type</param>
            <param name="name">expected node name (can be null)</param>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.ExpressionSerializationException">Thrown id node type and/or name don't match with provided type, name</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.MoveToElementWithCheck(System.Xml.XmlReader,System.String)">
            <summary>
            Moves to the node with specified type and name (if supplied)
            </summary>
            <param name="xr"></param>
            
            <param name="name">node name (can be null)</param>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.ExpressionSerializationException">Thrown if no node found in the stream</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ReadElementWithCheck(System.Xml.XmlReader,System.String)">
            <summary>
            Reads and verifies named element in its entirety (Element - Text - EndElement)
            </summary>
            <param name="xr"></param>
            <param name="name">element's name</param>
            <returns>element's text</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ReadNodeWithCheck(System.Xml.XmlReader,System.String[])">
            <summary>
            Reads and veirifies specified ExpressionNode properties. Stops after reading the last requested element.
            Requested properties must be listed in the order they appear in the stream.
            </summary>
            <param name="xr"></param>
            <param name="elements">list of elements</param>
            <returns>list of element text values</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ReadSimpleNodeWithCheck(System.Xml.XmlReader,Microsoft.SqlServer.Management.Dmf.ExpressionNodeType,System.String[])">
            <summary>
            Reads and verifies simple (with no children) ExpressionNode  in its entirety, including end element.
            Requested properties must be listed in the order they appear in the stream.
            </summary>
            <param name="xr"></param>
            <param name="type">ExpressionNode's type</param>
            <param name="elements">list of properties</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.Deserialize(System.String)">
            <summary>
            Deserialize from string - creates XmlReader and calls actual deserializer
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.DeserializeWithResult(System.String)">
            <summary>
            Deserialize from string - creates XmlReader and calls actual 
            deserializer. The ExpressionNode will contail the result of the 
            evaluation for the particular target that has generated this 
            serialized version of the node.
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.Deserialize(System.Xml.XmlReader)">
            <summary>
            Static method - provides generic way for deserializing nodes
            calls to virtual method DeserializeProperties to read properties 
            of particular node objectTypeName
            </summary>
            <param name="xr">XmlReader - must ignore whitespaces</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.SerializeNode(Microsoft.SqlServer.Management.Dmf.ExpressionNode)">
            <summary>
            Serializes given Node to a string 
            </summary>
            <param name="node"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.SerializeNodeWithResult(Microsoft.SqlServer.Management.Dmf.ExpressionNode)">
            <summary>
            Serializes given Node to a string
            </summary>
            <param name="node"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.MatchType``1(System.String)">
            <summary>
            Static method - provides generic way of obtaining enum types from their string names
            </summary>
            <typeparam name="T">ObjectType to match</typeparam>
            <param name="value">name</param>
            
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ConvertToString(System.Object)">
            <summary>
            Converts an object to its string representation
            understandable by ConvertFromString
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ResolveEnum(System.String,System.String)">
            <summary>
            Constructs Enum object from its string representation
            </summary>
            <param name="stringObjType"></param>
            <param name="stringValue"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ConvertFromString(System.String,System.String)">
            <summary>
            Converts string to an object of specified type. 
            Type must be supported. UnsupportedTypeException thrown otherwise.
            Catches FormatException and throws TypeConversionException.
            </summary>
            <param name="stringObjType"></param>
            <param name="stringValue"></param>
            <returns></returns>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.TypeConversionException"></exception>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.UnsupportedTypeException"></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ConvertToIntWithCheck(System.String)">
            <summary>
            Converts string to integer. 
            Catches FormatException and throws TypeConversionException.
            </summary>
            <param name="value"></param>
            <returns></returns>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.TypeConversionException"></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.Parse(System.String)">
            <summary>
            Parses input string into ExpressionNode
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.Parse(System.String,System.Type)">
            <summary>
            Parses input string into ExpressionNode and verifies against given Facet
            </summary>
            <param name="input"></param>
            <param name="facet"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.CheckForDateFunctions(System.String)">
            <summary>
            Verifies if parsing failed due to unquoted datepart argument
            Throws specific Exception if the pattern detected
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ConvertFromFilterNode(Microsoft.SqlServer.Management.Sdk.Sfc.FilterNode)">
            <summary>
            
            </summary>
            <param name="filterNode"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ConvertFromFilterNode(Microsoft.SqlServer.Management.Sdk.Sfc.FilterNode,System.Type)">
            <summary>
            
            </summary>
            <param name="filterNode"></param>
            <param name="facet"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ConvertToFilterNode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.DoConvertToFilterNode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ConstructNode(System.Object)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNode.ToStringForUrn">
            <summary>
            Represents Expression as a string in T-SQL like syntax, which can be used in URN
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant">
            <summary>
            Node representing Constants
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.Value">
            <summary>
            Constant Value
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.#ctor">
            <summary>
            Default constructor for deserialization
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.#ctor(System.Object)">
            <summary>
            Creates Constant node
            </summary>
            <param name="obj">Constant value</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.ToString">
            <summary>
            Represents Expression as a string in T-SQL like syntax
            </summary>
            <returns></returns>
            <remarks>If the Expression is a string object it will be quoted with '</remarks>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.ToStringForDisplay">
            <summary>
            A special method to display some simple node in the UI in simplified form
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.EqualProperties(System.Object)">
            <summary>
            Constant specific type comparison
            type and nullability of comparison object checked by caller (Equals)
            </summary>
            <param name="obj">Object to compare to</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.ResetResult">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.DoEvaluate(Microsoft.SqlServer.Management.Facets.FacetEvaluationContext,System.Boolean)">
            <summary>
            
            </summary>
            <param name="context"></param>
            <param name="checkSqlScriptAsProxy"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.SerializeProperties(System.Xml.XmlWriter,System.Boolean)">
            <summary>
            Serializes Constant node properties
            </summary>
            <param name="xw"></param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.DeserializeProperties(System.Xml.XmlReader,System.Boolean)">
            <summary>
            Deserializes Constant node properties
            </summary>
            <param name="xr">XmlReader - must ignore whitespaces</param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.DoConvertToFilterNode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeConstant.DeepClone">
            <summary>
            Deep clone of the current node.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute">
            <summary>
            Node representing Attributes - properties of Management Facets
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.Name">
            <summary>
            Attribute Name - property of Management Facet
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.#ctor">
            <summary>
            Default constructor for deserialization
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.#ctor(System.String)">
            <summary>
            Creates Attribute node
            </summary>
            <param name="name">Attribute name</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.#ctor(System.String,System.Type)">
            <summary>
            Creates Attribute node and verifies it against Facet
            </summary>
            <param name="name"></param>
            <param name="facet"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.ToString">
            <summary>
            Represents Expression as a string in T-SQL like syntax
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.ToStringForDisplay">
            <summary>
            A special method to display some simple node in the UI in simplified form
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.EqualProperties(System.Object)">
            <summary>
            Attribute specific type comparison
            type and nullability of comparison object checked by caller (Equals)
            </summary>
            <param name="obj">Object to compare to</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.DoEvaluate(Microsoft.SqlServer.Management.Facets.FacetEvaluationContext,System.Boolean)">
            <summary>
            
            </summary>
            <param name="context"></param>
            <param name="checkSqlScriptAsProxy"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.SerializeProperties(System.Xml.XmlWriter,System.Boolean)">
            <summary>
            Serializes Attribute node properties
            </summary>
            <param name="xw"></param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.DeserializeProperties(System.Xml.XmlReader,System.Boolean)">
            <summary>
            Deserializes Attribute node properties
            </summary>
            <param name="xr">XmlReader - must ignore whitespaces</param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.DoConvertToFilterNode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.DeepClone">
            <summary>
            Deep clone of the current node.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeAttribute.DoEnumAttributes(System.Collections.Generic.List{System.String})">
            <summary>
            This override is implemented in case somebody calls EnumAttribues directly on Attribute node.
            </summary>
            <param name="list"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren">
            <summary>
            Base class for nodes, having children; cannot be instantiated
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.childrenList">
            <summary>
            List of children nodes
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.ChildrenList">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.EnumerableChildrenList">
            <summary>
            List of children nodes
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.EnumChildren">
            <summary>
            Enumerates Children of the Node
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.Count">
            <summary>
            Count of children nodes
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.Add(Microsoft.SqlServer.Management.Dmf.ExpressionNode)">
            <summary>
            Adds node to the list
            Does NOT check if the node added has the same Type as previously added nodes
            </summary>
            <param name="node"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.EqualProperties(System.Object)">
            <summary>
            Children node specific type comparison
            type and nullability of comparison object checked by caller (Equals)
            </summary>
            <param name="obj">Object to compare to</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.ResetResult">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.AnalyzeForConfiguration(System.Collections.Generic.List{Microsoft.SqlServer.Management.Dmf.ConfigurationItem})">
            <summary>
            
            </summary>
            <param name="configurationList"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.DoEnumAttributes(System.Collections.Generic.List{System.String})">
            <summary>
            Recursively enumerates Attributes
            </summary>
            <param name="list"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.ToString">
            <summary>
            Represents Expression as a string in T-SQL like syntax
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.SerializeProperties(System.Xml.XmlWriter,System.Boolean)">
            <summary>
            Serializes Children nodes 
            </summary>
            <param name="xw"></param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeChildren.DeserializeProperties(System.Xml.XmlReader,System.Boolean)">
            <summary>
            Deserializes Children nodes
            </summary>
            <param name="xr">XmlReader - must ignore whitespaces</param>
            <param name="includeResult"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ExpressionNodeGroup">
            <summary>
            Node, representing a Group - node in parentheses
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeGroup.#ctor">
            <summary>
            Default constructor for deserialization
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeGroup.#ctor(Microsoft.SqlServer.Management.Dmf.ExpressionNode)">
            <summary>
            Creates Group node
            </summary>
            <param name="node">Group node</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeGroup.Group">
            <summary>
            Group node - node inside paretheses - usually operator
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeGroup.SetProperties">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeGroup.ResetResult">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeGroup.DoEvaluate(Microsoft.SqlServer.Management.Facets.FacetEvaluationContext,System.Boolean)">
            <summary>
            Evaluates Group node (usually operator)
            </summary>
            <param name="context">Management Facet context</param>
            <param name="checkSqlScriptAsProxy"></param>
            <returns>object</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeGroup.DeserializeProperties(System.Xml.XmlReader,System.Boolean)">
            <summary>
            Deserializes Group node properties
            </summary>
            <param name="xr">XmlReader - must ignore whitespaces</param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeGroup.DeepClone">
            <summary>
            Deep clone of the current node.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeGroup.DoConvertToFilterNode">
            <summary>
            Converts Group to FilterNodeGrop
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator">
            <summary>
            Operator node - boolean function with 2 arguments
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.OpType">
            <summary>
            Operator objectTypeName
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.#ctor">
            <summary>
            Default constructor for deserialization
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.DeepClone">
            <summary>
            Deep clone of the current node.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.SetProperties">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.#ctor(Microsoft.SqlServer.Management.Dmf.OperatorType,Microsoft.SqlServer.Management.Dmf.ExpressionNode,Microsoft.SqlServer.Management.Dmf.ExpressionNode)">
            <summary>
            Creates Operator node
            </summary>
            <param name="type">Operator type</param>
            <param name="left">Left node</param>
            <param name="right">Right node</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.Left">
            <summary>
            Left operand node
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.Right">
            <summary>
            Right operand node
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.OperatorTypeToString(Microsoft.SqlServer.Management.Dmf.OperatorType)">
            <summary>
            Symbolic repesentation of OperatorType (T-Sql style)
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.OperatorTypeFromString(System.String)">
            <summary>
            Returns OperatorType for given string representation (opposite to OperatorTypeToString)
            </summary>
            <param name="opType"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentException">No match is found for given string</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.ToString">
            <summary>
            Represents Expression as a string in T-SQL like syntax
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.EqualProperties(System.Object)">
            <summary>
            Operator specific type comparison
            type and nullability of comparison object checked by caller (Equals)
            </summary>
            <param name="obj">Object to compare to</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.ResetResult">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.DoEvaluate(Microsoft.SqlServer.Management.Facets.FacetEvaluationContext,System.Boolean)">
            <summary>
            Compares left and right nodes according to operator's type
            </summary>
            <param name="context">Management Facet context</param>
            <param name="checkSqlScriptAsProxy"></param>
            <returns>boolean result</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.AnalyzeForConfiguration(System.Collections.Generic.List{Microsoft.SqlServer.Management.Dmf.ConfigurationItem})">
            <summary>
            
            </summary>
            <param name="configurationList"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.SerializeProperties(System.Xml.XmlWriter,System.Boolean)">
            <summary>
            Serializes Operator node properties
            </summary>
            <param name="xw"></param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.DeserializeProperties(System.Xml.XmlReader,System.Boolean)">
            <summary>
            Deserializes Operator node properties
            </summary>
            <param name="xr">XmlReader - must ignore whitespaces</param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.SupportedFilterOperators(System.Type,Microsoft.SqlServer.Management.Dmf.AutomatedPolicyEvaluationMode)">
            <summary>
            Returns a list of operators supported for the given type in Filters
            (Filters are more restrictive than Condition expressions)
            Empty list if evaluation for the type is not supported
            </summary>
            <param name="type"></param>
            <param name="mode"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator.DoConvertToFilterNode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction">
            <summary>
            Function - returns object for given set of arguments
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function">
            <summary>
            Type of Function
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.ExecuteSql">
            <summary>
            Execute scalar SQL
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.ExecuteWql">
            <summary>
            Execute WMI query
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.GetDate">
            <summary>
            Get current date
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.DateAdd">
            <summary>
            Add a number to a date
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.DatePart">
            <summary>
            Extract a part of a date
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Sum">
            <summary>
            Sum a series of values
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Avg">
            <summary>
            Average a series of values
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Count">
            <summary>
            Count a series of values
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Len">
            <summary>
            Get the length of a string
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.IsNull">
            <summary>
            Substitute a value for null 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Array">
            <summary>
            Return an array of values
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Add">
            <summary>
            Add two values
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Subtract">
            <summary>
            Substract two values
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Multiply">
            <summary>
            Multiple two values
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Divide">
            <summary>
            Divide one value by another
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.BitwiseAnd">
            <summary>
            Bitwise AND
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.BitwiseOr">
            <summary>
            Bitwise OR
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Power">
            <summary>
            Raise a value to an exponential power
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Mod">
            <summary>
            Return the modulus of one number divided by another
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Round">
            <summary>
            Round a number
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Enum">
            <summary>
            Return the textual description for an enum
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.DateTime">
            <summary>
            Return a datetime from a string
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.String">
            <summary>
            Convert a value to a string
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.True">
            <summary>
            Logical true
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.False">
            <summary>
            Logical false
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Guid">
            <summary>
            Return guid from string
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Upper">
            <summary>
            Return upper-case string
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Lower">
            <summary>
            Return lower-case string
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Concatenate">
            <summary>
            Concatenate strings
            </summary>
            Defect 247787, Ability to pass dynamic Arguments to ExecuteWQL
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function.Escape">
            <summary>
            Escape string
            Defect 247787, Ability to pass dynamic Arguments to ExecuteWQL
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.FunctionType">
            <summary>
            Type of Function
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.ReturnType">
            <summary>
            Return type
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.FunctionsDefinitions">
            <summary>
            Function definition dictionary for consumption by the GUI 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.SetProperties">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.#ctor">
            <summary>
            Default constructor for deserialization
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.#ctor(Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.Function,Microsoft.SqlServer.Management.Dmf.ExpressionNode[])">
            <summary>
            Creates Function node
            </summary>
            <param name="functionType">Function type</param>
            <param name="args">Function arguments</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.ToString">
            <summary>
            Represents Expression as a string in T-SQL like syntax
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.ToStringForDisplay">
            <summary>
            A special method to display some simple functions in UI in simplified form,
            cannot be parsed back
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.EqualProperties(System.Object)">
            <summary>
            Function specific type comparison
            type and nullability of comparison object checked by caller (Equals)
            </summary>
            <param name="obj">Object to compare to</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.ResetResult">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.DoEvaluate(Microsoft.SqlServer.Management.Facets.FacetEvaluationContext,System.Boolean)">
            <summary>
            Calculates result of the function
            </summary>
            <param name="context">Management Facet context</param>
            <param name="checkSqlScriptAsProxy"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.EvaluateExecuteSqlScalar(System.Boolean)">
            <summary>
            To evaluate an ExecuteSql() expression we can have one of two modes:
                1- The OnDemand mode, in which case the policy is executed in the caller's context.
                2- The OnSchedule mode, in which case agent executes the policy in the checkSqlScriptAsProxy mode.
                   In this mode, we are supposed to do the evaluation in the context of the policy LPU ##MS_PolicyTsqlExecutionLogin##.
            </summary>
            <param name="checkSqlScriptAsProxy">true if we will impersonate the LPU ##MS_PolicyTsqlExecutionLogin## for evaluation.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.SerializeProperties(System.Xml.XmlWriter,System.Boolean)">
            <summary>
            Serializes Operator node properties
            </summary>
            <param name="xw"></param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.DeserializeProperties(System.Xml.XmlReader,System.Boolean)">
            <summary>
            Deserializes Operator node properties
            </summary>
            <param name="xr">XmlReader - must ignore whitespaces</param>
            <param name="includeResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.DoConvertToFilterNode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ExpressionNodeFunction.DeepClone">
            <summary>
            Deep clone of the current node.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.FacetInfo">
            <summary>
            This class provides information about
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.FacetInfo.#ctor(System.Type)">
            
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.FacetInfo.#ctor(System.String)">
            
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.FacetInfo.Name">
            <summary>
            Returns a AssemblyQualifiedName of the facet type.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.FacetInfo.DisplayName">
            <summary>
            Returns a display name of the facet.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.FacetInfo.ToString">
            <summary>
            Returns the DisplayName property.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.FacetInfo.Description">
            <summary>
            Returns the description of the facet.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.FacetInfo.FacetType">
            <summary>
            Returns the Type of the facet itself.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.FacetInfo.TargetTypes">
            <summary>
            Returns the Types that this facet operates on.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.FacetInfo.EvaluationMode">
            <summary>
            Returns the AutomatedPolicyEvaluationMode of the facet.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.FacetInfo.FacetPropertyDescriptors">
            <summary>
            Returns static information about the properties descriptors exposed by
            this facet.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.FacetInfo.FacetProperties">
            <summary>
            Returns static information about the properties exposed by
            this facet.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.FacetInfo.GetTargetProperty(System.String,System.Object)">
            <summary>
            Given a property name and a target object, this method
            returns the value of that property as seen on the target
            by this facet.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.FacetInfoCollection">
            <summary>
            This is the collection for FacetInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.FacetInfoCollection.GetKeyForItem(Microsoft.SqlServer.Management.Dmf.FacetInfo)">
             <summary>
            
             </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ObjectSet">
            <summary>
            </summary>
            <summary>
            This is a non-generated part of ObjectSet class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.#ctor">
            <summary>
            This is the non-generated part of the ObjectSet class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore,System.String)">
            <summary>
            
            </summary>
            <param name="parent"></param>
            <param name="name"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.PopulateTargetSetsBasedOnFacet(Microsoft.SqlServer.Management.Dmf.ObjectSet,System.Type)">
            <summary>
            Populates TargetSets based on path information supplied by target types ('physical' path)
            </summary>
            <param name="objectSet"></param>
            <param name="facetType"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.PopulateTargetSetsBasedOnFacet(Microsoft.SqlServer.Management.Dmf.ObjectSet,System.Type,Microsoft.SqlServer.Management.Sdk.Sfc.SfcDomainInfo)">
            <summary>
            Populates TargetSets based on path information supplied by specified domain ('view' path)
            if the domain implements ISfcDomain2, otherwise it defaults to 'physical' path
            </summary>
            <param name="objectSet"></param>
            <param name="facetType"></param>
            <param name="domainInfo"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ObjectSet.TargetSets">
            <summary>
            Collection of TargetSet objects.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.CalculateTargets(Microsoft.SqlServer.Management.Common.ISfcConnection)">
            <summary>
            
            </summary>
            <param name="targetConnection"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.GetTargetDomain">
            <summary>
            The following function gets the domain of the targets belonging to the target set.
            We assume all targets in a targetset have the same domain.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.CalculateTargets(Microsoft.SqlServer.Management.Common.ISfcConnection,Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression)">
            <summary>
            
            </summary>
            <param name="targetConnection"></param>
            <param name="sfcQueryExpression"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.CalculateTargets(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String)">
            <summary>
            
            </summary>
            <param name="sqlStoreConnection"></param>
            <param name="policyCategory"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.GetNonExpensiveProps(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Helper function that calculates what are the non-expensive properties 
            that are associated with a certain Urn.
            Note that in the event of an empty set we are returning null.
            </summary>
            <param name="server"></param>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.CalculateTargets(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,Microsoft.SqlServer.Management.Dmf.Condition,Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode,System.String,System.Object[]@,Microsoft.SqlServer.Management.Dmf.TargetEvaluation[]@)">
            <summary>
            
            </summary>
            <param name="targetConnection"></param>
            <param name="condition"></param>
            <param name="evaluationMode"></param>
            <param name="policyCategory"></param>
            <param name="conforming"></param>
            <param name="violating"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ObjectSet.TargetsDatabaseObjects">
            <summary>
            Verifies that all TargetSets target Databases or objects under Databases
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.IsEventingFilter">
            <summary>
            Verifies that all TargetSets can be used in Enforce and CoC modes
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.GetAdjustedFilter(Microsoft.SqlServer.Management.Dmf.TargetSet,Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Dmf.PolicyCategory)">
            <summary>
            DO NOT USE outside of ObjectSet!
            This is essentially private method, exposed as INTERNAL for testing purposes only.
            Generates ObjectQuery for TargetSet honoring category subscriptions, system object and database accessibility rules
            </summary>
            <param name="ts"></param>
            <param name="server"></param>
            <param name="pc"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.AdjustForSystem(System.Version,Microsoft.SqlServer.Management.Dmf.Policy,Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression)">
            <summary>
            ! This method is used exclusively by PolicyEvaluationHelper and works in SQLCLR only !
            
            Adjusts given QueryExpression to exclude System objects
            unless object name specified explicitly in policy's filter (currently could only be a DB)
            </summary>
            <param name="ver"></param>
            <param name="policy"></param>
            <param name="targetQueryExpression"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.GenerateUniqueName(Microsoft.SqlServer.Management.Dmf.Policy)">
            <summary>
            Generates unique (at the moment of request) ObjectSet name based on Policy name.
            Policy has to be parented (otherwise uniqueness cannot be verified)
            </summary>
            <param name="policy"></param>
            <returns>generated name, NULL if policy has no Parent</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Discover(Microsoft.SqlServer.Management.Sdk.Sfc.ISfcDependencyDiscoveryObjectSink)">
            <summary>
            
            </summary>
            <param name="sink"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.ScriptCreate">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Create">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.PostCreate(System.Object)">
            <summary>
            Perform post-create action
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.ScriptCreateWithDependencies(System.String)">
            <summary>
            Scripts Create with all referenced Conditions, excluding policyCondition,
            in case it's refernced by both Policy and OS
            </summary>
            <param name="policyCondition">Policy's Condition name</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.ScriptDrop">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Drop">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.ScriptAlter">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Alter">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.#ctor(Microsoft.SqlServer.Management.Dmf.ObjectSet.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Dmf.ObjectSet.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.ObjectSet.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.ObjectSet.Key,Microsoft.SqlServer.Management.Dmf.ObjectSet.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Dmf.ObjectSet.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.ObjectSet.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.ObjectSet.Key,Microsoft.SqlServer.Management.Dmf.ObjectSet.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ObjectSet.Parent">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ObjectSet.IdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ObjectSet.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.SetName(System.String)">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ObjectSet.ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ObjectSet.Facet">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.SetFacetWithDomain(System.String,System.String)">
            <summary>
            Constructs ObjectSet, using domain to generate TargetSet paths
            </summary>
            <param name="facet"></param>
            <param name="domain"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ObjectSet.IsSystemObject">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Validate(System.String,System.Boolean,Microsoft.SqlServer.Management.Sdk.Sfc.ValidationState)">
            <summary>
            master validation method
            </summary>
            <param name="validationMode"></param>
            <param name="throwOnFirst"></param>
            <param name="validationState"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Validate(System.String)">
            <summary>
            Policy validation
            Unlike ISfcValidate.Validate, this method will throw the first exception it encounters
            If execution of this method doesn't produce any exceptions, validation passed
            </summary>
            <param name="mode"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSet.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcValidate#Validate(System.String,System.Object[])">
            <summary>
            ISfcValidate implementation for Policy
            </summary>
            <param name="validationMethod"></param>
            <param name="arguments"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.ObjectSetCollection">
            <summary>
            This is the collection for ObjectSets.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSetCollection.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.ObjectSetCollection.Item(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSetCollection.Contains(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.ObjectSetCollection.GetElementFactoryImpl">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Policy">
            <summary>
            This is the non-generated part of the Policy class.
            </summary>
            <summary>
            This is the non-generated part of the Policy class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.#ctor">
            <summary>
            Default constructor used for deserialization. VSTS 55852.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore,System.String)">
            <summary>
            Instantiates a new Policy object.
            </summary>
            <param name="parent"></param>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.IsLogAvailable">
            <summary>
            Indicates when the log is available
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.InitializeUIPropertyState">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.UpdateUIPropertyState">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Create">
            <summary>
            Creates the object on the server.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ScriptCreate">
            <summary>
            Scripts creation of the object on the server.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.PostCreate(System.Object)">
            <summary>
            Perform post-create action
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ScriptCreateWithDependencies">
            <summary>
            Scripts Create Policy with all dependencies, including ObjectSet and all referenced Conditions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ScriptCreateWithObjectSet">
            <summary>
            Scripts Create Policy with dependent ObjectSet. Doesn't include referenced Conditions.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Alter">
            <summary>
            Persists all changes made to this object.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ScriptAlter">
            <summary>
            Scripts all changes made to this object.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.PostAlter(System.Object)">
            <summary>
            Perform post-alter action
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ScriptAlterWithObjectSet">
            <summary>
            Scripts Alter Policy with dependent ObjectSet. Doesn't include referenced Conditions.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Drop">
            <summary>
            Drops the object and removes it from the collection.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ScriptDrop">
            <summary>
            Scripts deletion of the object
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ScriptDropWithObjectSet">
            <summary>
            Scripts Drop Policy with dependent ObjectSet. Doesn't include referenced Conditions.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Rename(System.String)">
            <summary>
            Renames the object on the server.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcRenamable#Rename(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Renames the object on the server.
            </summary>
            <param name="key"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ProcessViolators(Microsoft.SqlServer.Management.Dmf.Condition,Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode,Microsoft.SqlServer.Management.Dmf.TargetEvaluation[],Microsoft.SqlServer.Management.Dmf.LogPolicyEvents)">
            <summary>
            Helper function that handles logging and configuration 
            of violating targets
            </summary>
            <param name="condition"></param>
            <param name="evaluationMode"></param>
            <param name="violating"></param>
            <param name="logPolicyEvents"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Evaluate(Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode,System.Object[])">
            <summary>
            
            </summary>
            <param name="evaluationMode"></param>
            <param name="targetObjects"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Evaluate(Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode,Microsoft.SqlServer.Management.Common.ISfcConnection[])">
            <summary>
            
            </summary>
            <param name="evaluationMode"></param>
            <param name="targetConnections"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Evaluate(Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode,System.Int64@,Microsoft.SqlServer.Management.Common.ISfcConnection[])">
            <summary>
            
            </summary>
            <param name="evaluationMode"></param>
            <param name="historyId"></param>
            <param name="targetConnections"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Evaluate(Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode,Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression,Microsoft.SqlServer.Management.Common.ISfcConnection[])">
            <summary>
            
            </summary>
            <param name="evaluationMode"></param>
            <param name="targetQueryExpression"></param>
            <param name="targetConnections"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Evaluate(Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode,Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression,System.Int64@,Microsoft.SqlServer.Management.Common.ISfcConnection[])">
            <summary>
            
            </summary>
            <param name="evaluationMode"></param>
            <param name="targetQueryExpression"></param>
            <param name="historyId"></param>
            <param name="targetConnections"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.EvaluatePolicyUsingConnections(Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode,Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression,System.Int64@,Microsoft.SqlServer.Management.Common.ISfcConnection[])">
            <summary>
            
            </summary>
            <param name="evaluationMode"></param>
            <param name="targetQueryExpression"></param>
            <param name="historyId"></param>
            <param name="targetConnections"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ProduceConfigureScript(System.Object)">
            <summary>
            
            </summary>
            <param name="target"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.IsInTargetSet(Microsoft.SqlServer.Management.Sdk.Sfc.SfcConnection,Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression,System.Boolean)">
            <summary>
            
            </summary>
            <param name="targetConnection"></param>
            <param name="targetQE"></param>
            <param name="checkSqlScriptAsProxy"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.RaisePolicyResultEvent(System.String)">
            <summary>
            Call RAISERROR to generate an event in case of policy execution failure.
            </summary>
            <param name="targetUri">Uri expression pointing to the target 
            that generated the event. If null it means that the policy has 
            been executed for all the targets in the target set.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Discover(Microsoft.SqlServer.Management.Sdk.Sfc.ISfcDependencyDiscoveryObjectSink)">
            <summary>
            
            </summary>
            <param name="sink"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.UpdateScheduleProperties">
            <summary>
            This function fills in the schedule properties
            based on the ScheduleUid, which is the only persisted 
            property for this policy.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Policy.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.Policy.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.#ctor(Microsoft.SqlServer.Management.Dmf.Policy.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Dmf.Policy.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.Policy.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.Policy.Key,Microsoft.SqlServer.Management.Dmf.Policy.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Dmf.Policy.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.Policy.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.Policy.Key,Microsoft.SqlServer.Management.Dmf.Policy.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.Parent">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.IdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.SetName(System.String)">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.Description">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.CreateDate">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.Condition">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.ObjectSet">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.RootCondition">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.PolicyCategory">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.Enabled">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.AutomatedPolicyEvaluationMode">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.ScheduleUid">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.CreatedBy">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.DateModified">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.ModifiedBy">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.CategoryId">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.HelpText">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.HelpLink">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.IsSystemObject">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.EvaluationHistories">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.HasScript">
            <summary>
            Shows if any of the conditions referenced by this policy references 
            a t-sql or wql script
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.UsesFacet(System.String)">
            <summary>
            Returns a boolean indicating if this policy's Condition uses the given facet.
            </summary>
            <param name="facet"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationStartedEventHandler">
            <summary>
            Signals the start of policy execution.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="E:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationStarted">
            <summary>
            Event fired before the policy execution starts.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingStartedEventHandler">
            <summary>
            Signals the start of policy execution for one connection.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="E:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingStarted">
            <summary>
            Event fired before the policy execution starts for one connection.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingStartedEventArgs">
            <summary>
            Argument for ConnectionProcessingStarted event.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingStartedEventArgs.#ctor(Microsoft.SqlServer.Management.Common.ISfcConnection)">
            <summary>
            Internal ctor.
            </summary>
            <param name="connection"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingStartedEventArgs.Connection">
            Connection used to evaluate the policy
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventHandler">
            <summary>
            Delegate for target execution
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="E:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessed">
            <summary>
            Event fired after a target has been processed.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventArgs">
            <summary>
            Arguments for the TargetProcessed event.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventArgs.#ctor(System.Object,System.String,System.Boolean,System.Boolean,System.String,Microsoft.SqlServer.Management.Dmf.ExpressionNode,Microsoft.SqlServer.Management.Dmf.PolicyEvaluationException,System.String)">
            <summary>
            Internal ctor.
            </summary>
            <param name="targetObject"></param>
            <param name="targetExpression"></param>
            <param name="result"></param>
            <param name="isConfigurable"></param>
            <param name="configMessage"></param>
            <param name="expressionNode"></param>
            <param name="exception"></param>
            <param name="serverName"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventArgs.TargetObject">
            The actual target object that was processed. This could be anything at all, so it
            is of type Object.
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventArgs.TargetExpression">
            query expression representing the target
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventArgs.Result">
            Result of the evaluation
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventArgs.ExpressionNode">
            Expression node that contains the result.
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventArgs.Exception">
            Exception thrown during evaluation, null if no exception occured.
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventArgs.ServerName">
            Connection used to evaluate the policy
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventArgs.IsConfigurable">
            indicates the object can be configured
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventArgs.ConfigurationErrorMessage">
            provides additional information on why the object cannot be configured
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinishedEventHandler">
            <summary>
            Delegate that will be called when policy execution finishes for that connection.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="E:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinished">
            <summary>
            Event that gets fired after the policy execution has ended.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinishedEventArgs">
            <summary>
            Argument for ConnectionProcessingFinished event.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinishedEventArgs.#ctor(System.Boolean,Microsoft.SqlServer.Management.Dmf.PolicyEvaluationException,Microsoft.SqlServer.Management.Common.ISfcConnection,System.Int32,System.Boolean)">
            <summary>
            Internal ctor.
            </summary>
            <param name="result"></param>
            <param name="exception"></param>
            <param name="connection"></param>
            <param name="targetsEvaluated"></param>
            <param name="rootCheckPassed"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinishedEventArgs.#ctor(System.Boolean,Microsoft.SqlServer.Management.Dmf.PolicyEvaluationException,Microsoft.SqlServer.Management.Common.ISfcConnection,System.Int32)">
            <summary>
            Internal ctor.
            </summary>
            <param name="result"></param>
            <param name="exception"></param>
            <param name="connection"></param>
            <param name="targetsEvaluated"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinishedEventArgs.Result">
            Final result of the policy execution.
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinishedEventArgs.Exception">
            Exception thrown during evaluation, null if no exception occured.
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinishedEventArgs.Connection">
            Conneciton used to evaluate the policy
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinishedEventArgs.TargetsEvaluated">
            Number of targets evaluated
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinishedEventArgs.RootCheckPassed">
            Indicates if connection passed RootCheck
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationFinishedEventHandler">
            <summary>
            Delegate that will be called when policy execution finishes.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="E:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationFinished">
            <summary>
            Event that gets fired after the policy execution has ended.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationFinishedEventArgs">
            <summary>
            Argument for PolicyEvaluatioFinished event.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationFinishedEventArgs.#ctor(System.Boolean,Microsoft.SqlServer.Management.Dmf.PolicyEvaluationException)">
            <summary>
            Constructs a new PolicyEvaluationFinishedEventArgs
            </summary>
            <param name="result"></param>
            <param name="exception"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationFinishedEventArgs.Result">
            Final result of the policy execution.
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationFinishedEventArgs.Exception">
            Exception thrown during evaluation, null if no exception occured.
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationFinishedEventArgs.EvaluationHistory">
            Evaluation history for this run of the policy
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationStartedHistoryBuilder">
            <summary>
            Policy execution started worker.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationFinishedHistoryBuilder(Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationFinishedEventArgs)">
            <summary>
            Policy execution worker.
            </summary>
            <param name="e">The <see cref="T:Microsoft.SqlServer.Management.Dmf.Policy.PolicyEvaluationFinishedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingStartedHistoryBuilder(Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingStartedEventArgs)">
            <summary>
            Connection Processing started worker.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinishedHistoryBuilder(Microsoft.SqlServer.Management.Dmf.Policy.ConnectionProcessingFinishedEventArgs)">
            <summary>
            Policy execution finished started worker.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedHistoryBuilder(Microsoft.SqlServer.Management.Dmf.Policy.TargetProcessedEventArgs)">
            <summary>
            Target processed started worker.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.Policy.HelpTextStringMaxLength">
            <summary>
            Limit on the length of HelpText property specified by user.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.Policy.HelpLinkStringMaxLength">
            <summary>
            Limit on the length of HelpLink property. Note that this is the max hyperlink length accepted by IE.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Validate(System.String,System.Boolean,Microsoft.SqlServer.Management.Sdk.Sfc.ValidationState)">
            <summary>
            master validation method
            </summary>
            <param name="validationMode"></param>
            <param name="throwOnFirst"></param>
            <param name="validationState"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Validate(System.String)">
            <summary>
            Policy validation
            Unlike ISfcValidate.Validate, this method will throw the first exception it encounters
            If Evaluation of this method doesn't produce any exceptions, validation passed
            </summary>
            <param name="mode"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcValidate#Validate(System.String,System.Object[])">
            <summary>
            ISfcValidate implementation for Policy
            </summary>
            <param name="validationMethod"></param>
            <param name="arguments"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Policy.ValidateConnectionCompatibility(System.String,Microsoft.SqlServer.Management.Common.ISfcConnection[])">
            <summary>
            This method evaluates if supplied connections can be used to retrieve 
            objects for the policy
            </summary>
            <param name="facet"></param>
            <param name="targetConnections"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode">
            <summary>
            The AdHocPolicyEvaluationMode bit flag enum provides the execution 
            mode for a policy that is "run now".
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode.Check">
            Immediately runs the policy in check mode.
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode.Configure">
            Reconfigures the target(s) to comply with the policy.
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode.CheckSqlScriptAsProxy">
            Check the policy, executing any Sql scripts under the special
            ##MS_PolicyTsqlExecutionLogin## proxy login
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyCategory">
            <summary>
            This is the non-generated part of the PolicyCategory class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.#ctor">
            <summary>
            Default constructor used for deserialization. VSTS 55852.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore,System.String)">
            <summary>
            
            </summary>
            <param name="parent"></param>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategory.DefaultCategory">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.SetName(System.String)">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategory.ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategory.MandateDatabaseSubscriptions">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.#ctor">
            <summary>
            Default constructor for generic key creation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key,Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key,Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Parent">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategory.IdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Create">
            <summary>
            Creates the object on the server
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.PostCreate(System.Object)">
            <summary>
            Perform post-create action
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Drop">
            <summary>
            Drops the object from the server
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Rename(System.String)">
            <summary>
            Renames the object on the server.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcRenamable#Rename(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Renames the object on the server.
            </summary>
            <param name="key"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Alter">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategory.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcAlterable#ScriptAlter">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyCategoryCollection">
            <summary>
            This is the collection for Policy categories.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategoryCollection.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategoryCollection.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore,System.Collections.Generic.IComparer{System.String})">
            <summary>
            
            </summary>
            <param name="parent"></param>
            <param name="customComparer"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategoryCollection.Item(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategoryCollection.Contains(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategoryCollection.GetElementFactoryImpl">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation">
            <summary>
            Information about a policy category.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyCategory)">
            <summary>
            Ctor.
            </summary>
            <param name="policyCategory"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyCategory,Microsoft.SqlServer.Management.Dmf.Policy,System.Boolean)">
            <summary>
            Ctor.
            </summary>
            <param name="policyCategory"></param>
            <param name="policy"></param>
            <param name="targetSubscribes"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.ID">
            <summary>
            Policy Category ID
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.Name">
            <summary>
            Policy name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.IsSubscribed">
            <summary>
            Is subscribed
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.MandateDatabaseSubscriptions">
            <summary>
            The category is active on the server or not.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.PolicyId">
            <summary>
            Policy ID
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.IsEmptyCategory">
            <summary>
            The category does contain any policy or not.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.PolicyName">
            <summary>
            Policy name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.PolicyEnabled">
            <summary>
            Policy Enabled
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.EvaluationMode">
            <summary>
            Policy Evaluation Mode
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation.CompareByCategoryIDPolicyName(Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation,Microsoft.SqlServer.Management.Dmf.PolicyCategoryInformation)">
            Used for sorting
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription">
            <summary>
            This is the non-generated part of the PolicyCategorySubscription class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.#ctor">
            <summary>
            Default constructor used for deserialization. VSTS 55852.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore,Microsoft.SqlServer.Management.Smo.SqlSmoObject)">
            <summary>
            Constructor, accepting SmoObject (has to be a Database)
            </summary>
            <param name="parent"></param>
            <param name="obj"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore,Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression,System.String)">
            <summary>
            Constructor, accepting target and the category name
            </summary>
            <param name="parent"></param>
            <param name="target"></param>
            <param name="policyCategory"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.id">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.#ctor(System.Int32)">
            <summary>
            
            </summary>
            <param name="id"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.ID">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key,Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key,Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Parent">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.IdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.SetID(System.Int32)">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.TargetType">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Target">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.PolicyCategory">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Create">
            <summary>
            Create the object
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.PostCreate(System.Object)">
            <summary>
            Perform post-create action
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Alter">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Drop">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.Validate(System.String)">
            <summary>
            Validates object
            </summary>
            <param name="mode"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscription.ValidateProperties(System.String)">
            <summary>
            Validates object properties
            </summary>
            <param name="mode"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscriptionCollection">
            <summary>
            This is the collection for PolicyCategorySubscriptions.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscriptionCollection.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscriptionCollection.Item(System.Int32)">
            <summary>
            
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscriptionCollection.Contains(System.Int32)">
            <summary>
            
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCategorySubscriptionCollection.GetElementFactoryImpl">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyCollection">
            <summary>
            This is the collection for Policies.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCollection.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCollection.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore,System.Collections.Generic.IComparer{System.String})">
            <summary>
            
            </summary>
            <param name="parent"></param>
            <param name="customComparer"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyCollection.Item(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCollection.Contains(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyCollection.GetElementFactoryImpl">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationResultsWriter">
            <summary>
            Writes evaluation histories into a single rooted xml document with a root element named PolicyEvaluationResults.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationResultsWriter.#ctor(System.Xml.XmlWriter)">
            <summary>
            A PolicyEvaluationResultsWriter writes evaluation histories into a single rooted xml document
            with the root element PolicyEvaluationResults.  Add histories to the xml by calling the 
            WriteEvaluationHistory method.  Close the root element and document by calling Dispose().
            </summary>
            <param name="xmlWriter"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationResultsWriter.WriteEvaluationHistory(Microsoft.SqlServer.Management.Dmf.EvaluationHistory)">
            <summary>
            Write an EvaluationHistory to the writer stream 
            </summary>
            <param name="history"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationResultsWriter.GetXmlWriterSettings">
            <summary>
            The settings an XmlWriter can use to create an aggregate evaluation history
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationResultsWriter.WriteAggergateEvaluationHistoryStart">
            <summary>
            Start an aggregation of EvaluationHistories.  Add specific evaluationHistories to
            the stream by calling WriteEvaluationHistory(EvaluationHistory, XmlWriter)
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationResultsWriter.WriteAggergateEvaluationHistoryEnd">
            <summary>
            End the aggregation of EvaluationHistories.  This method does not close the writer.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationResultsWriter.Dispose">
            <summary>
            End the document and release the reference to the xmlWriter
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationResultsWriter.Dispose(System.Boolean)">
            <summary>
            End the document and release the reference to the xmlWriter.
            
            If disposing equals true, the method has been called directly
            or indirectly by a user's code. Managed and unmanaged resources
            can be disposed.
            If disposing equals false, the method has been called by the
            runtime from inside the finalizer and you should not reference
            other objects. Only unmanaged resources can be disposed.
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyExtender">
            <summary>
            Decorator for the Policy object. Used add additional properties to the base Policy object
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyExtender.#ctor">
            <summary>
            default ctor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyExtender.#ctor(Microsoft.SqlServer.Management.Dmf.Policy)">
            <summary>
            ctor. Takes parent Policy object to aggregate on
            </summary>
            <param name="policy"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyExtender.#ctor(Microsoft.SqlServer.Management.Dmf.PolicyStore,System.String)">
            <summary>
            ctor. Create a new Policy object and aggregates on it.
            </summary>
            <param name="policyStore"></param>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyExtender.ConditionInstance">
            <summary>
            Translates parent's 'Condition' string property into Condition object property
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyExtender.SupportedPolicyEvaluationMode">
            <summary>
            Returns supported evaluation modes for selected Condition
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyExtender.Category">
            <summary>
            Translates parent's 'PolicyCategory' string property into PolicyCategory object property
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyExtender.Categories">
            <summary>
            Provides a list of available categories
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyExtender.Filters">
            <summary>
            Current Filters
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyExtender.PolicyFilePath">
            <summary>
            Policy File path in offline mode
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyExtender.OfflineMode">
            <summary>
            Offline mode or not
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyExtender.RootName">
            <summary>
            Root name of the policy object
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyExtender.EnableRootRestriction">
            <summary>
            Root name of the policy object
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyStore">
            <summary>
            The PolicyStore object is the root object for the DMF hierarchy.
            </summary>
            <summary>
            This is the non-generated part of the PolicyStore class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.#ctor">
            <summary>
            Don't ever call this, or if you do remember to set SqlStoreConnection
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.#ctor(Microsoft.SqlServer.Management.Sdk.Sfc.SfcConnection)">
            <summary>
            
            </summary>
            <param name="connection"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Alter">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.ScriptAlter">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.ToString">
            <summary>
            The string identity of a policy store is the associated Server name.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.IdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#GetConnection">
            <summary>
            Get the current connection to query on.
            Return a connection supporting a single serial query, so the query must end before another one may begin.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#SetConnection(Microsoft.SqlServer.Management.Common.ISfcConnection)">
            <summary>
            Sets the active connection.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#GetConnection(Microsoft.SqlServer.Management.Sdk.Sfc.SfcObjectQueryMode)">
            <summary>
            Get the current connection to query on.
            Return a connection supporting either a single serial query or multiple simultaneously open queries as requested.
            </summary>
            <param name="mode"></param>
            <returns>The connection to use, or null to use Cache mode. Cache mode avoids connection and open data reader issues.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#ConnectionContext">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.SqlStoreConnection">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.Conditions">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.ObjectSets">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.Policies">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.PolicyCategories">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.Facets">
            <summary>
            Return a Collection of FacetInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumDomainFacets(System.String[])">
            <summary>
            Return a Collection of FacetInfo for listed domains.
            Pass String.Empty for Facets, not associated with SFC Domain
            </summary>
            <param name="args"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumRootFacets(System.Type)">
            <summary>
            Returns a collection of Root facets
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.PolicyCategorySubscriptions">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumerationMode">
            <summary>
            Defines behavior towards IsSystemObject flag
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumerationMode.NonSystemOnly">
            <summary>
            Include only objects with IsSystemObject=false
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumerationMode.SystemOnly">
            <summary>
            Include only objects with IsSystemObject=true
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumerationMode.All">
            <summary>
            Include all objects regardless of IsSystemObject state
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumConditionsOnFacet(System.String)">
            <summary>
            Enumerate Conditions associated with the given facet
            </summary>
            <param name="facet"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumConditionsOnFacet(System.String,Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumerationMode)">
            <summary>
            Enumerate Conditions associated with the given facet, honoring IsSystemObject flag
            </summary>
            <param name="facet"></param>
            <param name="enumerationMode"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumRootConditions(System.Type)">
            <summary>
            Enmerate root conditions for a given type
            </summary>
            <param name="rootType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumPoliciesOnFacet(System.String)">
            <summary>
            Enumerate Policies associated with the given facet
            </summary>
            <param name="facet"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumPoliciesOnFacet(System.String,Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumerationMode)">
            <summary>
            Enumerate Policies associated with the given facet, honoring IsSystemObject flag
            </summary>
            <param name="facet"></param>
            <param name="enumerationMode"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumTargetSetConditions(System.Type)">
            <summary>
            Enumerate Conditions for a given Type that can be use in TargetSet filters
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumTargetSetConditions(System.Type,Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumerationMode)">
            <summary>
            Enumerate Conditions for a given Type that can be use in TargetSet filters, honoring IsSystemObject flag
            </summary>
            <param name="type"></param>
            <param name="enumerationMode"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetType(System.String)">
            <summary>
            
            </summary>
            <param name="typeName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetKey(Microsoft.SqlServer.Management.Sdk.Sfc.IUrnFragment)">
            <summary>
            returns the Key object given Urn fragment
            </summary>
            <param name="urnFragment"></param>
            <returns>SfcKey</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomainLite#GetLogicalVersion">
            <summary>
            Returns the logical version of the domain
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Discover(Microsoft.SqlServer.Management.Sdk.Sfc.ISfcDependencyDiscoveryObjectSink)">
            <summary>
            
            </summary>
            <param name="sink"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumApplicablePolicies(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression)">
            <summary>
            Returns a DataTable containing the
            policies that apply to the same target type as the
            supplied target. In other words, if a Database target is
            given then policies that apply to Database, Table and any
            other types under database will be considered.
            </summary>
            <param name="target"></param>
            <returns>A DataTable object containing the policies.
            The schema for this is as follows:
            Column                          Type                
            Name                            string              
            PolicyCategory                  String
            Effective                       bool
            AutomatedPolicyEvaluationMode    enum
            PolicyEffectiveState            enum
            PolicyHealthState               enum
            LastExecutionTime               DateTime
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.GetPoliciesHealthStateTable(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression)">
            <summary>
            This function returns a DataTable that contains information about
            the health state of all policies relative to one single target.
            </summary>
            <param name="target"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.GetAggregatedHealthState(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression)">
            <summary>
            Returns the aggregated health information of the target query, which 
            can describe either a single object or a collection of objects.
            </summary>
            <param name="target"></param>
            <returns>If there is a violation for this object or one of its descendents
            we will return Critical, otherwise Unknown.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.GetAggregatedHealthStateWithFilter(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression,Microsoft.SqlServer.Management.Sdk.Sfc.SfcObjectQuery)">
            <summary>
            Returns aggregated health state information for a query that contains 
            a filter on the last level.
            </summary>
            <param name="target"></param>
            <param name="policyQuery"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EscapeLikePattern(System.String)">
            <summary>
            Escapes the pattern that is supplied into a t-sql query doing 
            pattern matching with the LIKE keyword.
            </summary>
            <param name="pattern"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumPolicyCategories">
            <summary>
            Returns a read-only collection containing all of the
            policy categories.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EnumApplicablePolicyCategories(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression)">
            <summary>
            Returns a read-only collection containing each policy
            category, repeated for each of the policies in that category,
            and a flag if the target subscribes to the category, all of
            the database and database descendent policies in the
            category, and the execution mode of the policy. Note: this
            method is currently only valid on database targets.
            </summary>
            <param name="target"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.IsTargetInCategory(System.String,Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression)">
            <summary>
            This function checks to see if the current target belongs to the category 
            It is used to define effective policies
            </summary>
            <param name="target"></param>
            <param name="policyCategory"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.SubscribeToPolicyCategory(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression,System.String)">
            <summary>
            Creates a subscription for the provided target to the
            specified policy category. This will throw if the
            subscription already exists.
            </summary>
            <param name="target">An expression that SfcObjectQuery will
            evaluate to a specific instance.</param>
            <param name="policyCategory">The category name to subscribe to.</param>
            <returns>PolicyCategorySubscription</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.UnsubscribeFromPolicyCategory(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression,System.String)">
            <summary>
            Unsubscribe the provided target from the specified policy
            category. This will throw if the target does not subscribe to
            the category.
            </summary>
            <param name="target">An expression that SfcObjectQuery will
            evaluate to a specific instance.</param>
            <param name="policyCategory">The category name to unsubscribe from.</param>
            <returns>void</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.GeneratePolicyFromFacet(System.Object,System.String,System.String,System.String,System.Collections.Generic.List{Microsoft.SqlServer.Management.Dmf.ObjectToValidate})">
            <summary>
            This method instantiates a Policy object in memory which
            applies to the given target. The policy is
            associated with a Condition whose ConditionExpression
            objects reflect the properties of the target as seen
            through the given facet at the time this method is
            called. None of these objects have had Create called on
            them yet so they are only in memory. 
            </summary>
            <param name="target"></param>
            <param name="facetName"></param>
            <param name="policyName"></param>
            <param name="conditionName"></param>
            <param name="objectsToValidate"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.DoCreatePolicyFromFacet(System.Object,System.String,System.String,System.String,System.Xml.XmlWriter,System.Boolean)">
            <summary>
            This method does the actual creation of the policy from the facet.
            This method does validation of the created policy regardless of whether the policy is created
            to be persisted in the store, or just to be serialized.
            
            If the caller requested committing the policy, it means the policy is to be
            created in the current policy store backend, otherwise, a temp store is created the policy 
            and the policy object and temp store are discareded after the policy is serialized. 
            </summary>
            <param name="target"></param>
            <param name="facetName"></param>
            <param name="policyName"></param>
            <param name="conditionName"></param>
            <param name="writer"></param>
            <param name="commit">If true, the caller requires the policy to be committed to the policy store.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.CreatePolicyFromFacet(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression,System.String,System.String,System.String)">
            <summary>
            This method instantiates a Policy object which applies to
            the given target, and then add the policy to permanent
            storage. The policy is associated with a Condition whose
            ConditionExpression objects reflect the properties of the
            target as seen through the given facet at the time this
            method is called. You can access the newly created policy
            through this PolicyStore's Policies[policyName] collection.
            </summary>
            <param name="target"></param>
            <param name="facetName"></param>
            <param name="policyName"></param>
            <param name="conditionName"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.CreatePolicyFromFacet(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression,System.String,System.String,System.String,System.Xml.XmlWriter)">
            <summary>
            This method instantiates a temporary Policy object and
            then serializes it through the given XmlWriter. The policy
            is associated with a Condition whose ConditionExpression
            objects reflect the properties of the target as seen
            through the given facet at the time this method is called.
            This PolicyStore object is not changed by this call.
            </summary>
            <param name="target"></param>
            <param name="facetName"></param>
            <param name="policyName"></param>
            <param name="conditionName"></param>
            <param name="writer"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.CreatePolicyFromFacet(System.Object,System.String,System.String,System.String,System.Xml.XmlWriter)">
            <summary>
            This method instantiates a Policy object which applies to
            the given target, and then add the policy to permanent
            storage. The policy is associated with a Condition whose
            ConditionExpression objects reflect the properties of the
            target as seen through the given facet at the time this
            method is called. You can access the newly created policy
            through this PolicyStore's Policies[policyName] collection.
            </summary>
            <param name="target"></param>
            <param name="facetName"></param>
            <param name="policyName"></param>
            <param name="conditionName"></param>
            <param name="writer"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.ImportPolicy(System.Xml.XmlReader,Microsoft.SqlServer.Management.Dmf.ImportPolicyEnabledState,System.Boolean,System.Boolean)">
            <summary>
            Import a single policy from the given XML reader. The reader should be previously created by a supported Export method.
            The imported Policy is also created in the PolicyStore on the server.
            </summary>
            <param name="xmlReader">The XML reader to import from.</param>
            <param name="importEnabledState">The Policy.Enabled flag will be set based on this
            enum value. The exception is if the policy's execution mode is Check on Schedule but
            there is no schedule information associated with the policy, in which case the policy
            is automatically disabled and its execution mode is reset to None.</param>
            <param name="overwriteExistingPolicy">If true, then if a Policy of the same name already
            exists in the Store then it will be overwritten.</param>
            <param name="overwriteExistingCondition">If true, then if any Condition associated
            with the Policy, either directly or through a TargetSet, already exists in the Store
            then it will be overwritten. Otherwise the existing condition will remain unchanged
            and the imported policy will use the pre-existing condition rather than the version in
            the XmlReader.</param>
            <returns>The Policy object imported. If the XML reader fails to validate properly or is missing it will throw an exception.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.DeserializePolicy(System.Xml.XmlReader,System.Boolean,System.Boolean)">
            <summary>
            Deserialize a single policy from the given XML reader. The reader should be previously created by a supported Export method.
            The deserialized Policy exists in-memory only and must be created in the PolicyStore on the server to persist it.
            </summary>
            <param name="xmlReader">The XML reader to import from.</param>
            <param name="overwriteExistingPolicy">If true, then if a Policy of the same name already
            exists in the Store then it will be overwritten.</param>
            <param name="overwriteExistingCondition">If true, then if any Condition associated
            with the Policy, either directly or through a TargetSet already exists in the Store
            then it will be overwritten.</param>
            <returns>The Policy object imported. If the XML reader fails to validate properly it may throw an exception.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.IsPolicyApplicableForGivenTarget(Microsoft.SqlServer.Management.Dmf.Policy,Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression)">
            <summary>
            Function to take a policy and a target and then check whether a given policy is applicable on this target
            or not.
            </summary>
            <param name="policy"></param>
            <param name="target"></param>
            <returns>True if applicable, otherwise false</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key">
            Internal key class
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.GetHashCode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Dmf.PolicyStore.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.PolicyStore.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.PolicyStore.Key,Microsoft.SqlServer.Management.Dmf.PolicyStore.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Dmf.PolicyStore.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.PolicyStore.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.PolicyStore.Key,Microsoft.SqlServer.Management.Dmf.PolicyStore.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.Name">
            <summary>
            The name of the server connected to
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.Enabled">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.HistoryRetentionInDays">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.PolicyStore.LogOnSuccess">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.RepairPolicyAutomation">
            <summary>
            Restores all artifacts that have been created by policy-based 
            management to support policy automation. 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.EraseSystemHealthPhantomRecords">
            <summary>
            This function erases the phantom records from the system health table
            by iterating through all the violations and verifying that they 
            correspond to an existing object.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.StartSerializationUpgrade">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.PurgeHealthState">
            <summary>
            Purge all records from the policy health state table.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.PurgeHealthState(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression)">
            <summary>
            Purge records from the policy health state table that are 
            associated with the nodes in the tree, starting with the value 
            that is passed to targetTreeRoot.
            </summary>
            <param name="targetTreeRoot"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.MarkSystemObject(System.Object,System.Boolean)">
            <summary>
            Sets System flag on objects in the store
            </summary>
            <param name="obj"></param>
            <param name="marker"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Validate(System.String,System.Boolean,Microsoft.SqlServer.Management.Sdk.Sfc.ValidationState)">
            <summary>
            master validation method
            </summary>
            <param name="validationMode"></param>
            <param name="throwOnFirst"></param>
            <param name="validationState"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcValidate#Validate(System.String,System.Object[])">
            <summary>
            ISfcValidate implementation for Policy
            </summary>
            <param name="validationMethod"></param>
            <param name="arguments"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyStoreUpgradeSession">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStoreUpgradeSession.PostProcessUpgrade(System.Collections.Generic.Dictionary{System.String,System.Object},System.Int32)">
            <summary>
            
            </summary>
            <param name="sfcCache"></param>
            <param name="fileVersion"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStoreUpgradeSession.CTP6ToCTP6RefreshPostProcessUpgrade(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            For CTP6 to CTP6 Refresh so far we only have Facet's whos evaluation mode have removed CoC and Enforce.
            So, we find out if the evaluation mode for the policy is still valid for the target facet.  If it is not
            valid for the facet, then the evaluation mode is set to none.
            </summary>
            <param name="sfcCache"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStoreUpgradeSession.CTP5ToCTP6PostProcessUpgrade(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            
            </summary>
            <param name="sfcCache"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStoreUpgradeSession.IsUpgradeRequiredOnType(System.String,System.Int32)">
            <summary>
            
            </summary>
            <param name="instanceType"></param>
            <param name="fileVersion"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStoreUpgradeSession.UpgradeInstance(System.Collections.Generic.List{Microsoft.SqlServer.Management.Sdk.Sfc.SfcInstanceSerializedData},System.Int32,System.String,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            
            </summary>
            <param name="sfcInstanceData"></param>
            <param name="fileVersion"></param>
            <param name="smlUri"></param>
            <param name="sfcCache"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyStoreUpgradeSession.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyScheduleHelper.FixPolicySchedule(Microsoft.SqlServer.Management.Dmf.Policy)">
            <summary>
            Makes sure that a CoS policy has a valid schedule to run with. 
            </summary>
            <param name="policy"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationHelper">
            <summary>
            Helper class that contains the policy evaluation functions exposed 
            through SQLCLR
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationHelper.EvaluateAutomatedPolicy(System.String,System.Data.SqlTypes.SqlXml,System.Int64@)">
            <summary>
            Helper for policy automation. Executes a policy for one target, if 
            specified through the target XML blob. 
            </summary>
            <param name="policy">Policy name</param>
            <param name="eventData">XML blob identifying the target. Its schema is
            identical to the one returned by EVENTDATA() function.</param>
            <param name="historyId">ID of the entry in history table 
            generated by executing this policy</param>
            <returns>0 for success, 1 for failure. This method
            can throw an exception in some failure cases.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationHelper.#cctor">
            <summary>
            Static ctor for this class, initializes the error message
            buffer 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationHelper.LogExceptionMessage(System.String,Microsoft.Data.SqlClient.SqlConnection)">
            <summary>
            Logs exception via a RAISERROR WITH LOG statement. 
            The message will appear as a Sev 1 State 1 informational message
            and it will also be logged to the Windows event log.
            </summary>
            <param name="message"></param>
            <param name="connection"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationHelper.GetTargetQueryExpression(System.Data.SqlTypes.SqlXml,Microsoft.SqlServer.Management.Dmf.Policy,System.Text.StringBuilder,System.String@)">
            <summary>
            Returns a SfcQueryExpression representing the object defined in eventData
            </summary>
            <param name="eventData"></param>
            <param name="p"></param>
            <param name="targetPsPathBuilder"></param>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationHelper.GetExpressionGeneric(System.Collections.Generic.SortedDictionary{System.String,System.String},System.Text.StringBuilder,System.Data.SqlTypes.SqlXml,Microsoft.SqlServer.Management.Dmf.Policy)">
            <summary>
            Builds an Urn from the pairs of name and values in the dictionary.
            </summary>
            <param name="eventValues"></param>
            <param name="targetUrn"></param>
            <param name="eventData"></param>
            <param name="p"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationHelper.GetEventValue(System.Collections.Generic.SortedDictionary{System.String,System.String},System.Data.SqlTypes.SqlXml,System.String)">
            <summary>
            Retrieves the value for an element from the name-value
            pairs read from the eventData
            </summary>
            <param name="eventValues"></param>
            <param name="eventData"></param>
            <param name="elementName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.PolicyEvaluationHelper.GetTypeCorrectCase(System.String)">
            <summary>
            Returns a version of the string with the correct case that
            is understood by the Enumerator. Using the wrong case will
            result in an exception inside of the Enumerator.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.TargetSet">
            <summary>
            This is the non-generated part of the TargetSet class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.ScriptCreate(System.Boolean)">
            <summary>
            Script Create the object
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.ScriptDrop">
            <summary>
            Script Drop this object
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.ScriptAlter">
            <summary>
            Script Alter this object
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.#ctor">
            <summary>
            Default constructor used for deserialization.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.#ctor(Microsoft.SqlServer.Management.Dmf.ObjectSet,System.String)">
            <summary>
            
            </summary>
            <param name="parent"></param>
            <param name="targetTypeSkeleton"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.Levels">
            <summary>
            Collection of TargetSetLevel objects.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.RootLevel">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.IsAllDatabasesFilter">
            <summary>
            Filter is an All Database filter
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Discover(Microsoft.SqlServer.Management.Sdk.Sfc.ISfcDependencyDiscoveryObjectSink)">
            <summary>
            
            </summary>
            <param name="sink"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.TargetsDatabaseObjects">
            <summary>
            Filter is on objects at or under Server/Database
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.TargetsDatabases">
            <summary>
            Filter is on objects at Server/Database
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.TargetSet.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.targetTypeSkeletonValue">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.#ctor">
            <summary>
            Default constructor for generic key creation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.#ctor(Microsoft.SqlServer.Management.Dmf.TargetSet.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="targetTypeSkeleton"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.targetTypeSkeleton">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Dmf.TargetSet.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.TargetSet.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.TargetSet.Key,Microsoft.SqlServer.Management.Dmf.TargetSet.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Dmf.TargetSet.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.TargetSet.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.TargetSet.Key,Microsoft.SqlServer.Management.Dmf.TargetSet.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.Parent">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.IdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.ObjectSetID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.TargetTypeSkeleton">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.Enabled">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.TargetType">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSet.Type">
            <summary>
            Type corresponding with TargetTypeSkeleton
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.GetFilter">
            <summary>
            Constructs URN from Levels' conditions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.GetFilterWithNodeReplacement(System.Collections.Generic.Dictionary{System.String,Microsoft.SqlServer.Management.Dmf.ExpressionNode})">
            <summary>
            This method reuses GetFilter logic, but allows to replace filters for a particular levels
            it's intended to be used to produce a filter, 
            which is an intersection of TS and Category filters (if applicable), and honors SystemObject rules
            the level filters have to be calculated by the caller
            </summary>
            <param name="adjustments"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.GetLevelsSorted">
            <summary>
            Returns sorted collection of TargetSetLevel objects
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.GetLevel(System.String)">
            <summary>
            Returns reference object for particular skeleton if it exists, null otherwise
            </summary>
            <param name="skeleton"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.SetLevelCondition(Microsoft.SqlServer.Management.Dmf.TargetSetLevel,System.String)">
            <summary>
            Sets level Condition
            </summary>
            <param name="level"></param>
            <param name="condition"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSet.IsEventingFilter">
            <summary>
            Defines if this filter can be used for Enforce and CoC modes
            according to current rules (only allows Name condition on DB level)
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.TargetSetCollection">
            <summary>
            This is the collection for TargetSet.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetCollection.#ctor(Microsoft.SqlServer.Management.Dmf.ObjectSet)">
            <summary>
            
            </summary>
            <param name="parent"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetCollection.#ctor(Microsoft.SqlServer.Management.Dmf.ObjectSet,System.Collections.Generic.IComparer{System.String})">
            <summary>
            
            </summary>
            <param name="parent"></param>
            <param name="customComparer"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSetCollection.Item(System.String)">
            <summary>
            
            </summary>
            <param name="filterTypeSkeleton"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetCollection.Contains(System.String)">
            <summary>
            
            </summary>
            <param name="filterTypeSkeleton"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetCollection.GetElementFactoryImpl">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.TargetSetLevel">
            <summary>
            This is the non-generated part of the TargetSetLevel class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.#ctor">
            <summary>
            Default constructor used for deserialization.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.#ctor(Microsoft.SqlServer.Management.Dmf.TargetSet,System.String)">
            <summary>
            
            </summary>
            <param name="parent"></param>
            <param name="TargetTypeSkeleton"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.TargetType">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Equals(System.Object)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.GetHashCode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.CompareTo(Microsoft.SqlServer.Management.Dmf.TargetSetLevel)">
            <summary>
            Comparison based on the length of TargetTypeSkeleton
            </summary>
            <param name="level"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.GetTypeFilterProperties(System.String)">
            <summary>
            Returns filtering properties supported by the SMO
            type represented by the given expression, or null if it's
            not a valid SMO type.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.targetTypeSkeletonValue">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.#ctor">
            <summary>
            Default constructor for generic key creation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.#ctor(Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="targetTypeSkeleton"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.targetTypeSkeleton">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.op_Equality(Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key,Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.op_Inequality(Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key,Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Parent">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.IdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.ID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.TargetSetID">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.TargetTypeSkeleton">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.LevelName">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Condition">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevel.Discover(Microsoft.SqlServer.Management.Sdk.Sfc.ISfcDependencyDiscoveryObjectSink)">
            <summary>
            
            </summary>
            <param name="sink"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.TargetSetLevelCollection">
            <summary>
            This is the collection for TargetSet.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevelCollection.#ctor(Microsoft.SqlServer.Management.Dmf.TargetSet)">
            <summary>
            
            </summary>
            <param name="parent"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevelCollection.#ctor(Microsoft.SqlServer.Management.Dmf.TargetSet,System.Collections.Generic.IComparer{System.String})">
            <summary>
            
            </summary>
            <param name="parent"></param>
            <param name="customComparer"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Dmf.TargetSetLevelCollection.Item(System.String)">
            <summary>
            
            </summary>
            <param name="filterTypeSkeleton"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevelCollection.Contains(System.String)">
            <summary>
            
            </summary>
            <param name="filterTypeSkeleton"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.TargetSetLevelCollection.GetElementFactoryImpl">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Dmf.Utils">
            <summary>
            Class that provides various utilities. Public because UI modules also needs some methods here
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.GetDescriptionForEvaluationMode(Microsoft.SqlServer.Management.Dmf.AutomatedPolicyEvaluationMode)">
            <summary>
            Provides descriptive localized names for evaluation mode enums
            </summary>
            <param name="mode"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.GetDescriptionForAdHocEvaluationMode(Microsoft.SqlServer.Management.Dmf.AdHocPolicyEvaluationMode)">
            <summary>
            Provides descriptive localized names for ad hoc policy evaluation mode enums
            </summary>
            <param name="mode"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.GetEvaluationModeByDescription(System.String)">
            <summary>
            Given descriptive names, provides the evaluation mode enum
            </summary>
            <param name="execModeDescription"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.IsValidHelpLink(System.String)">
            <summary>
            Validates the link string for policy help link.
            </summary>
            <param name="link"> The link string</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.ReplaceSfcProperties(Microsoft.SqlServer.Management.Sdk.Sfc.SfcInstance,Microsoft.SqlServer.Management.Sdk.Sfc.SfcInstance)">
            <summary>
            Replaces the read/write properties in lhs with the read/write properties in rhs. The
            list of old properties are returned.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.ReplaceSfcProperties(Microsoft.SqlServer.Management.Sdk.Sfc.SfcInstance,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Sets the properties in lhs with the properties in the given Dictionary.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.ShouldProcessException(System.Exception)">
            <summary>
            This function decides whether the exception needs to be processed. 
            If the exception is considered to be recoverable it is processed,
            otherwise if the exception is unrecoverable we should be rethrowing it.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.GetPhysicalFacetProperties(System.Type)">
            <summary>
            This function uses reflection to return the SfcProperties and non DmfIgnore.
            Since we support non SMO domains we fork the code to check the domain and 
            access the Facet's properties accordignly
            </summary>
            <param name="managementFacet"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.IsSmoPath(System.String)">
            <summary>
            checks if the string path is reffering to an SMO object
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.GetDomainFromUrnSkeleton(System.String)">
            <summary>
            this function retieves the domain for the specific skeleton by enumerating the Registered domains.
            It is used to GetTypeFromUrnSkeleton and GetTargetDomain functions
            </summary>
            <param name="skeleton"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.GetTypeFromUrnSkeleton(System.String)">
            <summary>
            This function returns the object type from the skeleton string
            NOTE: This is a temporary solution, we have to move that code to SfcMetadata
            </summary>
            <param name="skeleton"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Dmf.Utils.GetQueryFromDomainInfo(Microsoft.SqlServer.Management.Sdk.Sfc.SfcDomainInfo,Microsoft.SqlServer.Management.Common.ISfcConnection)">
            <summary>
            The following function Instantiates an object query through a domainInfo. It instantiates the domain root
            from the domain Info and then uses it to create a new query. I tried to move this to 
            SFC but I had problems instantiating an SMO server due to Partially trusted caller issue.
            </summary>
            <param name="domainInfo"></param>
            <param name="connection"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Facets.FacetAttributes">
            <summary>
            Internal structure to represent Facet attributes in a single place
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext">
            <summary>
            Facet evaluation context - facet interface + implementing object
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.Interface">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.Target">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.FacetType">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.PhysicalTarget">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.#ctor(System.Type,System.Object,Microsoft.SqlServer.Management.Facets.FacetType)">
            <summary>
            
            </summary>
            <param name="iface"></param>
            <param name="target"></param>
            <param name="facetType"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.#ctor(System.Type,System.Object,Microsoft.SqlServer.Management.Facets.FacetType,System.Object)">
            <summary>
            
            </summary>
            <param name="iface"></param>
            <param name="target"></param>
            <param name="facetType"></param>
            <param name="physicalTarget"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.GetFacetEvaluationContext(System.String,System.Object)">
            <summary>
            
            </summary>
            <param name="facetName"></param>
            <param name="target"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.GetFacetEvaluationContext(System.Type,System.Object)">
            <summary>
            
            </summary>
            <param name="facetType"></param>
            <param name="target"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.Alter">
            <summary>
            Alter
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.Refresh">
            <summary>
            Refresh
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.GetPropertyValue(System.String)">
            <summary>
            Gets named property
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetEvaluationContext.SetPropertyValue(System.String,System.Object)">
            <summary>
            Sets named property
            </summary>
            <param name="name">property name</param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Facets.FacetRepository">
            <summary>
            Helper class that exposes the class factory method
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.#cctor">
            <summary>
            static constructor to initialize default lookup tables
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.BuildDmfLookupTables(System.Reflection.Assembly)">
            <summary>
            Builds lookup tables, used by DMF to associate Management Facets with Objects
            and Adapters
            </summary>
            <param name="assembly">source assembly</param>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.AdapterAlreadyExistsException"></exception>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.AdapterWrongNumberOfArgumentsException"></exception>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.AssemblyAlreadyRegisteredException"></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.BuildInterfaceLookup(System.Reflection.Assembly)">
            <summary>
            Scans assembly for management facets
            </summary>
            <param name="assembly">source assembly</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.BuildAdapterLookup(System.Reflection.Assembly)">
            <summary> 
            Scans Assembly for Adapters.
            Adapter inherits a management facet.
            Adapter is bound to {Interface; ObjectType} key pairs. 
            The key pairs have to be unique across the entire table.
            An object can only have one adapter exposing a management facet.
            Adapter constructors must have only one argument.
            </summary>
            <param name="assembly">source assembly</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.GetFacetProperties(System.Type)">
            <summary>
             Properties for given ManagementFacet
            </summary>
            <param name="managementFacet">ManagementFacet</param>
            <returns>facet properties or null if it's not a facet</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.IsPropertyConfigurable(System.Type,System.String)">
            <summary>
            
            </summary>
            <param name="managementFacet"></param>
            <param name="propertyName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.GetFacetSupportedTypes(System.Type)">
            <summary>
            Object types supported by given ManagementFacet
            </summary>
            <param name="managementFacet"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.GetRootFacets(System.Type)">
            <summary>
            Root facets currently registered
            </summary>
            <param name="rootType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.IsRootFacet(System.Type,System.Type)">
            <summary>
            Signals whether given facet is a root facet for given Root
            </summary>
            <param name="rootType"></param>
            <param name="facet"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.GetFacetSupportedRootType(System.Type)">
            <summary>
            Returns root type supported by given facet
            </summary>
            <param name="facet"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.GetFacetImplementingType(System.Type,System.Type)">
            <summary>
            Returns type implementing the facet
            </summary>
            <param name="target">ObjectType of object</param>
            <param name="facet">Interface</param>
            <returns>Type implementing the facet</returns>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.NullFacetException">facet is not registered</exception>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.MissingTypeFacetAssociationException">target is not associated with facet</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.GetAdapterObject(System.Object,System.Type)">
            <summary>
            Returns context (facet interface and implementing object)
            Instantiates an Adapter for adapted facets
            </summary>
            <param name="target"></param>
            <param name="facet"></param>
            <returns>Adapter or NULL if there is no adapter for given arguments</returns>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.NullFacetException">facet is not registered</exception>
            <exception cref="T:Microsoft.SqlServer.Management.Dmf.MissingTypeFacetAssociationException">target is not associated with facet</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.GetFacetsForType(System.Type)">
            <summary>
            Returns a list of management facets exposed by given Object Type
            </summary>
            <param name="target"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Facets.FacetRepository.RegisteredFacets">
            <summary>
            List of all registered facets
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.GetFacetType(System.String)">
            <summary>
            Gets type of named facet
            </summary>
            <param name="facetShortName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.GetFacetEvaluationMode(System.Type)">
            <summary>
            
            </summary>
            <param name="facet"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.IsRegisteredFacet(System.Type)">
            <summary>
            Checks if provided facet is Registered by DMF
            </summary>
            <param name="facet"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.IsManagementFacet(System.Type)">
            <summary>
            Checks if provided type is a facet (an interface, inheriting IDmfFacet)
            </summary>
            <param name="managementFacet"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.IsPhysicalFacet(System.Type)">
            <summary>
            Internal for test purposes. Do not use!
            Checks if provided type is a physical facet (a type marked as PhysicalFacet implementing ISfcPropertySet)
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.IsReadOnlyFacet(System.Type)">
            <summary>
            Checks if provided type is a read only physical facet (a type marked as PhysicalFacet(PhysicalFacetOptions.ReadOnly) implementing ISfcPropertySet)
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Facets.FacetRepository.IsAdapter(System.Type)">
            <summary>
            Checks if provided type is an adapter (a class, implementing IDmfAdapter)
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
    </members>
</doc>
