using System;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem.Models
{
    /// <summary>
    /// Base invoice class for Table-Per-Hierarchy (TPH) inheritance
    /// Maps to tblStockMovHeader table
    /// </summary>
    public abstract class BaseInvoice
    {
        [Required]
        [Key]
        public int TrxNo { get; set; } // Invoice number

        [Required]
        [StringLength(50)]
        public string TrxType { get; set; } = "";

        public DateTime? TrxDate { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string? Store { get; set; }

        [StringLength(50)]
        public string? Cashier { get; set; }

        public long? PartnerNo { get; set; } // Partner account code

        [StringLength(100)]
        public string? PartnerName { get; set; }

        [StringLength(20)]
        public string? PartnerPhoneNo { get; set; }

        [StringLength(50)]
        public string? PaymentMethod { get; set; }

        [StringLength(100)]
        public string? PartnerReference { get; set; }

        [StringLength(500)]
        public string? TrxNote { get; set; }

        public decimal? TrxVAT { get; set; } = 0;

        public decimal? TrxTotal { get; set; } = 0;

        public decimal? TrxDiscount { get; set; } = 0;

        public decimal? TrxDiscountValue { get; set; } = 0;

        public decimal? TrxNetAmount { get; set; } = 0;

        [StringLength(10)]
        public string? ReadyForUse { get; set; }

        public bool? VOIDSTTS { get; set; } = false;

        [StringLength(50)]
        public string? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        [StringLength(50)]
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
    }
} 