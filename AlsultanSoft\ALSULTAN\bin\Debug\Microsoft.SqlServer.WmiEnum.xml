<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.SqlServer.WmiEnum</name>
    </assembly>
    <members>
        <member name="M:Microsoft.SqlServer.Management.Smo.Wmi.WmiBase.Microsoft#SqlServer#Management#Sdk#Sfc#ISqlFilterDecoderCallback#AddPropertyForFilter(System.String)">
            <summary>	
            FilterDecoder reports that the property name is used in filter
            and requests its physical name</summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Smo.Wmi.WmiBase.Microsoft#SqlServer#Management#Sdk#Sfc#ISqlFilterDecoderCallback#AddConstantForFilter(System.String)">
            <summary>	
            FilterDecoder reports that a constant is used in filter
            gives client a chance to modify it</summary>
        </member>
    </members>
</doc>
