﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
MobileSalespoint
</name>
</assembly>
<members>
<member name="T:Mobile.StoreSearch.tblStockTrxDataTable">
	<summary>
Represents the strongly named DataTable class.
</summary>
</member><member name="T:Mobile.StoreSearch.tblStockTrxRow">
	<summary>
Represents strongly named DataRow class.
</summary>
</member><member name="T:Mobile.StoreSearch.tblStockTrxRowChangeEvent">
	<summary>
Row event argument class
</summary>
</member><member name="T:Mobile.StoreSearch">
	<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member><member name="T:Mobile.StoreSearchTableAdapters.tblStockTrxTableAdapter">
	<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member><member name="M:Mobile.StoreSearchTableAdapters.TableAdapterManager.UpdateUpdatedRows(Mobile.StoreSearch,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
	<summary>
Update rows in top-down order.
</summary>
</member><member name="M:Mobile.StoreSearchTableAdapters.TableAdapterManager.UpdateInsertedRows(Mobile.StoreSearch,System.Collections.Generic.List{System.Data.DataRow})">
	<summary>
Insert rows in top-down order.
</summary>
</member><member name="M:Mobile.StoreSearchTableAdapters.TableAdapterManager.UpdateDeletedRows(Mobile.StoreSearch,System.Collections.Generic.List{System.Data.DataRow})">
	<summary>
Delete rows in bottom-up order.
</summary>
</member><member name="M:Mobile.StoreSearchTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
	<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member><member name="M:Mobile.StoreSearchTableAdapters.TableAdapterManager.UpdateAll(Mobile.StoreSearch)">
	<summary>
Update all changes to the dataset.
</summary>
</member><member name="T:Mobile.StoreSearchTableAdapters.TableAdapterManager.UpdateOrderOption">
	<summary>
Update Order Option
</summary>
</member><member name="T:Mobile.StoreSearchTableAdapters.TableAdapterManager.SelfReferenceComparer">
	<summary>
Used to sort self-referenced table's rows
</summary>
</member><member name="T:Mobile.StoreSearchTableAdapters.TableAdapterManager">
	<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member><member name="P:Mobile.My.Resources.Resources.ResourceManager">
	<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member><member name="P:Mobile.My.Resources.Resources.Culture">
	<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member><member name="T:Mobile.My.Resources.Resources">
	<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
</members>
</doc>