using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Models;
using AccountingSystem.Web.Models;
using System.Security.Claims;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class PurchaseController : Controller
    {
        private readonly IPurchaseService _purchaseService;
        private readonly ILogger<PurchaseController> _logger;

        public PurchaseController(IPurchaseService purchaseService, ILogger<PurchaseController> logger)
        {
            _purchaseService = purchaseService;
            _logger = logger;
        }

        public IActionResult Index()
        {
            // Redirect to the migrated Create page to avoid the legacy/development placeholder
            return RedirectToAction("Create");
        }

        public IActionResult Invoice()
        {
            // Redirect to the Create page since that's the main functional page
            return RedirectToAction("Create");
        }

        // Legacy route shims to ensure old navigation opens the new page
        [HttpGet("/Purchasing/NewInvoice")]
        [HttpGet("/Purchase/New")]
        [HttpGet("/Purchase/NewInvoice")]
        public IActionResult NewInvoiceRedirect()
        {
            return RedirectToAction("Create");
        }

        public IActionResult Return()
        {
            ViewBag.PageTitle = "مرتجع المشتريات";
            ViewBag.Message = "صفحة مرتجع المشتريات - قيد التطوير";
            return View();
        }

        public async Task<IActionResult> Create()
        {
            try
            {
                var username = User.FindFirstValue(ClaimTypes.Name) ?? "admin";

                // Track which step we are in to improve diagnostics if anything fails
                string currentStep = "GetCurrentInvoiceAsync";
                var currentInvoice = await _purchaseService.GetCurrentInvoiceAsync();

                if (currentInvoice == null)
                {
                    currentStep = "CreateNewInvoiceAsync";
                    currentInvoice = await _purchaseService.CreateNewInvoiceAsync(username);
                }

                var viewModel = new PurchaseInvoiceViewModel
                {
                    TrxNo = currentInvoice.TrxNo,
                    TrxDate = currentInvoice.TrxDate,
                    Store = currentInvoice.Store,
                    Cashier = currentInvoice.Cashier,
                    PartnerNo = currentInvoice.PartnerNo,
                    PartnerName = currentInvoice.PartnerName,
                    PaymentMethod = currentInvoice.PaymentMethod,
                    PartnerReference = currentInvoice.PartnerReference,
                    ReferenceInvoice = currentInvoice.ReferenceInvoice,
                    TrxNote = currentInvoice.TrxNote,
                    TrxVAT = currentInvoice.TrxVAT,
                    TrxTotal = currentInvoice.TrxTotal,
                    TrxDiscount = currentInvoice.TrxDiscount,
                    TrxDiscountValue = currentInvoice.TrxDiscountValue,
                    TrxNetAmount = currentInvoice.TrxNetAmount,
                    PaymentStatus = currentInvoice.PaymentStatus,
                    Items = new List<PurchaseInvoiceItemViewModel>(),
                    Vendors = await _purchaseService.GetVendorsAsync(),
                    Cashiers = await _purchaseService.GetCashiersAsync(),
                    Stores = await _purchaseService.GetStoresAsync(),
                    Settings = await _purchaseService.GetPurchaseSettingsAsync(),
                    UserAuthorization = await _purchaseService.GetUserAuthorizationAsync(username)
                };

                // Load invoice items
                currentStep = "GetInvoiceItemsAsync";
                var items = await _purchaseService.GetInvoiceItemsAsync(currentInvoice.TrxNo);
                foreach (var item in items)
                {
                    currentStep = "GetItemByNumberAsync";
                    var itemDetails = await _purchaseService.GetItemByNumberAsync(item.ItemNo);
                    viewModel.Items.Add(new PurchaseInvoiceItemViewModel
                    {
                        LineSN = item.LineSN,
                        ItemNo = item.ItemNo,
                        ItemDescription = itemDetails?.ItemDescription,
                        TrxQTY = item.TrxQTY,
                        UofM = item.UofM,
                        UnitPrice = item.UnitPrice,
                        LineTotal = item.LineAmount,
                        VATAmount = item.VATAmount,
                        UofMConversion = item.UofMConversion
                    });
                }

                ViewBag.PageTitle = "إنشاء فاتورة مشتريات جديدة";
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading purchase invoice create page");
                TempData["Error"] = "حدث خطأ أثناء تحميل صفحة إنشاء الفاتورة";
                TempData["ErrorDetail"] = ex.Message;
                if (ex.InnerException != null)
                {
                    TempData["ErrorDetail"] = $"{ex.Message} | {ex.InnerException.Message}";
                }
                // Avoid redirecting to Index to prevent redirect loops
                var errorModel = new ErrorViewModel { RequestId = HttpContext.TraceIdentifier };
                return View("Error", errorModel);
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddItem(int invoiceNo, long itemNo, decimal quantity, decimal unitPrice, string uofM, decimal uofMConversion)
        {
            try
            {
                var username = User.FindFirstValue(ClaimTypes.Name) ?? "admin";
                var store = Request.Form["store"].ToString();
                
                var item = await _purchaseService.AddItemToInvoiceAsync(invoiceNo, itemNo, quantity, unitPrice, uofM, uofMConversion, username, store);
                
                return Json(new { success = true, message = "تم إضافة الصنف بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding item to invoice");
                return Json(new { success = false, message = "حدث خطأ أثناء إضافة الصنف" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> UpdateItem(int invoiceNo, int lineSN, decimal quantity, decimal unitPrice, string uofM, decimal uofMConversion)
        {
            try
            {
                var username = User.FindFirstValue(ClaimTypes.Name) ?? "admin";
                var success = await _purchaseService.UpdateInvoiceItemAsync(invoiceNo, lineSN, quantity, unitPrice, uofM, uofMConversion, username);
                
                if (success)
                    return Json(new { success = true, message = "تم تحديث الصنف بنجاح" });
                else
                    return Json(new { success = false, message = "لم يتم العثور على الصنف" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating item");
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث الصنف" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteItem(int invoiceNo, int lineSN)
        {
            try
            {
                var username = User.FindFirstValue(ClaimTypes.Name) ?? "admin";
                var success = await _purchaseService.DeleteInvoiceItemAsync(invoiceNo, lineSN, username);
                
                if (success)
                    return Json(new { success = true, message = "تم حذف الصنف بنجاح" });
                else
                    return Json(new { success = false, message = "لم يتم العثور على الصنف" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting item");
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الصنف" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveInvoice([FromBody] PurchaseInvoiceCreateViewModel model)
        {
            try
            {
                var username = User.FindFirstValue(ClaimTypes.Name) ?? "admin";
                
                if (!await _purchaseService.ValidateInvoiceAsync(model))
                {
                    return Json(new { success = false, message = "بيانات الفاتورة غير صحيحة" });
                }

                // Get current invoice or create new one
                var currentInvoice = await _purchaseService.GetCurrentInvoiceAsync();
                if (currentInvoice == null)
                {
                    currentInvoice = await _purchaseService.CreateNewInvoiceAsync(username);
                }

                var invoice = await _purchaseService.SaveInvoiceAsync(currentInvoice.TrxNo, model, username);
                
                return Json(new { success = true, message = "تم حفظ الفاتورة بنجاح", invoiceNo = invoice.TrxNo });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving invoice");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ الفاتورة" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> SearchItems(string term)
        {
            try
            {
                var items = await _purchaseService.SearchItemsAsync(term);
                return Json(items);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching items");
                return Json(new List<Item>());
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetItemDetails(long itemNo)
        {
            try
            {
                var item = await _purchaseService.GetItemByNumberAsync(itemNo);
                var units = await _purchaseService.GetItemUnitsAsync(itemNo);
                var purchasePrice = await _purchaseService.GetItemPurchasePriceAsync(itemNo);
                
                return Json(new { 
                    item = item, 
                    units = units, 
                    purchasePrice = purchasePrice 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item details");
                return Json(new { item = (Item?)null, units = new List<ItemUnit>(), purchasePrice = 0 });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetInvoiceItems(int invoiceNo)
        {
            try
            {
                var items = await _purchaseService.GetInvoiceItemsAsync(invoiceNo);
                var itemViewModels = new List<PurchaseInvoiceItemViewModel>();
                
                foreach (var item in items)
                {
                    var itemDetails = await _purchaseService.GetItemByNumberAsync(item.ItemNo);
                    itemViewModels.Add(new PurchaseInvoiceItemViewModel
                    {
                        LineSN = item.LineSN,
                        ItemNo = item.ItemNo,
                        ItemDescription = itemDetails?.ItemDescription,
                        TrxQTY = item.TrxQTY,
                        UofM = item.UofM,
                        UnitPrice = item.UnitPrice,
                        LineTotal = item.LineAmount,
                        VATAmount = item.VATAmount,
                        UofMConversion = item.UofMConversion
                    });
                }
                
                return Json(itemViewModels);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice items");
                return Json(new List<PurchaseInvoiceItemViewModel>());
            }
        }

        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var invoice = await _purchaseService.GetInvoiceByNumberAsync(id);
                if (invoice == null)
                {
                    TempData["Error"] = "لم يتم العثور على الفاتورة";
                    return RedirectToAction("Index");
                }

                var username = User.FindFirstValue(ClaimTypes.Name) ?? "admin";
                var viewModel = new PurchaseInvoiceViewModel
                {
                    TrxNo = invoice.TrxNo,
                    TrxDate = invoice.TrxDate,
                    Store = invoice.Store,
                    Cashier = invoice.Cashier,
                    PartnerNo = invoice.PartnerNo,
                    PartnerName = invoice.PartnerName,
                    PaymentMethod = invoice.PaymentMethod,
                    PartnerReference = invoice.PartnerReference,
                    ReferenceInvoice = invoice.ReferenceInvoice,
                    TrxNote = invoice.TrxNote,
                    TrxVAT = invoice.TrxVAT,
                    TrxTotal = invoice.TrxTotal,
                    TrxDiscount = invoice.TrxDiscount,
                    TrxDiscountValue = invoice.TrxDiscountValue,
                    TrxNetAmount = invoice.TrxNetAmount,
                    PaymentStatus = invoice.PaymentStatus,
                    Items = new List<PurchaseInvoiceItemViewModel>(),
                    Vendors = await _purchaseService.GetVendorsAsync(),
                    Cashiers = await _purchaseService.GetCashiersAsync(),
                    Stores = await _purchaseService.GetStoresAsync(),
                    Settings = await _purchaseService.GetPurchaseSettingsAsync(),
                    UserAuthorization = await _purchaseService.GetUserAuthorizationAsync(username)
                };

                // Load invoice items
                var items = await _purchaseService.GetInvoiceItemsAsync(invoice.TrxNo);
                foreach (var item in items)
                {
                    var itemDetails = await _purchaseService.GetItemByNumberAsync(item.ItemNo);
                    viewModel.Items.Add(new PurchaseInvoiceItemViewModel
                    {
                        LineSN = item.LineSN,
                        ItemNo = item.ItemNo,
                        ItemDescription = itemDetails?.ItemDescription,
                        TrxQTY = item.TrxQTY,
                        UofM = item.UofM,
                        UnitPrice = item.UnitPrice,
                        LineTotal = item.LineAmount,
                        VATAmount = item.VATAmount,
                        UofMConversion = item.UofMConversion
                    });
                }

                ViewBag.PageTitle = "تعديل فاتورة المشتريات";
                ViewBag.InvoiceId = id;
                return View("Create", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading purchase invoice edit page");
                TempData["Error"] = "حدث خطأ أثناء تحميل صفحة تعديل الفاتورة";
                return RedirectToAction("Index");
            }
        }

        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var invoice = await _purchaseService.GetInvoiceByNumberAsync(id);
                if (invoice == null)
                {
                    TempData["Error"] = "لم يتم العثور على الفاتورة";
                    return RedirectToAction("Index");
                }

                var viewModel = new PurchaseInvoiceViewModel
                {
                    TrxNo = invoice.TrxNo,
                    TrxDate = invoice.TrxDate,
                    Store = invoice.Store,
                    Cashier = invoice.Cashier,
                    PartnerNo = invoice.PartnerNo,
                    PartnerName = invoice.PartnerName,
                    PaymentMethod = invoice.PaymentMethod,
                    PartnerReference = invoice.PartnerReference,
                    ReferenceInvoice = invoice.ReferenceInvoice,
                    TrxNote = invoice.TrxNote,
                    TrxVAT = invoice.TrxVAT,
                    TrxTotal = invoice.TrxTotal,
                    TrxDiscount = invoice.TrxDiscount,
                    TrxDiscountValue = invoice.TrxDiscountValue,
                    TrxNetAmount = invoice.TrxNetAmount,
                    PaymentStatus = invoice.PaymentStatus,
                    Items = new List<PurchaseInvoiceItemViewModel>()
                };

                // Load invoice items
                var items = await _purchaseService.GetInvoiceItemsAsync(invoice.TrxNo);
                foreach (var item in items)
                {
                    var itemDetails = await _purchaseService.GetItemByNumberAsync(item.ItemNo);
                    viewModel.Items.Add(new PurchaseInvoiceItemViewModel
                    {
                        LineSN = item.LineSN,
                        ItemNo = item.ItemNo,
                        ItemDescription = itemDetails?.ItemDescription,
                        TrxQTY = item.TrxQTY,
                        UofM = item.UofM,
                        UnitPrice = item.UnitPrice,
                        LineTotal = item.LineAmount,
                        VATAmount = item.VATAmount,
                        UofMConversion = item.UofMConversion
                    });
                }

                ViewBag.PageTitle = "تفاصيل فاتورة المشتريات";
                ViewBag.InvoiceId = id;
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading purchase invoice details page");
                TempData["Error"] = "حدث خطأ أثناء تحميل صفحة تفاصيل الفاتورة";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var username = User.FindFirstValue(ClaimTypes.Name) ?? "admin";
                var success = await _purchaseService.DeleteInvoiceAsync(id, username);
                
                if (success)
                {
                    TempData["Success"] = "تم حذف الفاتورة بنجاح";
                }
                else
                {
                    TempData["Error"] = "لم يتم العثور على الفاتورة";
                }
                
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice");
                TempData["Error"] = "حدث خطأ أثناء حذف الفاتورة";
                return RedirectToAction("Index");
            }
        }
    }
}
