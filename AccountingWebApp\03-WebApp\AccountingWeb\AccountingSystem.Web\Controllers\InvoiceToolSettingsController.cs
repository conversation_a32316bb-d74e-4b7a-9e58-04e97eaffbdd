using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Models;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using AccountingSystem.Web.ViewModels;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    [Route("InvoiceToolSettings")]
    public class InvoiceToolSettingsController : Controller
    {
        private readonly IInvoiceToolSettingService _settingsService;
        private readonly ILogger<InvoiceToolSettingsController> _logger;

        public InvoiceToolSettingsController(IInvoiceToolSettingService settingsService, ILogger<InvoiceToolSettingsController> logger)
        {
            _settingsService = settingsService;
            _logger = logger;
        }

        private InvoiceToolSettingsViewModel MapToViewModel(InvoiceToolSetting model)
        {
            return new InvoiceToolSettingsViewModel
            {
                InvoiceType = model.InvoiceType,
                DefaultPrinter = model.InvoicePrinter,
                PrintOption = model.PrintOption,
                PaymentType = model.DefPaymentType,
                PriceIncludeVATDef = model.DefPriceIncludeVAT?.ToLower() == "true",
                NonVATInvoiceDef = model.DefNonVATInvoice?.ToLower() == "true",
                ReferenceMandatory = model.ReferenceMandatory?.ToLower() == "true",
                MandatoryCustomerVATReg = model.MandatoryVendorVATReg?.ToLower() == "true"
            };
        }

        private InvoiceToolSetting MapToDbModel(InvoiceToolSettingsViewModel viewModel, InvoiceToolSetting existingModel)
        {
            existingModel.InvoiceType = viewModel.InvoiceType;
            existingModel.InvoicePrinter = viewModel.DefaultPrinter;
            existingModel.PrintOption = viewModel.PrintOption;
            existingModel.DefPaymentType = viewModel.PaymentType;
            existingModel.DefPriceIncludeVAT = viewModel.PriceIncludeVATDef.ToString();
            existingModel.DefNonVATInvoice = viewModel.NonVATInvoiceDef.ToString();
            existingModel.ReferenceMandatory = viewModel.ReferenceMandatory.ToString();
            existingModel.MandatoryVendorVATReg = viewModel.MandatoryCustomerVATReg.ToString();
            return existingModel;
        }

        [HttpGet("")]
        public async Task<IActionResult> Index()
        {
            ViewBag.InvoiceTypes = await _settingsService.GetInvoiceTypesAsync();
            ViewBag.Printers = await _settingsService.GetPrinterNamesAsync();
            
            var defaultSettings = await _settingsService.GetSettingsByInvoiceTypeAsync("مبيعات");
            var viewModel = MapToViewModel(defaultSettings);
            
            return View(viewModel);
        }

        [HttpGet("GetSettings")]
        public async Task<IActionResult> GetSettings(string invoiceType)
        {
            if (string.IsNullOrEmpty(invoiceType))
            {
                return BadRequest();
            }
            var settings = await _settingsService.GetSettingsByInvoiceTypeAsync(invoiceType);
            var viewModel = MapToViewModel(settings);
            return PartialView("_SettingsForm", viewModel);
        }

        [HttpPost("Update")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Update(InvoiceToolSettingsViewModel viewModel)
        {
            if (!ModelState.IsValid)
            {
                TempData["ErrorMessage"] = "البيانات المدخلة غير صالحة.";
                return RedirectToAction(nameof(Index));
            }

            var currentUser = User.Identity?.Name ?? "System";
            var existingSettings = await _settingsService.GetSettingsByInvoiceTypeAsync(viewModel.InvoiceType) ?? new InvoiceToolSetting { InvoiceType = viewModel.InvoiceType };
            
            var settingsToUpdate = MapToDbModel(viewModel, existingSettings);

            var success = await _settingsService.UpdateSettingsAsync(settingsToUpdate, currentUser);

            if (success)
            {
                TempData["SuccessMessage"] = $"تم حفظ الإعدادات لنوع الفاتورة '{viewModel.InvoiceType}' بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في حفظ الإعدادات.";
            }

            return RedirectToAction(nameof(Index));
        }
    }
} 