@model AccountingSystem.Web.Models.POSViewModel
@{
    ViewData["Title"] = "نقاط البيع";
    Layout = "_Layout";
}

<div class="container-fluid">
    <!-- Session Validation Alert -->
    @if (!Model.HasActiveSession)
    {
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تحذير:</strong> لا توجد جلسة نشطة لنقاط البيع. 
            <a href="/POSSessions/Create" class="alert-link">اضغط هنا لفتح جلسة جديدة</a>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }
    else if (Model.ActiveSession != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <strong>جلسة نشطة:</strong> 
            الجلسة رقم @Model.ActiveSession.SessionSN - 
            الجهاز: @(Model.ActiveSession.Device?.DeviceName ?? "غير محدد") - 
            الوردية: @(Model.ActiveSession.Shift?.ShiftName ?? "غير محدد") - 
            فتحت في: @Model.ActiveSession.OpenTime?.ToString("yyyy/MM/dd HH:mm")
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="row">
        <!-- Left Panel - Main POS Interface -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-cash-register me-2"></i>
                        نقاط البيع - الفاتورة رقم <span id="invoiceNumber">@Model.InvoiceNo</span>
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-light btn-sm" onclick="createNewInvoice()">
                            <i class="fas fa-plus me-1"></i> فاتورة جديدة
                        </button>
                        <button class="btn btn-light btn-sm" onclick="printReceipt()">
                            <i class="fas fa-print me-1"></i> طباعة
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Header Section -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label class="form-label">المتجر</label>
                            <select id="storeSelect" class="form-select" onchange="changeStore()" @(!Model.CanChangeStore ? "disabled" : "")>
                                @foreach (var store in Model.Stores)
                                {
                                    <option value="@store.StoreName" selected="@(store.StoreName == Model.Store)">
                                        @store.StoreName
                                    </option>
                                }
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الكاشير</label>
                            <input type="text" id="cashierName" class="form-control" value="@Model.CashierAccount" readonly>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">التاريخ</label>
                            <input type="text" id="invoiceDate" class="form-control" value="@DateTime.Now.ToString("yyyy/MM/dd")" readonly>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الوقت</label>
                            <input type="text" id="invoiceTime" class="form-control" value="@DateTime.Now.ToString("HH:mm")" readonly>
                        </div>
                    </div>

                    <!-- Customer Section -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">العميل</label>
                            <select id="customerSelect" class="form-select" onchange="selectCustomer()" @(!Model.CanChangeCustomer ? "disabled" : "")>
                                <option value="">اختر العميل</option>
                                @foreach (var customer in Model.Customers)
                                {
                                    <option value="@customer.AccountCode" selected="@(customer.AccountCode == Model.CustomerId?.ToString())">
                                        @customer.AccountName
                                    </option>
                                }
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">اسم العميل</label>
                            <input type="text" id="customerName" class="form-control" value="@Model.CustomerName" placeholder="اسم العميل" @(!Model.CanChangeCustomer ? "readonly" : "")>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">رقم الهاتف</label>
                            <div class="input-group">
                                <input type="text" id="customerPhone" class="form-control" value="@Model.CustomerPhone" placeholder="رقم الهاتف">
                                <button class="btn btn-outline-secondary" type="button" onclick="searchCustomerByPhone()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Barcode Section -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">البحث / الباركود</label>
                            <div class="input-group">
                                <input type="text" id="searchItem" class="form-control" placeholder="ابحث عن صنف أو أدخل الباركود..." dir="rtl" onkeydown="handleSearchKeyDown(event)">
                                <button class="btn btn-outline-secondary" type="button" onclick="searchItems()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الخصم %</label>
                            <input type="number" id="discountPercent" class="form-control" value="0" min="0" max="@Model.MaxDiscountPercent" step="0.01" onchange="updateDiscount()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">مبلغ الخصم</label>
                            <input type="text" id="discountAmount" class="form-control" value="0.00" readonly>
                        </div>
                    </div>

                    <!-- Items Grid -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الصنف</th>
                                    <th>الكمية</th>
                                    <th>الوحدة</th>
                                    <th>السعر</th>
                                    <th>الإجمالي</th>
                                    <th>الضريبة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="invoiceItemsBody">
                                @foreach (var item in Model.Items)
                                {
                                    <tr data-line-sn="@item.LineSN">
                                        <td>@item.LineSN</td>
                                        <td>@item.ItemDescription</td>
                                        <td>
                                            <input type="number" class="form-control form-control-sm quantity-input" 
                                                   value="@item.Quantity" min="0.001" step="0.001" 
                                                   onchange="updateItemQuantity(@item.LineSN, this.value)">
                                        </td>
                                        <td>@item.UofM</td>
                                        <td>
                                            <input type="number" class="form-control form-control-sm price-input" 
                                                   value="@item.UnitPrice" min="0" step="0.01" 
                                                   onchange="updateItemPrice(@item.LineSN, this.value)"
                                                   @(!Model.CanChangePrice ? "readonly" : "")>
                                        </td>
                                        <td>@item.LineAmount.ToString("N2")</td>
                                        <td>@item.VATAmount.ToString("N2")</td>
                                        <td>
                                            <button class="btn btn-danger btn-sm" onclick="deleteItem(@item.LineSN)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Totals Section -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">الإجماليات</h6>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>عدد الأصناف:</span>
                                        <span id="itemCount">@Model.ItemCount</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>إجمالي الكمية:</span>
                                        <span id="totalQuantity">@Model.TotalQuantity.ToString("N3")</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>المجموع:</span>
                                        <span id="totalAmount">@Model.TotalAmount.ToString("N2")</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>الخصم:</span>
                                        <span id="totalDiscount">@Model.DiscountAmount.ToString("N2")</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>الضريبة:</span>
                                        <span id="vatAmount">@Model.VATAmount.ToString("N2")</span>
                                    </div>
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>الإجمالي النهائي:</span>
                                        <span id="netAmount">@Model.NetAmount.ToString("N2")</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">طرق الدفع</h6>
                                    <div class="mb-3">
                                        <div class="d-flex gap-2 mb-2">
                                            <button class="btn btn-success flex-fill" onclick="setPaymentMethod('cash')">
                                                <i class="fas fa-money-bill me-1"></i> نقدي
                                            </button>
                                            <button class="btn btn-info flex-fill" onclick="setPaymentMethod('card')">
                                                <i class="fas fa-credit-card me-1"></i> بطاقة
                                            </button>
                                            <button class="btn btn-warning flex-fill" onclick="setPaymentMethod('split')">
                                                <i class="fas fa-cut me-1"></i> مقسم
                                            </button>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <label class="form-label">المبلغ النقدي</label>
                                        <input type="number" id="cashAmount" class="form-control" value="0.00" min="0" step="0.01" onchange="updatePaymentAmounts()">
                                    </div>
                                    <div class="mb-2">
                                        <label class="form-label">المبلغ بالبطاقة</label>
                                        <input type="number" id="cardAmount" class="form-control" value="0.00" min="0" step="0.01" onchange="updatePaymentAmounts()">
                                    </div>
                                    <div class="mb-2">
                                        <label class="form-label">المتبقي</label>
                                        <input type="text" id="remainingAmount" class="form-control" value="0.00" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex gap-2 justify-content-center">
                                @if (!Model.HasCashier)
                                {
                                    <button id="saveInvoiceBtn" class="btn btn-primary btn-lg" onclick="saveInvoice()" disabled>
                                        <i class="fas fa-save me-2"></i> حفظ وإتمام البيع
                                    </button>
                                }
                                else
                                {
                                    <button id="saveInvoiceBtn" class="btn btn-primary btn-lg" onclick="saveInvoice()">
                                        <i class="fas fa-save me-2"></i> حفظ وإتمام البيع
                                    </button>
                                }
                                <button class="btn btn-secondary btn-lg" onclick="clearInvoice()">
                                    <i class="fas fa-eraser me-2"></i> مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel - Search Results and Favorites -->
        <div class="col-lg-4">
            <!-- Search Results -->
            <div class="card mb-3" id="searchResults" style="display: none;">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-search me-2"></i>
                        نتائج البحث
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row" id="searchResultsList">
                        <!-- Search results will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Favorite Items -->
            <div class="card mb-3" id="favoriteItemsList">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        الأصناف المفضلة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row" id="favoriteItemsContainer">
                        @foreach (var item in Model.FavoriteItems)
                        {
                            <div class="col-6 mb-2">
                                <button class="btn btn-outline-warning w-100 text-start" 
                                        onclick="addItemDirectly(@item.ItemNo, '@item.ItemDescription', @(item.UnitSalesPrice ?? 0), '@item.SalesUofM')">
                                    <small>@item.ItemDescription</small><br>
                                    <strong>@((item.UnitSalesPrice ?? 0).ToString("N2")) @item.SalesUofM</strong>
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Numeric Keypad -->
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-keyboard me-2"></i>
                        لوحة المفاتيح الرقمية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-4">
                            <button class="btn btn-outline-secondary w-100" onclick="appendToInput('7')">7</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-secondary w-100" onclick="appendToInput('8')">8</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-secondary w-100" onclick="appendToInput('9')">9</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-secondary w-100" onclick="appendToInput('4')">4</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-secondary w-100" onclick="appendToInput('5')">5</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-secondary w-100" onclick="appendToInput('6')">6</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-secondary w-100" onclick="appendToInput('1')">1</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-secondary w-100" onclick="appendToInput('2')">2</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-secondary w-100" onclick="appendToInput('3')">3</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-secondary w-100" onclick="appendToInput('0')">0</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-secondary w-100" onclick="appendToInput('.')">.</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-danger w-100" onclick="clearInput()">
                                <i class="fas fa-backspace"></i>
                            </button>
                        </div>
                        <div class="col-12 mt-2">
                            <button class="btn btn-primary w-100" onclick="processCurrentInput()">
                                <i class="fas fa-arrow-right me-2"></i> إدخال
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Session Required Modal -->
<div class="modal fade" id="sessionRequiredModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    جلسة مطلوبة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>لا يمكن إنشاء فاتورة بدون جلسة نشطة لنقاط البيع.</p>
                <p>يرجى فتح جلسة جديدة أولاً من صفحة إدارة الجلسات.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="/POSSessions/Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    فتح جلسة جديدة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Customer Search Modal -->
<div class="modal fade" id="customerSearchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info">
                <h5 class="modal-title">
                    <i class="fas fa-users me-2"></i>
                    البحث عن العميل
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" id="customerSearchInput" class="form-control" placeholder="ابحث بالاسم أو رقم الهاتف..." onkeyup="searchCustomers(this.value)">
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>رقم الهاتف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="customerSearchResults">
                            <!-- Customer search results will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let currentInvoiceNo = @Model.InvoiceNo;
        let canChangePrice = @Json.Serialize(Model.CanChangePrice);
        let canChangeCustomer = @Json.Serialize(Model.CanChangeCustomer);
        let maxDiscountPercent = @Model.MaxDiscountPercent;
        let currentInputField = null;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            updateTotals();
            updatePaymentAmounts();
            setInterval(updateTime, 1000);
            
            // Auto-select default customer if available
            autoSelectDefaultCustomer();
        });

        // Auto-select default customer
        function autoSelectDefaultCustomer() {
            const customerSelect = document.getElementById('customerSelect');
            if (customerSelect.options.length > 1) { // More than just "اختر العميل"
                // If there's a default customer from server, it should already be selected
                // Otherwise, select the first available customer
                if (!customerSelect.value && customerSelect.options.length > 1) {
                    customerSelect.selectedIndex = 1; // Select first customer (skip empty option)
                    selectCustomer(); // Trigger the selection logic
                }
            }
        }

        // Update time display
        function updateTime() {
            const now = new Date();
            document.getElementById('invoiceTime').value = now.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
        }

        // Numeric keypad functions
        function appendToInput(value) {
            if (currentInputField) {
                const currentValue = currentInputField.value;
                if (value === '.' && currentValue.includes('.')) return;
                currentInputField.value = currentValue + value;
            }
        }

        function clearInput() {
            if (currentInputField) {
                currentInputField.value = currentInputField.value.slice(0, -1);
            }
        }

        function processCurrentInput() {
            if (currentInputField && currentInputField.id === 'searchItem') {
                const barcode = currentInputField.value.trim();
                if (barcode) {
                    processBarcode(barcode);
                    currentInputField.value = '';
                }
            }
        }

        // Set focus to input field for keypad
        function setCurrentInputField(field) {
            currentInputField = field;
        }

        // Search items
        function searchItems() {
            const searchTerm = document.getElementById('searchItem').value.trim();
            if (!searchTerm) {
                document.getElementById('searchResults').style.display = 'none';
                document.getElementById('favoriteItemsList').style.display = 'block';
                return;
            }

            fetch('/POS/SearchItems', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ searchTerm: searchTerm })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySearchResults(data.items);
                    document.getElementById('favoriteItemsList').style.display = 'none';
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ أثناء البحث');
            });
        }

        function handleSearchKeyDown(event) {
            if (event.key === 'Enter') {
                const searchTerm = event.target.value.trim();
                if (searchTerm) {
                    processBarcode(searchTerm);
                    event.target.value = '';
                }
            }
        }

        function displaySearchResults(items) {
            const container = document.getElementById('searchResultsList');
            container.innerHTML = '';

            items.forEach(item => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'col-6 mb-2';
                itemDiv.innerHTML = `
                    <button class="btn btn-outline-info w-100 text-start" 
                            onclick="addItemDirectly(${item.itemNo}, '${item.description}', ${item.unitPrice}, '${item.uofM}')">
                        <small>${item.description}</small><br>
                        <strong>${item.unitPrice.toFixed(2)} ${item.uofM}</strong>
                    </button>
                `;
                container.appendChild(itemDiv);
            });

            document.getElementById('searchResults').style.display = 'block';
        }

        // Enhanced barcode processing with weight support
        function processBarcode(barcode) {
            fetch('/POS/ProcessBarcode', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    barcode: barcode,
                    invoiceNo: currentInvoiceNo,
                    store: document.getElementById('storeSelect').value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    if (data.requiresSession) {
                        showSessionRequiredModal();
                    } else {
                        showAlert('error', data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ أثناء معالجة الباركود');
            });
        }

        // Direct item addition
        function addItemDirectly(itemNo, description, unitPrice, uofM) {
            const store = document.getElementById('storeSelect').value;
            const quantity = 1;

            fetch('/POS/AddItem', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    invoiceNo: currentInvoiceNo,
                    itemNo: itemNo,
                    quantity: quantity,
                    unitPrice: unitPrice,
                    store: store
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the invoice items list without page reload
                    updateInvoiceItems();
                    updateTotals();
                    showAlert('success', 'تم إضافة الصنف بنجاح');
                } else {
                    if (data.requiresSession) {
                        showSessionRequiredModal();
                    } else {
                        showAlert('error', data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ أثناء إضافة الصنف');
            });

            document.getElementById('searchResults').style.display = 'none';
            document.getElementById('searchItem').value = '';
            document.getElementById('favoriteItemsList').style.display = 'block';
        }

        // Item management
        function updateItemQuantity(lineSN, quantity) {
            const unitPrice = document.querySelector(`tr[data-line-sn="${lineSN}"] .price-input`).value;
            updateItem(lineSN, quantity, unitPrice);
        }

        function updateItemPrice(lineSN, unitPrice) {
            const quantity = document.querySelector(`tr[data-line-sn="${lineSN}"] .quantity-input`).value;
            updateItem(lineSN, quantity, unitPrice);
        }

        function updateItem(lineSN, quantity, unitPrice) {
            fetch('/POS/UpdateItem', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    invoiceNo: currentInvoiceNo,
                    lineSN: lineSN,
                    quantity: parseFloat(quantity),
                    unitPrice: parseFloat(unitPrice)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the invoice items list without page reload
                    updateInvoiceItems();
                    updateTotals();
                    showAlert('success', 'تم تحديث الصنف بنجاح');
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ أثناء تحديث الصنف');
            });
        }

        function deleteItem(lineSN) {
            if (!confirm('هل أنت متأكد من حذف هذا الصنف؟')) return;

            fetch('/POS/DeleteItem', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    invoiceNo: currentInvoiceNo,
                    lineSN: lineSN
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the invoice items list without page reload
                    updateInvoiceItems();
                    updateTotals();
                    showAlert('success', 'تم حذف الصنف بنجاح');
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ أثناء حذف الصنف');
            });
        }

        // Customer management
        function selectCustomer() {
            const customerSelect = document.getElementById('customerSelect');
            const customerId = customerSelect.value;
            const customerName = customerSelect.options[customerSelect.selectedIndex].text;
            const customerPhone = document.getElementById('customerPhone').value;
            
            document.getElementById('customerName').value = customerName;
            
            // Save customer information to database
            if (currentInvoiceNo > 0) {
                fetch('/POS/SaveCustomerInfo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        invoiceNo: currentInvoiceNo,
                        customerId: customerId ? parseInt(customerId) : null,
                        customerName: customerName,
                        customerPhone: customerPhone
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        showAlert('error', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error saving customer info:', error);
                });
            }
        }

        function searchCustomerByPhone() {
            const phone = document.getElementById('customerPhone').value.trim();
            if (phone) {
                fetch('/POS/SearchCustomerByPhone', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phone: phone })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.customer) {
                        document.getElementById('customerSelect').value = data.customer.accountCode;
                        document.getElementById('customerName').value = data.customer.accountName;
                        document.getElementById('customerPhone').value = data.customer.phone || phone;
                    } else {
                        showCustomerSearchModal();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showCustomerSearchModal();
                });
            } else {
                showCustomerSearchModal();
            }
        }

        function showCustomerSearchModal() {
            const modal = new bootstrap.Modal(document.getElementById('customerSearchModal'));
            modal.show();
            searchCustomers('');
        }

        function searchCustomers(searchTerm) {
            fetch('/POS/SearchCustomers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ searchTerm: searchTerm })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayCustomerSearchResults(data.customers);
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ أثناء البحث عن العملاء');
            });
        }

        function displayCustomerSearchResults(customers) {
            const container = document.getElementById('customerSearchResults');
            container.innerHTML = '';

            customers.forEach(customer => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${customer.accountName}</td>
                    <td>${customer.phone || ''}</td>
                    <td>
                        <button class="btn btn-primary btn-sm" onclick="selectCustomerFromSearch(${customer.accountCode}, '${customer.accountName}', '${customer.phone || ''}')">
                            <i class="fas fa-check me-1"></i> اختيار
                        </button>
                    </td>
                `;
                container.appendChild(row);
            });
        }

        function selectCustomerFromSearch(accountCode, accountName, phone) {
            document.getElementById('customerSelect').value = accountCode;
            document.getElementById('customerName').value = accountName;
            document.getElementById('customerPhone').value = phone;
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('customerSearchModal'));
            modal.hide();
        }

        // Discount management
        function updateDiscount() {
            const discountPercent = parseFloat(document.getElementById('discountPercent').value) || 0;
            const totalAmount = parseFloat(document.getElementById('totalAmount').textContent.replace(',', '')) || 0;
            
            if (discountPercent > maxDiscountPercent) {
                showAlert('warning', `الخصم يتجاوز الحد المسموح به (${maxDiscountPercent}%)`);
                document.getElementById('discountPercent').value = maxDiscountPercent;
                // Re-calculate discount amount after setting value
                const discountAmount = (discountPercent / 100) * totalAmount;
                document.getElementById('discountAmount').value = discountAmount.toFixed(2);
            } else {
                const discountAmount = (discountPercent / 100) * totalAmount;
                document.getElementById('discountAmount').value = discountAmount.toFixed(2);
            }
            
            updateTotals();
        }

        // Payment methods
        function setPaymentMethod(method) {
            const netAmount = parseFloat(document.getElementById('netAmount').textContent.replace(',', '')) || 0;
            
            switch(method) {
                case 'cash':
                    document.getElementById('cashAmount').value = netAmount.toFixed(2);
                    document.getElementById('cardAmount').value = '0.00';
                    break;
                case 'card':
                    document.getElementById('cashAmount').value = '0.00';
                    document.getElementById('cardAmount').value = netAmount.toFixed(2);
                    break;
                case 'split':
                    const halfAmount = netAmount / 2;
                    document.getElementById('cashAmount').value = halfAmount.toFixed(2);
                    document.getElementById('cardAmount').value = halfAmount.toFixed(2);
                    break;
            }
            
            updatePaymentAmounts();
        }

        function updatePaymentAmounts() {
            const cashAmount = parseFloat(document.getElementById('cashAmount').value) || 0;
            const cardAmount = parseFloat(document.getElementById('cardAmount').value) || 0;
            const netAmount = parseFloat(document.getElementById('netAmount').textContent.replace(',', '')) || 0;
            
            const totalPaid = cashAmount + cardAmount;
            const remaining = netAmount - totalPaid;
            
            document.getElementById('remainingAmount').value = remaining.toFixed(2);
            
            // Highlight remaining amount
            const remainingField = document.getElementById('remainingAmount');
            if (remaining < 0) {
                remainingField.classList.add('is-invalid');
                remainingField.classList.remove('is-valid');
            } else if (remaining === 0) {
                remainingField.classList.add('is-valid');
                remainingField.classList.remove('is-invalid');
            } else {
                remainingField.classList.remove('is-valid', 'is-invalid');
            }
        }

        // Store management
        function changeStore() {
            const store = document.getElementById('storeSelect').value;
            const canChangeStore = @Json.Serialize(Model.CanChangeStore);
            
            if (!canChangeStore) {
                showAlert('warning', 'ليس لديك صلاحية لتغيير المتجر');
                return;
            }
            
            // Validate session for the new store
            fetch('/POS/ValidateSession', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    store: store
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Save store information to database
                    if (currentInvoiceNo > 0) {
                        fetch('/POS/SaveStoreInfo', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                invoiceNo: currentInvoiceNo,
                                store: store
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showAlert('success', 'تم حفظ بيانات المتجر بنجاح');
                            } else {
                                showAlert('error', data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error saving store info:', error);
                            showAlert('error', 'حدث خطأ أثناء حفظ بيانات المتجر');
                        });
                    }
                    // Fetch cashier account for the selected store
                    fetch('/POS/GetCashierAccount', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(store)
                    })
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('cashierName').value = data.cashierAccount || '';
                        const saveBtn = document.getElementById('saveInvoiceBtn');
                        if (!data.hasCashier) {
                            showAlert('danger', 'لا يوجد كاشير معين لهذا المتجر. لا يمكن تنفيذ العمليات حتى يتم تعيين كاشير.');
                            if (saveBtn) saveBtn.disabled = true;
                        } else {
                            if (saveBtn) saveBtn.disabled = false;
                        }
                    });
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error validating session:', error);
                showAlert('error', 'حدث خطأ أثناء التحقق من الجلسة');
            });
        }

        // Invoice management
        function createNewInvoice() {
            const store = document.getElementById('storeSelect').value;
            fetch('/POS/CreateNewInvoice', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ store: store })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the invoice number and reset the form
                    currentInvoiceNo = data.invoiceNo;
                    document.getElementById('invoiceNumber').textContent = data.invoiceNo;
                    
                    // Clear items but keep customer and store selection
                    updateInvoiceItems();
                    updateTotals();
                    
                    showAlert('success', 'تم إنشاء فاتورة جديدة بنجاح');
                } else {
                    if (data.requiresSession) {
                        showSessionRequiredModal();
                    } else {
                        showAlert('error', data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ أثناء إنشاء الفاتورة');
            });
        }

        // Enhanced save invoice with complete transaction functionality
        function saveInvoice() {
            const customerId = document.getElementById('customerSelect').value;
            const customerName = document.getElementById('customerName').value;
            const customerPhone = document.getElementById('customerPhone').value;
            const discountPercent = parseFloat(document.getElementById('discountPercent').value) || 0;
            const itemsCount = document.querySelectorAll('#invoiceItemsBody tr').length;
            const cashAmount = parseFloat(document.getElementById('cashAmount').value) || 0;
            const cardAmount = parseFloat(document.getElementById('cardAmount').value) || 0;
            const remaining = parseFloat(document.getElementById('remainingAmount').value) || 0;

            // Client-side validation
            if (itemsCount === 0) {
                showAlert('warning', 'يجب إضافة صنف واحد على الأقل للفاتورة');
                return;
            }
            if (remaining !== 0) {
                showAlert('warning', 'يجب أن يكون المبلغ المدفوع مساوٍ للإجمالي النهائي');
                return;
            }

            // Save and complete transaction in one call
            fetch('/POS/SaveAndCompleteInvoice', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    invoiceNo: currentInvoiceNo,
                    customerId: customerId,
                    customerName: customerName,
                    customerPhone: customerPhone,
                    discountPercent: discountPercent,
                    cashAmount: cashAmount,
                    cardAmount: cardAmount
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', 'تم حفظ وإتمام البيع بنجاح');
                    
                    // Clear cart and totals
                    document.getElementById('invoiceItemsBody').innerHTML = '';
                    updateTotals();
                    
                    setTimeout(() => {
                        window.open(`/POS/ThermalReceipt/${currentInvoiceNo}`, '_blank');
                        setTimeout(() => {
                            createNewInvoice(); // Create new invoice after successful completion
                        }, 700);
                    }, 700);
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ أثناء حفظ الفاتورة');
            });
        }

        // Clear invoice - only clear cart items, keep customer and payment info
        function clearInvoice() {
            if (!confirm('هل أنت متأكد من مسح أصناف الفاتورة؟')) return;
            
            fetch('/POS/ClearInvoiceItems', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ invoiceNo: currentInvoiceNo })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the invoice items list without page reload
                    updateInvoiceItems();
                    updateTotals();
                    showAlert('success', 'تم مسح أصناف الفاتورة بنجاح');
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ أثناء مسح أصناف الفاتورة');
            });
        }

        function printReceipt() {
            if (currentInvoiceNo === 0) {
                showAlert('warning', 'لا توجد فاتورة للطباعة');
                return;
            }
            
            window.open(`/POS/ThermalReceipt/${currentInvoiceNo}`, '_blank');
        }

        // Update invoice items dynamically
        function updateInvoiceItems() {
            fetch(`/POS/GetInvoiceItems/${currentInvoiceNo}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const tbody = document.getElementById('invoiceItemsBody');
                        tbody.innerHTML = '';
                        
                        data.items.forEach(item => {
                            const row = document.createElement('tr');
                            row.setAttribute('data-line-sn', item.lineSN);
                            row.innerHTML = `
                                <td>${item.itemDescription}</td>
                                <td>
                                    <input type="number" class="form-control form-control-sm quantity-input" 
                                           value="${item.quantity}" step="0.001" min="0.001"
                                           onchange="updateItemQuantity(${item.lineSN}, this.value)">
                                </td>
                                <td>${item.uofM}</td>
                                <td>
                                    <input type="number" class="form-control form-control-sm price-input" 
                                           value="${item.unitPrice}" step="0.01" min="0"
                                           onchange="updateItemPrice(${item.lineSN}, this.value)">
                                </td>
                                <td class="text-end">${item.lineAmount.toFixed(2)}</td>
                                <td class="text-end">${item.vatAmount.toFixed(2)}</td>
                                <td>
                                    <button class="btn btn-danger btn-sm" onclick="deleteItem(${item.lineSN})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            `;
                            tbody.appendChild(row);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error updating invoice items:', error);
                });
        }

        // Utility functions
        function updateTotals() {
            // This would be calculated server-side, but for demo purposes we'll do it client-side
            const rows = document.querySelectorAll('#invoiceItemsBody tr');
            let totalAmount = 0;
            let totalQuantity = 0;
            let itemCount = rows.length;
            
            rows.forEach(row => {
                const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
                const price = parseFloat(row.querySelector('.price-input').value) || 0;
                const lineTotal = quantity * price;
                
                totalAmount += lineTotal;
                totalQuantity += quantity;
            });
            
            const discountPercent = parseFloat(document.getElementById('discountPercent').value) || 0;
            const discountAmount = (discountPercent / 100) * totalAmount;
            const amountAfterDiscount = totalAmount - discountAmount;
            const vatAmount = amountAfterDiscount * 0.15; // 15% VAT
            const netAmount = amountAfterDiscount + vatAmount;
            
            document.getElementById('itemCount').textContent = itemCount;
            document.getElementById('totalQuantity').textContent = totalQuantity.toFixed(3);
            document.getElementById('totalAmount').textContent = totalAmount.toFixed(2);
            document.getElementById('totalDiscount').textContent = discountAmount.toFixed(2);
            document.getElementById('vatAmount').textContent = vatAmount.toFixed(2);
            document.getElementById('netAmount').textContent = netAmount.toFixed(2);
            
            updatePaymentAmounts();
        }

        function showSessionRequiredModal() {
            const modal = new bootstrap.Modal(document.getElementById('sessionRequiredModal'));
            modal.show();
        }

        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Set up input field focus for keypad and initialize defaults
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('input[type="text"], input[type="number"]');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    setCurrentInputField(this);
                });
            });
            
            // Set default customer if available
            const defaultCustomerId = '@Model.DefaultCustomerId';
            if (defaultCustomerId && defaultCustomerId !== '0') {
                const customerSelect = document.getElementById('customerSelect');
                if (customerSelect) {
                    customerSelect.value = defaultCustomerId;
                    selectCustomer();
                }
            }
            
            // Set default store if available
            const defaultStore = '@Model.DefaultStore';
            if (defaultStore) {
                const storeSelect = document.getElementById('storeSelect');
                if (storeSelect) {
                    storeSelect.value = defaultStore;
                }
            }
        });
    </script>
} 
