﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Core\AccountingSystem.Core\AccountingSystem.Core.csproj" />
    <ProjectReference Include="..\..\Data\AccountingSystem.Data\AccountingSystem.Data.csproj" />
    <ProjectReference Include="..\..\Models\AccountingSystem.Models\AccountingSystem.Models.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="QRCoder" Version="1.6.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
  </ItemGroup>

</Project>
