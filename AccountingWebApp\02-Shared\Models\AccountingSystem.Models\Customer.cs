using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    /// <summary>
    /// Customer entity matching tblCustomers table structure from legacy VB.NET application
    /// </summary>
    [Table("tblCustomers")]
    public class Customer
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        [Display(Name = "رقم العميل")]
        public long CustomerNo { get; set; }

        [StringLength(255)]
        [Display(Name = "الاسم التجاري")]
        public string? CustomerName { get; set; }

        [StringLength(50)]
        [Display(Name = "الاسم الأول")]
        public string? FirstName { get; set; }

        [StringLength(50)]
        [Display(Name = "الاسم الأخير")]
        public string? LastName { get; set; }

        [Display(Name = "الجوال")]
        public int? Mobile { get; set; }

        [Display(Name = "الهاتف")]
        public int? Phone { get; set; }

        [StringLength(255)]
        [EmailAddress]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(50)]
        [Display(Name = "عنوان الشارع 1")]
        public string? StreetAddress1 { get; set; }

        [StringLength(50)]
        [Display(Name = "عنوان الشارع 2")]
        public string? StreetAddress2 { get; set; }

        [StringLength(50)]
        [Display(Name = "المدينة")]
        public string? City { get; set; }

        [StringLength(50)]
        [Display(Name = "المنطقة")]
        public string? Region { get; set; }

        [Display(Name = "الرمز البريدي")]
        public int? PostalCode { get; set; }

        [StringLength(50)]
        [Display(Name = "طرق الدفع")]
        public string? PaymentMethod { get; set; }

        [Display(Name = "الحد الإئتماني")]
        public double? CreditLimit { get; set; }

        [StringLength(255)]
        [Display(Name = "شروط الدفع")]
        public string? PaymentTerm { get; set; }

        [StringLength(255)]
        [Display(Name = "للتواصل")]
        public string? ContactPerson { get; set; }

        [StringLength(10)]
        [Display(Name = "السجل التجاري")]
        public string? CR { get; set; }

        [StringLength(50)]
        [Display(Name = "الرقم الضريبي")]
        public string? VATRegNo { get; set; }

        [StringLength(50)]
        [Display(Name = "المتجر")]
        public string? Shop { get; set; }

        [StringLength(50)]
        [Display(Name = "الحالة")]
        public string? Status { get; set; }

        [StringLength(50)]
        [Display(Name = "داخل / خارج المملكة")]
        public string? LocalCustomer { get; set; }

        [Display(Name = "المندوب")]
        public int? EmployeeNo { get; set; }

        [StringLength(255)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "الرمز القديم")]
        public int? OldCode { get; set; }

        [Display(Name = "رقم المبنى")]
        public int? BuildingNo { get; set; }

        [Display(Name = "الرقم الإضافي")]
        public int? AdditionalNo { get; set; }

        [StringLength(255)]
        [Display(Name = "الحي")]
        public string? District { get; set; }

        // Audit fields that exist in legacy database
        [StringLength(50)]
        [Display(Name = "أنشئ بواسطة")]
        public string? CreatedBy { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime? CreatedOn { get; set; }

        [StringLength(50)]
        [Display(Name = "عدل بواسطة")]
        public string? ModifiedBy { get; set; }

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedOn { get; set; }

        // Navigation properties
        public virtual Employee? Employee { get; set; }
    }
}