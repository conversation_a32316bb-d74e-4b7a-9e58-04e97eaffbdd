<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Data.Tools.Sql.BatchParser</name>
    </assembly>
    <members>
        <member name="M:Microsoft.Data.Tools.Sql.BatchParser.BatchParser.RaiseScriptError(System.String,Microsoft.Data.Tools.Sql.BatchParser.ScriptMessageType)">
            <summary>
            Called when the script parsing has errors/warnings
            </summary>
            <param name="message"></param>
            <param name="messageType"></param>
        </member>
        <member name="M:Microsoft.Data.Tools.Sql.BatchParser.BatchParser.RaiseScriptMessage(System.String)">
            <summary>
            Called on parsing info message
            </summary>
            <param name="message"></param>
        </member>
        <member name="M:Microsoft.Data.Tools.Sql.BatchParser.BatchParser.RaiseHaltParser">
            <summary>
            Called on parsing info message
            </summary>
        </member>
        <member name="M:Microsoft.Data.Tools.Sql.BatchParser.Lexer.AcceptEscapableQuotedText(System.Char)">
            <summary>
            This method reads ahead until the closingChar is found.  When closing<PERSON>har is found,
            the next character is checked.  If it's the same as closingChar, the character is
            escaped and the method resumes looking for a non-escaped closingChar.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Tools.Sql.BatchParser.Lexer.AcceptQuotedText(System.Char)">
            <summary>
            This method reads ahead until the closingChar is found.  This method does not allow for escaping
            of the closingChar.
            </summary>
        </member>
    </members>
</doc>
