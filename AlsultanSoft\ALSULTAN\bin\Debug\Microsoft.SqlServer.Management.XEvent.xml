<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.SqlServer.Management.XEvent</name>
    </assembly>
    <members>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Action">
            <summary>
            RunTime class for Action. Each instance of this class represents a row in sys.server_event_session_actions.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Action.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Action"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.#ctor(Microsoft.SqlServer.Management.XEvent.Event,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Action"/> class with the given parent and name.
            The name here should be in form of eventModuleId.packagename.eventname or packagename.eventname.
            </summary>
            <param name="parent">The parent.</param>
            <param name="name">The name.</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">The event name is malformed or wrong.</exception>
            <exception cref="T:System.ArgumentNullException">Parameter name is null.</exception>
            <exception cref="T:System.NullReferenceException">The parent(or grandparent) of Event is not set yet.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.#ctor(Microsoft.SqlServer.Management.XEvent.Event,Microsoft.SqlServer.Management.XEvent.ActionInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Action"/> class from an instance of ActionInfo.
            </summary>
            <param name="parent">The parent.</param>
            <param name="actionInfo">The ActionInfo object.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.SetActionInfo(Microsoft.SqlServer.Management.XEvent.ActionInfo)">
            <summary>
            Set the ActionInfo for a pending Action.
            </summary>
            <param name="actionInfo">an instance of ActionInfo</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">if the Action object is not in pending state.</exception>
            <exception cref="T:System.ArgumentNullException">if the input actionInfo is null.</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Action.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.SetPackage(System.String)">
            <summary>
            Sets the package.
            </summary>
            <param name="packageName">Name of the package.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Action.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns>By now, action has no child, so this function always throw exception.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Action.Key">
            <summary>
            A key class for identification.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Action.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.#ctor(Microsoft.SqlServer.Management.XEvent.Action.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Action.Key"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Action.Key.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Action.Key"/> class.
            </summary>
            <param name="filedDict">The filed dict.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.Equals(System.Object,System.Object)">
            <summary>
            Equalses the specified obj1.
            </summary>
            <param name="obj1">The obj1.</param>
            <param name="obj2">The obj2.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Equalses the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.Action.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.Action.Key,System.Object)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.Action.Key,Microsoft.SqlServer.Management.XEvent.Action.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.Action.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.Action.Key,System.Object)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.Action.Key,Microsoft.SqlServer.Management.XEvent.Action.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.IsEqual(Microsoft.SqlServer.Management.XEvent.Action.Key)">
            <summary>
            Determines whether the specified key is equal.
            </summary>
            <param name="key">The key.</param>
            <returns>
            	<c>true</c> if the specified key is equal; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Action.ObjectFactory.Instance">
            <summary>
            Gets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.ObjectFactory.CreateImpl">
            <summary>
            Creates the impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Action.Name">
            <summary>
            The name of the Action
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Action.ModuleID">
            <summary>
            Gets the module ID.
            </summary>
            <value>The module ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Action.PackageName">
            <summary>
            Gets the name of the package this action belongs to.
            </summary>
            <value>The name of the package.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Action.Description">
            <summary>
            Gets or sets action description. Set accessor is for internal use only.
            </summary>
            <value>The description.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.GetScriptCreate">
            <summary>
            Gets Create script for the Action.
            </summary>
            <returns>A string containting the script.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Action.State">
            <summary>
            State of the object, used in Alter function in session.
            </summary>
            <value>The state.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Action.GetTypeMetadata">
            <summary>
            Gets Sfc Type Metadata.
            </summary>
            <returns>Type Metadata.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ActionCollection">
            <summary>
            SFC Collection class for Action.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Event)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.ActionCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.Action"/> with the specified name.
            </summary>
            <value>name of the action</value>
            <returns>Action with the specify name</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionCollection.Contains(System.String)">
            <summary>
            Determines whether the collection contains the Action.
            </summary>
            <param name="name">The name of the Action.</param>
            <returns>
                <c>true</c> if the collection contains Action; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionCollection.AddShadow(Microsoft.SqlServer.Management.XEvent.Action)">
            <summary>
            Adds the obj to the internal shadow collection
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ActionInfo">
            <summary>
            Metadata for Action.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.ActionInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.ActionInfo"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfo.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.SetName(System.String)">
            <summary>
            Sets the name.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns>By now, action has no child, so this function always throw exception.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key">
            <summary>
            A key class for identification.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.ActionInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key"/> class.
            </summary>
            <param name="filedDict">The filed dict.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            Equalses the specified obj1.
            </summary>
            <param name="obj1">The obj1.</param>
            <param name="obj2">The obj2.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Equalses the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.ActionInfo.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.ActionInfo.Key,System.Object)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.ActionInfo.Key,Microsoft.SqlServer.Management.XEvent.ActionInfo.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.ActionInfo.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.ActionInfo.Key,System.Object)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.ActionInfo.Key,Microsoft.SqlServer.Management.XEvent.ActionInfo.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.IsEqual(Microsoft.SqlServer.Management.XEvent.ActionInfo.Key)">
            <summary>
            Determines whether the specified key is equal.
            </summary>
            <param name="key">The key.</param>
            <returns>
            	<c>true</c> if the specified key is equal; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfo.ObjectFactory.Instance">
            <summary>
            Gets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.ObjectFactory.CreateImpl">
            <summary>
            Creates the impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfo.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfo.Name">
            <summary>
            The name of the Action
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfo.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfo.Capabilities">
            <summary>
            Gets the capabilities.
            </summary>
            <value>The capabilities.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfo.CapabilitiesDesc">
            <summary>
            Gets the capabilities desc.
            </summary>
            <value>The capabilities desc.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfo.TypeName">
            <summary>
            Gets the name of the type.
            </summary>
            <value>The name of the type.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfo.TypePackageID">
            <summary>
            Gets package ID of the type.
            </summary>
            <value>The package ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfo.TypePackageName">
            <summary>
            Gets the name of the type package.
            </summary>
            <value>The name of the type package.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ActionInfoCollection">
            <summary>
            SFC Collection class for ActionInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Package)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.ActionInfoCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ActionInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.ActionInfo"/> with the specified name.
            </summary>
            <value>name of the action</value>
            <returns>ActionInfo with the specify name</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfoCollection.Contains(System.String)">
            <summary>
            Determines whether the collection contains the Action.
            </summary>
            <param name="name">The name of the Action.</param>
            <returns>
                <c>true</c> if the collection contains Action; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ActionInfoCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo">
            <summary>
            An EventInfo object may have three kinds of columns: customizable, data, readonly.
            A DataEventColumnInfo object reprensents a data column of an EventInfo object and can be used in PredOperand.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.SetName(System.String)">
            <summary>
            Sets the name.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key,Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key,Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.ObjectFactory">
            <summary>
            Singleton class used by collection class
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.ID">
            <summary>
            Gets the ID.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.TypeName">
            <summary>
            Gets or sets the name of the type.
            </summary>
            <value>The name of the type.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.TypePackageID">
            <summary>
            Gets package ID of the type.
            </summary>
            <value>The package ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.TypePackageName">
            <summary>
            Gets the name of the type package.
            </summary>
            <value>The name of the type package.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.Capabilities">
            <summary>
            Gets the capabilities.
            </summary>
            <value>The capabilities.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo.CapabilitiesDesc">
            <summary>
            Gets the capabilities description.
            </summary>
            <value>The capabilities description.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfoCollection">
            <summary>
            This is collection class for DataEventColumnInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.EventInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfoCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfoCollection.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified name].
            </summary>
            <param name="name">The name.</param>
            <returns>
            	<c>true</c> if [contains] [the specified name]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.DataEventColumnInfoCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Event">
            <summary>
            Runtime class for Event. Each instance of this class represents a row in sys.server_event_session_events.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Event.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Event"/> class.
            Empty constructor is a convention in SFC.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.#ctor(Microsoft.SqlServer.Management.XEvent.Session,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Event"/> class with given parent and name.
            </summary>
            <param name="parent">The parent.</param>
            <param name="name">The full qulified name of the event.</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">The event name is malformed or wrong.</exception>
            <exception cref="T:System.ArgumentNullException">Parameter name is null.</exception>
            <exception cref="T:System.NullReferenceException">The parent of Session is not set yet.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.#ctor(Microsoft.SqlServer.Management.XEvent.Session,Microsoft.SqlServer.Management.XEvent.EventInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Event"/> class from an EventInfo object.
            </summary>
            <param name="parent">The parent.</param>
            <param name="eventInfo">The event info.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.SetEventInfo(Microsoft.SqlServer.Management.XEvent.EventInfo)">
            <summary>
            Set the EventInfo for a pending Event.
            </summary>
            <param name="eventInfo">An instance of EventInfo</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">if the Event object is not in pending state.</exception>
            <exception cref="T:System.ArgumentNullException">if the input eventInfo is null.</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.Parent">
            <summary>
            Parent Property for Event.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.SetPackage(System.String)">
            <summary>
            Sets the package.
            </summary>
            <param name="packageName">Name of the package.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.GetChildCollection(System.String)">
            <summary>
            Return child collection based on element type. Event is the parent of EventField and Action.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.EventFields">
            <summary>
            Gets the event fileds collection.
            </summary>
            <value>The collection of event fields.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.Actions">
            <summary>
            Gets the actions for this event.
            </summary>
            <value>The event column info set.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Event.Key">
            <summary>
            A key class for identification.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Event.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.#ctor(Microsoft.SqlServer.Management.XEvent.Event.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Event.Key"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.Key.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Event.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.Equals(System.Object,System.Object)">
            <summary>
            Equalses the specified obj1.
            </summary>
            <param name="obj1">The obj1.</param>
            <param name="obj2">The obj2.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Equalses the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.Event.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.Event.Key,System.Object)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.Event.Key,Microsoft.SqlServer.Management.XEvent.Event.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.Event.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.Event.Key,System.Object)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.Event.Key,Microsoft.SqlServer.Management.XEvent.Event.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Event.ObjectFactory">
            <summary>
            Singleton factory class for Event
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.ObjectFactory.Instance">
            <summary>
            Gets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.ObjectFactory.CreateImpl">
            <summary>
            Creates the impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.Name">
            <summary>
            The name of the Event
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.ID">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.ModuleID">
            <summary>
            Gets the module ID.
            </summary>
            <value>The module ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.PackageName">
            <summary>
            Gets the package name that the event belongs to.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.PredicateExpression">
            <summary>
            This is the string representation of the predicate.
            </summary>
            <value>The predicate string.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.PredicateXml">
            <summary>
            This is the xml string representation of the predicate.
            </summary>
            <value>The predicate xml string.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.Description">
            <summary>
            Gets or sets event description. Set accessor is for internal use only.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.Predicate">
            <summary>
            Predicate tree for PredicateExpression.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.PostCreate(System.Object)">
            <summary>
             post create operation
            </summary>
            <param name="executionResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.PostAlter(System.Object)">
            <summary>
            post alter operation
            </summary>
            <param name="executionResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.ResetPredicate">
            <summary>
             takes care of resetting the dirty flag
             post create/alter operations
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.AddAction(Microsoft.SqlServer.Management.XEvent.ActionInfo)">
            <summary>
            Create an action from the ActionInfo and add it into the action collection.
            </summary>
            <param name="actionInfo">The ActionInfo object.</param>
            <returns>The newly created action.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.AddAction(System.String)">
            <summary>
            Create an action with the specify name and add it to the actions.
            </summary>
            <param name="actionName">Full qulified name of the action.</param>
            <returns>The new created action</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.RemoveAction(Microsoft.SqlServer.Management.XEvent.Action)">
            <summary>
            Removes the action from the session.
            </summary>
            <param name="action">The action.</param>
            <returns>true if the action is found and removed, false otherwise.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.ScriptName">
            <summary>
            Gets Event name formatted for scripting.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Microsoft#SqlServer#Management#XEvent#ISessionObject#GetCreateScript">
            <summary>
            Generate the script for add an event. Used in Create Session.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.HasCustomizableField">
            <summary>
            Determines whether the event has at least one field needs to be set.
            </summary>
            <returns>
            True if at least customizable fields exist. False otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.Microsoft#SqlServer#Management#XEvent#ISessionObject#GetDropScript">
            <summary>
            Generating the script for drop the event. Used in Alter Session.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Event.State">
            <summary>
            State of the object, used in Alter function in session.
            </summary>
            <value>The state.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Event.GetTypeMetadata">
            <summary>
            Gets Sfc Type Metadata.
            </summary>
            <returns>Type Metadata.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventCollection">
            <summary>
            SFC Collection class for Event.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Session)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.EventCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.Event"/> with the specified name.
            </summary>
            <value>name of the Event</value>
            <returns>Event with the specify name</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventCollection.Contains(System.String)">
            <summary>
            Determines whether the collection contains the Event.
            </summary>
            <param name="name">The name of the Event.</param>
            <returns>
                <c>true</c> if the collection contains Event; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventCollection.AppendAlterScripts(System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            Check the current state of the events and return the script based on it.
            This should be used only in generating the alter script for session.
            </summary>
            <returns></returns>      
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventColumnInfo">
            <summary>
            An EventInfo object may have three kinds of columns: customizable, data, readonly.
            An EventColumnInfo object reprensents a customizable column of an EventInfo object.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.SetName(System.String)">
            <summary>
            Sets the name.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key,Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key,Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.ObjectFactory">
            <summary>
            Singleton class used by collection class
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.ID">
            <summary>
            Gets the ID.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.TypeName">
            <summary>
            Gets or sets the name of the type.
            </summary>
            <value>The name of the type.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.TypePackageID">
            <summary>
            Gets package ID of the type.
            </summary>
            <value>The package ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.TypePackageName">
            <summary>
            Gets the name of the type package.
            </summary>
            <value>The name of the type package.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Value">
            <summary>
            Gets the value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.Capabilities">
            <summary>
            Gets the capabilities.
            </summary>
            <value>The capabilities.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfo.CapabilitiesDesc">
            <summary>
            Gets the capabilities description.
            </summary>
            <value>The capabilities description.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventColumnInfoCollection">
            <summary>
            This is collection class for EventColumnInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.EventInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.EventColumnInfoCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventColumnInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.EventColumnInfo"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfoCollection.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified name].
            </summary>
            <param name="name">The name.</param>
            <returns>
            	<c>true</c> if [contains] [the specified name]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventColumnInfoCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventField">
            <summary>
            An EventField object reprensents a row in sys.server_event_session_fields.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.EventField.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.#ctor(Microsoft.SqlServer.Management.XEvent.Event,Microsoft.SqlServer.Management.XEvent.EventColumnInfo)">
            <summary>
            Constructor takes an Event and an EventColumnInfo as parameters
            </summary>
            <param name="parent">The parent.</param>
            <param name="eventColumnInfo">The event column info.</param>
            <exception cref="T:System.ArgumentNullException">Parameter parent or eventColumnInfo is null.</exception>
            <exception cref="T:System.NullReferenceException">eventColumnInfo's parent(or grandparent) is null. Mostly because it's not enumerated correctly.</exception>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">Parameter eventColumnInfo is invalid.</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventField.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventField.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventField.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.EventField.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.#ctor(Microsoft.SqlServer.Management.XEvent.EventField.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventField.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.EventField.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.EventField.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.EventField.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.EventField.Key,Microsoft.SqlServer.Management.XEvent.EventField.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.EventField.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.EventField.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.EventField.Key,Microsoft.SqlServer.Management.XEvent.EventField.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventField.ObjectFactory">
            <summary>
            Singleton class used by collection class
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventField.ID">
            <summary>
            Gets the ID.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventField.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventField.Value">
            <summary>
            Gets the value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventField.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventField.State">
            <summary>
            State of the object, used in Alter function in session.
            </summary>
            <value>The state.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventField.GetTypeMetadata">
            <summary>
            Gets Sfc Type Metadata.
            </summary>
            <returns>Type Metadata.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventFieldCollection">
            <summary>
            This is collection class for EventField.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventFieldCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Event)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.EventFieldCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventFieldCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.EventField"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventFieldCollection.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified name].
            </summary>
            <param name="name">The name.</param>
            <returns>
            	<c>true</c> if [contains] [the specified name]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventFieldCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventInfo">
            <summary>
            Metadata class for Event.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.EventInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.EventInfo"/> class.
            Empty constructor is an convention is a convention in SFC.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfo.Parent">
            <summary>
            Parent Property for EventInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.SetName(System.String)">
            <summary>
            Set the name of the Event.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.GetChildCollection(System.String)">
            <summary>
            Return child collection based on element type. Event is the parent of EventColumn.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfo.EventColumnInfoSet">
            <summary>
            Gets customizable event column info set.
            </summary>
            <value>The event column info set.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfo.DataEventColumnInfoSet">
            <summary>
            Gets data event column info set.
            </summary>
            <value>The event column info set.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfo.ReadOnlyEventColumnInfoSet">
            <summary>
            Gets readonly event column info set.
            </summary>
            <value>The event column info set.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventInfo.Key">
            <summary>
            A key class for identification.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.EventInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.EventInfo.Key"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.EventInfo.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            Equalses the specified obj1.
            </summary>
            <param name="obj1">The obj1.</param>
            <param name="obj2">The obj2.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Equalses the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.EventInfo.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.EventInfo.Key,System.Object)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.EventInfo.Key,Microsoft.SqlServer.Management.XEvent.EventInfo.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.EventInfo.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.EventInfo.Key,System.Object)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.EventInfo.Key,Microsoft.SqlServer.Management.XEvent.EventInfo.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventInfo.ObjectFactory">
            <summary>
            Singleton factory class for EventInfo
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfo.ObjectFactory.Instance">
            <summary>
            Gets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.ObjectFactory.CreateImpl">
            <summary>
            Creates the impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfo.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfo.Name">
            <summary>
            The name of the EventInfo
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfo.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfo.Capabilities">
            <summary>
            Gets the capabilities.
            </summary>
            <value>The capabilities.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfo.CapabilitiesDesc">
            <summary>
            Gets the capabilities desc.
            </summary>
            <value>The capabilities desc.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.EventInfoCollection">
            <summary>
            SFC Collection class for EventInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Package)">
            <summary>
            Initialize a new instance of EventInfoCollection given the Parent.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.EventInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.EventInfo"/> with the specified name.
            </summary>
            <value>name of the event</value>
            <returns>EventInfo object with the specify name.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfoCollection.Contains(System.String)">
            <summary>
            Determines whether the collection contains Event.
            </summary>
            <param name="name">The name of the Event.</param>
            <returns>
            	<c>true</c> if the collection contains Event; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.EventInfoCollection.GetElementFactoryImpl">
            <summary>
            Return the instance of object factory for EventInfo.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.MapInfo">
            <summary>
            Metadata class for Map
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.MapInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.MapInfo"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapInfo.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.SetName(System.String)">
            <summary>
            Sets the name.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns>The collection of MapValueInfo.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapInfo.MapValueInfoSet">
            <summary>
            Gets the map value info set.
            </summary>
            <value>The map value info set.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.MapInfo.Key">
            <summary>
            A key class for identification.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.MapInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other">The other.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.MapInfo.Key"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.MapInfo.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj">The obj.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            Equalses the specified obj1.
            </summary>
            <param name="obj1">The obj1.</param>
            <param name="obj2">The obj2.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Equalses the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.MapInfo.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.MapInfo.Key,System.Object)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.MapInfo.Key,Microsoft.SqlServer.Management.XEvent.MapInfo.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.MapInfo.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.MapInfo.Key,System.Object)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.MapInfo.Key,Microsoft.SqlServer.Management.XEvent.MapInfo.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.IsEqual(Microsoft.SqlServer.Management.XEvent.MapInfo.Key)">
            <summary>
            Determines whether the specified key is equal.
            </summary>
            <param name="key">The key.</param>
            <returns>
                <c>true</c> if the specified key is equal; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapInfo.ObjectFactory.Instance">
            <summary>
            Gets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.ObjectFactory.CreateImpl">
            <summary>
            Creates the impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfo.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapInfo.Name">
            <summary>
            The name of the MapInfo.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapInfo.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapInfo.Capabilities">
            <summary>
            Gets the capabilities.
            </summary>
            <value>The capabilities.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapInfo.CapabilitiesDesc">
            <summary>
            Gets the capabilities desc.
            </summary>
            <value>The capabilities desc.Null is possible.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.MapInfoCollection">
            <summary>
            SFC Collection class for MapInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Package)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.MapInfoCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.MapInfo"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfoCollection.Contains(System.String)">
            <summary>
            Determines whether the collection contains the Map.
            </summary>
            <param name="name">The name of the Map.</param>
            <returns>
            	<c>true</c> if the collection contains Map; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapInfoCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.MapValueInfo">
            <summary>
            MapValueInfo class represents a record in sys.dm_xe_map_values.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.MapValueInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.SetName(System.String)">
            <summary>
            Sets the name.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapValueInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key">
            <summary>
            Internal used Key class
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key,Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key,Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfo.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Name">
            <summary>
            Gets the name. The name of MapValueInfo equals to CAST(map_key as varchar(10))
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapValueInfo.Value">
            <summary>
            Gets or sets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.MapValueInfoCollection">
            <summary>
            This is collection class for MapValueInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.MapInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.MapValueInfoCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.MapValueInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.MapValueInfo"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfoCollection.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified name].
            </summary>
            <param name="name">The name.</param>
            <returns>
            	<c>true</c> if [contains] [the specified name]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.MapValueInfoCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Package">
            <summary>
            Metadata class for Package. 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Package.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Package"/> class.
            Empty constructor is an convention is a convention in SFC.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.SetName(System.String)">
            <summary>
            Set the name of package.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.CreateIdentityKey">
            <summary>
            Create a key from package name. The key is used by SFC framework. 
            </summary>
            <returns>a instance of Package.Key</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.IdentityKey">
            <summary>
            Key Property.
            </summary>
            <value>The identity key.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.GetChildCollection(System.String)">
            <summary>
            Return the child collection based on the element type.
            </summary>
            <param name="elementType">type of the collection element</param>
            <returns>child collection of the specify type</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.GetChildCollection``1">
            <summary>
             An internal wrapper over the corresponding protected method.
            </summary>
            <returns>child collection of the specified type</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.EventInfoSet">
            <summary>
            Collection of EventInfo.
            </summary> 
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.ActionInfoSet">
            <summary>
            Collection of ActionInfo.
            </summary>
            <value>The ActionInfo set.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.TargetInfoSet">
            <summary>
            Collection of TargetInfoSet.
            </summary>
            <value>The TargetInfo set.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.TypeInfoSet">
            <summary>
            
            </summary> 
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.MapInfoSet">
            <summary>
            Collection of MapInfo.
            </summary>
            <value>The MapInfo set.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.PredSourceInfoSet">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.PredCompareInfoSet">
            <summary>
            Collection of PredCompareInfo
            </summary>
            <value>The PredCompareInfo set.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Package.Key">
            <summary>
            A key class for identification.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.#ctor(Microsoft.SqlServer.Management.XEvent.Package.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Package.Key"/> class.
            </summary>
            <param name="guid">The module GUID.</param>
            <param name="name">The non-fully qualified package name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Package.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.Equals(System.Object,System.Object)">
            <summary>
            Equalses the specified obj1.
            </summary>
            <param name="obj1">The obj1.</param>
            <param name="obj2">The obj2.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Equalses the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.Package.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.Package.Key,System.Object)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.Package.Key,Microsoft.SqlServer.Management.XEvent.Package.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.Package.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.Package.Key,System.Object)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.Package.Key,Microsoft.SqlServer.Management.XEvent.Package.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.IsEqual(Microsoft.SqlServer.Management.XEvent.Package.Key)">
            <summary>
            Determines whether the specified key is equal.
            </summary>
            <param name="key">The key.</param>
            <returns>
                <c>true</c> if the specified key is equal; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.ObjectFactory.Instance">
            <summary>
            Gets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.ObjectFactory.CreateImpl">
            <summary>
            Creates the impl for the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Package.GetObjectFactory">
            <summary>
            Gets the object factory instance for Package.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.ID">
            <summary>
            Gets the ID.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.Name">
            <summary>
            Gets the name of the package.
            </summary>
            <value>The name of the package.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.Capabilities">
            <summary>
            Gets the capabilities.
            </summary>
            <value>The capabilities.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.CapabilitiesDesc">
            <summary>
            Gets the capabilities desc.
            </summary>
            <value>The capabilities desc.Null is possible.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.ModuleID">
            <summary>
            Gets the module ID.
            </summary>
            <value>The module ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Package.ModuleAddress">
            <summary>
            Gets the module address.
            </summary>
            <value>The module address.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PackageCollection">
            <summary>
            SFC Collection class for Package
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PackageCollection.#ctor(Microsoft.SqlServer.Management.XEvent.BaseXEStore)">
            <summary>
            Initialize a new instance of PackageCollection given the XEStore.
            </summary>
            <param name="parent"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PackageCollection.Item(System.Guid,System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.Package"/> 
               by module id and name.
            </summary>
            <param name="moduleID"> module id of the package</param>
            <param name="name">name of the package</param>
            <returns>package with the specified module id and name</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PackageCollection.Item(System.Guid)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.Package"/> with the specified package GUID.
            </summary>
            <param name="packageID">GUID of the package</param>
            <returns>package with the specified GUID</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PackageCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.Package"/> by name.
            </summary>
            <param name="name">just the package name, without the module id</param>
            <returns>Package with the specified name, if the name is unique</returns>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">if the package name is not unique</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PackageCollection.Contains(System.Guid,System.String)">
            <summary>
            Determines whether the collection contains Package.
            </summary>
            <param name="moduleID"> module id of the package</param>
            <param name="name">name of the package</param>
            <returns>
            	<c>true</c> if the collection contains Package; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PackageCollection.Contains(System.String)">
            <summary>
            Determines whether the collection contains Package.
            </summary>
            <param name="name">just the package name, without the module id</param>
            <returns>
            	<c>true</c> if the collection contains Package; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PackageCollection.GetElementFactoryImpl">
            <summary>
            Return the instance of object factory for Package.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PackageCollection.GetPackages(System.String)">
            <summary>
             returns list of all packages with the package name matching the given name
            </summary>        
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredCompareExpr">
            <summary>
            PredCompareExpr class represents expression like sqlserver.database_id=7
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredCompareExpr.ComparatorType">
            <summary>
            Define what comparator could be used between PredOperand and PredValue
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredCompareExpr.ComparatorType.EQ">
            <summary>
            =
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredCompareExpr.ComparatorType.NE">
            <summary>
            &lt;&gt;
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredCompareExpr.ComparatorType.GT">
            <summary>
            &gt;
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredCompareExpr.ComparatorType.GE">
            <summary>
            &gt;=
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredCompareExpr.ComparatorType.LT">
            <summary>
            &lt;
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredCompareExpr.ComparatorType.LE">
            <summary>
            &lt;=
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareExpr.#ctor(Microsoft.SqlServer.Management.XEvent.PredCompareExpr.ComparatorType,Microsoft.SqlServer.Management.XEvent.PredOperand,Microsoft.SqlServer.Management.XEvent.PredValue)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredCompareExpr"/> class.
            </summary>
            <param name="type">The type.</param>
            <param name="operand">The operand.</param>
            <param name="value">The value.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareExpr.Operand">
            <summary>
            Get the operand.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareExpr.Value">
            <summary>
            Get the value.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareExpr.Operator">
            <summary>
            Get the compare operator.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredCompareInfo">
            <summary>
            Metadata class for Pred_Compare
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredCompareInfo"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.SetName(System.String)">
            <summary>
            Sets the name.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns>A Pred_Compare has no child, so this function always throw the exception</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key">
            <summary>
            A key class for identification.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other">The other.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            Equalses the specified obj1.
            </summary>
            <param name="obj1">The obj1.</param>
            <param name="obj2">The obj2.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Equalses the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key,System.Object)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key,Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key,System.Object)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key,Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.IsEqual(Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key)">
            <summary>
            Determines whether the specified key is equal.
            </summary>
            <param name="key">The key.</param>
            <returns>
                <c>true</c> if the specified key is equal; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.ObjectFactory.Instance">
            <summary>
            Gets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.ObjectFactory.CreateImpl">
            <summary>
            Creates the impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Name">
            <summary>
            The name of the PredCompareInfo.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.TypeName">
            <summary>
            Gets the name of the type.
            </summary>
            <value>The name of the type.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.TypePackageID">
            <summary>
            Gets package ID of the type.
            </summary>
            <value>The package ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareInfo.TypePackageName">
            <summary>
            Gets the name of the type package.
            </summary>
            <value>The name of the type package.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredCompareInfoCollection">
            <summary>
            SFC Collection class for PredCompareInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Package)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredCompareInfoCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredCompareInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredCompareInfo"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfoCollection.Contains(System.String)">
            <summary>
            Determines whether the collection contains the Pred_Source.
            </summary>
            <param name="name">The name of the Pred_Source.</param>
            <returns>
            	<c>true</c> if the collection contains Pred_Source; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredCompareInfoCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredExpr">
            <summary>
            Base class for all of the predicate expression.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredFunctionExpr">
            <summary>
            Class for the function expression in a predicate. A function expression is like this:
            Pred_Compare(operand, value) where operand is an instance of PredOperand and value is an
            instance of PredValue.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredFunctionExpr.#ctor(Microsoft.SqlServer.Management.XEvent.PredCompareInfo,Microsoft.SqlServer.Management.XEvent.PredOperand,Microsoft.SqlServer.Management.XEvent.PredValue)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredFunctionExpr"/> class.
            </summary>
            <param name="func">The PredCompareInfo object represent the function name.</param>
            <param name="operand">The operand.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredFunctionExpr.CheckNotNull(Microsoft.SqlServer.Management.XEvent.MethodTraceContext,System.Object,System.String)">
            <summary>
            Helper function to check if a parameter is null.
            </summary>
            <param name="tm">The Trace context.</param>
            <param name="param">The parameter.</param>
            <param name="paramName">Name of the parameter.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredFunctionExpr.Operand">
            <summary>
            Get the operand.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredFunctionExpr.Value">
            <summary>
            Get the value.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredFunctionExpr.Operator">
            <summary>
            Get the pred_Compare instance.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Predicate">
            <summary>
            Predicate is the base class of all predicate related classes.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredLogicalExpr">
            <summary>
            PredLogicalExpr class can apply NOT on one PredExpr or AND/OR on two PredExprs
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredLogicalExpr.LogicalOperatorType">
            <summary>
            Define logical operator that could be used in PredLogicalExpr
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredLogicalExpr.LogicalOperatorType.Not">
            <summary>
            Not
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredLogicalExpr.LogicalOperatorType.And">
            <summary>
            Logical and
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredLogicalExpr.LogicalOperatorType.Or">
            <summary>
            Logical or
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredLogicalExpr.#ctor(Microsoft.SqlServer.Management.XEvent.PredLogicalExpr.LogicalOperatorType,Microsoft.SqlServer.Management.XEvent.PredExpr,Microsoft.SqlServer.Management.XEvent.PredExpr)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredLogicalExpr"/> class.
            </summary>
            <param name="type">The logical operator that will be applied on predExpr1 and predExpr2.</param>
            <param name="predExpr1">PredExpr1.</param>
            <param name="predExpr2">PredExpr2.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredLogicalExpr.LeftExpr">
            <summary>
            Get the left sub-expression.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredLogicalExpr.RightExpr">
            <summary>
            Get the right sub-expression.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredLogicalExpr.Operator">
            <summary>
            Get the logic operator.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredOperand">
            <summary>
            Class for predicate operand. A predicate operand can be an event column or
            a pred_source.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredOperand.name">
            <summary>
            name of the event field. 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredOperand.package">
            <summary>
            package has meaningful value only when the operand is a pred_source.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredOperand.operandObject">
            <summary>
            Store the DataEventColumnInfo or PredSourceInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredOperand.#ctor(Microsoft.SqlServer.Management.XEvent.DataEventColumnInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredOperand"/> class with event column.
            </summary>
            <param name="eventColumn">The event column.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredOperand.#ctor(Microsoft.SqlServer.Management.XEvent.PredSourceInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredOperand"/> class with Pred_source.
            </summary>
            <param name="sourceInfo">The source info.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredOperand.TypeName">
            <summary>
            Gets TypeName of the Operand.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredOperand.TypePackageId">
            <summary>
            Gets TypePackageID of the Operand.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredOperand.OperandObject">
            <summary>
            Get the object used to construct the operand. The object should be an instance of DataEventColumnInfo or PredSourceInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredOperand.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current PredOperand.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current PredOperand.
            </returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredSourceInfo">
            <summary>
            PredSourceInfo class represents pred_source objects in sys.dm_xe_objects.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.SetName(System.String)">
            <summary>
            Sets the name.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key">
            <summary>
            Internal used Key class
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key,Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key,Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.Description">
            <summary>
            Gets or sets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.TypeName">
            <summary>
            Gets or sets the name of the type.
            </summary>
            <value>The name of the type.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.TypePackageID">
            <summary>
            Gets package ID of the type.
            </summary>
            <value>The package ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredSourceInfo.TypePackageName">
            <summary>
            Gets the name of the type package.
            </summary>
            <value>The name of the type package.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredSourceInfoCollection">
            <summary>
            This is collection class for PredSourceInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Package)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredSourceInfoCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.PredSourceInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredSourceInfo"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfoCollection.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified name].
            </summary>
            <param name="name">The name.</param>
            <returns>
            	<c>true</c> if [contains] [the specified name]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredSourceInfoCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.PredValue">
            <summary>
            PredValue represents is rvalue in PredCompareExpr or second parameter in PredFunctionExpr
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredValue.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.PredValue"/> class with a Unicode string.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.PredValue.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.IXEStoreProvider">
            <summary>
              defines the interface that component providers need to implement
                for the XEStore, which is the root for all metadata classes and runtime classes.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.IXEStoreProvider.GetExecutionEngine">
            <summary>
            Gets the execution engine.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.IXEStoreProvider.GetConnection(Microsoft.SqlServer.Management.Sdk.Sfc.SfcObjectQueryMode)">
            <summary>
            Get the current connection to query on.
            Return a connection supporting either a single serial query or multiple simultaneously open queries as requested.
            </summary>
            <param name="mode"></param>
            <returns>The connection to use, or null to use Cache mode. Cache mode avoids connection and open data reader issues.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.IXEStoreProvider.DomainInstanceName">
            <summary>
            Gets the name of the domain instance.
            </summary>
            <value>The name of the domain instance.</value>        
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.IXEStoreProvider.GetComparer">
            <summary>
             Gets the comparer for the child collections
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ISessionProvider">
            <summary>
              defines the interface that component providers need to implement
                for the Session, the main object user code interacts with.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ISessionProvider.GetCreateScript">
            <summary>
              Script create for this session.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ISessionProvider.GetAlterScript">
            <summary>
            Script alter for this session.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ISessionProvider.GetDropScript">
            <summary>
            Scripts drop for this session
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ISessionProvider.ValidateAlter">
            <summary>
             backend specfic validations to Alter the session.
            </summary>
            <returns> true iff validation succeeds</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ISessionProvider.Start">
            <summary>
            Starts this session.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ISessionProvider.Stop">
            <summary>
            Stops this session.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.IEventProvider">
            <summary>
              defines the interface that component providers need to implement
                for the Event, the Runtime class for the Events.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.IEventProvider.GetCreateScript">
            <summary>
            Script create for this session.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.IEventProvider.GetDropScript">
            <summary>
            Scripts drop for this session
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ITargetProvider">
            <summary>
              defines the interface that component providers need to implement
                for the Target, the Runtime class for Target
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ITargetProvider.GetCreateScript">
            <summary>
            Script create for this session.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ITargetProvider.GetDropScript">
            <summary>
            Scripts drop for this session
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ITargetProvider.GetTargetData">
            <summary>
            Gets the target data.
            </summary>
            <returns>Target data xml string.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo">
            <summary>
            An EventInfo object may have three kinds of columns: customizable, data, readonly.
            A ReadOnlyEventColumnInfo object reprensents a readonly column of an EventInfo object.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.SetName(System.String)">
            <summary>
            Sets the name.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key,Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key,Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.ObjectFactory">
            <summary>
            Singleton class used by collection class
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.ID">
            <summary>
            Gets the ID.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.TypeName">
            <summary>
            Gets or sets the name of the type.
            </summary>
            <value>The name of the type.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.TypePackageID">
            <summary>
            Gets package ID of the type.
            </summary>
            <value>The package ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.TypePackageName">
            <summary>
            Gets the name of the type package.
            </summary>
            <value>The name of the type package.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Value">
            <summary>
            Gets the value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.Capabilities">
            <summary>
            Gets the capabilities.
            </summary>
            <value>The capabilities.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo.CapabilitiesDesc">
            <summary>
            Gets the capabilities description.
            </summary>
            <value>The capabilities description.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfoCollection">
            <summary>
            This is collection class for ReadOnlyEventColumnInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.EventInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfoCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfo"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfoCollection.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified name].
            </summary>
            <param name="name">The name.</param>
            <returns>
            	<c>true</c> if [contains] [the specified name]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ReadOnlyEventColumnInfoCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Session">
            <summary>
            Session is the main object user code interacts with.
            A Session object represents a row in sys.server_event_sessions and also includes some data from sys.dm_xe_sessions if it's started.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Session.EventCapabilities">
            <summary>
             Enumeration of the list of all possible event capabilities
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.MaxMemoryProperty">
            <summary>
            MaxMemory
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.EventRetentionModeProperty">
            <summary>
            EventRetentionMode
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.MaxDispatchLatencyProperty">
            <summary>
            MaxDispatchLatency
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.MaxEventSizeProperty">
            <summary>
            MaxEventSize
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.MemoryPartitionModeProperty">
            <summary>
            MemoryPartitionMode
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.TrackCausalityProperty">
            <summary>
            TrackCausality
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.AutoStartProperty">
            <summary>
            AutoStart
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.#ctor(Microsoft.SqlServer.Management.XEvent.BaseXEStore,System.String)">
            <summary>
            Mostly used constructor
            </summary> 
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.InitializeUIPropertyState">
            <summary>
            Initializes the state of the UI property.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.UpdateUIPropertyState">
            <summary>
            Updates the state of the UI property.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Validate(System.String)">
            <summary>
            Validates the specified method name.
            </summary>
            <param name="methodName">Name of the method, ValidationMethod.Create or ValidationMethod.Alter</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">Validation failed.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Validate(System.String,System.Object[])">
            <summary>
            Validates the specified method name.
            </summary>
            <param name="methodName">Name of the method.</param>
            <param name="arguments">The arguments.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Create">
            <summary>
            Create the session in the back-end server.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.ScriptCreate">
            <summary>
            Script create for this session.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Alter">
            <summary>
            Alter the session in the back-end server.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.ScriptAlter">
            <summary>
            Script alter for this session.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Drop">
            <summary>
            Drop the session in the back-end server.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.ScriptDrop">
            <summary>
            Scripts drop for this session
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Start">
            <summary>
            Starts this session.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Stop">
            <summary>
            Stops this session.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Session.Key">
            <summary>
            Internal used Key class
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.#ctor(Microsoft.SqlServer.Management.XEvent.Session.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Session.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.Session.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.Session.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.Session.Key,Microsoft.SqlServer.Management.XEvent.Session.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.Session.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.Session.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.Session.Key,Microsoft.SqlServer.Management.XEvent.Session.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Session.ObjectFactory">
            <summary>
            Singleton class used by collection class
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.ID">
            <summary>
            Gets the ID.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.Name">
            <summary>
            The name of the Session
            </summary>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">Set name for existing session or set name to null/empty string.</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.Events">
            <summary>
            Gets the event collection.
            </summary>
            <value>The event collection.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.Targets">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.IsRunning">
            <summary>
            Gets a value indicating whether this session is running.
            </summary>
            <value>
            	<c>true</c> if this session is running; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Session.EventRetentionModeEnum">
            <summary>
            Event retention mode describes how event loss is handled.        
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.EventRetentionModeEnum.AllowSingleEventLoss">
            <summary>
            Events can be lost from the session.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.EventRetentionModeEnum.AllowMultipleEventLoss">
            <summary>
            Full event buffers can be lost from the session.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.EventRetentionModeEnum.NoEventLoss">
            <summary>
            No event loss is allowed.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.EventRetentionMode">
            <summary>
            Gets or sets the event retention mode.
            </summary>
            <value>The event retention mode.</value>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">Set EventRetentionMode to an unknown value.</exception>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.InfiniteDispatchLatency">
            <summary>
            0 indicates that dispatch latency is infinite.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.DefaultDispatchLatency">
            <summary>
            Default dispatch latency is 30 seconds.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.MaxDispatchLatency">
            <summary>
            Gets or sets the max dispatch latency (in seconds).
            </summary>
            <value>The max dispatch latency.</value>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.DefaultMaxMemory">
            <summary>
            The maximum amount of memeory by default is 4 MB.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.MaxMemory">
            <summary>
            Gets or sets the max memory (in KB).
            </summary>
            <value>The max memory.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.MaxEventSize">
            <summary>
            Gets or sets the size (in KB) of the max event.
            </summary>
            <value>The size of the max event.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Session.MemoryPartitionModeEnum">
            <summary>
            Memory partition mode describes the location in memory where event buffers are created.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.MemoryPartitionModeEnum.None">
            <summary>
            A single set of buffers are created within a SQL Server instance.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.MemoryPartitionModeEnum.PerNode">
            <summary>
            A set of buffers is created for each non-uniform memory access (NUMA) node.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.MemoryPartitionModeEnum.PerCpu">
            <summary>
            A set of buffers is created for each CPU.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.MemoryPartitionMode">
            <summary>
            Gets or sets the memory partition mode.
            </summary>
            <value>The memory partition mode.</value>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">Set MemoryPartitionMode to an unknown value.</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.TrackCausality">
            <summary>
            Gets or sets a value indicating whether [track causality].
            </summary>
            <value><c>true</c> if [track causality]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.AutoStart">
            <summary>
            Gets or sets a value indicating whether [auto start].
            </summary>
            <value><c>true</c> if [auto start]; otherwise, <c>false</c>.</value>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Session.NotStarted">
            <summary>
            Session is currently not running.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.StartTime">
            <summary>
            Gets the start time.
            </summary>
            <value>The start time.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Session.State">
            <summary>
            State of the object, used in Alter function in session.
            </summary>
            <value>The state.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.AddEvent(Microsoft.SqlServer.Management.XEvent.EventInfo)">
            <summary>
            New an event and add it to this session.
            </summary>
            <param name="eventInfo">The event info.</param>
            <returns>The newly created event object.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.AddEvent(System.String)">
            <summary>
            New an event from fully qualified event name and add it to this session.
            </summary>
            <param name="eventName">Name of the event.</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">Event name is malformed or wrong.</exception>
            <exception cref="T:System.ArgumentNullException">Parameter eventName is null</exception>
            <returns>The newly created event object.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.RemoveEvent(Microsoft.SqlServer.Management.XEvent.Event)">
            <summary>
            Removes the event object from the session.
            </summary>
            <param name="evt">The event object.</param>
            <exception cref="T:System.ArgumentNullException">Parameter evt is null.</exception>
            <returns>Returns whether the event is successfully removed.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.AddTarget(Microsoft.SqlServer.Management.XEvent.TargetInfo)">
            <summary>
            New a target and add it to this session.
            </summary>
            <param name="targetInfo">The target info.</param>
            <returns>The newly created target object.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.AddTarget(System.String)">
            <summary>
            New a target from fully qualified target name and add it to this session .
            </summary>
            <param name="targetName">Name of the target.</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">Target name is malformed or wrong.</exception>
            <exception cref="T:System.ArgumentNullException">Parameter targetName is null</exception>
            <returns>The newly created target object.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.RemoveTarget(Microsoft.SqlServer.Management.XEvent.Target)">
            <summary>
            Removes the target object from this session.
            </summary>
            <param name="target">The target object.</param>
            <exception cref="T:System.ArgumentNullException">Parameter target is null.</exception>
            <returns>Returns whether the target is successfully removed.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Session.IsDirty">
            <summary>
               Checks if the session's state is dirty
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ISessionObject">
            <summary>
              common interface part of Events and Targets
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ISessionObject.State">
            <summary>
            Gets Session state.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ISessionObject.GetCreateScript">
            <summary>
            Gets Create script for the Session.
            </summary>
            <returns>A string containting the script.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ISessionObject.GetDropScript">
            <summary>
            Gets Drop script for the Session.
            </summary>
            <returns>A string containting the script.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ISessionObject.IsDirty">
            <summary>
            Indicates whether the Session is Dirty.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.SessionCollection">
            <summary>
            This is the collection for Sessions.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionCollection.#ctor(Microsoft.SqlServer.Management.XEvent.BaseXEStore)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.SessionCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.SessionCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.Session"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionCollection.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified name].
            </summary>
            <param name="name">The name.</param>
            <returns>
            	<c>true</c> if [contains] [the specified name]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.SessionProviderBase">
            <summary>
            Sql provider for Session.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionProviderBase.#ctor(Microsoft.SqlServer.Management.XEvent.Session,System.String)">
            <summary>
            Constructs a new SessionProviderBase for the given session. 
            </summary>
            <param name="session"></param>
            <param name="scopeName">Holds a value of either "DATABASE" or "SERVER"</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionProviderBase.GetCreateScript">
            <summary>
            Script Create for this session.
            </summary>
            <returns>Session Create script.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionProviderBase.GetAlterScript">
            <summary>
            Script Alter for this session.
            </summary>
            <returns>Session Alter script.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionProviderBase.GetDropScript">
            <summary>
            Scripts Drop for this session.
            </summary>
            <returns>Session Drop script.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionProviderBase.ValidateAlter">
            <summary>
             Backend specfic validations to Alter the session.
             NB: In case of sql engine the Alter statement is not atomic
             so it uses a dummy session to validate before executing alter on the actual session.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionProviderBase.Start">
            <summary>
            Starts this session.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionProviderBase.Stop">
            <summary>
            Stops this session.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionProviderBase.CreateScript(System.String)">
            <summary>
            Generates a script to create the XE session for the selected targets and with the selected options
            </summary>
            <param name="createStatment"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionProviderBase.GetCreateScript(System.String)">
            <summary>
            Script Create for this session.
            </summary>
            <param name="sessionName">A session name.</param>
            <returns>Session Create script.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionProviderBase.GetOptionString(System.Boolean)">
            <summary>
            Gets the session option string.
            </summary>
            <param name="create">Create flag.</param>
            <returns>Session option string.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.SessionProviderBase.AppendOptionsAlterScript(System.Text.StringBuilder)">
            <summary>
            Appends options for the session to the alter session script
            </summary>
            <param name="sessionAlterScripts"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Target">
            <summary>
            Runtime class for Target. Each instance of this class represents a row in sys.server_event_session_targets.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Target.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Target"/> class.
            Empty constructor is an convention is a convention in SFC.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.#ctor(Microsoft.SqlServer.Management.XEvent.Session,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Target"/> class with given parent and name.
            </summary>
            <param name="parent">The parent.</param>
            <param name="name">The full qulified name of the Target.</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">The target name is malformed or wrong.</exception>
            <exception cref="T:System.ArgumentNullException">Parameter name is null</exception>
            <exception cref="T:System.NullReferenceException">The parent of Session is not set yet.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.#ctor(Microsoft.SqlServer.Management.XEvent.Session,Microsoft.SqlServer.Management.XEvent.TargetInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Event"/> class from an TargetInfo object.
            </summary>
            <param name="parent">The parent.</param>
            <param name="targetInfo">The target info.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.SetTargetInfo(Microsoft.SqlServer.Management.XEvent.TargetInfo)">
            <summary>
            Set the TargetInfo for a pending Target.
            </summary>
            <param name="targetInfo"></param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">if the Target object is not in pending state.</exception>
            <exception cref="T:System.ArgumentNullException">if the input targetInfo is null.</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.Parent">
            <summary>
            Parent Property for Event.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.SetPackage(System.String)">
            <summary>
            Sets the package.
            </summary>
            <param name="packageName">Name of the package.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.GetChildCollection(System.String)">
            <summary>
            Return child collection based on element type. Event is the parent of EventColumn.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.GetTargetData">
            <summary>
            Gets the target data.
            </summary>
            <returns>Target data xml string.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.TargetFields">
            <summary>
            Gets the target column info set.
            </summary>
            <value>The target column info set.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Target.Key">
            <summary>
            A key class for identification.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.Target.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.#ctor(Microsoft.SqlServer.Management.XEvent.Target.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Target.Key"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.Key.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.Target.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.Equals(System.Object,System.Object)">
            <summary>
            Equalses the specified obj1.
            </summary>
            <param name="obj1">The obj1.</param>
            <param name="obj2">The obj2.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Equalses the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.Target.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.Target.Key,System.Object)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.Target.Key,Microsoft.SqlServer.Management.XEvent.Target.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.Target.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.Target.Key,System.Object)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.Target.Key,Microsoft.SqlServer.Management.XEvent.Target.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.Target.ObjectFactory">
            <summary>
            Singleton factory class for Event
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.ObjectFactory.Instance">
            <summary>
            Gets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.ObjectFactory.CreateImpl">
            <summary>
            Creates the impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.Name">
            <summary>
            The name of the Target
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.ID">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.ModuleID">
            <summary>
            Gets the module ID.
            </summary>
            <value>The module ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.PackageName">
            <summary>
            Gets the package name that the event belongs to.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.Description">
            <summary>
            Gets or sets target description. Set accessor is for internal use only.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.ScriptName">
            <summary>
            Gets Name formatted for scripting.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Microsoft#SqlServer#Management#XEvent#ISessionObject#GetCreateScript">
            <summary>
            Generate the script for add an target. Used in Create Session.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.HasCustomizableField">
            <summary>
            Determines whether the target has at least one field needs to be set.
            </summary>
            <returns>
            True if at least customizable fields exist. False otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.Microsoft#SqlServer#Management#XEvent#ISessionObject#GetDropScript">
            <summary>
            Generating the script for drop the event. Used in Alter Session.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.Target.State">
            <summary>
            State of the object, used in Alter function in session.
            </summary>
            <value>The state.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.Target.GetTypeMetadata">
            <summary>
            Gets Sfc Type Metadata.
            </summary>
            <returns>Type Metadata.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TargetCollection">
            <summary>
            SFC Collection class for Target.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Session)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.Target"/> with the specified name.
            </summary>
            <value>name of the Target</value>
            <returns>Target with the specify name</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetCollection.Contains(System.String)">
            <summary>
            Determines whether the collection contains the Target.
            </summary>
            <param name="name">The name of the Target.</param>
            <returns>
                <c>true</c> if the collection contains Target; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetCollection.AppendAlterScripts(System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            Appends Alter scripts to given script.
            </summary>
            <param name="addScript">Add script.</param>
            <param name="dropScript">Drop script.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo">
            <summary>
            TargetInfo currently has only one kind of column: customizable column.
            A TargetColumnInfo object reprensents a customizable column of a TargetInfo object.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.SetName(System.String)">
            <summary>
            Sets the name.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns>A target column has no child, so this function always throw the exception</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key">
            <summary>
            A key class for identification.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other">The other.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            Equalses the specified obj1.
            </summary>
            <param name="obj1">The obj1.</param>
            <param name="obj2">The obj2.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Equalses the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key,System.Object)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key,Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key,System.Object)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key,Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.IsEqual(Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key)">
            <summary>
            Determines whether the specified key is equal.
            </summary>
            <param name="key">The key.</param>
            <returns>
            	<c>true</c> if the specified key is equal; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.ObjectFactory.Instance">
            <summary>
            Gets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.ObjectFactory.CreateImpl">
            <summary>
            Creates the impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Name">
            <summary>
            The name of the TarColumnInfo.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.ID">
            <summary>
            Gets the ID.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Value">
            <summary>
            Gets the value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description for the column. Null is possible.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.TypeName">
            <summary>
            Gets the name of the type.
            </summary>
            <value>The name of the type.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.TypePackageID">
            <summary>
            Gets package ID of the type.
            </summary>
            <value>The package ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.TypePackageName">
            <summary>
            Gets the name of the type package.
            </summary>
            <value>The name of the type package.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.Capabilities">
            <summary>
            Gets the capabilities.
            </summary>
            <value>The capabilities.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo.CapabilitiesDesc">
            <summary>
            Gets the capabilities description.
            </summary>
            <value>The capabilities description.Null is possible.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TargetColumnInfoCollection">
            <summary>
            SFC Collection class for TargetColumnInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.TargetInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetColumnInfoCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetColumnInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetColumnInfo"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfoCollection.Contains(System.String)">
            <summary>
            Determines whether the collection contains the TargetColumn.
            </summary>
            <param name="name">The name of the TargetColumn.</param>
            <returns>
            	<c>true</c> if the collection contains TargetColumn; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetColumnInfoCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TargetField">
            <summary>
            A TargetField object reprensents a row in sys.server_event_session_fields.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.TargetField.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.#ctor(Microsoft.SqlServer.Management.XEvent.Target,Microsoft.SqlServer.Management.XEvent.TargetColumnInfo)">
            <summary>
            Constructor takes a Target and a name as parameters
            </summary>
            <param name="parent">The parent.</param>
            <param name="targetColumnInfo">The target column info.</param>
            <exception cref="T:System.ArgumentNullException">Parameter parent or targetColumnInfo is null.</exception>
            <exception cref="T:System.NullReferenceException">targetColumnInfo's parent(or grandparent) is null. Mostly because it's not enumerated correctly.</exception>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">Parameter targetColumnInfo is invalid.</exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetField.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetField.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TargetField.Key">
            <summary>
            A key class for identification.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.TargetField.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.#ctor(Microsoft.SqlServer.Management.XEvent.TargetField.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other">The other.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetField.Key"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetField.Key.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetField.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.Equals(System.Object,System.Object)">
            <summary>
            Equalses the specified obj1.
            </summary>
            <param name="obj1">The obj1.</param>
            <param name="obj2">The obj2.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Equalses the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.TargetField.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.TargetField.Key,System.Object)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.TargetField.Key,Microsoft.SqlServer.Management.XEvent.TargetField.Key)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.TargetField.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="obj">The obj.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.TargetField.Key,System.Object)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="obj">The obj.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.TargetField.Key,Microsoft.SqlServer.Management.XEvent.TargetField.Key)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="leftOperand">The left operand.</param>
            <param name="rightOperand">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.IsEqual(Microsoft.SqlServer.Management.XEvent.TargetField.Key)">
            <summary>
            Determines whether the specified key is equal.
            </summary>
            <param name="key">The key.</param>
            <returns>
            	<c>true</c> if the specified key is equal; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetField.ObjectFactory.Instance">
            <summary>
            Gets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.ObjectFactory.CreateImpl">
            <summary>
            Creates the impl.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.GetObjectFactory">
            <summary>
            Gets the object factory.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetField.ID">
            <summary>
            Gets the ID.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetField.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetField.Value">
            <summary>
            Gets the value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetField.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description of the field. Could be null.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetField.State">
            <summary>
            State of the object, used in Alter function in session.
            </summary>
            <value>The state.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetField.GetTypeMetadata">
            <summary>
            Gets Sfc Type Metadata.
            </summary>
            <returns>Type Metadata.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TargetFieldCollection">
            <summary>
            SFC Collection class for TargetField.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetFieldCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Target)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetFieldCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetFieldCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetField"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetFieldCollection.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified name].
            </summary>
            <param name="name">The name.</param>
            <returns>
            	<c>true</c> if [contains] [the specified name]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetFieldCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TargetInfo">
            <summary>
            TargetInfo class represents target objects in sys.dm_xe_objects.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.TargetInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetInfo.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.SetName(System.String)">
            <summary>
            Sets the name.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key">
            <summary>
            Internal used Key class
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.TargetInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.TargetInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.TargetInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.TargetInfo.Key,Microsoft.SqlServer.Management.XEvent.TargetInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.TargetInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.TargetInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.TargetInfo.Key,Microsoft.SqlServer.Management.XEvent.TargetInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfo.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetInfo.Name">
            <summary>
            The name of the TargetInfo
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetInfo.Description">
            <summary>
            Gets or sets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetInfo.Capabilities">
            <summary>
            Gets the capabilities.
            </summary>
            <value>The capabilities.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetInfo.CapabilitiesDesc">
            <summary>
            Gets the capabilities description.
            </summary>
            <value>The capabilities description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetInfo.TargetColumnInfoSet">
            <summary>
            Gets the target column info set.
            </summary>
            <value>The target column info set.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TargetInfoCollection">
            <summary>
            This is the collection for TargetInfo objects.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Package)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetInfoCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TargetInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.TargetInfo"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfoCollection.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified name].
            </summary>
            <param name="name">The name.</param>
            <returns>
            	<c>true</c> if [contains] [the specified name]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TargetInfoCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TypeInfo">
            <summary>
            TypeInfo class represents type objects in sys.dm_xe_objects.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.TypeInfo.TypeTypeName">
            <summary>
            Type name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TypeInfo.Parent">
            <summary>
            Gets or sets the parent.
            </summary>
            <value>The parent.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.SetName(System.String)">
            <summary>
            Sets the name.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.CreateIdentityKey">
            <summary>
            Creates the identity key.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TypeInfo.IdentityKey">
            <summary>
            Gets the identity key.
            </summary>
            <value>The identity key.</value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.GetChildCollection(System.String)">
            <summary>
            Gets the child collection.
            </summary>
            <param name="elementType">Type of the element.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key">
            <summary>
            Internal used Key class
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.#ctor(Microsoft.SqlServer.Management.XEvent.TypeInfo.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key"/> class.
            </summary>
            <param name="filedDict">A set of name-value pairs that represent Urn fragment.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.TypeInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.TypeInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.TypeInfo.Key,Microsoft.SqlServer.Management.XEvent.TypeInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.TypeInfo.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.TypeInfo.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.TypeInfo.Key,Microsoft.SqlServer.Management.XEvent.TypeInfo.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfo.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TypeInfo.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TypeInfo.Description">
            <summary>
            Gets or sets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TypeInfo.Size">
            <summary>
            Gets the size.
            </summary>
            <value>The size.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TypeInfo.Capabilities">
            <summary>
            Gets the capabilities.
            </summary>
            <value>The capabilities.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TypeInfo.CapabilitiesDesc">
            <summary>
            Gets the capabilities description.
            </summary>
            <value>The capabilities description.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.TypeInfoCollection">
            <summary>
            This is collection class for TypeInfo.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfoCollection.#ctor(Microsoft.SqlServer.Management.XEvent.Package)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.TypeInfoCollection"/> class.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.TypeInfoCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.TypeInfo"/> with the specified name.
            </summary>
            <value></value>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfoCollection.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified name].
            </summary>
            <param name="name">The name.</param>
            <returns>
            	<c>true</c> if [contains] [the specified name]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.TypeInfoCollection.GetElementFactoryImpl">
            <summary>
            Gets the element factory impl.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo">
            <summary>
              interface for the child objects of Package
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo.Name">
            <summary>
            The name of the EventInfo
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>        
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.IXEObjectInfoCollection`1.Item(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> with the specified name.
            </summary>
            <value>name of the Object</value>
            <returns>Object with the specify name</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.BaseXEStore">
            <summary>
            XEStore is the root for all metadata classes and runtime classes.
            </summary>
            <summary>
            XEStore is the root for all metadata classes and runtime classes.
            </summary>
            <summary>
            XEStore is the root for all metadata classes and runtime classes.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.GetStoreProvider">
            <summary>
            Gets provider to perform Store operations.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.GetSessionProivder(Microsoft.SqlServer.Management.XEvent.Session)">
            <summary>
            Gets provider to perform Session operations.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.GetTargetProvider(Microsoft.SqlServer.Management.XEvent.Target)">
            <summary>
            Gets provider to perform Target operations.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.GetEventProvider(Microsoft.SqlServer.Management.XEvent.Event)">
            <summary>
            Gets provider to perform Event operations.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.GetSessionProviderInternal(Microsoft.SqlServer.Management.XEvent.Session)">
            <summary>
            Gets provider to perform Session operations.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.GetTargetProviderInternal(Microsoft.SqlServer.Management.XEvent.Target)">
            <summary>
            Gets provider to perform Target operations.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.GetEventProviderInternal(Microsoft.SqlServer.Management.XEvent.Event)">
            <summary>
            Gets provider to perform Event operations.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.#ctor(Microsoft.SqlServer.Management.Common.ISfcConnection)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.BaseXEStore"/> class.
            </summary>
            <param name="connection">The connection.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.#ctor">
            <summary>
            Don't ever call this, or if you do remember to set SfcConnection
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Name">
            <summary>
            Gets the name of XEStore.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ServerName">
            <summary>
            Gets the name of XEStore.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Sessions">
            <summary>
            Gets the sessions.
            </summary>
            <value>The sessions.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Packages">
            <summary>
            Gets the packages.
            </summary>
            <value>The packages.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.RunningSessionCount">
            <summary>
            Gets the running session count.
            </summary>
            <value>The running session count.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Package0Package">
            <summary>
            Gets the package0 package.
            </summary>
            <value>The package0 package.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.HistogramTargetInfo">
            <summary>
            Gets the histogram target info.
            </summary>
            <value>The histogram target info.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.EventFileTargetInfo">
            <summary>
            Gets the event_file target info.
            </summary>
            <value>The event_file target info.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.EtwClassicSyncTargetInfo">
            <summary>
            Gets the etw_classic_sync_target target info.
            </summary>
            <value>The etw_classic_sync_target target info.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.PairMatchingTargetInfo">
            <summary>
            Gets the pair_matching target info.
            </summary>
            <value>The pair_matching target info.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.RingBufferTargetInfo">
            <summary>
            Gets the ring_buffer target info.
            </summary>
            <value>The ring_buffer target info.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.EventCounterTargetInfo">
            <summary>
            Gets the event_counter target info.
            </summary>
            <value>The event_counter target info.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#GetConnection">
            <summary>
            Get the current connection to query on.
            Return a connection supporting a single serial query, so the query must end before another one may begin.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#SetConnection(Microsoft.SqlServer.Management.Common.ISfcConnection)">
            <summary>
            Sets the active connection.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.SetConnection(Microsoft.SqlServer.Management.Common.ISfcConnection)">
            <summary>
            Sets the active connection.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#GetConnection(Microsoft.SqlServer.Management.Sdk.Sfc.SfcObjectQueryMode)">
            <summary>
            Get the current connection to query on.
            Return a connection supporting either a single serial query or multiple simultaneously open queries as requested.
            </summary>
            <param name="mode"></param>
            <returns>The connection to use, or null to use Cache mode. Cache mode avoids connection and open data reader issues.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#ConnectionContext">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ToString">
            <summary>
            The string identity of a policy store is the associated Server name.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.IdentityKey">
            <summary>
             This is used by SFC.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.CreateIdentityKey">
            <summary>
            This is used by SFC.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.GetChildCollection(System.String)">
            <summary>
            This is used by SFC.
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.GetComparer">
            <summary>
             Gets the comparer for the child collections
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.OriginalConnection">
            <summary>
            Gets connection used to instantiate the store
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.InitConnection">
            <summary>
            Initializes connections and related objects.
            Should not be called directly.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.SfcConnection">
            <summary>
            Gets or sets the SQL store connection.
            </summary>
            <value>The SQL store connection.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.SfcObjectQuery">
            <summary>
            Gets the SFC object query.
            </summary>
            <value>The SFC object query.</value>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetType(System.String)">
            <summary>
            Gets the type.
            </summary>
            <param name="typeName">Name of the type.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetKey(Microsoft.SqlServer.Management.Sdk.Sfc.IUrnFragment)">
            <summary>
            returns the Key object given Urn fragment
            </summary>
            <param name="urnFragment">The urn fragment.</param>
            <returns>SfcKey</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ExecutionEngine">
            <summary>
            Gets ExecutionEngine to perform operations on the Store.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetExecutionEngine">
            <summary>
            Gets the execution engine.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomainLite#GetLogicalVersion">
            <summary>
            Returns the logical version of the domain
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomainLite#DomainName">
            <summary>
            Gets the name of the domain.
            </summary>
            <value>The name of the domain.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomainLite#DomainInstanceName">
            <summary>
            Gets the name of the domain instance.
            </summary>
            <value>The name of the domain instance.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.GetHashCode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.op_Equality(Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key,Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.op_Inequality(Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key,Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.FormatFieldValue(System.String,System.Guid,System.String)">
            <summary>
            Formats the field value based on the type information.
            </summary>
            <param name="fieldValue"> string represenation of Value of EventField, TargetField or PredValue </param>
            <param name="typePackageID"> identity of the package containing the type name</param>
            <param name="typeName"> represents the corresponding managed type</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.FormatPredicateExpression(Microsoft.SqlServer.Management.XEvent.PredExpr)">
            <summary>
            Gets the string representation of the predicate expression.
            </summary>
            <param name="predExpr"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.StoreProvider">
            <summary>
            Gets provider for store operations.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ObjectInfoSet">
            <summary>
            Gets <see cref="T:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ObjectMetadata"/> for the Store.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ObjectMetadata">
            <summary>
            Provides helpers methods over the metadata hierarchy objects        
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ObjectMetadata.#ctor(Microsoft.SqlServer.Management.XEvent.BaseXEStore)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ObjectMetadata"/> class.
            </summary>
            <param name="store">A store - source of metadata.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ObjectMetadata.GetAll``1(System.String)">
            <summary>
             Returns a collection of all the <see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given name.
            </summary>
            <typeparam name="T">Type of object to enumerate.</typeparam>
            <param name="name">format: package_name.object_name; NB: the first part of the name is optional</param>
            <exception cref="T:System.ArgumentNullException">if the name provided is null.</exception>
            <returns>A collection of all the <see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given name.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ObjectMetadata.GetAll``1(System.String,System.String)">
            <summary>
            Returns a collection of the <see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given package name, object name.
            </summary>
            <typeparam name="T">Type of object to enumerate.</typeparam>
            <param name="pkgName">Package name.</param>
            <param name="objName">Object name.</param>
            <returns>A collection of the <see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given package name, object name.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ObjectMetadata.Get``1(System.String)">
            <summary>
             Returns <see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given name, if it exists and unique.
            </summary>
            <typeparam name="T">Type of object to get.</typeparam>
            <param name="name">format: [module_guid].package_name.object_name; NB: the first two parts of the name are optional</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">if the object does not exist, or if the object name is not unique</exception>
            <returns><see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given name.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ObjectMetadata.Get``1(System.String,System.String)">
            <summary>
             Returns <see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given package name and object name, if it exists and unique.
            </summary>
            <typeparam name="T">Type of object to get.</typeparam>
            <param name="pkgName">Package name.</param>
            <param name="objName">Object name.</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">if the object does not exist, or if the object name is not unique</exception>
            <returns><see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given package name and object name.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ObjectMetadata.Get``1(System.Guid,System.String)">
            <summary>
             Returns <see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given module id and name, if it exists and unique.
            </summary>
            <typeparam name="T">Type of object to get.</typeparam>
            <param name="moduleID">Module ID.</param>
            <param name="name">Format: package_name.object_name; both parts must be specified.</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">if the object does not exist, or if the object name is not unique.</exception>
            <returns><see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given module id and name.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.ObjectMetadata.Get``1(System.Guid,System.String,System.String)">
            <summary>
             Returns <see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given module id, package name and object name, if it exists and unique.
            </summary>
            <typeparam name="T">Type of object to get.</typeparam>
            <param name="moduleID">Module ID.</param>
            <param name="pkgName">Package name.</param>
            <param name="objName">Object name.</param>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">if the object does not exist, or if the object name is not unique.</exception>
            <returns><see cref="T:Microsoft.SqlServer.Management.XEvent.IXEObjectInfo"/> matching the given module id, package name and object name.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.SaveSessionToTemplate(Microsoft.SqlServer.Management.XEvent.Session,System.String,System.Boolean)">
            <summary>
            Saves the session to template.
            </summary>
            <param name="session">The session.</param>
            <param name="fileName">Name of the file.</param>
            <param name="overwrite">if set to <c>true</c> [overwrite].</param>
            <exception cref="T:System.IO.IOException">The fileName includes an incorrect or invalid syntax for file name, directory name, or volume label syntax.</exception>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">Parameters are wrong or failed to save session.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.CreateSession(System.String)">
            <summary>
            A wrapper for Session constructor to avoid accidentally passing an wrong parent.
            </summary>
            <param name="sessionName">Name of the session.</param>
            <returns>A new Session.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.BaseXEStore.CreateSessionFromTemplate(System.String,System.String)">
            <summary>
            Creates a session from template.
            </summary>
            <param name="sessionName">Name of the session.</param>
            <param name="fileName">Name of the file.</param>
            <exception cref="T:System.UnauthorizedAccessException">The template file can't be accessed.</exception>
            <exception cref="T:System.Xml.XmlException">The template file is malformed.</exception>
            <exception cref="T:System.Xml.Schema.XmlSchemaValidationException">The template file doesn't conform to the schema.</exception>
            <exception cref="T:Microsoft.SqlServer.Management.XEvent.XEventException">Parameters are wrong or failed to create session.</exception>
            <returns>A new Session.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.XEUtils">
            <summary>
             Provides helper methods for scripting.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEUtils.AppendAlterScripts``1(System.Text.StringBuilder,System.Text.StringBuilder,System.Collections.Generic.IEnumerable{``0},Microsoft.SqlServer.Management.XEvent.Session)">
            <summary>
            Check the current state of the events and return the script based on it.
            This should be used only in generating the alter script for session.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEUtils.ToBeCreated(Microsoft.SqlServer.Management.Sdk.Sfc.SfcObjectState)">
            <summary>
            Indicates whether given state is for creation
            </summary>
            <param name="state">Object state.</param>
            <returns>True if object to be created.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEUtils.ConvertToXsdEnumerationValue(Microsoft.SqlServer.Management.XEvent.Session.EventRetentionModeEnum)">
            <summary>
            Converts <see cref="T:Microsoft.SqlServer.Management.XEvent.Session.EventRetentionModeEnum"/> to string defined in XSD.
            </summary>
            <param name="retentionMode">Value to convert.</param>
            <returns>XSD defined mode string.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEUtils.ConvertToXsdEnumerationValue(Microsoft.SqlServer.Management.XEvent.Session.MemoryPartitionModeEnum)">
            <summary>
            Converts <see cref="T:Microsoft.SqlServer.Management.XEvent.Session.MemoryPartitionModeEnum"/> to string defined in XSD.
            </summary>
            <param name="partitionMode">Value to convert.</param>
            <returns>XSD defined mode string.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.XEventException">
            <summary>
            Base exception class for all XEvent exception classes
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEventException.#ctor">
            <summary>
            Base constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEventException.#ctor(System.String)">
            <summary>
            Base constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEventException.#ctor(System.String,System.Exception)">
            <summary>
            Base constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEventException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Base constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEventException.Init">
            <summary>
            Initializes instance properties
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.XEventException.ProdVer">
            <summary>
            Product Version
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEventException.SetHelpContext(System.String)">
            <summary>
            Sets Help Context
            </summary>
            <param name="resource"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.XEventException.HelpLink">
            <summary>
            will output a link to the help web site
            <!--http://www.microsoft.com/products/ee/transform.aspx?ProdName=Microsoft%20SQL%20Server&ProdVer=09.00.0000.00&EvtSrc=MSSQLServer&EvtID=15401-->
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.XsdResource">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.XsdResource.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.XsdResource.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.XsdResource.xeconfig">
             <summary>
               Looks up a localized string similar to &lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;
            &lt;xs:schema targetNamespace=&quot;http://schemas.microsoft.com/sqlserver/2008/07/extendedeventsconfig&quot; elementFormDefault=&quot;qualified&quot; xmlns=&quot;http://schemas.microsoft.com/sqlserver/2008/07/extendedeventsconfig&quot; xmlns:xs=&quot;http://www.w3.org/2001/XMLSchema&quot;&gt;
            	&lt;xs:simpleType name=&quot;retentionModes&quot;&gt;
            		&lt;xs:annotation&gt;
            			&lt;xs:documentation xml:lang=&quot;en&quot;&gt;
            				retention modes supported
            			&lt;/xs:documentation&gt;
            		&lt;/xs:annotation&gt;
            		&lt;xs:restriction base=&quot;xs:string&quot;&gt;
            			&lt;xs:enumeration value=&quot;allowSingleEventLo [rest of string was truncated]&quot;;.
             </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ServerEventProvider">
            <summary>
            Sql provider for Event.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ServerEventProvider.GetCreateScript">
            <summary>
            Script Create for the Event.
            </summary>
            <returns>Event Create script.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ServerEventProvider.GetDropScript">
            <summary>
            Scripts Drop for this event.
            </summary>
            <returns>Event Drop script.</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ServerSessionProvider">
            <summary>
            Sql provider for Session at server scope.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ServerSessionProvider.#ctor(Microsoft.SqlServer.Management.XEvent.Session)">
            <summary>
            Constructs a ServerSessionProvider
            </summary>
            <param name="session"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ServerTargetProvider">
            <summary>
            Provider for Target.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ServerTargetProvider.GetCreateScript">
            <summary>
            Script Create for the Target.
            </summary>
            <returns>Target Create script.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ServerTargetProvider.GetDropScript">
            <summary>
            Scripts Drop for this Target.
            </summary>
            <returns>Target Drop script.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ServerTargetProvider.GetTargetData">
            <summary>
            Gets the target data.
            </summary>
            <returns>Target data xml string.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ServerTargetProvider.ServerConnection">
            <summary>
            Gets the underlying ServerConnection
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.XEStore">
            <summary>
            XEStore is the root for all metadata classes and runtime classes.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.XEvent.XEStore.TypeTypeName">
            <summary>
            Type name.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.#ctor">
            <summary>
            Don't ever call this, or if you do remember to set SfcConnection
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.#ctor(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Management.XEvent.XEStore"/> class.
            </summary>
            <param name="connection">The connection.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ToString">
            <summary>
            The string identity of a policy store is the associated Server name.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.CreateIdentityKey">
            <summary>
            This is used by SFC.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.XEStore.IdentityKey">
            <summary>
             This is used by SFC.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#GetConnection">
            <summary>
            Get the current connection to query on.
            Return a connection supporting a single serial query, so the query must end before another one may begin.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#SetConnection(Microsoft.SqlServer.Management.Common.ISfcConnection)">
            <summary>
            Sets the active connection.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#GetConnection(Microsoft.SqlServer.Management.Sdk.Sfc.SfcObjectQueryMode)">
            <summary>
            Get the current connection to query on.
            Return a connection supporting either a single serial query or multiple simultaneously open queries as requested.
            </summary>
            <param name="mode"></param>
            <returns>The connection to use, or null to use Cache mode. Cache mode avoids connection and open data reader issues.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.XEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#ConnectionContext">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetType(System.String)">
            <summary>
            Gets the type.
            </summary>
            <param name="typeName">Name of the type.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetKey(Microsoft.SqlServer.Management.Sdk.Sfc.IUrnFragment)">
            <summary>
            returns the Key object given Urn fragment
            </summary>
            <param name="urnFragment">The urn fragment.</param>
            <returns>SfcKey</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetExecutionEngine">
            <summary>
            Gets the execution engine.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomainLite#GetLogicalVersion">
            <summary>
            Returns the logical version of the domain
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.XEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomainLite#DomainName">
            <summary>
            Gets the name of the domain.
            </summary>
            <value>The name of the domain.</value>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.XEStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomainLite#DomainInstanceName">
            <summary>
            Gets the name of the domain instance.
            </summary>
            <value>The name of the domain instance.</value>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.GetHashCode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.op_Equality(System.Object,Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.op_Equality(Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.op_Equality(Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey,Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.op_Inequality(System.Object,Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.op_Inequality(Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.op_Inequality(Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey,Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.ServerKey.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.GetStoreProvider">
            <summary>
            Gets provider to perform operations on the Store.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.GetSessionProivder(Microsoft.SqlServer.Management.XEvent.Session)">
            <summary>
            Gets provider to perform Session operations.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.GetTargetProvider(Microsoft.SqlServer.Management.XEvent.Target)">
            <summary>
            Gets provider to perform Target operations.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEStore.GetEventProvider(Microsoft.SqlServer.Management.XEvent.Event)">
            <summary>
            Gets provider to perform Event operations.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.XEvent.ServerXEStoreProvider">
            <summary>
            Sql provider for the ServerXEStore.
            </summary>    
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ServerXEStoreProvider.GetExecutionEngine">
            <summary>
            Gets an execution engine associated with Store's connection.
            </summary>
            <returns>Execution engine.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ServerXEStoreProvider.GetConnection(Microsoft.SqlServer.Management.Sdk.Sfc.SfcObjectQueryMode)">
            <summary>
            Get the current connection to query on.
            Return a connection supporting either a single serial query or multiple simultaneously open queries as requested.
            </summary>
            <param name="mode">Query mode.</param>
            <returns>The connection to use, or null to use Cache mode. Cache mode avoids connection and open data reader issues.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ServerXEStoreProvider.DomainInstanceName">
            <summary>
            Gets the name of the domain instance.
            </summary>
            <value>The name of the domain instance.</value>        
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.ServerXEStoreProvider.GetComparer">
            <summary>
             Gets a comparer for the child collections.
            </summary>
            <returns>Requested comparer.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.ServerXEStoreProvider.ServerConnection">
            <summary>
            Gets the underlying ServerConnection
            </summary>
        </member>
    </members>
</doc>
