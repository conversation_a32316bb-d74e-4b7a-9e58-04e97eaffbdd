using Microsoft.EntityFrameworkCore;
using AccountingSystem.Models;

namespace AccountingSystem.Data
{
    public class AccountingDbContext : DbContext
    {
        public AccountingDbContext(DbContextOptions<AccountingDbContext> options) : base(options)
        {
        }

        // User Management DbSets
        public DbSet<User> Users { get; set; }
        public DbSet<UserGroup> UserGroups { get; set; }
        public DbSet<GroupPermission> GroupPermissions { get; set; }
        public DbSet<SessionLog> SessionLogs { get; set; }

        // Configuration DbSets
        public DbSet<SystemConfig> SystemConfigs { get; set; }
        public DbSet<GLConfig> GLConfigs { get; set; }
        public DbSet<FormPermission> FormPermissions { get; set; }
        public DbSet<GroupFormPermission> GroupFormPermissions { get; set; }

        // Chart of Accounts DbSet
        public DbSet<ChartOfAccount> ChartOfAccounts { get; set; }

        // Items Category DbSet
        public DbSet<ItemsCategory> ItemsCategories { get; set; }

        // Employees DbSet
        public DbSet<Employee> Employees { get; set; }
        public DbSet<InvoiceToolSetting> InvoiceToolSettings { get; set; }

        // POS Sessions DbSets
        public DbSet<POSSession> POSSessions { get; set; }
        public DbSet<POSSessionDetail> POSSessionDetails { get; set; }
        public DbSet<Store> Shops { get; set; } // Corrected: Using Store model, mapped to tblShops
        public DbSet<POSDevice> POSDevices { get; set; }
        public DbSet<POSShift> POSShifts { get; set; }

        // POS System DbSets
        public DbSet<BaseInvoice> BaseInvoices { get; set; }
        public DbSet<POSInvoice> POSInvoices { get; set; }
        public DbSet<BaseInvoiceItem> BaseInvoiceItems { get; set; }
        public DbSet<POSInvoiceItem> POSInvoiceItems { get; set; }
        public DbSet<POSPayment> POSPayments { get; set; }
        public DbSet<Item> Items { get; set; }
        // public DbSet<Store> Stores { get; set; } // Intentionally removed
        public DbSet<UserPOSItem> UserPOSItems { get; set; }
        public DbSet<BarcodeSettings> BarcodeSettings { get; set; }

        // Purchase System DbSets
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; }


        // Master Data DbSets
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Vendor> Vendors { get; set; }
        public DbSet<UserStore> UserStores { get; set; }
        public DbSet<DashboardQuickActionConfig> DashboardQuickActionConfigs { get; set; }

        // Example DbSets - Replace with your actual entities
        // public DbSet<Account> Accounts { get; set; }
        // public DbSet<Transaction> Transactions { get; set; }
        // public DbSet<Invoice> Invoices { get; set; }
        // public DbSet<InvoiceItem> InvoiceItems { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User Management entities to match existing database schema
            ConfigureUserEntities(modelBuilder);

            // Configure System Configuration entities
            ConfigureSystemEntities(modelBuilder);

            // Configure Chart of Accounts entities
            ConfigureChartOfAccountsEntities(modelBuilder);

            // Configure Items Category entities
            ConfigureItemsCategoryEntities(modelBuilder);

            // Configure POS Sessions entities
            ConfigurePOSEntities(modelBuilder);

            // Configure POS System entities
            ConfigurePOSSystemEntities(modelBuilder);

            // Configure Master Data entities
            ConfigureMasterDataEntities(modelBuilder);

            // Configure Purchase System entities
            ConfigurePurchaseEntities(modelBuilder);

            // Configure entities to match existing database schema
            // Example configurations:

            /*
            // Map to existing table names (if different from entity names)
            modelBuilder.Entity<Customer>().ToTable("Customers");
            modelBuilder.Entity<Vendor>().ToTable("Vendors");
            modelBuilder.Entity<Account>().ToTable("ChartOfAccounts");

            // Map properties to existing column names (if different)
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.Property(e => e.CustomerId).HasColumnName("CustomerID");
                entity.Property(e => e.CustomerName).HasColumnName("CustomerName").HasMaxLength(100);
                entity.Property(e => e.ContactPerson).HasColumnName("ContactPerson").HasMaxLength(50);
                entity.Property(e => e.PhoneNumber).HasColumnName("Phone").HasMaxLength(20);
                entity.Property(e => e.EmailAddress).HasColumnName("Email").HasMaxLength(100);
                entity.Property(e => e.Address).HasColumnName("Address").HasMaxLength(200);
                entity.Property(e => e.City).HasColumnName("City").HasMaxLength(50);
                entity.Property(e => e.State).HasColumnName("State").HasMaxLength(50);
                entity.Property(e => e.ZipCode).HasColumnName("ZipCode").HasMaxLength(10);
                entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
                entity.Property(e => e.CreatedDate).HasColumnName("CreatedDate").HasDefaultValueSql("GETDATE()");
            });

            modelBuilder.Entity<Account>(entity =>
            {
                entity.Property(e => e.AccountId).HasColumnName("AccountID");
                entity.Property(e => e.AccountNumber).HasColumnName("AccountNumber").HasMaxLength(20);
                entity.Property(e => e.AccountName).HasColumnName("AccountName").HasMaxLength(100);
                entity.Property(e => e.AccountType).HasColumnName("AccountType").HasMaxLength(50);
                entity.Property(e => e.ParentAccountId).HasColumnName("ParentAccountID");
                entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
                entity.Property(e => e.Balance).HasColumnName("Balance").HasColumnType("decimal(18,2)");
            });

            modelBuilder.Entity<Transaction>(entity =>
            {
                entity.Property(e => e.TransactionId).HasColumnName("TransactionID");
                entity.Property(e => e.TransactionDate).HasColumnName("TransactionDate");
                entity.Property(e => e.Description).HasColumnName("Description").HasMaxLength(200);
                entity.Property(e => e.ReferenceNumber).HasColumnName("ReferenceNumber").HasMaxLength(50);
                entity.Property(e => e.DebitAccountId).HasColumnName("DebitAccountID");
                entity.Property(e => e.CreditAccountId).HasColumnName("CreditAccountID");
                entity.Property(e => e.Amount).HasColumnName("Amount").HasColumnType("decimal(18,2)");
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(50);
                entity.Property(e => e.CreatedDate).HasColumnName("CreatedDate").HasDefaultValueSql("GETDATE()");
            });

            // Configure relationships to match existing foreign keys
            modelBuilder.Entity<Transaction>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(t => t.DebitAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Transaction>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(t => t.CreditAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Account>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.ParentAccountId)
                .OnDelete(DeleteBehavior.Restrict);
            */

            // Add indexes for performance (if they don't exist in the database)
            /*
            modelBuilder.Entity<Customer>()
                .HasIndex(c => c.CustomerName)
                .HasDatabaseName("IX_Customers_CustomerName");

            modelBuilder.Entity<Account>()
                .HasIndex(a => a.AccountNumber)
                .IsUnique()
                .HasDatabaseName("IX_Accounts_AccountNumber");

            modelBuilder.Entity<Transaction>()
                .HasIndex(t => t.TransactionDate)
                .HasDatabaseName("IX_Transactions_TransactionDate");
            */
        }

        private void ConfigureUserEntities(ModelBuilder modelBuilder)
        {
            // Configure Users table to match actual tblUsers schema
            modelBuilder.Entity<User>(entity =>
            {
                entity.ToTable("tblUsers");
                entity.Property(e => e.Id).HasColumnName("SN"); // Primary key is SN, not UserID
                entity.Property(e => e.Username).HasColumnName("Username").HasMaxLength(50);
                entity.Property(e => e.Password).HasColumnName("Password").HasMaxLength(255);
                entity.Property(e => e.GroupID).HasColumnName("GroupID");
                
                // Note: These columns don't exist in actual tblUsers table
                // entity.Property(e => e.FullName).HasColumnName("FullName").HasMaxLength(100);
                // entity.Property(e => e.Email).HasColumnName("Email").HasMaxLength(100);
                // entity.Property(e => e.IsLocked).HasColumnName("IsLocked").HasDefaultValue(false);
                // entity.Property(e => e.FailedLoginAttempts).HasColumnName("FailedLoginAttempts").HasDefaultValue(0);
                // entity.Property(e => e.LastLoginDate).HasColumnName("LastLoginDate");
                // entity.Property(e => e.LastPasswordChange).HasColumnName("LastPasswordChange");
                // entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
                // entity.Property(e => e.CreatedDate).HasColumnName("CreatedDate").HasDefaultValueSql("GETDATE()");
                // entity.Property(e => e.ModifiedDate).HasColumnName("ModifiedDate");
                
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(50);
                entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy").HasMaxLength(50);

                // POS-specific fields - Mapped to actual database schema
                entity.Property(e => e.DefaultStore).HasColumnName("DefaultStore").HasMaxLength(50);
                entity.Property(e => e.StoreChange).HasColumnName("StoreChange").HasColumnType("tinyint").HasConversion(
                    v => v ? (byte)1 : (byte)0,
                    v => v == 1);
                entity.Property(e => e.DefaultCustomer).HasColumnName("DefaultCustomer").HasColumnType("bigint");
                entity.Property(e => e.CustomerChange).HasColumnName("CustomerChange").HasColumnType("tinyint").HasConversion(
                    v => v ? (byte)1 : (byte)0,
                    v => v == 1);
                entity.Property(e => e.DefaultCashier).HasColumnName("DefaultCashier").HasMaxLength(50);
                entity.Property(e => e.CashierChange).HasColumnName("CashierChange").HasColumnType("tinyint").HasConversion(
                    v => v ? (byte)1 : (byte)0,
                    v => v == 1);
                entity.Property(e => e.ChangeInvoicePrice).HasColumnName("ChangeInvoicePrice").HasColumnType("tinyint").HasConversion(
                    v => v ? (byte)1 : (byte)0,
                    v => v == 1);
                entity.Property(e => e.MaxDiscountPercent).HasColumnName("MaxDiscountPercent").HasColumnType("decimal(5,2)");

                // Unique constraint on username
                entity.HasIndex(e => e.Username).IsUnique();
            });

            // Configure UserGroups table to match tblGroupsAuth
            modelBuilder.Entity<UserGroup>(entity =>
            {
                entity.ToTable("tblGroupsAuth");
                entity.Property(e => e.GroupID).HasColumnName("GroupID");
                entity.Property(e => e.GroupName).HasColumnName("GroupName").HasMaxLength(100);
                // Note: Description column does not exist in actual tblGroupsAuth table
                // entity.Property(e => e.Description).HasColumnName("Description").HasMaxLength(500);
                // Note: These properties don't exist in actual tblGroupsAuth table
                // entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
                // entity.Property(e => e.CreatedDate).HasColumnName("CreatedDate").HasDefaultValueSql("GETDATE()");
                // entity.Property(e => e.ModifiedDate).HasColumnName("ModifiedDate");
                // entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(50);
                // entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy").HasMaxLength(50);

                // Unique constraint on GroupID
                entity.HasIndex(e => e.GroupID).IsUnique();
            });

            // Configure GroupPermissions table
            modelBuilder.Entity<GroupPermission>(entity =>
            {
                entity.ToTable("tblGroupPermissions");
                entity.Property(e => e.Id).HasColumnName("PermissionID");
                entity.Property(e => e.GroupID).HasColumnName("GroupID");
                entity.Property(e => e.PermissionName).HasColumnName("PermissionName").HasMaxLength(100);
                entity.Property(e => e.ModuleName).HasColumnName("ModuleName").HasMaxLength(50);
                entity.Property(e => e.CanView).HasColumnName("CanView").HasDefaultValue(false);
                entity.Property(e => e.CanAdd).HasColumnName("CanAdd").HasDefaultValue(false);
                entity.Property(e => e.CanEdit).HasColumnName("CanEdit").HasDefaultValue(false);
                entity.Property(e => e.CanDelete).HasColumnName("CanDelete").HasDefaultValue(false);
                entity.Property(e => e.CanPrint).HasColumnName("CanPrint").HasDefaultValue(false);
                // Note: These properties don't exist in actual tblGroupPermissions table
                // entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
                // entity.Property(e => e.CreatedDate).HasColumnName("CreatedDate").HasDefaultValueSql("GETDATE()");
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(50);
            });

            // Configure SessionLogs table
            modelBuilder.Entity<SessionLog>(entity =>
            {
                entity.ToTable("tblSessionLogs");
                entity.Property(e => e.Id).HasColumnName("LogID");
                entity.Property(e => e.SessionID).HasColumnName("SessionID");
                entity.Property(e => e.Username).HasColumnName("Username").HasMaxLength(50);
                entity.Property(e => e.LoginStatus).HasColumnName("LoginStatus").HasMaxLength(20);
                entity.Property(e => e.MachineName).HasColumnName("MachineName").HasMaxLength(100);
                entity.Property(e => e.MachineUser).HasColumnName("MachineUser").HasMaxLength(100);
                entity.Property(e => e.SystemUsername).HasColumnName("SystemUsername").HasMaxLength(50);
                entity.Property(e => e.IPAddress).HasColumnName("IPAddress").HasMaxLength(45);
                entity.Property(e => e.UserAgent).HasColumnName("UserAgent").HasMaxLength(500);
                entity.Property(e => e.SessionToken).HasColumnName("SessionToken").HasMaxLength(255);
                entity.Property(e => e.LoginTime).HasColumnName("LoginTime").HasDefaultValueSql("GETDATE()");
                entity.Property(e => e.LogoutTime).HasColumnName("LogoutTime");
                entity.Property(e => e.FailureReason).HasColumnName("FailureReason").HasMaxLength(500);
                // Note: IsActive property doesn't exist in actual tblSessionLogs table
                // entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
            });

            // Configure relationships
            modelBuilder.Entity<User>()
                .HasOne(u => u.Group)
                .WithMany(g => g.Users)
                .HasForeignKey(u => u.GroupID)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<GroupPermission>()
                .HasOne(gp => gp.Group)
                .WithMany(g => g.Permissions)
                .HasForeignKey(gp => gp.GroupID)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<SessionLog>()
                .HasOne(sl => sl.User)
                .WithMany(u => u.SessionLogs)
                .HasForeignKey(sl => sl.Username)
                .HasPrincipalKey(u => u.Username)
                .OnDelete(DeleteBehavior.SetNull);
        }

        private void ConfigureSystemEntities(ModelBuilder modelBuilder)
        {
            // Configure SystemConfig table to match tblConfig
            modelBuilder.Entity<SystemConfig>(entity =>
            {
                entity.ToTable("tblConfig");
                entity.Property(e => e.Id).HasColumnName("ConfigID");
                entity.Property(e => e.ConfigKey).HasColumnName("ConfigKey").HasMaxLength(100);
                entity.Property(e => e.ConfigValue).HasColumnName("ConfigValue").HasMaxLength(500);
                entity.Property(e => e.Description).HasColumnName("Description").HasMaxLength(200);
                entity.Property(e => e.CreatedDate).HasColumnName("CreatedDate").HasDefaultValueSql("GETDATE()");
                entity.Property(e => e.ModifiedDate).HasColumnName("ModifiedDate");
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(50);
                entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy").HasMaxLength(50);

                entity.HasIndex(e => e.ConfigKey).IsUnique();
            });

            // Configure GLConfig table to match tblGLConfig
            modelBuilder.Entity<GLConfig>(entity =>
            {
                entity.ToTable("tblGLConfig");
                // The actual table doesn't have a primary key column, so we'll use EntryReferenceModule as the key
                entity.HasKey(e => e.EntryReferenceModule);
                entity.Property(e => e.EntryReferenceModule).HasColumnName("EntryReferenceModule").HasMaxLength(100);
                entity.Property(e => e.AccountNo).HasColumnName("AccountNo");
                // Note: The actual table only has EntryReferenceModule and AccountNo columns
                // Additional columns like CreatedBy, CreatedOn, ModifiedBy, ModifiedOn are handled by the database
            });

            // Configure FormPermission table to match tblForms
            modelBuilder.Entity<FormPermission>(entity =>
            {
                entity.ToTable("tblForms");
                entity.Property(e => e.Id).HasColumnName("FormID");
                entity.Property(e => e.FormName).HasColumnName("FormName").HasMaxLength(100);
                entity.Property(e => e.FormTitle).HasColumnName("FormTitle").HasMaxLength(200);
                entity.Property(e => e.ModuleName).HasColumnName("ModuleName").HasMaxLength(100);
                // Note: IsActive property doesn't exist in actual tblForms table
                // entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
            });

            // Configure GroupFormPermission table to match tblGroupFormPermissions
            modelBuilder.Entity<GroupFormPermission>(entity =>
            {
                entity.ToTable("tblGroupFormPermissions");
                entity.Property(e => e.Id).HasColumnName("PermissionID");
                entity.Property(e => e.GroupID).HasColumnName("GroupID");
                entity.Property(e => e.FormID).HasColumnName("FormID");
                entity.Property(e => e.CanView).HasColumnName("CanView").HasDefaultValue(true);
                entity.Property(e => e.CanAdd).HasColumnName("CanAdd").HasDefaultValue(false);
                entity.Property(e => e.CanEdit).HasColumnName("CanEdit").HasDefaultValue(false);
                entity.Property(e => e.CanDelete).HasColumnName("CanDelete").HasDefaultValue(false);
                entity.Property(e => e.CanPrint).HasColumnName("CanPrint").HasDefaultValue(false);
                // Note: IsActive property doesn't exist in actual tblGroupFormPermissions table
                // entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
            });

            // Configure relationships for form permissions
            modelBuilder.Entity<GroupFormPermission>()
                .HasOne<UserGroup>()
                .WithMany()
                .HasForeignKey(gfp => gfp.GroupID)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<GroupFormPermission>()
                .HasOne(gfp => gfp.Form)
                .WithMany(f => f.GroupPermissions)
                .HasForeignKey(gfp => gfp.FormID)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigureChartOfAccountsEntities(ModelBuilder modelBuilder)
        {
            // Configure ChartOfAccount table to match tbl_Acc_Accounts
            modelBuilder.Entity<ChartOfAccount>(entity =>
            {
                entity.ToTable("tbl_Acc_Accounts");
                entity.HasKey(e => e.AccountCode);
                
                entity.Property(e => e.AccountCode).HasColumnName("AccountCode").HasMaxLength(50);
                entity.Property(e => e.SegmentCode).HasColumnName("SegmentCode").HasMaxLength(20);
                entity.Property(e => e.AccountName).HasColumnName("AccountName").HasMaxLength(200);
                entity.Property(e => e.ParentAccountCode).HasColumnName("ParentAccountCode").HasMaxLength(50);
                entity.Property(e => e.AccountLevel).HasColumnName("AccountLevel");
                entity.Property(e => e.IsPosting).HasColumnName("IsPosting");
                entity.Property(e => e.AccountType).HasColumnName("AccountType").HasMaxLength(50);
                entity.Property(e => e.AccountNature).HasColumnName("AccountNature").HasMaxLength(20);
                entity.Property(e => e.OpeningBalance).HasColumnName("OpeningBalance").HasColumnType("decimal(18,2)");
                entity.Property(e => e.Notes).HasColumnName("Notes").HasMaxLength(500);
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(50);
                entity.Property(e => e.CreatedOn).HasColumnName("CreatedOn");
                entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy").HasMaxLength(50);
                entity.Property(e => e.ModifiedOn).HasColumnName("ModifiedOn");
            });
        }
        
        private void ConfigureItemsCategoryEntities(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ItemsCategory>(entity =>
            {
                entity.HasOne(e => e.Parent)
                    .WithMany(e => e.Children)
                    .HasForeignKey(e => e.ParentId)
                    .OnDelete(DeleteBehavior.Restrict); // Prevent cascading deletes to maintain data integrity
            });
        }

        private void ConfigurePOSEntities(ModelBuilder modelBuilder)
        {
            // Configure POS Sessions entities to match existing database schema

            // Configure POSSession table to match tblPOSSessions
            modelBuilder.Entity<POSSession>(entity =>
            {
                entity.ToTable("tblPOSSessions");
                entity.HasKey(e => e.SessionID);
                entity.Property(e => e.SessionID).HasColumnName("SessionID");
                entity.Property(e => e.SessionSN).HasColumnName("SessionSN").HasMaxLength(50);
                entity.Property(e => e.ShopID).HasColumnName("ShopID");
                entity.Property(e => e.DeviceID).HasColumnName("DeviceID");
                entity.Property(e => e.ShiftID).HasColumnName("ShiftID");
                entity.Property(e => e.OpenedBy).HasColumnName("OpenedBy").HasMaxLength(50);
                entity.Property(e => e.OpenTime).HasColumnName("OpenTime");
                entity.Property(e => e.ClosingCash).HasColumnName("ClosingCash").HasColumnType("decimal(18,2)");
                entity.Property(e => e.ClosedBy).HasColumnName("ClosedBy").HasMaxLength(50);
                entity.Property(e => e.CloseTime).HasColumnName("CloseTime");
                entity.Property(e => e.Status).HasColumnName("Status").HasMaxLength(20);
                entity.Property(e => e.Note).HasColumnName("Note").HasMaxLength(500);
            });

            // The Shop/Store configuration is now handled by attributes on the Store model.
            // No Fluent API configuration is needed here for Shop or Store.
            
            // Configure POSDevice table to match tblPOSDevices
            modelBuilder.Entity<POSDevice>(entity =>
            {
                entity.ToTable("tblPOSDevices");
                entity.HasKey(e => e.DeviceID);
                entity.Property(e => e.DeviceID).HasColumnName("DeviceID");
                entity.Property(e => e.DeviceName).HasColumnName("DeviceName").HasMaxLength(100);
            });

            // Configure POSShift table to match tblPOSShifts
            modelBuilder.Entity<POSShift>(entity =>
            {
                entity.ToTable("tblPOSShifts");
                entity.HasKey(e => e.ShiftID);
                entity.Property(e => e.ShiftID).HasColumnName("ShiftID");
                entity.Property(e => e.ShiftName).HasColumnName("ShiftName").HasMaxLength(100);
            });

            // Configure relationships
            modelBuilder.Entity<POSSession>()
                .HasOne(s => s.Shop)
                .WithMany()
                .HasForeignKey(s => s.ShopID)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<POSSession>()
                .HasOne(s => s.Device)
                .WithMany()
                .HasForeignKey(s => s.DeviceID)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<POSSession>()
                .HasOne(s => s.Shift)
                .WithMany()
                .HasForeignKey(s => s.ShiftID)
                .OnDelete(DeleteBehavior.SetNull);
        }

        private void ConfigurePOSSystemEntities(ModelBuilder modelBuilder)
        {
            // Configure BaseInvoice table to match tblStockMovHeader with TPH inheritance
            modelBuilder.Entity<BaseInvoice>(entity =>
            {
                entity.ToTable("tblStockMovHeader");
                entity.HasKey(e => e.TrxNo);
                entity.Property(e => e.TrxNo).HasColumnName("TrxNo").HasColumnType("int");
                entity.Property(e => e.TrxType).HasColumnName("TrxType").HasMaxLength(50);
                entity.Property(e => e.TrxDate).HasColumnName("TrxDate");
                entity.Property(e => e.Store).HasColumnName("Store").HasMaxLength(50);
                entity.Property(e => e.Cashier).HasColumnName("Cashier").HasMaxLength(50);
                entity.Property(e => e.PartnerNo).HasColumnName("PartnerNo");
                entity.Property(e => e.PartnerName).HasColumnName("PartnerName").HasMaxLength(100);
                entity.Property(e => e.PartnerPhoneNo).HasColumnName("PartnerPhoneNo").HasMaxLength(20);
                entity.Property(e => e.PaymentMethod).HasColumnName("PaymentMethod").HasMaxLength(50);
                entity.Property(e => e.PartnerReference).HasColumnName("PartnerReference").HasMaxLength(100);
                entity.Property(e => e.TrxNote).HasColumnName("TrxNote").HasMaxLength(500);
                entity.Property(e => e.TrxVAT).HasColumnName("TrxVAT").HasColumnType("decimal(18,2)");
                entity.Property(e => e.TrxTotal).HasColumnName("TrxTotal").HasColumnType("decimal(18,2)");
                entity.Property(e => e.TrxDiscount).HasColumnName("TrxDiscount").HasColumnType("decimal(18,2)");
                entity.Property(e => e.TrxDiscountValue).HasColumnName("TrxDiscountValue").HasColumnType("decimal(18,2)");
                entity.Property(e => e.TrxNetAmount).HasColumnName("TrxNetAmount").HasColumnType("decimal(18,2)");
                entity.Property(e => e.ReadyForUse).HasColumnName("ReadyForUse").HasMaxLength(10);
                entity.Property(e => e.VOIDSTTS).HasColumnName("VOIDSTTS").HasConversion(
                    v => v == true ? 1 : 0,
                    v => v == 1 ? true : false);
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(50);
                entity.Property(e => e.CreatedOn).HasColumnName("CreatedOn");
                entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy").HasMaxLength(50);
                entity.Property(e => e.ModifiedOn).HasColumnName("ModifiedOn");

                // Configure TPH discriminator
                entity.HasDiscriminator<string>("TrxType")
                    .HasValue<POSInvoice>("مبيعات")
                    .HasValue<PurchaseInvoice>("مشتريات");
            });

            // Configure POSInvoice specific properties
            modelBuilder.Entity<POSInvoice>(entity =>
            {
                entity.Property(e => e.QRCodeImage).HasColumnName("QRCodeImage");
            });

            // Configure PurchaseInvoice specific properties
            modelBuilder.Entity<PurchaseInvoice>(entity =>
            {
                entity.Property(e => e.ReferenceInvoice).HasColumnName("ReferenceInvoice").HasMaxLength(100);
                entity.Property(e => e.PaymentStatus).HasColumnName("PaymentStatus").HasColumnType("int");
            });

            // Configure BaseInvoiceItem table to match tblStockMovement with TPH inheritance
            modelBuilder.Entity<BaseInvoiceItem>(entity =>
            {
                entity.ToTable("tblStockMovement");
                entity.HasKey(e => new { e.DocNo, e.LineSN });
                entity.Property(e => e.DocNo).HasColumnName("DocNo").HasColumnType("int");
                entity.Property(e => e.LineSN).HasColumnName("LineSN");
                entity.Property(e => e.TrxDate).HasColumnName("TrxDate");
                entity.Property(e => e.ItemNo).HasColumnName("ItemNo").HasColumnType("bigint");
                entity.Property(e => e.Store).HasColumnName("Store").HasMaxLength(50);
                entity.Property(e => e.TrxQTY).HasColumnName("TrxQTY").HasColumnType("decimal(18,3)");
                entity.Property(e => e.TrxType).HasColumnName("TrxType").HasMaxLength(50);
                entity.Property(e => e.UnitPrice).HasColumnName("UnitPrice").HasColumnType("decimal(18,2)");
                entity.Property(e => e.UofM).HasColumnName("UofM").HasMaxLength(50);
                entity.Property(e => e.UofMConversion).HasColumnName("UofMConversion").HasColumnType("decimal(18,3)");
                entity.Property(e => e.VATAmount).HasColumnName("VATAmount").HasColumnType("decimal(18,2)");
                entity.Property(e => e.LineAmount).HasColumnName("LineAmount").HasColumnType("decimal(18,2)");
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(50);
                entity.Property(e => e.CreatedOn).HasColumnName("CreatedOn");
                entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy").HasMaxLength(50);
                entity.Property(e => e.ModifiedOn).HasColumnName("ModifiedOn");

                // Configure TPH discriminator
                entity.HasDiscriminator<string>("TrxType")
                    .HasValue<POSInvoiceItem>("مبيعات")
                    .HasValue<PurchaseInvoiceItem>("مشتريات");
            });

            // Configure POSPayment table to match tblPayMethodTrx
            modelBuilder.Entity<POSPayment>(entity =>
            {
                entity.ToTable("tblPayMethodTrx");
                entity.HasKey(e => new { e.TrxNo, e.Pay_mthd });
                entity.Property(e => e.TrxNo).HasColumnName("TrxNo").HasColumnType("int");
                entity.Property(e => e.Pay_mthd).HasColumnName("Pay_mthd");
                entity.Property(e => e.Pay_amnt).HasColumnName("Pay_amnt").HasColumnType("decimal(18,2)");
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(50);
                entity.Property(e => e.CreatedOn).HasColumnName("CreatedOn");
            });

            // Configure Item table to match tblItems
            modelBuilder.Entity<Item>(entity =>
            {
                entity.ToTable("tblItems");
                entity.HasKey(e => e.SN);
                entity.Property(e => e.SN).HasColumnName("SN");
                entity.Property(e => e.ItemNo).HasColumnName("ItemNo").HasColumnType("bigint");
                entity.Property(e => e.ItemDescription).HasColumnName("ItemDescription");
                entity.Property(e => e.ItemDescription2).HasColumnName("ItemDescription2");
                entity.Property(e => e.Barcode).HasColumnName("Barcode").HasMaxLength(50);
                entity.Property(e => e.CategoryId).HasColumnName("ItemCategory");
                entity.Property(e => e.UofM).HasColumnName("UofM").HasMaxLength(50);
                entity.Property(e => e.EnableSN).HasColumnName("SNEnable").HasConversion<byte?>();
                entity.Property(e => e.NegativeEnable).HasColumnName("NegativeStock").HasConversion<byte?>();
                entity.Property(e => e.UnitSalesPrice).HasColumnName("UnitSalesPrice").HasColumnType("decimal(10, 2)");
                entity.Property(e => e.SalesUofM).HasColumnName("SalesUofM").HasMaxLength(50);
                entity.Property(e => e.UnitPurchasePrice).HasColumnName("UnitPurchasePrice").HasColumnType("decimal(10, 2)");
                entity.Property(e => e.ItemType).HasColumnName("ItemType").HasMaxLength(50);
                entity.Property(e => e.AUofM).HasColumnName("AUofM").HasMaxLength(50);
                entity.Property(e => e.AUofMX).HasColumnName("AUofMX").HasColumnType("decimal(10, 4)");
                entity.Property(e => e.AUofM_Price).HasColumnName("AUofM_Price").HasColumnType("decimal(10, 2)");
                entity.Property(e => e.AUofM2).HasColumnName("AUofM2").HasMaxLength(50);
                entity.Property(e => e.AUofMX2).HasColumnName("AUofMX2").HasColumnType("decimal(10, 4)");
                entity.Property(e => e.AUofM2_Price).HasColumnName("AUofM2_Price").HasColumnType("decimal(10, 2)");
                entity.Property(e => e.AUofM3).HasColumnName("AUofM3").HasMaxLength(50);
                entity.Property(e => e.AUofMX3).HasColumnName("AUofMX3").HasColumnType("decimal(10, 4)");
                entity.Property(e => e.AUofM3_Price).HasColumnName("AUofM3_Price").HasColumnType("decimal(10, 2)");
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy");
                entity.Property(e => e.CreatedOn).HasColumnName("CreatedOn");
                entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy");
                entity.Property(e => e.ModifiedOn).HasColumnName("ModifiedOn");
                entity.Property(e => e.Tax_Percent).HasColumnName("Tax_Percent").HasColumnType("decimal(5, 2)");
                entity.Property(e => e.Brand).HasColumnName("Brand").HasMaxLength(50);
                entity.Property(e => e.Status).HasColumnName("Status");
                entity.Property(e => e.Photo).HasColumnName("Item_Photo");
                entity.Property(e => e.Notes).HasColumnName("Notes");
                entity.Property(e => e.Shop).HasColumnName("Shop").HasMaxLength(50);
                entity.Property(e => e.UnitAveragePrice).HasColumnName("UnitAveragePrice").HasColumnType("decimal(10, 0)");
                entity.Property(e => e.OldCode).HasColumnName("OldCode");
            });
            
            // The Store configuration is now handled by attributes on the Store model itself.

            // Configure UserPOSItems table to match tblUserPOSItems
            modelBuilder.Entity<UserPOSItem>(entity =>
            {
                entity.ToTable("tblUserPOSItems");
                entity.HasKey(e => new { e.Username, e.ItemNo });
                entity.Property(e => e.Username).HasColumnName("Username").HasMaxLength(50);
                entity.Property(e => e.ItemNo).HasColumnName("ItemNo").HasColumnType("bigint");
            });

            // Configure BarcodeSettings table to match tblBarcodeSettings
            modelBuilder.Entity<BarcodeSettings>(entity =>
            {
                entity.ToTable("tblBarcodeSettings");
                entity.HasKey(e => e.ID); 
                entity.Property(e => e.ID).HasColumnName("ID").ValueGeneratedOnAdd();
                entity.Property(e => e.Shop).HasColumnName("Shop").HasMaxLength(50).IsRequired();
                entity.Property(e => e.BarcodeType).HasColumnName("BarcodeType").HasMaxLength(50);
                entity.Property(e => e.EnableEmbeddedWeight).HasColumnName("EnableEmbeddedWeight");
                entity.Property(e => e.EmbeddedFormat).HasColumnName("EmbeddedFormat").HasMaxLength(50);
                entity.Property(e => e.WeightDivisor).HasColumnName("WeightDivisor");
                entity.Property(e => e.CurrencyDivisor).HasColumnName("CurrencyDivisor");
                entity.Property(e => e.Notes).HasColumnName("Notes");
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(100);
                entity.Property(e => e.CreatedOn).HasColumnName("CreatedOn");
                entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy").HasMaxLength(100);
                entity.Property(e => e.ModifiedOn).HasColumnName("ModifiedOn");
                entity.Property(e => e.Barcode).HasColumnName("Barcode").HasMaxLength(50);
                entity.Property(e => e.Weight).HasColumnName("Weight").HasMaxLength(50);
                entity.Property(e => e.FixedCode).HasColumnName("FixedCode").HasMaxLength(50);
            });
        }

        private void ConfigureMasterDataEntities(ModelBuilder modelBuilder)
        {
            // Configure Customer table to match legacy tblCustomers structure
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.ToTable("tblCustomers");
                entity.HasKey(e => e.CustomerNo);
                entity.Property(e => e.CustomerNo).HasColumnName("CustomerNo").HasColumnType("bigint").IsRequired();
                entity.Property(e => e.CustomerName).HasColumnName("CustomerName").HasMaxLength(255);
                entity.Property(e => e.FirstName).HasColumnName("FirstName").HasMaxLength(50);
                entity.Property(e => e.LastName).HasColumnName("LastName").HasMaxLength(50);
                entity.Property(e => e.Mobile).HasColumnName("Mobile").HasColumnType("int");
                entity.Property(e => e.Phone).HasColumnName("Phone").HasColumnType("int");
                entity.Property(e => e.Email).HasColumnName("Email").HasMaxLength(255);
                entity.Property(e => e.StreetAddress1).HasColumnName("StreetAddress1").HasMaxLength(50);
                entity.Property(e => e.StreetAddress2).HasColumnName("StreetAddress2").HasMaxLength(50);
                entity.Property(e => e.City).HasColumnName("City").HasMaxLength(50);
                entity.Property(e => e.Region).HasColumnName("Region").HasMaxLength(50);
                entity.Property(e => e.PostalCode).HasColumnName("PostalCode").HasColumnType("int");
                entity.Property(e => e.PaymentMethod).HasColumnName("PaymentMethod").HasMaxLength(50);
                entity.Property(e => e.CreditLimit).HasColumnName("CreditLimit").HasColumnType("float");
                entity.Property(e => e.PaymentTerm).HasColumnName("PaymentTerm").HasMaxLength(255);
                entity.Property(e => e.ContactPerson).HasColumnName("ContactPerson").HasMaxLength(255);
                entity.Property(e => e.CR).HasColumnName("CR").HasMaxLength(10);
                entity.Property(e => e.VATRegNo).HasColumnName("VATRegNo").HasMaxLength(50);
                entity.Property(e => e.Shop).HasColumnName("Shop").HasMaxLength(50);
                entity.Property(e => e.Status).HasColumnName("Status").HasMaxLength(50);
                entity.Property(e => e.LocalCustomer).HasColumnName("LocalCustomer").HasMaxLength(50);
                entity.Property(e => e.EmployeeNo).HasColumnName("EmployeeNo").HasColumnType("int");
                entity.Property(e => e.Notes).HasColumnName("Notes").HasMaxLength(255);
                entity.Property(e => e.OldCode).HasColumnName("OldCode").HasColumnType("int");
                entity.Property(e => e.BuildingNo).HasColumnName("BuildingNo").HasColumnType("int");
                entity.Property(e => e.AdditionalNo).HasColumnName("AdditionalNo").HasColumnType("int");
                entity.Property(e => e.District).HasColumnName("District").HasMaxLength(255);
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(50);
                entity.Property(e => e.CreatedOn).HasColumnName("CreatedOn");
                entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy").HasMaxLength(50);
                entity.Property(e => e.ModifiedOn).HasColumnName("ModifiedOn");

                // Unique constraint on CustomerNo
                entity.HasIndex(e => e.CustomerNo).IsUnique();
            });

            // Configure Vendor table to match legacy tblVendors structure
            modelBuilder.Entity<Vendor>(entity =>
            {
                entity.ToTable("tblVendors");
                entity.HasKey(e => e.VendorNo);
                entity.Property(e => e.VendorNo).HasColumnName("VendorNo").HasColumnType("bigint").IsRequired();
                entity.Property(e => e.VendorName).HasColumnName("VendorName").HasMaxLength(255);
                entity.Property(e => e.FirstName).HasColumnName("FirstName").HasMaxLength(50);
                entity.Property(e => e.LastName).HasColumnName("LastName").HasMaxLength(50);
                entity.Property(e => e.Mobile).HasColumnName("Mobile").HasColumnType("int");
                entity.Property(e => e.Phone).HasColumnName("Phone").HasColumnType("int");
                entity.Property(e => e.Email).HasColumnName("Email").HasMaxLength(255);
                entity.Property(e => e.StreetAddress1).HasColumnName("StreetAddress1").HasMaxLength(50);
                entity.Property(e => e.StreetAddress2).HasColumnName("StreetAddress2").HasMaxLength(50);
                entity.Property(e => e.City).HasColumnName("City").HasMaxLength(50);
                entity.Property(e => e.Region).HasColumnName("Region").HasMaxLength(50);
                entity.Property(e => e.PostalCode).HasColumnName("PostalCode").HasColumnType("int");
                entity.Property(e => e.PaymentMethod).HasColumnName("PaymentMethod").HasMaxLength(50);
                entity.Property(e => e.CreditLimit).HasColumnName("CreditLimit").HasColumnType("float");
                entity.Property(e => e.PaymentTerm).HasColumnName("PaymentTerm").HasMaxLength(255);
                entity.Property(e => e.ContactPerson).HasColumnName("ContactPerson").HasMaxLength(255);
                entity.Property(e => e.CR).HasColumnName("CR").HasMaxLength(10);
                entity.Property(e => e.VATRegNo).HasColumnName("VATRegNo").HasMaxLength(50);
                entity.Property(e => e.Shop).HasColumnName("Shop").HasMaxLength(50);
                entity.Property(e => e.Status).HasColumnName("Status").HasMaxLength(50);
                entity.Property(e => e.LocalVendor).HasColumnName("LocalVendor").HasMaxLength(50);
                entity.Property(e => e.Notes).HasColumnName("Notes").HasMaxLength(255);
                entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy").HasMaxLength(50);
                entity.Property(e => e.CreatedOn).HasColumnName("CreatedOn");
                entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy").HasMaxLength(50);
                entity.Property(e => e.ModifiedOn).HasColumnName("ModifiedOn");

                // Unique constraint on VendorNo
                entity.HasIndex(e => e.VendorNo).IsUnique();
            });



            // Configure relationships
            modelBuilder.Entity<Customer>()
                .HasOne(c => c.Employee)
                .WithMany()
                .HasForeignKey(c => c.EmployeeNo)
                .OnDelete(DeleteBehavior.SetNull);
        }

        private void ConfigurePurchaseEntities(ModelBuilder modelBuilder)
        {
            // PurchaseInvoiceItem is now configured through TPH inheritance in ConfigurePOSSystemEntities
            // No additional configuration needed here
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // This will be overridden by dependency injection in the web app
                // Only used for design-time operations
                optionsBuilder.UseSqlServer("Server=localhost;Database=AccountingDB;Trusted_Connection=true;TrustServerCertificate=true;");
            }
        }
    }
}
