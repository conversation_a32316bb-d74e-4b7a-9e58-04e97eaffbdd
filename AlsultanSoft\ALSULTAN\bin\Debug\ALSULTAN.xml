﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
ALSULTAN
</name>
</assembly>
<members>
<member name="T:ACCOUNTING.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:ACCOUNTING.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:ACCOUNTING.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="P:ACCOUNTING.My.Resources.Resources.AlsultanLogo">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
</members>
</doc>
