using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public class VendorService : IVendorService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<VendorService> _logger;

        public VendorService(AccountingDbContext context, ILogger<VendorService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<Vendor>> GetVendorsAsync(string searchTerm = "")
        {
            var query = _context.Vendors.AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(v => 
                    v.VendorName.Contains(searchTerm) || 
                    v.VendorNo.ToString().Contains(searchTerm) ||
                    v.FirstName.Contains(searchTerm) ||
                    v.LastName.Contains(searchTerm) ||
                    (v.Mobile != null && v.Mobile.ToString().Contains(searchTerm))
                );
            }

            return await query.OrderBy(v => v.VendorName).ToListAsync();
        }

        public async Task<Vendor?> GetVendorByNoAsync(long vendorNo)
        {
            return await _context.Vendors.FindAsync(vendorNo);
        }

        public async Task<long> GetNextVendorNoAsync()
        {
            if (await _context.Vendors.AnyAsync())
            {
                return await _context.Vendors.MaxAsync(v => v.VendorNo) + 1;
            }
            return 1;
        }

        public async Task<string?> GetVendorParentAccountCodeAsync()
        {
            try
            {
                var config = await _context.GLConfigs
                    .Where(g => g.EntryReferenceModule == "موردون")
                    .FirstOrDefaultAsync();
                
                return config?.AccountNo.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vendor parent account code");
                return null;
            }
        }

        public async Task<string> GenerateNextVendorAccountCodeAsync(string parentCode)
        {
            string nextSegment = "000001";
            try
            {
                var startIndex = parentCode.Length + 1;
                var maxAccountCode = await _context.ChartOfAccounts
                    .Where(a => a.ParentAccountCode == parentCode)
                    .Select(a => a.AccountCode)
                    .ToListAsync();

                if (maxAccountCode.Any())
                {
                    var maxSegment = maxAccountCode
                        .Where(code => code.Length >= startIndex + 5) // Ensure we have enough characters
                        .Select(code => 
                        {
                            var segment = code.Substring(startIndex - 1, 6);
                            return int.TryParse(segment, out int result) ? result : 0;
                        })
                        .DefaultIfEmpty(0)
                        .Max();

                    nextSegment = (maxSegment + 1).ToString("D6");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating next vendor account code");
            }

            return parentCode + nextSegment;
        }

        public async Task<(bool success, string? accountCode)> CreateVendorAsync(Vendor vendor, string currentUser)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Get parent account code for vendors
                var parentAccountCode = await GetVendorParentAccountCodeAsync();
                if (string.IsNullOrEmpty(parentAccountCode))
                {
                    _logger.LogError("Vendor parent account code not found in GL configuration");
                    return (false, null);
                }

                // Generate vendor number if not provided
                if (vendor.VendorNo == 0)
                {
                    vendor.VendorNo = await GetNextVendorNoAsync();
                }

                // Check if vendor number already exists
                if (await _context.Vendors.AnyAsync(v => v.VendorNo == vendor.VendorNo))
                {
                    return (false, null);
                }

                // Generate account code for the vendor
                var generatedAccountCode = await GenerateNextVendorAccountCodeAsync(parentAccountCode);

                // Create account in Chart of Accounts (Liability/Credit for vendors)
                var vendorAccount = new ChartOfAccount
                {
                    AccountCode = generatedAccountCode,
                    SegmentCode = generatedAccountCode.Substring(parentAccountCode.Length),
                    AccountName = vendor.VendorName ?? $"Vendor {vendor.VendorNo}",
                    ParentAccountCode = parentAccountCode,
                    AccountLevel = generatedAccountCode.Split('.').Length,
                    IsPosting = true,
                    AccountType = "Liability",  // Different from customers (Asset)
                    AccountNature = "Credit",   // Different from customers (Debit)
                    OpeningBalance = 0,
                    Notes = "حساب آلي للمورد",    // Different from customers
                    CreatedBy = currentUser,
                    CreatedOn = DateTime.Now
                };

                _context.ChartOfAccounts.Add(vendorAccount);

                // Set vendor audit fields
                vendor.CreatedBy = currentUser;
                vendor.CreatedOn = DateTime.Now;

                // Add vendor
                _context.Vendors.Add(vendor);

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation($"Vendor {vendor.VendorNo} created successfully with account code {generatedAccountCode}");
                return (true, generatedAccountCode);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error creating vendor {vendor.VendorNo}");
                return (false, null);
            }
        }

        public async Task<bool> UpdateVendorAsync(Vendor vendor, string currentUser)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingVendor = await _context.Vendors.FindAsync(vendor.VendorNo);
                if (existingVendor == null)
                {
                    return false;
                }

                // Update vendor fields
                existingVendor.VendorName = vendor.VendorName;
                existingVendor.FirstName = vendor.FirstName;
                existingVendor.LastName = vendor.LastName;
                existingVendor.Mobile = vendor.Mobile;
                existingVendor.Phone = vendor.Phone;
                existingVendor.Email = vendor.Email;
                existingVendor.StreetAddress1 = vendor.StreetAddress1;
                existingVendor.StreetAddress2 = vendor.StreetAddress2;
                existingVendor.City = vendor.City;
                existingVendor.Region = vendor.Region;
                existingVendor.PostalCode = vendor.PostalCode;
                existingVendor.PaymentMethod = vendor.PaymentMethod;
                existingVendor.CreditLimit = vendor.CreditLimit;
                existingVendor.PaymentTerm = vendor.PaymentTerm;
                existingVendor.ContactPerson = vendor.ContactPerson;
                existingVendor.CR = vendor.CR;
                existingVendor.VATRegNo = vendor.VATRegNo;
                existingVendor.Shop = vendor.Shop;
                existingVendor.Status = vendor.Status;
                existingVendor.LocalVendor = vendor.LocalVendor;
                existingVendor.Notes = vendor.Notes;
                existingVendor.ModifiedBy = currentUser;
                existingVendor.ModifiedOn = DateTime.Now;

                // Update corresponding account name if vendor name changed
                if (existingVendor.VendorName != vendor.VendorName)
                {
                    var parentAccountCode = await GetVendorParentAccountCodeAsync();
                    if (!string.IsNullOrEmpty(parentAccountCode))
                    {
                        var vendorAccount = await _context.ChartOfAccounts
                            .Where(a => a.ParentAccountCode == parentAccountCode && 
                                       a.AccountName.Contains(existingVendor.VendorName ?? ""))
                            .FirstOrDefaultAsync();

                        if (vendorAccount != null)
                        {
                            vendorAccount.AccountName = vendor.VendorName ?? $"Vendor {vendor.VendorNo}";
                            vendorAccount.ModifiedBy = currentUser;
                            vendorAccount.ModifiedOn = DateTime.Now;
                        }
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation($"Vendor {vendor.VendorNo} updated successfully");
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error updating vendor {vendor.VendorNo}");
                return false;
            }
        }

        public async Task<bool> DeleteVendorAsync(long vendorNo)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var vendor = await _context.Vendors.FindAsync(vendorNo);
                if (vendor == null)
                {
                    return false;
                }

                // Check if vendor is used in any transactions (you may want to add this logic)
                // For now, we'll allow deletion

                // Find and delete the corresponding account
                var parentAccountCode = await GetVendorParentAccountCodeAsync();
                if (!string.IsNullOrEmpty(parentAccountCode))
                {
                    var vendorAccount = await _context.ChartOfAccounts
                        .Where(a => a.ParentAccountCode == parentAccountCode && 
                                   a.AccountName.Contains(vendor.VendorName ?? ""))
                        .FirstOrDefaultAsync();

                    if (vendorAccount != null)
                    {
                        _context.ChartOfAccounts.Remove(vendorAccount);
                    }
                }

                _context.Vendors.Remove(vendor);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation($"Vendor {vendorNo} deleted successfully");
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error deleting vendor {vendorNo}");
                return false;
            }
        }
    }
}
