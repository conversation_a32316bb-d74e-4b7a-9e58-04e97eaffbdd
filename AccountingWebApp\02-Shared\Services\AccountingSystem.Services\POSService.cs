using Microsoft.EntityFrameworkCore;
using AccountingSystem.Data;
using AccountingSystem.Models;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using Microsoft.Data.SqlClient;
using System.Data;

namespace AccountingSystem.Services
{




    /// <summary>
    /// Result of barcode processing
    /// </summary>
    public class BarcodeProcessResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public Item? Item { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
    }

    public class POSService : IPOSService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<POSService> _logger;

        public POSService(AccountingDbContext context, ILogger<POSService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<bool> ValidateActiveSessionAsync(string? username = null, string? store = null)
        {
            try
            {
                // Check for active POS session in tblPOSSessions
                var activeSession = await _context.POSSessions
                    .Include(s => s.Shop)
                    .Where(s => s.Status == "Open" && s.Shop.StoreName == store)
                    .FirstOrDefaultAsync();

                if (activeSession == null)
                {
                    _logger.LogWarning("No active POS session found for store: {Store}", store);
                    return false;
                }

                // If username is provided, check if user has access to this store
                if (!string.IsNullOrEmpty(username))
                {
                    var user = await _context.Users
                        .Where(u => u.Username == username)
                        .FirstOrDefaultAsync();

                    if (user != null)
                    {
                        // If user has a default store and it's different from the requested store
                        if (!string.IsNullOrEmpty(user.DefaultStore) && user.DefaultStore != store)
                        {
                            // Check if user can change stores
                            if (!user.StoreChange)
                            {
                                _logger.LogWarning("User {Username} cannot change store from {DefaultStore} to {RequestedStore}", 
                                    username, user.DefaultStore, store);
                                return false;
                            }
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating active session for user: {Username}, store: {Store}", username, store);
                return false;
            }
        }

        public async Task<POSSession?> GetActiveSessionAsync(string? username = null, string? store = null)
        {
            try
            {
                var query = _context.POSSessions
                    .Include(s => s.Shop)
                    .Where(s => s.Status == "Open");

                if (!string.IsNullOrEmpty(store))
                {
                    query = query.Where(s => s.Shop.StoreName == store);
                }

                if (!string.IsNullOrEmpty(username))
                {
                    query = query.Where(s => s.OpenedBy == username);
                }

                var session = await query.FirstOrDefaultAsync();
                return session;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active session for user: {Username}, store: {Store}", username, store);
                return null;
            }
        }

        public async Task<int> GetNextInvoiceNumberAsync()
        {
            try
            {
                // First, try to get an existing unused invoice
                var existingInvoice = await _context.POSInvoices
                    .Where(i => i.ReadyForUse == "False" && i.TrxType == "مبيعات")
                    .OrderBy(i => i.TrxNo)
                    .FirstOrDefaultAsync();

                if (existingInvoice != null)
                {
                    return existingInvoice.TrxNo;
                }

                // If no existing invoice, create a new one with controlled numbering
                var maxInvoiceNo = await _context.POSInvoices
                    .Where(i => i.TrxType == "مبيعات")
                    .MaxAsync(i => (int?)i.TrxNo) ?? 0;

                // Generate next invoice number with proper formatting
                var nextInvoiceNo = maxInvoiceNo + 1;
                
                // Ensure the invoice number is unique
                while (await _context.POSInvoices.AnyAsync(i => i.TrxNo == nextInvoiceNo))
                {
                    nextInvoiceNo++;
                }

                return nextInvoiceNo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting next invoice number");
                throw;
            }
        }

        public async Task<POSInvoice?> GetCurrentInvoiceAsync()
        {
            try
            {
                // Use a more direct approach to avoid casting issues
                var invoice = await _context.POSInvoices
                    .Where(i => i.ReadyForUse == "False" && i.TrxType == "مبيعات")
                    .OrderByDescending(i => i.TrxNo)
                    .FirstOrDefaultAsync();
                if (invoice == null) return null;
                // جلب عناصر الفاتورة يدويًا
                // يمكن للمستدعي أن ينفذ استعلامًا آخر إذا احتاج العناصر
                return invoice;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current invoice");
                throw;
            }
        }

        public async Task<POSInvoice> CreateNewInvoiceAsync(string username, string store)
        {
            try
            {
                // Validate active session before creating invoice
                if (!await ValidateActiveSessionAsync(username, store))
                {
                    throw new InvalidOperationException($"لا يوجد جلسة نشطة للمستخدم {username} في المتجر {store}. يرجى فتح جلسة جديدة أولاً.");
                }

                var invoiceNo = await GetNextInvoiceNumberAsync();

                // Check if invoice already exists
                var existingInvoice = await _context.POSInvoices
                    .FirstOrDefaultAsync(i => i.TrxNo == invoiceNo);

                if (existingInvoice != null)
                {
                    // Update existing invoice
                    existingInvoice.ReadyForUse = "False";
                    existingInvoice.ModifiedBy = username;
                    existingInvoice.ModifiedOn = DateTime.Now;
                    await _context.SaveChangesAsync();
                    return existingInvoice;
                }

                // Create new invoice
                var invoice = new POSInvoice
                {
                    TrxNo = invoiceNo,
                    TrxType = "مبيعات",
                    TrxDate = DateTime.Now,
                    Store = store,
                    Cashier = username,
                    ReadyForUse = "False",
                    CreatedBy = username,
                    CreatedOn = DateTime.Now
                };

                _context.POSInvoices.Add(invoice);
                await _context.SaveChangesAsync();

                return invoice;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating new invoice for user {Username} in store {Store}", username, store);
                throw;
            }
        }

        public async Task<POSInvoiceItem> AddItemToInvoiceAsync(int invoiceNo, long itemNo, decimal quantity, decimal unitPrice, string username, string store)
        {
            try
            {
                // Get item details
                var item = await _context.Items.FirstOrDefaultAsync(i => i.ItemNo == itemNo);
                if (item == null)
                {
                    throw new ArgumentException($"Item with number {itemNo} not found");
                }

                // Get next line number
                var maxLineSN = await _context.POSInvoiceItems
                    .Where(pi => pi.DocNo == invoiceNo)
                    .MaxAsync(pi => (int?)pi.LineSN) ?? 0;

                var lineSN = maxLineSN + 1;

                // Check if item already exists in invoice
                var existingItem = await _context.POSInvoiceItems
                    .FirstOrDefaultAsync(pi => pi.DocNo == invoiceNo && pi.ItemNo == itemNo);

                if (existingItem != null)
                {
                    // Update existing item
                    existingItem.TrxQTY += quantity;
                    existingItem.UnitPrice = unitPrice;
                    existingItem.LineAmount = existingItem.TrxQTY * unitPrice;
                    existingItem.VATAmount = await CalculateVATAsync(existingItem.LineAmount, item.Tax_Percent ?? 0, false);
                    existingItem.ModifiedBy = username;
                    existingItem.ModifiedOn = DateTime.Now;

                    await _context.SaveChangesAsync();
                    return existingItem;
                }

                // Calculate VAT and line amount
                var lineAmount = quantity * unitPrice;
                var vatAmount = await CalculateVATAsync(lineAmount, item.Tax_Percent ?? 0, false);

                // Create new invoice item
                var invoiceItem = new POSInvoiceItem
                {
                    DocNo = invoiceNo,
                    LineSN = lineSN,
                    TrxDate = DateTime.Now,
                    ItemNo = itemNo,
                    Store = store,
                    TrxQTY = quantity,
                    TrxType = "مبيعات",
                    UnitPrice = unitPrice,
                    UofM = item.SalesUofM ?? item.UofM,
                    UofMConversion = 1, // Default conversion factor
                    VATAmount = vatAmount,
                    LineAmount = lineAmount,
                    CreatedBy = username,
                    CreatedOn = DateTime.Now
                };

                _context.POSInvoiceItems.Add(invoiceItem);
                await _context.SaveChangesAsync();

                return invoiceItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding item to invoice");
                throw;
            }
        }

        public async Task<bool> UpdateInvoiceItemAsync(int invoiceNo, int lineSN, decimal quantity, decimal unitPrice, string username)
        {
            try
            {
                var invoiceItem = await _context.POSInvoiceItems
                    .FirstOrDefaultAsync(pi => pi.DocNo == invoiceNo && pi.LineSN == lineSN);

                if (invoiceItem == null)
                {
                    return false;
                }

                // جلب نسبة الضريبة من جدول الأصناف
                var item = await _context.Items.FirstOrDefaultAsync(i => i.ItemNo == invoiceItem.ItemNo);
                var taxPercent = item?.Tax_Percent ?? 0;

                invoiceItem.TrxQTY = quantity;
                invoiceItem.UnitPrice = unitPrice;
                invoiceItem.LineAmount = quantity * unitPrice;
                invoiceItem.VATAmount = await CalculateVATAsync(invoiceItem.LineAmount, taxPercent, false);
                invoiceItem.ModifiedBy = username;
                invoiceItem.ModifiedOn = DateTime.Now;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice item");
                throw;
            }
        }

        public async Task<bool> DeleteInvoiceItemAsync(int invoiceNo, int lineSN, string username)
        {
            try
            {
                var invoiceItem = await _context.POSInvoiceItems
                    .FirstOrDefaultAsync(pi => pi.DocNo == invoiceNo && pi.LineSN == lineSN);

                if (invoiceItem == null)
                {
                    return false;
                }

                _context.POSInvoiceItems.Remove(invoiceItem);

                // Renumber remaining lines
                var remainingItems = await _context.POSInvoiceItems
                    .Where(pi => pi.DocNo == invoiceNo && pi.LineSN > lineSN)
                    .OrderBy(pi => pi.LineSN)
                    .ToListAsync();

                foreach (var item in remainingItems)
                {
                    item.LineSN--;
                    item.ModifiedBy = username;
                    item.ModifiedOn = DateTime.Now;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice item");
                throw;
            }
        }

        public async Task<POSInvoice> SaveInvoiceAsync(int invoiceNo, string username)
        {
            try
            {
                var invoice = await _context.POSInvoices
                    .FirstOrDefaultAsync(i => i.TrxNo == invoiceNo);

                if (invoice == null)
                {
                    throw new ArgumentException($"Invoice {invoiceNo} not found");
                }

                // جلب عناصر الفاتورة يدويًا
                var items = await _context.POSInvoiceItems.Where(x => x.DocNo == invoiceNo).ToListAsync();
                var totalAmount = items.Sum(item => item.LineAmount);
                var totalVAT = items.Sum(item => item.VATAmount);

                invoice.TrxTotal = totalAmount - totalVAT;
                invoice.TrxVAT = totalVAT;
                invoice.TrxNetAmount = totalAmount;
                invoice.ModifiedBy = username;
                invoice.ModifiedOn = DateTime.Now;

                await _context.SaveChangesAsync();
                return invoice;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving invoice");
                throw;
            }
        }

        public async Task<bool> UpdateInvoiceCustomerAsync(int invoiceNo, long? customerId, string customerName, string customerPhone, string username)
        {
            try
            {
                var invoice = await _context.POSInvoices
                    .FirstOrDefaultAsync(i => i.TrxNo == invoiceNo);

                if (invoice == null)
                {
                    return false;
                }

                invoice.PartnerNo = customerId;
                invoice.PartnerName = customerName;
                invoice.PartnerPhoneNo = customerPhone;
                invoice.ModifiedBy = username;
                invoice.ModifiedOn = DateTime.Now;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice customer");
                return false;
            }
        }

        public async Task<bool> UpdateInvoiceStoreAsync(int invoiceNo, string store, string username)
        {
            try
            {
                var invoice = await _context.POSInvoices
                    .FirstOrDefaultAsync(i => i.TrxNo == invoiceNo);

                if (invoice == null)
                {
                    return false;
                }

                invoice.Store = store;
                invoice.ModifiedBy = username;
                invoice.ModifiedOn = DateTime.Now;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice store");
                return false;
            }
        }

        public async Task<POSInvoice> CompleteInvoiceAsync(int invoiceNo, decimal cashAmount, decimal cardAmount, string username)
        {
            try
            {
                var invoice = await _context.POSInvoices
                    .FirstOrDefaultAsync(i => i.TrxNo == invoiceNo);

                if (invoice == null)
                {
                    throw new ArgumentException($"Invoice {invoiceNo} not found");
                }

                // Save invoice first
                await SaveInvoiceAsync(invoiceNo, username);

                // Add payment records
                if (cashAmount > 0)
                {
                    var cashPayment = new POSPayment
                    {
                        TrxNo = invoiceNo,
                        Pay_mthd = 1, // Cash
                        Pay_amnt = cashAmount,
                        CreatedBy = username,
                        CreatedOn = DateTime.Now
                    };
                    _context.POSPayments.Add(cashPayment);
                }

                if (cardAmount > 0)
                {
                    var cardPayment = new POSPayment
                    {
                        TrxNo = invoiceNo,
                        Pay_mthd = 2, // Card
                        Pay_amnt = cardAmount,
                        CreatedBy = username,
                        CreatedOn = DateTime.Now
                    };
                    _context.POSPayments.Add(cardPayment);
                }

                // Generate QR code for ZATCA compliance
                var timestamp = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ");
                var totalAmount = (invoice.TrxNetAmount ?? 0).ToString("F2");
                var vatAmount = (invoice.TrxVAT ?? 0).ToString("F2");
                
                var qrCodeData = await GenerateQRCodeAsync(
                    "نظام السلطان للمبيعات والمحاسبة", // Seller name - should be configurable
                    "123456789012345", // VAT registration number - should be configurable
                    timestamp,
                    totalAmount,
                    vatAmount
                );
                
                if (qrCodeData != null)
                {
                    // Store QR code data in the invoice
                    invoice.QRCodeImage = qrCodeData;
                    _logger.LogInformation("QR code generated and saved for invoice {InvoiceNo}", invoiceNo);
                }

                // Mark invoice as completed
                invoice.ReadyForUse = "True";
                invoice.ModifiedBy = username;
                invoice.ModifiedOn = DateTime.Now;

                await _context.SaveChangesAsync();

                // Create GL entries using stored procedures (same as VB.NET frmPOS)
                try
                {
                    _logger.LogInformation("Creating GL entries for invoice: {InvoiceNo}", invoiceNo);
                    
                    // Create all GL entries (Sales, Payment, and COGS)
                    var glResult = await CreateAllGLEntriesAsync(invoiceNo);
                    
                    if (glResult)
                    {
                        _logger.LogInformation("Successfully created all GL entries for invoice: {InvoiceNo}", invoiceNo);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to create some GL entries for invoice: {InvoiceNo}", invoiceNo);
                        // Note: We don't throw here to avoid breaking the invoice completion process
                        // The GL entries can be created manually later if needed
                    }
                }
                catch (Exception glEx)
                {
                    _logger.LogError(glEx, "Error creating GL entries for invoice: {InvoiceNo}", invoiceNo);
                    // Note: We don't throw here to avoid breaking the invoice completion process
                    // The GL entries can be created manually later if needed
                }

                return invoice;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing invoice");
                throw;
            }
        }

        public async Task<List<Item>> GetFavoriteItemsAsync(string username)
        {
            try
            {
                // For now, return top selling items or most used items
                return await _context.Items
                    .Where(i => i.Status == 1)
                    .OrderBy(i => i.ItemDescription)
                    .Take(10)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting favorite items for user: {Username}", username);
                return new List<Item>();
            }
        }

        public async Task<List<Item>> SearchItemsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await _context.Items.Where(i => i.Status == 1).Take(50).ToListAsync();
            }

            // Search by item code (exact match) or description (contains)
            return await _context.Items
                .Where(i => i.Status == 1 && (
                    i.ItemNo.ToString() == searchTerm || 
                    i.Barcode == searchTerm ||
                    i.ItemDescription.Contains(searchTerm) ||
                    i.ItemDescription2.Contains(searchTerm)
                ))
                .OrderBy(i => i.ItemDescription)
                .Take(20) // Limit results for performance
                .ToListAsync();
        }

        public async Task<Item?> GetItemByBarcodeAsync(string barcode)
        {
            return await _context.Items.FirstOrDefaultAsync(i => i.Barcode == barcode && i.Status == 1);
        }

        public async Task<List<Store>> GetStoresAsync()
        {
            try
            {
                // Get stores from tblShops (which is mapped to Store entity)
                return await _context.Shops
                    .OrderBy(s => s.StoreName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stores from store table");
                return new List<Store>();
            }
        }

        public async Task<List<ChartOfAccount>> GetCustomersAsync()
        {
            try
            {
                // Get customers from Chart of Accounts where parent is customers account
                var customerParentAccount = await _context.GLConfigs
                    .Where(g => g.EntryReferenceModule == "عملاء")
                    .Select(g => g.AccountNo.ToString()) // Convert int to string for comparison
                    .FirstOrDefaultAsync();

                if (string.IsNullOrEmpty(customerParentAccount))
                {
                    return new List<ChartOfAccount>();
                }

                return await _context.ChartOfAccounts
                    .Where(c => c.ParentAccountCode == customerParentAccount && c.IsPosting)
                    .OrderBy(c => c.AccountName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customers");
                throw;
            }
        }

        public async Task<InvoiceToolSetting?> GetPOSSettingsAsync()
        {
            // Now uses the correct, unified model
            return await _context.InvoiceToolSettings.FirstOrDefaultAsync(s => s.InvoiceType == "مبيعات");
        }

        public async Task<decimal> CalculateVATAsync(decimal amount, decimal vatPercentage, bool isPriceIncludeVAT)
        {
            try
            {
                if (isPriceIncludeVAT)
                {
                    // VAT is included in price, extract it
                    return Math.Round((amount * vatPercentage) / (100 + vatPercentage), 2);
                }
                else
                {
                    // VAT is added to price
                    return Math.Round((amount * vatPercentage) / 100, 2);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating VAT");
                throw;
            }
        }

        public async Task<decimal> CalculateDiscountAsync(decimal amount, decimal discountPercentage)
        {
            try
            {
                return Math.Round((amount * discountPercentage) / 100, 2);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating discount");
                throw;
            }
        }

        public async Task<byte[]?> GenerateQRCodeAsync(string sellerName, string vatReg, string timestamp, string total, string vatAmount)
        {
            try
            {
                // Generate QR code data according to ZATCA requirements
                // Format: TLV (Tag-Length-Value) encoding
                var qrData = new StringBuilder();
                
                // Seller Name (Tag 1)
                qrData.Append("01");
                qrData.Append(sellerName.Length.ToString("D2"));
                qrData.Append(sellerName);
                
                // VAT Registration Number (Tag 2)
                qrData.Append("02");
                qrData.Append(vatReg.Length.ToString("D2"));
                qrData.Append(vatReg);
                
                // Timestamp (Tag 3)
                qrData.Append("03");
                qrData.Append(timestamp.Length.ToString("D2"));
                qrData.Append(timestamp);
                
                // Total Amount (Tag 4)
                qrData.Append("04");
                qrData.Append(total.Length.ToString("D2"));
                qrData.Append(total);
                
                // VAT Amount (Tag 5)
                qrData.Append("05");
                qrData.Append(vatAmount.Length.ToString("D2"));
                qrData.Append(vatAmount);
                
                var qrString = qrData.ToString();
                
                // For now, return the QR string as bytes
                // TODO: Implement proper QR code image generation
                return System.Text.Encoding.UTF8.GetBytes(qrString);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating QR code");
                return null;
            }
        }

        public async Task<List<POSInvoiceItem>> GetInvoiceItemsAsync(int invoiceNo)
        {
            try
            {
                return await _context.POSInvoiceItems
                    .Where(pi => pi.DocNo == invoiceNo && pi.TrxType == "مبيعات")
                    .OrderBy(pi => pi.LineSN)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice items for invoice: {InvoiceNo}", invoiceNo);
                return new List<POSInvoiceItem>();
            }
        }

        public async Task<List<POSPayment>> GetInvoicePaymentsAsync(int invoiceNo)
        {
            try
            {
                return await _context.POSPayments
                    .Where(p => p.TrxNo == invoiceNo)
                    .OrderBy(p => p.Pay_mthd)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice payments");
                throw;
            }
        }

        public async Task<POSInvoice?> GetInvoiceByNumberAsync(int invoiceNo)
        {
            return await _context.POSInvoices.FirstOrDefaultAsync(i => i.TrxNo == invoiceNo);
        }

        public async Task<string> GetThermalReceiptContentAsync(int invoiceNo)
        {
            try
            {
                var invoice = await _context.POSInvoices
                    .FirstOrDefaultAsync(i => i.TrxNo == invoiceNo);

                if (invoice == null)
                    throw new ArgumentException($"Invoice {invoiceNo} not found");

                var invoiceItems = await GetInvoiceItemsAsync(invoiceNo);
                var payments = await GetInvoicePaymentsAsync(invoiceNo);

                var cashAmount = payments.Where(p => p.Pay_mthd == 1).Sum(p => p.Pay_amnt);
                var cardAmount = payments.Where(p => p.Pay_mthd == 2).Sum(p => p.Pay_amnt);

                // Generate thermal receipt content
                var receipt = new StringBuilder();
                
                // Header
                receipt.AppendLine("=".PadRight(32, '='));
                receipt.AppendLine("نظام السلطان للمبيعات والمحاسبة");
                receipt.AppendLine("=".PadRight(32, '='));
                receipt.AppendLine($"رقم الفاتورة: {invoice.TrxNo}");
                receipt.AppendLine($"التاريخ: {invoice.TrxDate:yyyy/MM/dd HH:mm}");
                receipt.AppendLine($"المتجر: {invoice.Store}");
                receipt.AppendLine($"الكاشير: {invoice.Cashier}");
                receipt.AppendLine($"العميل: {invoice.PartnerName ?? "عميل نقدي"}");
                if (!string.IsNullOrEmpty(invoice.PartnerPhoneNo))
                    receipt.AppendLine($"الهاتف: {invoice.PartnerPhoneNo}");
                receipt.AppendLine("-".PadRight(32, '-'));

                // Items
                receipt.AppendLine("البيان".PadRight(15) + "الكمية".PadLeft(5) + "السعر".PadLeft(6) + "الإجمالي".PadLeft(6));
                receipt.AppendLine("-".PadRight(32, '-'));

                foreach (var item in invoiceItems)
                {
                    var description = await GetItemDescriptionAsync(item.ItemNo);
                    var line = $"{description.Substring(0, Math.Min(15, description.Length))}".PadRight(15) +
                              $"{item.TrxQTY:F2}".PadLeft(5) +
                              $"{item.UnitPrice:F2}".PadLeft(6) +
                              $"{item.LineAmount:F2}".PadLeft(6);
                    receipt.AppendLine(line);
                }

                receipt.AppendLine("-".PadRight(32, '-'));

                // Totals
                receipt.AppendLine($"المجموع: {invoice.TrxTotal:F2}".PadLeft(32));
                if (invoice.TrxVAT > 0)
                    receipt.AppendLine($"الضريبة: {invoice.TrxVAT:F2}".PadLeft(32));
                if (invoice.TrxDiscountValue > 0)
                    receipt.AppendLine($"الخصم: {invoice.TrxDiscountValue:F2}".PadLeft(32));
                receipt.AppendLine($"الإجمالي: {invoice.TrxNetAmount:F2}".PadLeft(32));

                // Payment
                receipt.AppendLine("-".PadRight(32, '-'));
                if (cashAmount > 0)
                    receipt.AppendLine($"نقدي: {cashAmount:F2}".PadLeft(32));
                if (cardAmount > 0)
                    receipt.AppendLine($"بطاقة: {cardAmount:F2}".PadLeft(32));
                
                var change = (cashAmount + cardAmount) - invoice.TrxNetAmount;
                if (change > 0)
                    receipt.AppendLine($"المتبقي: {change:F2}".PadLeft(32));

                // Footer
                receipt.AppendLine("=".PadRight(32, '='));
                receipt.AppendLine("شكراً لزيارتكم");
                receipt.AppendLine("نتمنى لكم يومٍ سعيدٍ");
                receipt.AppendLine("=".PadRight(32, '='));
                receipt.AppendLine(); // Extra line for paper feed

                return receipt.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating thermal receipt content for invoice {InvoiceNo}", invoiceNo);
                throw;
            }
        }

        public async Task<byte[]> GenerateThermalReceiptAsync(int invoiceNo)
        {
            try
            {
                var receiptContent = await GetThermalReceiptContentAsync(invoiceNo);
                
                // Convert to thermal printer format (ESC/POS commands)
                var thermalBytes = new List<byte>();
                
                // Initialize printer
                thermalBytes.AddRange(new byte[] { 0x1B, 0x40 }); // ESC @ - Initialize printer
                
                // Set alignment to center for header
                thermalBytes.AddRange(new byte[] { 0x1B, 0x61, 0x01 }); // ESC a 1 - Center alignment
                
                // Set font size to double height and width
                thermalBytes.AddRange(new byte[] { 0x1B, 0x21, 0x30 }); // ESC ! 48 - Double height and width
                
                // Add company name
                var companyName = "نظام السلطان للمبيعات والمحاسبة";
                thermalBytes.AddRange(Encoding.UTF8.GetBytes(companyName));
                thermalBytes.Add(0x0A); // Line feed
                
                // Reset font size
                thermalBytes.AddRange(new byte[] { 0x1B, 0x21, 0x00 }); // ESC ! 0 - Normal size
                
                // Set alignment to right
                thermalBytes.AddRange(new byte[] { 0x1B, 0x61, 0x02 }); // ESC a 2 - Right alignment
                
                // Add invoice details
                var invoice = await _context.POSInvoices.FirstOrDefaultAsync(i => i.TrxNo == invoiceNo);
                if (invoice != null)
                {
                    thermalBytes.AddRange(Encoding.UTF8.GetBytes($"رقم الفاتورة: {invoice.TrxNo}"));
                    thermalBytes.Add(0x0A);
                    thermalBytes.AddRange(Encoding.UTF8.GetBytes($"التاريخ: {invoice.TrxDate:yyyy/MM/dd HH:mm}"));
                    thermalBytes.Add(0x0A);
                    thermalBytes.AddRange(Encoding.UTF8.GetBytes($"المتجر: {invoice.Store}"));
                    thermalBytes.Add(0x0A);
                    thermalBytes.AddRange(Encoding.UTF8.GetBytes($"الكاشير: {invoice.Cashier}"));
                    thermalBytes.Add(0x0A);
                    
                    if (!string.IsNullOrEmpty(invoice.PartnerName))
                    {
                        thermalBytes.AddRange(Encoding.UTF8.GetBytes($"العميل: {invoice.PartnerName}"));
                        thermalBytes.Add(0x0A);
                    }
                }
                
                // Add separator line
                thermalBytes.AddRange(new byte[] { 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D });
                thermalBytes.Add(0x0A);
                
                // Set alignment to left for items
                thermalBytes.AddRange(new byte[] { 0x1B, 0x61, 0x00 }); // ESC a 0 - Left alignment
                
                // Add items
                var items = await GetInvoiceItemsAsync(invoiceNo);
                foreach (var item in items)
                {
                    var itemDescription = await GetItemDescriptionAsync(item.ItemNo);
                    thermalBytes.AddRange(Encoding.UTF8.GetBytes(itemDescription));
                    thermalBytes.Add(0x0A);
                    thermalBytes.AddRange(Encoding.UTF8.GetBytes($"{item.TrxQTY:F2} x {item.UnitPrice:F2} = {item.LineAmount:F2}"));
                    thermalBytes.Add(0x0A);
                }
                
                // Add separator
                thermalBytes.AddRange(new byte[] { 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D });
                thermalBytes.Add(0x0A);
                
                // Set alignment to right for totals
                thermalBytes.AddRange(new byte[] { 0x1B, 0x61, 0x02 }); // ESC a 2 - Right alignment
                
                // Add totals
                var totalAmount = items.Sum(i => i.LineAmount);
                var vatAmount = items.Sum(i => i.VATAmount);
                var netAmount = totalAmount;
                
                thermalBytes.AddRange(Encoding.UTF8.GetBytes($"المجموع: {totalAmount:F2}"));
                thermalBytes.Add(0x0A);
                thermalBytes.AddRange(Encoding.UTF8.GetBytes($"الضريبة: {vatAmount:F2}"));
                thermalBytes.Add(0x0A);
                thermalBytes.AddRange(Encoding.UTF8.GetBytes($"الإجمالي: {netAmount:F2}"));
                thermalBytes.Add(0x0A);
                
                // Add payments
                var payments = await GetInvoicePaymentsAsync(invoiceNo);
                var cashAmount = payments.Where(p => p.Pay_mthd == 1).Sum(p => p.Pay_amnt);
                var cardAmount = payments.Where(p => p.Pay_mthd == 2).Sum(p => p.Pay_amnt);
                
                if (cashAmount > 0)
                {
                    thermalBytes.AddRange(Encoding.UTF8.GetBytes($"نقدي: {cashAmount:F2}"));
                    thermalBytes.Add(0x0A);
                }
                if (cardAmount > 0)
                {
                    thermalBytes.AddRange(Encoding.UTF8.GetBytes($"بطاقة: {cardAmount:F2}"));
                    thermalBytes.Add(0x0A);
                }
                
                // Add footer
                thermalBytes.Add(0x0A);
                thermalBytes.AddRange(Encoding.UTF8.GetBytes("شكراً لزيارتكم"));
                thermalBytes.Add(0x0A);
                thermalBytes.AddRange(Encoding.UTF8.GetBytes("نتمنى لكم يومٍ سعيدٍ"));
                thermalBytes.Add(0x0A);
                
                // Cut paper
                thermalBytes.AddRange(new byte[] { 0x1D, 0x56, 0x00 }); // GS V 0 - Full cut
                
                return thermalBytes.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating thermal receipt for invoice {InvoiceNo}", invoiceNo);
                throw;
            }
        }

        public async Task<(List<Item> items, List<Store> stores)> GetPOSInitDataAsync()
        {
            var items = await _context.Items.Where(i => i.Status == 1).ToListAsync();
            var stores = await _context.Shops.ToListAsync();
            return (items, stores);
        }

        public async Task<List<string>> GetStoreNamesAsync()
        {
            return await _context.Shops
                .Select(s => s.StoreName)
                .ToListAsync();
        }

        public async Task<List<string>> GetCashierNamesAsync()
        {
            return await _context.Users
                .Select(u => u.Username)
                .ToListAsync();
        }

        public async Task<List<string>> GetPaymentMethodNamesAsync()
        {
            // This can be expanded to fetch from a database table if needed
            return await Task.FromResult(new List<string> { "نقدي", "شبكة" });
        }
        public async Task<Store?> GetUserDefaultStoreAsync(string username)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == username);

                if (user?.DefaultStore == null)
                    return null;

                return await _context.Shops
                    .FirstOrDefaultAsync(s => s.StoreName == user.DefaultStore);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user default store for user: {Username}", username);
                return null;
            }
        }

        public async Task<PurchaseUserAuthorization> GetUserAuthorizationAsync(string username)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.Group)
                    .FirstOrDefaultAsync(u => u.Username == username);

                if (user == null)
                {
                    return new PurchaseUserAuthorization { Username = username };
                }

                return new PurchaseUserAuthorization
                {
                    Username = user.Username,
                    GroupID = user.GroupID,
                    DefaultStore = user.DefaultStore,
                    StoreChange = user.StoreChange,
                    DefaultCustomer = user.DefaultCustomer,
                    CustomerChange = user.CustomerChange,
                    DefaultCashier = user.DefaultCashier,
                    CashierChange = user.CashierChange,
                    ChangeInvoicePrice = user.ChangeInvoicePrice,
                    MaxDiscountPercent = user.MaxDiscountPercent ?? 0m,
                    IsAdmin = user.GroupID == 1 // GroupID 1 is Admin
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user authorization for: {Username}", username);
                return new PurchaseUserAuthorization { Username = username };
            }
        }

        public async Task<bool> CanUserChangeCustomerAsync(string username)
        {
            try
            {
                var authInfo = await GetUserAuthorizationAsync(username);
                return authInfo.IsAdmin || authInfo.CustomerChange;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking customer change permission for user: {Username}", username);
                return false;
            }
        }

        public async Task<bool> CanUserChangePriceAsync(string username)
        {
            try
            {
                var authInfo = await GetUserAuthorizationAsync(username);
                return authInfo.IsAdmin || authInfo.ChangeInvoicePrice;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking price change permission for user: {Username}", username);
                return false;
            }
        }

        public async Task<decimal> GetUserMaxDiscountPercentAsync(string username)
        {
            try
            {
                var authInfo = await GetUserAuthorizationAsync(username);
                
                if (authInfo.IsAdmin)
                    return 99.99m; // Admin gets maximum discount
                
                return authInfo.MaxDiscountPercent ?? 0m;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting max discount percent for user: {Username}", username);
                return 0m;
            }
        }

        public async Task<bool> CanUserChangeStoreAsync(string username)
        {
            try
            {
                var authInfo = await GetUserAuthorizationAsync(username);
                return authInfo.IsAdmin || authInfo.StoreChange;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking store change permission for user: {Username}", username);
                return false;
            }
        }

        public async Task<BarcodeProcessResult> ProcessBarcodeAsync(string barcode, int invoiceNo, string store, string username)
        {
            try
            {
                _logger.LogInformation("Processing barcode: {Barcode} for invoice: {InvoiceNo} in store: {Store}", barcode, invoiceNo, store);

                // First, try to get barcode settings for weight processing
                var barcodeSettings = await _context.BarcodeSettings
                    .FirstOrDefaultAsync(bs => bs.Shop == store);

                decimal quantity = 1; // Default quantity
                long itemNo = 0;

                // Check if this is a weight barcode (starts with specific prefix and has embedded weight)
                if (barcodeSettings?.EnableEmbeddedWeight == true && 
                    !string.IsNullOrEmpty(barcodeSettings.EmbeddedFormat) &&
                    barcode.StartsWith("27")) // Common weight barcode prefix
                {
                    _logger.LogInformation("Processing weight barcode with format: {Format}", barcodeSettings.EmbeddedFormat);
                    
                    var format = barcodeSettings.EmbeddedFormat;
                    var weightDivisor = barcodeSettings.WeightDivisor ?? 1000;
                    
                    var itemLength = format.Count(c => c == 'x');
                    var weightLength = format.Count(c => c == 'w');
                    
                    _logger.LogInformation("Barcode length: {BarcodeLength}, Item length: {ItemLength}, Weight length: {WeightLength}", 
                        barcode.Length, itemLength, weightLength);
                    
                    if (barcode.Length >= itemLength + weightLength)
                    {
                        var itemCode = barcode.Substring(0, itemLength);
                        var weightStr = barcode.Substring(itemLength, weightLength);
                        
                        _logger.LogInformation("Extracted item code: {ItemCode}, weight string: {WeightStr}", itemCode, weightStr);
                        
                        if (int.TryParse(itemCode, out var parsedItemNo) && 
                            decimal.TryParse(weightStr, out var weightValue))
                        {
                            itemNo = parsedItemNo;
                            quantity = weightValue / weightDivisor;
                            _logger.LogInformation("Weight barcode processed - Item: {ItemNo}, Quantity: {Quantity}", itemNo, quantity);
                        }
                    }
                }

                // If not a weight barcode or weight processing failed, try regular barcode lookup
                if (itemNo == 0)
                {
                    _logger.LogInformation("Trying regular barcode lookup for: {Barcode}", barcode);
                    
                    // Try to find item by barcode first
                    var foundItem = await _context.Items
                        .FirstOrDefaultAsync(i => i.Barcode == barcode && i.Status == 1);

                    if (foundItem != null)
                    {
                        itemNo = foundItem.ItemNo ?? 0;
                        quantity = 1; // Default quantity for regular barcodes
                        _logger.LogInformation("Item found by barcode - Item: {ItemNo}", itemNo);
                    }
                    else
                    {
                        // Try to find by item number if barcode is numeric
                        if (int.TryParse(barcode, out var parsedItemNo))
                        {
                            foundItem = await _context.Items
                                .FirstOrDefaultAsync(i => i.ItemNo == parsedItemNo && i.Status == 1);
                            
                            if (foundItem != null)
                            {
                                itemNo = foundItem.ItemNo ?? 0;
                                quantity = 1;
                                _logger.LogInformation("Item found by item number - Item: {ItemNo}", itemNo);
                            }
                        }
                    }
                }

                // If still no item found, try partial description search
                if (itemNo == 0)
                {
                    _logger.LogInformation("Trying description search for: {Barcode}", barcode);
                    
                    var items = await _context.Items
                        .Where(i => i.ItemDescription.Contains(barcode) && i.Status == 1)
                        .Take(1)
                        .ToListAsync();

                    if (items.Any())
                    {
                        itemNo = items.First().ItemNo ?? 0;
                        quantity = 1;
                        _logger.LogInformation("Item found by description - Item: {ItemNo}", itemNo);
                    }
                }

                if (itemNo == 0)
                {
                    _logger.LogWarning("No item found for barcode: {Barcode}", barcode);
                    return new BarcodeProcessResult
                    {
                        Success = false,
                        Message = "لم يتم العثور على صنف بهذا الباركود"
                    };
                }

                // Get item details
                var item = await _context.Items.FirstOrDefaultAsync(i => i.ItemNo == itemNo);
                if (item == null)
                {
                    _logger.LogError("Item data not found for item number: {ItemNo}", itemNo);
                    return new BarcodeProcessResult
                    {
                        Success = false,
                        Message = "لم يتم العثور على بيانات الصنف"
                    };
                }

                _logger.LogInformation("Adding item to invoice - Item: {ItemNo}, Quantity: {Quantity}, UnitPrice: {UnitPrice}", 
                    itemNo, quantity, item.UnitSalesPrice);

                // Add item to invoice
                var unitPrice = item.UnitSalesPrice ?? 0;
                var invoiceItem = await AddItemToInvoiceAsync(invoiceNo, itemNo, quantity, unitPrice, username, store);

                return new BarcodeProcessResult
                {
                    Success = true,
                    Message = "تم إضافة الصنف بنجاح",
                    Item = item,
                    Quantity = quantity,
                    UnitPrice = unitPrice
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing barcode: {Barcode}", barcode);
                return new BarcodeProcessResult
                {
                    Success = false,
                    Message = "حدث خطأ أثناء معالجة الباركود"
                };
            }
        }

        public async Task<string> GetItemDescriptionAsync(long itemNo)
        {
            try
            {
                var item = await _context.Items.FirstOrDefaultAsync(i => i.ItemNo == itemNo);
                return item?.ItemDescription ?? $"صنف رقم {itemNo}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item description for item: {ItemNo}", itemNo);
                return $"صنف رقم {itemNo}";
            }
        }

        public async Task<List<POSInvoice>> SearchInvoicesAsync(string? invoiceNo, string? customer, string? user, string? warehouse, DateTime? date)
        {
            try
            {
                var query = _context.POSInvoices.Where(i => i.TrxType == "مبيعات");

                if (!string.IsNullOrEmpty(invoiceNo) && int.TryParse(invoiceNo, out int invNo))
                {
                    query = query.Where(i => i.TrxNo == invNo);
                }

                if (!string.IsNullOrEmpty(customer))
                {
                    query = query.Where(i => i.PartnerName != null && i.PartnerName.Contains(customer));
                }

                if (!string.IsNullOrEmpty(user))
                {
                    query = query.Where(i => i.Cashier != null && i.Cashier.Contains(user));
                }

                if (!string.IsNullOrEmpty(warehouse))
                {
                    query = query.Where(i => i.Store != null && i.Store.Contains(warehouse));
                }

                if (date.HasValue)
                {
                    var searchDate = date.Value.Date;
                    query = query.Where(i => i.TrxDate.HasValue && i.TrxDate.Value.Date == searchDate);
                }

                var results = await query
                    .OrderByDescending(i => i.TrxNo)
                    .Select(i => new POSInvoice
                    {
                        TrxNo = i.TrxNo,
                        TrxType = i.TrxType,
                        TrxDate = i.TrxDate,
                        Store = i.Store,
                        Cashier = i.Cashier,
                        PartnerNo = i.PartnerNo,
                        PartnerName = i.PartnerName,
                        PartnerPhoneNo = i.PartnerPhoneNo,
                        TrxTotal = i.TrxTotal,
                        TrxVAT = i.TrxVAT,
                        TrxDiscountValue = i.TrxDiscountValue,
                        TrxNetAmount = i.TrxNetAmount,
                        ReadyForUse = i.ReadyForUse
                    })
                    .ToListAsync();

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching invoices");
                return new List<POSInvoice>();
            }
        }

        public async Task<bool> ClearInvoiceItemsAsync(int invoiceNo)
        {
            try
            {
                var invoiceItems = await _context.POSInvoiceItems
                    .Where(pi => pi.DocNo == invoiceNo)
                    .ToListAsync();

                if (invoiceItems.Any())
                {
                    _context.POSInvoiceItems.RemoveRange(invoiceItems);
                    await _context.SaveChangesAsync();
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing invoice items for invoice: {InvoiceNo}", invoiceNo);
                return false;
            }
        }

        public async Task<long?> GetCashierAccountForStoreAsync(string store)
        {
            try
            {
                var cashier = await _context.Set<dynamic>()
                    .FromSqlRaw("SELECT TOP 1 CashierAccount FROM tblPOSCashier WHERE Store = {0}", store)
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
                if (cashier == null || cashier.CashierAccount == null)
                    return null;
                return (long)cashier.CashierAccount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching cashier account for store: {Store}", store);
                return null;
            }
        }

        #region GL Integration Methods

        /// <summary>
        /// Creates sales invoice GL entry using stored procedure
        /// </summary>
        public async Task<bool> CreateSalesInvoiceGLEntryAsync(int invoiceNo)
        {
            try
            {
                var connectionString = _context.Database.GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                {
                    _logger.LogError("Database connection string is null or empty");
                    return false;
                }

                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                using var command = new SqlCommand("sp_CreateSalesInvoiceEntryFromView", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };
                command.Parameters.AddWithValue("@InvoiceNo", invoiceNo);

                await command.ExecuteNonQueryAsync();
                
                _logger.LogInformation("Successfully created sales invoice GL entry for invoice: {InvoiceNo}", invoiceNo);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating sales invoice GL entry for invoice: {InvoiceNo}", invoiceNo);
                return false;
            }
        }

        /// <summary>
        /// Creates payment GL entry using stored procedure
        /// </summary>
        public async Task<bool> CreatePaymentGLEntryAsync(int invoiceNo)
        {
            try
            {
                var connectionString = _context.Database.GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                {
                    _logger.LogError("Database connection string is null or empty");
                    return false;
                }

                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                using var command = new SqlCommand("sp_CreatePaymentEntryFromView", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };
                command.Parameters.AddWithValue("@InvoiceNo", invoiceNo);

                await command.ExecuteNonQueryAsync();
                
                _logger.LogInformation("Successfully created payment GL entry for invoice: {InvoiceNo}", invoiceNo);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment GL entry for invoice: {InvoiceNo}", invoiceNo);
                return false;
            }
        }

        /// <summary>
        /// Creates COGS GL entry using stored procedure
        /// </summary>
        public async Task<bool> CreateCOGSGLEntryAsync(int invoiceNo)
        {
            try
            {
                var connectionString = _context.Database.GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                {
                    _logger.LogError("Database connection string is null or empty");
                    return false;
                }

                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                using var command = new SqlCommand("sp_CreateCOGSEntryFromView", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };
                command.Parameters.AddWithValue("@InvoiceNo", invoiceNo);

                await command.ExecuteNonQueryAsync();
                
                _logger.LogInformation("Successfully created COGS GL entry for invoice: {InvoiceNo}", invoiceNo);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating COGS GL entry for invoice: {InvoiceNo}", invoiceNo);
                return false;
            }
        }

        /// <summary>
        /// Creates all GL entries for a POS invoice (Sales, Payment, and COGS)
        /// </summary>
        public async Task<bool> CreateAllGLEntriesAsync(int invoiceNo)
        {
            try
            {
                _logger.LogInformation("Starting GL entry creation for invoice: {InvoiceNo}", invoiceNo);

                // Create sales invoice entry
                var salesResult = await CreateSalesInvoiceGLEntryAsync(invoiceNo);
                if (!salesResult)
                {
                    _logger.LogError("Failed to create sales invoice GL entry for invoice: {InvoiceNo}", invoiceNo);
                    return false;
                }

                // Create payment entry
                var paymentResult = await CreatePaymentGLEntryAsync(invoiceNo);
                if (!paymentResult)
                {
                    _logger.LogError("Failed to create payment GL entry for invoice: {InvoiceNo}", invoiceNo);
                    return false;
                }

                // Create COGS entry
                var cogsResult = await CreateCOGSGLEntryAsync(invoiceNo);
                if (!cogsResult)
                {
                    _logger.LogError("Failed to create COGS GL entry for invoice: {InvoiceNo}", invoiceNo);
                    return false;
                }

                _logger.LogInformation("Successfully created all GL entries for invoice: {InvoiceNo}", invoiceNo);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating all GL entries for invoice: {InvoiceNo}", invoiceNo);
                return false;
            }
        }

        #endregion
    }
} 
