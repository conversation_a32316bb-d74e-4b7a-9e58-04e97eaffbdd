@model AccountingSystem.Models.BarcodeSettings

@{
    ViewData["Title"] = "Barcode Settings Details";
}

<h1>@ViewData["Title"]</h1>

<div>
    <h4>Barcode Settings</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Shop)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Shop)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.BarcodeType)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.BarcodeType)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.EnableEmbeddedWeight)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnableEmbeddedWeight)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.EmbeddedFormat)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EmbeddedFormat)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.WeightDivisor)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.WeightDivisor)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CurrencyDivisor)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CurrencyDivisor)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Notes)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Notes)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Barcode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Barcode)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Weight)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Weight)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.FixedCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.FixedCode)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model.ID">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div> 