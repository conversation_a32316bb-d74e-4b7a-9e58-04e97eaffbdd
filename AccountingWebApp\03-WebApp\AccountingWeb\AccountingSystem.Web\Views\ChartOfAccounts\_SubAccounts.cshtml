@model List<AccountingSystem.Services.ViewModels.ChartOfAccountViewModel>

<table class="table table-sm table-bordered mb-0">
    <tbody>
        @foreach (var account in Model)
        {
            <tr data-account-code="@account.AccountCode" class="account-row">
                <td style="width: 20%;">
                    <span class="account-code">@account.AccountCode</span>
                </td>
                <td style="width: 30%;">
                    <span class="account-name">@account.AccountName</span>
                    @if (account.HasChildren)
                    {
                        <i class="fas fa-chevron-down ms-2 text-primary expand-icon" 
                           data-account-code="@account.AccountCode" 
                           style="cursor: pointer;"></i>
                    }
                </td>
                <td style="width: 15%;">
                    <span class="badge bg-info">@account.AccountType</span>
                </td>
                <td style="width: 15%;">
                    <span class="badge @(account.AccountNature == "Debit" ? "bg-success" : "bg-warning")">
                        @account.AccountNature
                    </span>
                </td>
                <td style="width: 10%;" class="text-end">
                    @account.OpeningBalance.ToString("N2")
                </td>
                <td style="width: 10%;">
                    <div class="btn-group" role="group">
                        <a href="@Url.Action("Create", "ChartOfAccounts", new { parentCode = account.AccountCode })" 
                           class="btn btn-sm btn-outline-primary" 
                           title="إضافة حساب فرعي">
                            <i class="fas fa-plus"></i>
                        </a>
                        <a href="@Url.Action("Edit", "ChartOfAccounts", new { accountCode = account.AccountCode })" 
                           class="btn btn-sm btn-outline-secondary" 
                           title="تعديل">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button type="button" 
                                class="btn btn-sm btn-outline-danger delete-account" 
                                data-account-code="@account.AccountCode"
                                data-account-name="@account.AccountName"
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            @if (account.HasChildren)
            {
                <tr class="sub-accounts-row" id="<EMAIL>" style="display: none;">
                    <td colspan="6" class="p-0">
                        <div class="sub-accounts-container ms-4">
                            @await Html.PartialAsync("_SubAccounts", account.Children)
                        </div>
                    </td>
                </tr>
            }
        }
    </tbody>
</table> 