﻿FUNCTION,dbo,CheckStockAvailability,SQL_SCALAR_FUNCTION,,bit,1,1,0,YES,OUTPUT,NULL,NULL
FUNCTION,dbo,CheckStockAvailability,SQL_SCALAR_FUNCTION,@ItemNo,bigint,8,19,0,YES,INPUT,NULL,NULL
FUNCTION,dbo,CheckStockAvailability,SQL_SCALAR_FUNCTION,@Store,nvarchar,-1,0,0,YES,INPUT,NULL,NULL
FUNCTION,dbo,CheckStockAvailability,SQL_SCALAR_FUNCTION,@RequiredQty,int,4,10,0,YES,INPUT,NULL,NULL
FUNCTION,dbo,ConvertToBaseUnit,SQL_TABLE_VALUED_FUNCTION,@ItemNo,bigint,8,19,0,YES,INPUT,NULL,NULL
FUNCTION,dbo,ConvertToBaseUnit,SQL_TABLE_VALUED_FUNCTION,@InputQty,decimal,9,18,4,YES,INPUT,NULL,NULL
FUNCTION,dbo,ConvertToBaseUnit,SQL_TABLE_VALUED_FUNCTION,@InputUnit,nvarchar,100,0,0,YES,INPUT,NULL,NULL
