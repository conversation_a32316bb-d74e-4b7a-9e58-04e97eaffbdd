using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class SystemSetupController : Controller
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SystemSetupController> _logger;
        private readonly IWebHostEnvironment _environment;

        public SystemSetupController(IConfiguration configuration, ILogger<SystemSetupController> logger, IWebHostEnvironment environment)
        {
            _configuration = configuration;
            _logger = logger;
            _environment = environment;
        }

        /// <summary>
        /// Display the system setup form
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                _logger.LogInformation("SystemSetup Index called by user: {Username}", User.Identity?.Name);

                // Check if user has admin privileges
                if (!await IsUserAdminAsync())
                {
                    _logger.LogWarning("User {Username} attempted to access SystemSetup without admin privileges", User.Identity?.Name);
                    TempData["Error"] = "ليس لديك صلاحية للوصول إلى إعدادات النظام";
                    return RedirectToAction("Index", "SimpleDashboard");
                }

                _logger.LogInformation("Loading system configuration for user: {Username}", User.Identity?.Name);
                var model = await LoadSystemConfigAsync();
                _logger.LogInformation("System configuration loaded successfully");
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading system setup page for user: {Username}", User.Identity?.Name);
                TempData["Error"] = "حدث خطأ أثناء تحميل إعدادات النظام";
                return RedirectToAction("Index", "SimpleDashboard");
            }
        }

        /// <summary>
        /// Save system configuration
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Index(SystemSetupViewModel model)
        {
            if (!await IsUserAdminAsync())
            {
                TempData["Error"] = "ليس لديك صلاحية لتعديل إعدادات النظام";
                return RedirectToAction("Index", "SimpleDashboard");
            }

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                await SaveSystemConfigAsync(model);
                TempData["Success"] = "تم حفظ إعدادات النظام بنجاح";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving system configuration");
                ModelState.AddModelError("", "حدث خطأ أثناء حفظ الإعدادات");
                return View(model);
            }
        }

        /// <summary>
        /// Upload logo file
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UploadLogo(IFormFile logoFile)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = "ليس لديك صلاحية لتعديل الشعار" });
            }

            if (logoFile == null || logoFile.Length == 0)
            {
                return Json(new { success = false, message = "يرجى اختيار ملف الشعار" });
            }

            // Validate file type
            var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif" };
            if (!allowedTypes.Contains(logoFile.ContentType.ToLower()))
            {
                return Json(new { success = false, message = "نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)" });
            }

            // Validate file size (max 5MB)
            if (logoFile.Length > 5 * 1024 * 1024)
            {
                return Json(new { success = false, message = "حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت" });
            }

            try
            {
                using (var memoryStream = new MemoryStream())
                {
                    await logoFile.CopyToAsync(memoryStream);
                    var logoBytes = memoryStream.ToArray();

                    var connectionString = _configuration.GetConnectionString("DefaultConnection");
                    using (var connection = new SqlConnection(connectionString))
                    {
                        await connection.OpenAsync();

                        var query = @"
                            UPDATE tblConfig 
                            SET LogoFile = @LogoFile, 
                                ModifiedBy = @ModifiedBy, 
                                ModifiedOn = GETDATE()";

                        using (var command = new SqlCommand(query, connection))
                        {
                            command.Parameters.AddWithValue("@LogoFile", logoBytes);
                            command.Parameters.AddWithValue("@ModifiedBy", User.Identity?.Name ?? "");
                            await command.ExecuteNonQueryAsync();
                        }
                    }
                }

                _logger.LogInformation("Logo uploaded successfully by user {Username}", User.Identity?.Name);
                return Json(new { success = true, message = "تم رفع الشعار بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading logo");
                return Json(new { success = false, message = "حدث خطأ أثناء رفع الشعار" });
            }
        }

        /// <summary>
        /// Delete logo
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteLogo()
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = "ليس لديك صلاحية لحذف الشعار" });
            }

            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        UPDATE tblConfig 
                        SET LogoFile = NULL, 
                            ModifiedBy = @ModifiedBy, 
                            ModifiedOn = GETDATE()";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ModifiedBy", User.Identity?.Name ?? "");
                        await command.ExecuteNonQueryAsync();
                    }
                }

                _logger.LogInformation("Logo deleted successfully by user {Username}", User.Identity?.Name);
                return Json(new { success = true, message = "تم حذف الشعار بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting logo");
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الشعار" });
            }
        }

        /// <summary>
        /// Get logo image
        /// </summary>
        public async Task<IActionResult> GetLogo()
        {
            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var query = "SELECT LogoFile FROM tblConfig";
                    using (var command = new SqlCommand(query, connection))
                    {
                        var logoBytes = await command.ExecuteScalarAsync() as byte[];
                        
                        if (logoBytes != null && logoBytes.Length > 0)
                        {
                            return File(logoBytes, "image/jpeg");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving logo");
            }

            // Return default placeholder image
            var placeholderPath = Path.Combine(_environment.WebRootPath, "images", "no-logo.png");
            if (System.IO.File.Exists(placeholderPath))
            {
                return PhysicalFile(placeholderPath, "image/png");
            }

            return NotFound();
        }

        /// <summary>
        /// Get store configuration for header display
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetStoreConfig()
        {
            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var query = "SELECT StoreName, LogoFile FROM tblConfig";
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                var config = new
                                {
                                    StoreName = reader["StoreName"]?.ToString() ?? "",
                                    HasLogo = reader["LogoFile"] != DBNull.Value
                                };
                                
                                return Json(config);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving store configuration");
            }

            return Json(new { StoreName = "", HasLogo = false });
        }

        #region Private Methods

        /// <summary>
        /// Load system configuration from database
        /// </summary>
        private async Task<SystemSetupViewModel> LoadSystemConfigAsync()
        {
            var model = new SystemSetupViewModel();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = "SELECT * FROM tblConfig";
                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            model.StoreName = reader["StoreName"]?.ToString() ?? "";
                            model.VATRegNo = reader["VATRegNo"]?.ToString() ?? "";
                            model.AddressFooter = reader["AddressFooter"]?.ToString() ?? "";
                            model.ItemDescription1 = reader["ItemDescription1"]?.ToString() ?? "";
                            model.ItemDescription2 = reader["ItemDescription2"]?.ToString() ?? "";
                            model.ItemLevel1 = reader["ItemLevel1"]?.ToString() ?? "";
                            model.ItemLevel2 = reader["ItemLevel2"]?.ToString() ?? "";
                            model.ItemLevel3 = reader["ItemLevel3"]?.ToString() ?? "";
                            model.ItemLevel4 = reader["ItemLevel4"]?.ToString() ?? "";
                            model.ItemLevel5 = reader["ItemLevel5"]?.ToString() ?? "";
                            model.HasLogo = reader["LogoFile"] != DBNull.Value;
                        }
                    }
                }
            }

            return model;
        }

        /// <summary>
        /// Save system configuration to database
        /// </summary>
        private async Task SaveSystemConfigAsync(SystemSetupViewModel model)
        {
            var connectionString = _configuration.GetConnectionString("DefaultConnection");
            var currentUser = User.Identity?.Name ?? "";

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Check if config exists
                var checkQuery = "SELECT COUNT(*) FROM tblConfig";
                using (var checkCommand = new SqlCommand(checkQuery, connection))
                {
                    var count = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());

                    string query;
                    if (count > 0)
                    {
                        // Update existing config
                        query = @"
                            UPDATE tblConfig 
                            SET StoreName = @StoreName,
                                VATRegNo = @VATRegNo,
                                AddressFooter = @AddressFooter,
                                ItemDescription1 = @ItemDescription1,
                                ItemDescription2 = @ItemDescription2,
                                ItemLevel1 = @ItemLevel1,
                                ItemLevel2 = @ItemLevel2,
                                ItemLevel3 = @ItemLevel3,
                                ItemLevel4 = @ItemLevel4,
                                ItemLevel5 = @ItemLevel5,
                                ModifiedBy = @ModifiedBy,
                                ModifiedOn = GETDATE()";
                    }
                    else
                    {
                        // Insert new config
                        query = @"
                            INSERT INTO tblConfig 
                            (StoreName, VATRegNo, AddressFooter, ItemDescription1, ItemDescription2,
                             ItemLevel1, ItemLevel2, ItemLevel3, ItemLevel4, ItemLevel5, 
                             ModifiedBy, ModifiedOn)
                            VALUES 
                            (@StoreName, @VATRegNo, @AddressFooter, @ItemDescription1, @ItemDescription2,
                             @ItemLevel1, @ItemLevel2, @ItemLevel3, @ItemLevel4, @ItemLevel5,
                             @ModifiedBy, GETDATE())";
                    }

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@StoreName", model.StoreName.Trim());
                        command.Parameters.AddWithValue("@VATRegNo", model.VATRegNo.Trim());
                        command.Parameters.AddWithValue("@AddressFooter", model.AddressFooter.Trim());
                        command.Parameters.AddWithValue("@ItemDescription1", model.ItemDescription1.Trim());
                        command.Parameters.AddWithValue("@ItemDescription2", model.ItemDescription2.Trim());
                        command.Parameters.AddWithValue("@ItemLevel1", model.ItemLevel1.Trim());
                        command.Parameters.AddWithValue("@ItemLevel2", model.ItemLevel2.Trim());
                        command.Parameters.AddWithValue("@ItemLevel3", model.ItemLevel3.Trim());
                        command.Parameters.AddWithValue("@ItemLevel4", model.ItemLevel4.Trim());
                        command.Parameters.AddWithValue("@ItemLevel5", model.ItemLevel5.Trim());
                        command.Parameters.AddWithValue("@ModifiedBy", currentUser);

                        await command.ExecuteNonQueryAsync();
                    }
                }
            }

            _logger.LogInformation("System configuration saved by user {Username}", currentUser);
        }

        /// <summary>
        /// Check if the current user has admin privileges
        /// </summary>
        private async Task<bool> IsUserAdminAsync()
        {
            var currentUsername = User.Identity?.Name;
            if (string.IsNullOrEmpty(currentUsername))
                return false;

            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT g.GroupName 
                        FROM tblUsers u 
                        INNER JOIN tblGroupsAuth g ON u.GroupID = g.GroupID 
                        WHERE u.Username = @Username";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Username", currentUsername);
                        var groupName = await command.ExecuteScalarAsync() as string;
                        
                        return !string.IsNullOrEmpty(groupName) && 
                               groupName.Equals("admin", StringComparison.OrdinalIgnoreCase);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking admin privileges for user {Username}", currentUsername);
                return false;
            }
        }

        #endregion
    }

    /// <summary>
    /// View model for system setup form
    /// </summary>
    public class SystemSetupViewModel
    {
        [Required(ErrorMessage = "اسم المتجر مطلوب")]
        [StringLength(200, ErrorMessage = "اسم المتجر يجب أن يكون أقل من 200 حرف")]
        [Display(Name = "اسم المتجر")]
        public string StoreName { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم التسجيل الضريبي مطلوب")]
        [StringLength(50, ErrorMessage = "رقم التسجيل الضريبي يجب أن يكون أقل من 50 حرف")]
        [Display(Name = "رقم التسجيل الضريبي")]
        public string VATRegNo { get; set; } = string.Empty;

        [Required(ErrorMessage = "عنوان التقرير مطلوب")]
        [StringLength(500, ErrorMessage = "عنوان التقرير يجب أن يكون أقل من 500 حرف")]
        [Display(Name = "عنوان التقرير")]
        public string AddressFooter { get; set; } = string.Empty;

        [Required(ErrorMessage = "وصف الصنف 1 مطلوب")]
        [StringLength(100, ErrorMessage = "وصف الصنف 1 يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "وصف الصنف 1")]
        public string ItemDescription1 { get; set; } = string.Empty;

        [Required(ErrorMessage = "وصف الصنف 2 مطلوب")]
        [StringLength(100, ErrorMessage = "وصف الصنف 2 يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "وصف الصنف 2")]
        public string ItemDescription2 { get; set; } = string.Empty;

        [Required(ErrorMessage = "المستوى 1 مطلوب")]
        [StringLength(50, ErrorMessage = "المستوى 1 يجب أن يكون أقل من 50 حرف")]
        [Display(Name = "تصنيف الأصناف - المستوى 1")]
        public string ItemLevel1 { get; set; } = string.Empty;

        [Required(ErrorMessage = "المستوى 2 مطلوب")]
        [StringLength(50, ErrorMessage = "المستوى 2 يجب أن يكون أقل من 50 حرف")]
        [Display(Name = "تصنيف الأصناف - المستوى 2")]
        public string ItemLevel2 { get; set; } = string.Empty;

        [Required(ErrorMessage = "المستوى 3 مطلوب")]
        [StringLength(50, ErrorMessage = "المستوى 3 يجب أن يكون أقل من 50 حرف")]
        [Display(Name = "تصنيف الأصناف - المستوى 3")]
        public string ItemLevel3 { get; set; } = string.Empty;

        [Required(ErrorMessage = "المستوى 4 مطلوب")]
        [StringLength(50, ErrorMessage = "المستوى 4 يجب أن يكون أقل من 50 حرف")]
        [Display(Name = "تصنيف الأصناف - المستوى 4")]
        public string ItemLevel4 { get; set; } = string.Empty;

        [Required(ErrorMessage = "المستوى 5 مطلوب")]
        [StringLength(50, ErrorMessage = "المستوى 5 يجب أن يكون أقل من 50 حرف")]
        [Display(Name = "تصنيف الأصناف - المستوى 5")]
        public string ItemLevel5 { get; set; } = string.Empty;

        public bool HasLogo { get; set; } = false;
    }
}
