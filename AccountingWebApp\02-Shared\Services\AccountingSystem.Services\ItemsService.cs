using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public class ItemsService : IItemsService
    {
        private readonly AccountingDbContext _context;

        public ItemsService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<List<Item>> GetItemsAsync(string searchTerm = "")
        {
            var query = _context.Items.Where(i => i.Status == 0 || i.Status == null);

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(i => i.ItemDescription.Contains(searchTerm) || i.ItemDescription2.Contains(searchTerm) || i.ItemNo.ToString().Contains(searchTerm));
            }

            return await query.ToListAsync();
        }

        public async Task<Item?> GetItemByIdAsync(long id)
        {
            return await _context.Items.FindAsync(id);
        }

        public async Task<Item> CreateItemAsync(Item item)
        {
            _context.Items.Add(item);
            await _context.SaveChangesAsync();
            return item;
        }

        public async Task<Item> UpdateItemAsync(Item item)
        {
            _context.Entry(item).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return item;
        }

        public async Task DeleteItemAsync(long id)
        {
            var item = await _context.Items.FindAsync(id);
            if (item != null)
            {
                _context.Items.Remove(item);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<string>> GetUofMsAsync()
        {
            // This assumes you have a tblUofM table, but we don't have a model for it.
            // Returning a hardcoded list for now.
            // You should create a UofM model and map it to tblUofM for a proper implementation.
            return await Task.FromResult(new List<string> { "حبة", "كرتون", "درزن" });
        }

        public async Task<List<ItemsCategory>> GetCategoriesAsync()
        {
            return await _context.ItemsCategories.Where(c => c.ParentId == 1).ToListAsync();
        }

        public async Task<List<string>> GetItemTypesAsync()
        {
            return await _context.ItemsCategories.Where(c => c.ParentId == 0).Select(c => c.Name).ToListAsync();
        }

        public async Task<List<decimal>> GetTaxesAsync()
        {
            // This assumes you have a tblTax table.
            // Returning a hardcoded list for now.
            return await Task.FromResult(new List<decimal> { 0, 5, 15 });
        }

        public async Task<List<string>> GetBrandsAsync()
        {
            // This assumes you have a tblbrands table.
            // Returning a hardcoded list for now.
            return await Task.FromResult(new List<string> { "Brand A", "Brand B", "Brand C" });
        }

        public async Task<List<Store>> GetShopsAsync()
        {
            return await _context.Shops.ToListAsync();
        }
    }
} 