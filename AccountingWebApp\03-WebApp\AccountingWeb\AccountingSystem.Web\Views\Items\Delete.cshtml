@model AccountingSystem.Models.Item

@{
    ViewData["Title"] = "حذف الصنف";
}

<h1>@ViewData["Title"]</h1>

<h3>هل أنت متأكد أنك تريد حذف هذا الصنف؟</h3>
<div>
    <h4>بيانات الصنف</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.ItemNo)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.ItemNo)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.ItemDescription)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.ItemDescription)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="SN" />
        <input type="submit" value="حذف" class="btn btn-danger" /> |
        <a asp-action="Index">العودة إلى القائمة</a>
    </form>
</div> 