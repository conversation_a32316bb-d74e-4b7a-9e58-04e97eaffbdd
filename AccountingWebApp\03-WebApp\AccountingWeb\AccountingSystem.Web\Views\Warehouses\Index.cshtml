@model IEnumerable<AccountingSystem.Models.Warehouse>

@{
    ViewData["Title"] = "إدارة المستودعات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إدارة المستودعات</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createWarehouseModal">
                            <i class="fas fa-plus"></i> إضافة مستودع جديد
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <table id="warehousesTable" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>الوصف</th>
                                <th>تاريخ الإنشاء</th>
                                <th>أنشئ بواسطة</th>
                                <th>تاريخ التعديل</th>
                                <th>عدل بواسطة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var warehouse in Model)
                            {
                                <tr>
                                    <td><strong>@warehouse.SN</strong></td>
                                    <td>@warehouse.WarehouseName</td>
                                    <td>@(warehouse.CreatedOn.HasValue ? warehouse.CreatedOn.Value.ToString("yyyy/MM/dd HH:mm") : "-")</td>
                                    <td>@(warehouse.CreatedBy ?? "-")</td>
                                    <td>@(warehouse.ModifiedOn.HasValue ? warehouse.ModifiedOn.Value.ToString("yyyy/MM/dd HH:mm") : "-")</td>
                                    <td>@(warehouse.ModifiedBy ?? "-")</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning" onclick="openEditModal(@warehouse.SN)">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="confirmDelete(@warehouse.SN)">
                                            <i class="fas fa-trash"></i> حذف
                                        </button>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Warehouse Modal -->
<div class="modal fade" id="createWarehouseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form asp-action="CreateWarehouse" method="post">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستودع جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="warehouseName" class="form-label">وصف المستودع *</label>
                        <input type="text" class="form-control" id="warehouseName" name="WarehouseName" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Warehouse Modal -->
<div class="modal fade" id="editWarehouseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form asp-action="EditWarehouse" method="post">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المستودع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="editWarehouseSn" name="SN">
                    <div class="mb-3">
                        <label for="editWarehouseName" class="form-label">وصف المستودع *</label>
                        <input type="text" class="form-control" id="editWarehouseName" name="WarehouseName" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Form -->
<form id="deleteForm" asp-action="DeleteWarehouse" method="post" style="display: none;">
    <input type="hidden" id="deleteWarehouseId" name="id">
</form>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#warehousesTable').DataTable({
                language: { url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json' },
                responsive: true,
                order: [[0, 'asc']],
                columnDefs: [{ orderable: false, targets: [6] }]
            });

            setTimeout(function() { $('.alert').fadeOut('slow'); }, 5000);
        });

        function openEditModal(id) {
            $.get(`/Warehouses/GetWarehouseDetails/${id}`, function (data) {
                $('#editWarehouseSn').val(data.sn);
                $('#editWarehouseName').val(data.warehouseName);
                var editModal = new bootstrap.Modal(document.getElementById('editWarehouseModal'));
                editModal.show();
            });
        }

        function confirmDelete(id) {
            if (confirm('هل أنت متأكد من أنك تريد حذف هذا المستودع؟')) {
                $('#deleteWarehouseId').val(id);
                $('#deleteForm').submit();
            }
        }
    </script>
}
