using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace AccountingSystem.Services
{
    public interface IGlobalConfigurationService
    {
        Task<T> GetConfigValueAsync<T>(string configKey, T defaultValue = default);
        Task SetConfigValueAsync<T>(string configKey, T value);
        Task<Dictionary<string, string>> GetAllConfigAsync();
        Task<bool> RefreshConfigCacheAsync();
    }

    public class GlobalConfigurationService : IGlobalConfigurationService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<GlobalConfigurationService> _logger;
        private static readonly ConcurrentDictionary<string, object> _configCache = new();
        private static DateTime _lastCacheRefresh = DateTime.MinValue;
        private static readonly TimeSpan CacheExpiry = TimeSpan.FromMinutes(30);

        public GlobalConfigurationService(AccountingDbContext context, ILogger<GlobalConfigurationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<T> GetConfigValueAsync<T>(string configKey, T defaultValue = default)
        {
            try
            {
                // Check if cache needs refresh
                if (DateTime.Now - _lastCacheRefresh > CacheExpiry)
                {
                    await RefreshConfigCacheAsync();
                }

                // Try to get from cache first
                if (_configCache.TryGetValue(configKey, out var cachedValue))
                {
                    return ConvertValue<T>(cachedValue, defaultValue);
                }

                // If not in cache, get from database
                var config = await _context.SystemConfigs
                    .FirstOrDefaultAsync(c => c.ConfigKey == configKey);

                if (config != null)
                {
                    var value = ConvertValue<T>(config.ConfigValue, defaultValue);
                    _configCache.TryAdd(configKey, config.ConfigValue);
                    return value;
                }

                return defaultValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting config value for key: {ConfigKey}", configKey);
                return defaultValue;
            }
        }

        public async Task SetConfigValueAsync<T>(string configKey, T value)
        {
            try
            {
                var stringValue = value?.ToString() ?? "";
                
                var config = await _context.SystemConfigs
                    .FirstOrDefaultAsync(c => c.ConfigKey == configKey);

                if (config != null)
                {
                    config.ConfigValue = stringValue;
                    config.ModifiedDate = DateTime.Now;
                }
                else
                {
                    config = new SystemConfig
                    {
                        ConfigKey = configKey,
                        ConfigValue = stringValue,
                        CreatedDate = DateTime.Now
                    };
                    _context.SystemConfigs.Add(config);
                }

                await _context.SaveChangesAsync();

                // Update cache
                _configCache.AddOrUpdate(configKey, stringValue, (key, oldValue) => stringValue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting config value for key: {ConfigKey}", configKey);
                throw;
            }
        }

        public async Task<Dictionary<string, string>> GetAllConfigAsync()
        {
            try
            {
                var configs = await _context.SystemConfigs
                    .ToDictionaryAsync(c => c.ConfigKey, c => c.ConfigValue);

                return configs;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all config values");
                return new Dictionary<string, string>();
            }
        }

        public async Task<bool> RefreshConfigCacheAsync()
        {
            try
            {
                var configs = await GetAllConfigAsync();
                
                _configCache.Clear();
                foreach (var config in configs)
                {
                    _configCache.TryAdd(config.Key, config.Value);
                }

                _lastCacheRefresh = DateTime.Now;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing config cache");
                return false;
            }
        }

        private static T ConvertValue<T>(object value, T defaultValue)
        {
            try
            {
                if (value == null) return defaultValue;

                var stringValue = value.ToString();
                if (string.IsNullOrEmpty(stringValue)) return defaultValue;

                var targetType = typeof(T);
                
                // Handle nullable types
                if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
                {
                    targetType = Nullable.GetUnderlyingType(targetType);
                }

                // Convert based on type
                if (targetType == typeof(string))
                    return (T)(object)stringValue;
                else if (targetType == typeof(int))
                    return (T)(object)int.Parse(stringValue);
                else if (targetType == typeof(long))
                    return (T)(object)long.Parse(stringValue);
                else if (targetType == typeof(decimal))
                    return (T)(object)decimal.Parse(stringValue);
                else if (targetType == typeof(bool))
                    return (T)(object)(stringValue == "1" || stringValue.ToLower() == "true");
                else if (targetType == typeof(DateTime))
                    return (T)(object)DateTime.Parse(stringValue);
                else
                    return (T)Convert.ChangeType(stringValue, targetType);
            }
            catch
            {
                return defaultValue;
            }
        }
    }



    // Global Application State Service
    public interface IApplicationStateService
    {
        string CurrentUsername { get; set; }
        string CurrentUserGroup { get; set; }
        string StoreName { get; set; }
        string VATRegistration { get; set; }
        int FormHeight { get; set; }
        int FormWidth { get; set; }
        int ReportHeight { get; set; }
        int ReportWidth { get; set; }
        string DatabaseBackupLocation { get; set; }
        bool LinkStoreCash { get; set; }
        string ItemClassL1 { get; set; }
        string ItemClassL2 { get; set; }
        string ItemClassL3 { get; set; }
        string ItemClassL4 { get; set; }
        string ItemClassL5 { get; set; }
        int ItemClassLevels { get; set; }
        string CashPayPrint { get; set; }
        string CashRecPrint { get; set; }
        bool IsTestDeploy { get; set; }
        
        Task LoadConfigurationAsync();
        Task<Dictionary<string, string>> GetGLAccountMappingAsync();
    }

    public class ApplicationStateService : IApplicationStateService
    {
        private readonly IGlobalConfigurationService _configService;
        private readonly AccountingDbContext _context;

        public ApplicationStateService(IGlobalConfigurationService configService, AccountingDbContext context)
        {
            _configService = configService;
            _context = context;
        }

        // Properties matching VB.NET global variables
        public string CurrentUsername { get; set; } = "";
        public string CurrentUserGroup { get; set; } = "";
        public string StoreName { get; set; } = "";
        public string VATRegistration { get; set; } = "";
        public int FormHeight { get; set; } = 600;
        public int FormWidth { get; set; } = 800;
        public int ReportHeight { get; set; } = 600;
        public int ReportWidth { get; set; } = 800;
        public string DatabaseBackupLocation { get; set; } = "";
        public bool LinkStoreCash { get; set; } = false;
        public string ItemClassL1 { get; set; } = "";
        public string ItemClassL2 { get; set; } = "";
        public string ItemClassL3 { get; set; } = "";
        public string ItemClassL4 { get; set; } = "";
        public string ItemClassL5 { get; set; } = "";
        public int ItemClassLevels { get; set; } = 0;
        public string CashPayPrint { get; set; } = "";
        public string CashRecPrint { get; set; } = "";
        public bool IsTestDeploy { get; set; } = false;

        public async Task LoadConfigurationAsync()
        {
            try
            {
                // Load configuration from tblConfig table (matching VB.NET logic)
                var config = await _context.SystemConfigs.FirstOrDefaultAsync();
                
                if (config != null)
                {
                    ReportHeight = await _configService.GetConfigValueAsync("RPTHeight", 600);
                    ReportWidth = await _configService.GetConfigValueAsync("RPTWidth", 800);
                    StoreName = await _configService.GetConfigValueAsync("StoreName", "");
                    FormHeight = await _configService.GetConfigValueAsync("frmHeight", 600);
                    FormWidth = await _configService.GetConfigValueAsync("frmWidth", 800);
                    DatabaseBackupLocation = await _configService.GetConfigValueAsync("BackupLocation", "");
                    LinkStoreCash = await _configService.GetConfigValueAsync("CashWithStore", false);
                    ItemClassL1 = await _configService.GetConfigValueAsync("ItemLevel1", "");
                    ItemClassL2 = await _configService.GetConfigValueAsync("ItemLevel2", "");
                    ItemClassL3 = await _configService.GetConfigValueAsync("ItemLevel3", "");
                    ItemClassL4 = await _configService.GetConfigValueAsync("ItemLevel4", "");
                    ItemClassL5 = await _configService.GetConfigValueAsync("ItemLevel5", "");
                    CashPayPrint = await _configService.GetConfigValueAsync("CashPayPrint", "");
                    CashRecPrint = await _configService.GetConfigValueAsync("CashRecPrint", "");
                    VATRegistration = await _configService.GetConfigValueAsync("VATRegNo", "");
                    IsTestDeploy = await _configService.GetConfigValueAsync("isTestDeploy", false);

                    // Calculate item class levels
                    ItemClassLevels = 0;
                    if (!string.IsNullOrEmpty(ItemClassL1)) ItemClassLevels++;
                    if (!string.IsNullOrEmpty(ItemClassL2)) ItemClassLevels++;
                    if (!string.IsNullOrEmpty(ItemClassL3)) ItemClassLevels++;
                    if (!string.IsNullOrEmpty(ItemClassL4)) ItemClassLevels++;
                    if (!string.IsNullOrEmpty(ItemClassL5)) ItemClassLevels++;
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw - use defaults
                Console.WriteLine($"Error loading configuration: {ex.Message}");
            }
        }

        public async Task<Dictionary<string, string>> GetGLAccountMappingAsync()
        {
            try
            {
                return await _context.GLConfigs
                    .ToDictionaryAsync(g => g.EntryReferenceModule, g => g.AccountNo.ToString());
            }
            catch
            {
                return new Dictionary<string, string>();
            }
        }
    }

}
