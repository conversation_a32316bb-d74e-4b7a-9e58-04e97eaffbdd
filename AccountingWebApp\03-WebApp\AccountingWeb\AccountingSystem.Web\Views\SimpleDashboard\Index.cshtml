@model DashboardViewModel
@{
    ViewData["Title"] = "الصفحة الرئيسية";
}

<!-- Dashboard Header -->
<div class="dashboard-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="dashboard-title">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </h1>
                <p class="dashboard-subtitle">مرحباً @Model.Username - @Model.StoreName</p>
            </div>
            <div class="col-md-6 text-end">
                <div class="user-info">
                    <span class="user-group badge bg-primary">@Model.UserGroup</span>
                    <span class="login-time">
                        <i class="fas fa-clock"></i>
                        @Model.LoginTime.ToString("yyyy-MM-dd HH:mm")
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

@if (User.IsInRole("admin"))
{
    <div class="container-fluid mt-3 mb-2 text-end">
        <a href="/Admin/QuickActions" class="btn btn-warning">
            <i class="fas fa-bolt"></i> إدارة الإجراءات السريعة
        </a>
    </div>
}

<!-- Statistics Cards -->
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <a href="/Sales/Search?date=@DateTime.Today.ToString("yyyy-MM-dd")" class="stat-card-link" aria-label="انتقل إلى صفحة بحث المبيعات مع فلتر اليوم">
                <div class="card stat-card stat-card-primary">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="card-title">مبيعات اليوم</h5>
                                <h2 class="stat-number">@Model.DashboardStats.TodaySales.ToString("N2")</h2>
                                <p class="stat-label">ريال سعودي</p>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-chart-line stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card stat-card-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title">مشتريات اليوم</h5>
                            <h2 class="stat-number">@Model.DashboardStats.TodayPurchases.ToString("N2")</h2>
                            <p class="stat-label">ريال سعودي</p>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card stat-card-info">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title">إجمالي العملاء</h5>
                            <h2 class="stat-number">@Model.DashboardStats.TotalCustomers</h2>
                            <p class="stat-label">عميل نشط</p>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card stat-card-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title">رصيد الصندوق</h5>
                            <h2 class="stat-number">@Model.DashboardStats.CashBalance.ToString("N2")</h2>
                            <p class="stat-label">ريال سعودي</p>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cash-register stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt"></i>
                        الإجراءات السريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach (var action in Model.QuickActions)
                        {
                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="@action.Action" class="quick-action-card">
                                    <div class="card h-100 quick-action">
                                        <div class="card-body text-center">
                                            <div class="quick-action-icon <EMAIL>">
                                                <i class="@action.Icon"></i>
                                            </div>
                                            <h6 class="quick-action-title">@action.Title</h6>
                                            <p class="quick-action-desc">@action.Description</p>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="row mt-4">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        تنبيهات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert-item">
                        <div class="alert-icon bg-warning">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="alert-content">
                            <h6>أصناف منخفضة المخزون</h6>
                            <p>@Model.DashboardStats.LowStockItems صنف يحتاج إعادة تموين</p>
                        </div>
                    </div>
                    <div class="alert-item">
                        <div class="alert-icon bg-danger">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="alert-content">
                            <h6>فواتير معلقة</h6>
                            <p>@Model.DashboardStats.PendingInvoices فاتورة تحتاج متابعة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i>
                        ملخص المخزون
                    </h5>
                </div>
                <div class="card-body">
                    <div class="inventory-summary">
                        <div class="summary-item">
                            <span class="summary-label">إجمالي قيمة المخزون</span>
                            <span class="summary-value">@Model.DashboardStats.TotalInventoryValue.ToString("N2") ر.س</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">إجمالي الموردين</span>
                            <span class="summary-value">@Model.DashboardStats.TotalVendors مورد</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin: -1.5rem -1.5rem 0 -1.5rem;
    }

    .dashboard-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .dashboard-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-direction: column;
    }

    .user-group {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .login-time {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .stat-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .stat-card-primary { border-left: 4px solid #007bff; }
    .stat-card-success { border-left: 4px solid #28a745; }
    .stat-card-info { border-left: 4px solid #17a2b8; }
    .stat-card-warning { border-left: 4px solid #ffc107; }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .stat-icon {
        font-size: 2.5rem;
        opacity: 0.3;
    }

    .quick-action-card {
        text-decoration: none;
        color: inherit;
    }

    .quick-action {
        border: none;
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .quick-action:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .quick-action-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 1.5rem;
    }

    .quick-action-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }

    .quick-action-desc {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 0;
    }

    .alert-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #eee;
    }

    .alert-item:last-child {
        border-bottom: none;
    }

    .alert-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        color: white;
    }

    .alert-content h6 {
        margin-bottom: 0.25rem;
        font-weight: 600;
    }

    .alert-content p {
        margin-bottom: 0;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .inventory-summary {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #eee;
    }

    .summary-item:last-child {
        border-bottom: none;
    }

    .summary-label {
        font-weight: 500;
        color: #495057;
    }

    .summary-value {
        font-weight: 600;
        color: #2c3e50;
    }

    .stat-card-link {
        text-decoration: none;
        color: inherit;
        display: block;
    }
    .stat-card-link:focus .stat-card,
    .stat-card-link:hover .stat-card {
        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.2);
        outline: 2px solid #007bff;
        outline-offset: 2px;
    }

    @@media (max-width: 768px) {
        .user-info {
            flex-direction: column;
            text-align: center;
        }
        
        .dashboard-title {
            font-size: 1.5rem;
        }
        
        .stat-number {
            font-size: 1.5rem;
        }
    }
</style>
