using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using AccountingSystem.Data;
using AccountingSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public class StoreServiceV2 : IStoreServiceV2
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<StoreServiceV2> _logger;

        public StoreServiceV2(AccountingDbContext context, ILogger<StoreServiceV2> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<Store>> GetStoresAsync()
        {
            return await _context.Shops
                .OrderBy(s => s.SN)
                .ToListAsync();
        }

        public async Task<Store?> GetStoreByIdAsync(int id)
        {
            return await _context.Shops.FindAsync(id);
        }

        public async Task<(bool success, Store? createdStore)> CreateStoreAsync(Store store, string currentUser)
        {
            if (string.IsNullOrWhiteSpace(store.StoreName))
            {
                return (false, null);
            }

            try
            {
                // Manually calculate the next SN
                var nextSn = 1;
                if (await _context.Shops.AnyAsync())
                {
                    nextSn = await _context.Shops.MaxAsync(s => s.SN) + 1;
                }

                var newStore = new Store
                {
                    SN = nextSn,
                    StoreName = store.StoreName,
                    CreatedBy = currentUser,
                    CreatedOn = DateTime.Now
                };

                _context.Shops.Add(newStore);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation($"Store created successfully with SN: {newStore.SN}");
                return (true, newStore);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating store in StoreServiceV2");
                return (false, null);
            }
        }

        public async Task<bool> UpdateStoreAsync(Store store, string currentUser)
        {
            var existingStore = await _context.Shops.FindAsync(store.SN);
            if (existingStore == null)
            {
                return false;
            }

            if (existingStore.StoreName != store.StoreName && !string.IsNullOrWhiteSpace(store.StoreName))
            {
                existingStore.StoreName = store.StoreName;
                existingStore.ModifiedBy = currentUser;
                existingStore.ModifiedOn = DateTime.Now;

                try
                {
                    await _context.SaveChangesAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error updating store in StoreServiceV2 for SN: {store.SN}");
                    return false;
                }
            }

            return false; // No changes were made
        }

        public async Task<(bool success, string errorMessage)> DeleteStoreAsync(int id)
        {
            var store = await _context.Shops.FindAsync(id);
            if (store == null)
            {
                return (false, "المتجر غير موجود.");
            }

            if (await IsStoreUsedInTransactionsAsync(id))
            {
                return (false, "لا يمكن حذف المتجر لأنه مستخدم في عمليات سابقة.");
            }

            try
            {
                _context.Shops.Remove(store);
                await _context.SaveChangesAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting store in StoreServiceV2 for SN: {id}");
                return (false, "حدث خطأ أثناء حذف المتجر.");
            }
        }

        public async Task<bool> IsStoreUsedInTransactionsAsync(int storeId)
        {
            // Get the store name using the ID, as other tables use the name.
            var storeName = await _context.Shops
                .Where(s => s.SN == storeId)
                .Select(s => s.StoreName)
                .FirstOrDefaultAsync();

            if (string.IsNullOrEmpty(storeName))
            {
                // If the store doesn't exist, it's not used.
                return false;
            }

            // Check if the store name is used in POS invoices or items.
            var isUsedInInvoices = await _context.POSInvoices.AnyAsync(i => i.Store == storeName);
            var isUsedInItems = await _context.POSInvoiceItems.AnyAsync(i => i.Store == storeName);

            return isUsedInInvoices || isUsedInItems;
        }
    }
} 