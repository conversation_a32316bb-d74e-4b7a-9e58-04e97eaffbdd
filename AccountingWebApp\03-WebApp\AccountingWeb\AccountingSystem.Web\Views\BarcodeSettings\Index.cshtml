@model IEnumerable<AccountingSystem.Models.BarcodeSettings>
@{
    ViewData["Title"] = "Barcode Settings";
}

<h1>@ViewData["Title"]</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Shop)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.BarcodeType)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.EnableEmbeddedWeight)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Shop)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.BarcodeType)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EnableEmbeddedWeight)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.ID">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.ID">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.ID">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table> 