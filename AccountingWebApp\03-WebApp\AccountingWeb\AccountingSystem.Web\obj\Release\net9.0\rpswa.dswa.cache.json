{"GlobalPropertiesHash": "QjKeZ+xDyimWk6u++TjVVfgrl+4XluY0jyH/00XTiRc=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["mgnG+30oAP/bDxFn5troUVHnfMO/63OrhvT/hpUbOjk=", "wAIVfgpe8J84DJfIfjE0wjjyyHbMndDdBW0r7KCRifI=", "3gUo/IeVVEylLgQTE0PnjuR4bjo3liXDQ0nUSCXVV0M=", "W5br5z1fTFtNVBxRLpgrwnhsnD4dmszVQBWTRr/KZxU=", "e4gIu1mtxtue6tWJcVZlAkSIWS2jW7MMTIGmHF+3rZk=", "IOFuUY+p89mVNZTuMmFCt+W+MlfdYGXeQB1MI6Edd30=", "rPtuy9BfvXE6prpNcNZCsLUwUhz/JFo+cy8wrqU7LyE=", "aOOGd2lqyYeoK8qucVY4wnOsN0kZOICdVXNMdYCll6s=", "273ccvcrlte3Grogifpnkv7VHrJjgG09s1lUQOVOTn4=", "XqWcqAckPrxrHGBpFkhaSR3b1F57FkNd3o5LdEVMXBA=", "PTlW6KYNWiZEHrByUGNFS6RAUXCWN4rt7OGs1vkdtYc=", "fZ0GYfrjms58twPYmHmC2SokdHayRGfQR6Jw//0Gxpk=", "ZrmxHgkG59mpHKJCJIQKb2MlvXKoyZIHKVbKnhOrlyc=", "FnCXeRZOsSXIp/H4zCzmjlBgCmK7Ik7dK+5Zs0lq99A=", "YAJQRXloa2fEh6nGpofs9UuA0vMfhHGyMRdkp/X/dYg=", "PhKKUdAk8Pafa+Nc9IQ+tjL8Pv4Qy7kwISVFWTiDSMM=", "YzGTeSf39VDbbC6y+T/X0vywvb4r2jiC8jxgppklefc=", "fwdroS7+sRsZPQhmVA+Yxswpwh/tKtpj2dpeLW0UaeM=", "fz9z5qa9kv+f0ErI/Skh/lgndE+lsARkEgfEic+wS+I=", "HrSsiDr8lPKm31+0gl+GllAm/0kk4FkiyPfV/3fVIA0=", "91hB/7iJvKIlD0IAQrb0qpYGgDyMcDxnT2QHcfisHQo=", "ZDRxMih58O7IjpR3ctMlII1AToM6dzHKsuFPngExv0g=", "gpsoUJ9S6yc/xU7d8w9tKwCFlwTszgUY2pC0XtHIXJ8=", "pnM4ZemkCcm+Ywrue4OdrlQtBNK0cUM9WsQ+gEX1nyg=", "fY3Z4ckyAoS8U2aCnDILSHsBYRaT/QwUNP5LmEmVn64=", "bFyVpZAuxc5BqTehANBTDOSJSAaHGXwmiaYPIt2ga64=", "uzq3i8z0UlZG29R1FnaeNoA5I1YgcB0zacwPbMvgdLM=", "dUxiZQpUtqTFyq5S7d5w0J0DXrDPoVZ7a4HPAXYzlqc=", "R6c4WUIG1WMC8k0nIL1abeoXlRFZev2FdIOOBX5XwwM=", "iJSYJpkmSlKR/hQJXd3ZygLduxFejFEJZzOx0fqSMfk=", "jHwFiOG7G4c8OCXT0WtCZHkPfAytXRu5m4WoZH+4IV0=", "Dy+it7321xFP4PexoMfUhJUsGXcfz3py6Y1lBdpqDqg=", "Lzz3YBPq3ogZ32t3LPiY/WrP6lVGL7Mril0XrFMvwds=", "7sBUBPQ3Mkvk5j1tEcV60FbD25s78v+HFwzkLtVhb6Q=", "t0Xth9PDfdzXCSrX2eofNNwz12E4KtDZZtwHe29l1nM=", "QEvGtPqPDzugJgeL2vQnW/5+x3OEEUKz9UJUgza8zIA=", "SCjt2qJ9erCsDDz0zXsebqeQpGyROg0bUxDVW1nGzlM=", "2Tol7InSGMU5SeNAuuKY/5z1SSY0n8/wzfciwJXcf0g=", "b104Otw11zXpgk8aAVvG2PSdBYxe3xXAryh4Myto1h0=", "5uuRQ8DkzoO0fnCYD7sLa7QZbb5As/SzvtdGKuuvOcA=", "z1zxzwxtEaJVPSJ/54Af+qfMEoLb6lzaDDfwHXr/ed4=", "3Wk4DkTdwpLg+y2O6RLE3Sn6cEJT/iiVl3klh4vn5ak=", "qcLki9uIMVriwFG8yd8GtDbjXad64TRLskuOfTFvlGY=", "2QyYRgrY8/RzMhA3dygjSmuPoqCkRCGyRNdMuzXvtg8=", "/6czZhNm//UNM13lrrWe6nUYECc9OAD3pW/xng/s6pM=", "1P61yc/h2lL40TqnpjGdqh+o1gCDON7RxHBozSUNgNY=", "rYa/gD2+rmQBu5Evguz6XfOhlLVWeH4uGeMr5yfFev8=", "hL0r/uestWAQTdil0iWfIa343Lj6mLw9B+ttHbvDZ5U=", "UZ8VsjTBVAJY7saNZmGnZxNBEtoD4swwh2e01CTxR5I=", "ZlmhJnS5SP8qX4gigT8Ticm/FupIufRSUPCG5W/GEd0=", "EsOI8aO+rKcCssVl471bbk94MakRGoKnAPGelwQ5R38=", "DHJlGiI+CEF9+YgLZSwoWr2jocXRK3GzYk08UvrRc0k=", "UnA63tff5N200efcBugz0AtqjBvhI8vEQCQpXC14Tc8=", "EtMMx+RjHxZC4+MSRG5IbZhDEtO1CGO+wgA3A+GBrCU=", "LF19V4HGjn2ttNntxPWEDayCzA7kMK0QkW3I1vuHE4A=", "hZGprgIfpDDtuFe5m9rRCUrM3aAXYrt2tChULQW3uv4=", "IGRT42/TdP2E3uj2g7kiPu/YXLGHSTbbCCOtmN9R+Tw=", "U78tBJq2e3f9Fmd6nfTGFWN1t74AV1MToSXi6l2if1E=", "9Zq0hmmoVcHpd6m42k06hk6ilip908Wu4o98N2sFbOs=", "DrmHt5q0BDdgaHCz/QUTy8Pz371lgjmV2x2b+SZZXiU=", "6sIMdUfMeeAOm0ygzckk0GrEH8iSZ77Fx2wWYc4/VcM=", "4KVed3u0/UBSXOcQcDh60OwYI319xOD7ivRIbbp+NMU=", "t2MmtLd+06ybaIoQS8yityquooyBvFHDTzzZEaccGnU=", "XKIMSRyFBRZPlh0Pjb4XnnmUcRrVMt0u9mU87EkZXVY=", "v8liwon63FnJoWWhWC1UmXTrjs9TJ9TYOJL5xu/hPRs=", "YuHsw9f3sJwXk4oMwXkBDFsZuXqx7s++SMysYRJYxZg=", "I8ARog2tPwOTznAyjOBVZ1Wq8mir591ZqaHcXOOyvhs=", "Y07MuYTQGOQ+pkVL+kYKBsUBhJoc5ZsP/27zYPaoUPI=", "AZ6ZLlmcmqOcUcGKfmiPGWUFauM7ihOGPhwjopPSi7c=", "Ag9yX5r/eYvgdLrs7USH2P3HkABJ7mCmFN/mo2djulw=", "Z/sS6hsGYRbhEzQSuEhvDFI7IW7EQC4UjE2gcRJpZT4=", "wOJwkJhrBZYgL0NEJqUsGU1Fp55Oav14Y4UuTc1NTGo=", "mJttapD+Hh+qcilRgIJG2KYg3N9zF/IgK1+/oo27GD4=", "4hMsnzs/fYRrSOCIL9KyknwQJOEdHQw/Up8deyNANmk=", "7i0bU1hKLrZwYhbJ8NvP5WJ1VoxRyqX4p+A/n7dedA0=", "rf7GVq8soggQIaDUEnyjW3k/854m+WFxZBZn9ai6DAM=", "WsAXDK8yfnjgHIzlWFAunK7wKPGOrAqPGjULYVzGngY=", "hAmyB2rahcm12O37JWGqpavXczLO1kXIy7WWBA8Sx/w=", "+zJ7RocI9Nge2FUvY9EpbngsA5R8/XetplIgXj7pveY=", "gDJLAZU4g9VOYpmSmXbzCnl4I2dAhln2DH1oQNJEy1s=", "e/fON2GNFYHbgPcvNBQ9eohAI9Y9v7hTVskW/xYhIgs=", "iyy0Z7RLFJXeUVAxpaYBR8AtdQ2noCPH0CvsyTQVUeM=", "o2etuNPSfWn/99BCEkJfmNGP9q6518ZoBO7pOioX8ck=", "tR3sYIECHkJfkG2QUGOiSblBzCzZyc+TrI24L59xm5E=", "cxHHRR1eb5rZqZWhJRtaIbo65tshzdcdpWLWg/P4Gxs=", "Chj1in9H8NMBLM9p9qH53SofrZCo9RoXcLn/XIQX7WA=", "plEjUrwNnVNrzEQ/SWWwxQ2iFanD+H7gv7KPyE5+pNM=", "9eSv792E58Hjq77vQw1AaAh9DABPbuINS+Gxk17mUfY=", "saxib3G0/VY1hFkyxOaCEwGG+1ewwh9F0uPzNNZU/x8=", "x0ADk4yd1ColBZBnkEiC9ImrWxX1deiRml8x5svuxZ0=", "Oj51BB0PDDNmLh3+JEVQFAWErNZS2pbMRkGg9nnG+60=", "Ni3KJplh5Qntbn1KsMLlh/yNWz7WLcrQJM+xiivEaqM=", "7Mk1DxOnaGjq9UP4CrISBSgA7hwqtgb788sIyDr15s4=", "I6a0cL4GYPBPJ6/jYLRZIm9QKAJZusaZo4WDZuNsxkA=", "jwMuouyokjeLBtaTY8quNtO9MI7voJahwlsY9bD0Vb0=", "tR5WvpU61S5rGjhZ/X7kwQVCqaHAJZokw+jhEPAxJwA=", "JQhyA3QxkxQh3vsThsoNZ8NbpPKUHX8XRQpP4hKj9W0=", "VqP1Fxf3nvJ3BhKIp1ZNx147PK+HrX6gVd50eJ7fA8g=", "IzJ4BNU/k1BOIdIZnmHAN/dguoTu0uRr3I2nYSBgc5E=", "niogckJ9Fj6ffuE6S83Y6Sf5Ew/LQQnli7KQZrHT1I8=", "u563hQaQ2oJiMzbud/AdJhEFJlKmFzEqc4tExJi7YiI=", "No5W93SijkfBgur5mZkOShn6ps5DC46Fnx1MMpPDjXA=", "RHIeHhWPInxV/tzaQVYrZyNJ0NFOgcNpHPRb0fGPBY4=", "caAuAKZqVLCFAM0u9NBZ58HnLcrtaE00rGLRMc2Okpo=", "fde/75lCxjRuJWS5TGY0iotApADvxuwjp51ti67F5+I=", "BI62f06GgquZV0jUhEaOLXlyzzcnigXspnUi/iCA4S0=", "JG2oE9bl4fxw6cxHgepxOReGycnxuSocoYvu+T39HwI=", "dKOlvgcsxZfCwnAvDRhF6zDDW5Mtz/djxHTVpjENLRU=", "14z/gICrzMy/8DLntYLPwLTg3gKVIxFAuq1gEm9karI=", "r8Gnrnw7f1PB31neo5wk67fL2JCrhAn3DgET6uT9eCM=", "woy7qkdtrlKRzgdfFbzzDFDzoy6JEnPdP29Lb/dCReM=", "L7zdNNIMdLYZgPsNKoW+qqQ64C62EbIMb2UcPtWTCsA=", "d5JDrWz5qJJAHsSbPbz96G0lZ+pvA7SmXYwU7t3gyHg=", "+J/ctxbKFzabHH0DbTItXGINLvjHAayHThf/82d2YLg=", "Jcigy99krLIEBUwS1ZnfQGKm4ISZTUTMSd7vYiqJDik=", "CDm73AW35RC+NOzZ+IHE8JBmGTDGlwDTwp5iqicgu+0=", "K2CLsgsCMyK7n9Gx6x86QeP2f/MyJra7FqbIYpo0RNA=", "68T3bhAAdikwYyjoLZo1XstRXT3PmXyVH6p00AjNAFk=", "nEHYoTYWsNiA7z1kzW2lZKf0hJS+X6jwToi70qJD2YY=", "Ypl5NkYE3b5O7/grIft1vEt1NR8Y79k9SVKyz2zri6c=", "hRyEA5C6I2o6nWN2+HiI8bTCHwh0KryH5GgdfXw+FTQ=", "6qKpxRebld6cHpVP8L0W3vFSsY6gHniNJzzCWqHoX20=", "9nLiEJs/Kh7+FFDBC6/actk2pRNAGaHj7EacSv7k6EM=", "o+z7MQ2kYIxVNOQ97f4Hs1nAlbGB+/Ji/dAojY7lptE=", "mtUYf5MMCl7hJbkzThvoOSQUGimplzzuRpvFYJaTTew=", "aSmYNdUVmWMiz/d331QeKiGF6UJI1rUnO2TUhnHGxQA=", "g7wY5jsWBRtBb+hCN5/3UP88kW0n2x/Y0zOzV4JPEuc=", "LgZ1lsPMYLYtq02LMEfll0Y9yRQJFugCAtSUL4A7Pgg=", "bKvkBAygrD8L4hTBo/6ezucsQhqh0ypmmkNn4REpxtA=", "vchTTCeJccRl+Ak4vZEFngfZKzIRqS03gi4SnOFndKs=", "/t0zIe/GKR8HMnZqXEjNYu/i0TiVeFrFLVOHn3TE+5A=", "WFLdQlvJIWXaomTGBsDMIufUYxv3csOvTSbdaxl3iW4=", "UuWTxOEHVQCDFdR/fR309AEMjLlaBs6Cwqw0D7l7gG4=", "9LolElyqDM/eKdsoVevEU/DWIK8WaKy+udj5ALF+dag=", "XYLfH+y7xIOcvEs8pTnn57QyqRRKa+F1meBlNovMRAs=", "Ap6kGrhUJFkNWTF1pSvFfedXkt/Bq+sZc8gUL+5iq14=", "jQOtTfKBpMGU/QS8BH7WA2ny0EN4UJRDLMz0ezw2na0=", "ZC8Ej8PvxsIxsYRazDAB3hLfTmDoBPY8BX3H7GRpNFE=", "HyccwiRY9JQL1bYg9OA/5N+aOKNxyeUiEzXGorkYFPI=", "X/Ml1ruj9OZ94BuUOBbzYfozpqj9ZeB4lanB5BmGxzg="], "CachedAssets": {"mgnG+30oAP/bDxFn5troUVHnfMO/63OrhvT/hpUbOjk=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\css\\site.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hsrfjvnq1z", "Integrity": "cfn14Tmg4sAy4irvFiAShMG69r+VWLJS//ZCf/uvNeg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 865, "LastWriteTime": "2025-07-14T22:07:23.6589377+00:00"}, "wAIVfgpe8J84DJfIfjE0wjjyyHbMndDdBW0r7KCRifI=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\favicon.ico", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "3gUo/IeVVEylLgQTE0PnjuR4bjo3liXDQ0nUSCXVV0M=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\images\\Alsultan-logo.jpg", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "images/Alsultan-logo#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ahdim8u5fj", "Integrity": "QtUzywDQE62QFxITh5qwpE1/gkdAHcIjo5vr90Jyga4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\Alsultan-logo.jpg", "FileLength": 96296, "LastWriteTime": "2019-05-11T15:56:20+00:00"}, "W5br5z1fTFtNVBxRLpgrwnhsnD4dmszVQBWTRr/KZxU=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\js\\qz-tray.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "js/qz-tray#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3sv8558lwh", "Integrity": "HZTMlTnPoQPhfb70q4e4iM8GU7NRrRtrjD4+r4Pbboc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\qz-tray.js", "FileLength": 140, "LastWriteTime": "2025-07-23T13:19:53.2467028+00:00"}, "e4gIu1mtxtue6tWJcVZlAkSIWS2jW7MMTIGmHF+3rZk=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\js\\site.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-07T18:22:48.070244+00:00"}, "IOFuUY+p89mVNZTuMmFCt+W+MlfdYGXeQB1MI6Edd30=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "rPtuy9BfvXE6prpNcNZCsLUwUhz/JFo+cy8wrqU7LyE=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "aOOGd2lqyYeoK8qucVY4wnOsN0kZOICdVXNMdYCll6s=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "273ccvcrlte3Grogifpnkv7VHrJjgG09s1lUQOVOTn4=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "XqWcqAckPrxrHGBpFkhaSR3b1F57FkNd3o5LdEVMXBA=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "PTlW6KYNWiZEHrByUGNFS6RAUXCWN4rt7OGs1vkdtYc=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "fZ0GYfrjms58twPYmHmC2SokdHayRGfQR6Jw//0Gxpk=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "ZrmxHgkG59mpHKJCJIQKb2MlvXKoyZIHKVbKnhOrlyc=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "FnCXeRZOsSXIp/H4zCzmjlBgCmK7Ik7dK+5Zs0lq99A=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "YAJQRXloa2fEh6nGpofs9UuA0vMfhHGyMRdkp/X/dYg=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "PhKKUdAk8Pafa+Nc9IQ+tjL8Pv4Qy7kwISVFWTiDSMM=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "YzGTeSf39VDbbC6y+T/X0vywvb4r2jiC8jxgppklefc=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "fwdroS7+sRsZPQhmVA+Yxswpwh/tKtpj2dpeLW0UaeM=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "fz9z5qa9kv+f0ErI/Skh/lgndE+lsARkEgfEic+wS+I=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "HrSsiDr8lPKm31+0gl+GllAm/0kk4FkiyPfV/3fVIA0=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "91hB/7iJvKIlD0IAQrb0qpYGgDyMcDxnT2QHcfisHQo=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "ZDRxMih58O7IjpR3ctMlII1AToM6dzHKsuFPngExv0g=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "gpsoUJ9S6yc/xU7d8w9tKwCFlwTszgUY2pC0XtHIXJ8=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-07T18:22:48.0020805+00:00"}, "pnM4ZemkCcm+Ywrue4OdrlQtBNK0cUM9WsQ+gEX1nyg=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-07T18:22:48.0020805+00:00"}, "fY3Z4ckyAoS8U2aCnDILSHsBYRaT/QwUNP5LmEmVn64=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-07T18:22:48.0030964+00:00"}, "bFyVpZAuxc5BqTehANBTDOSJSAaHGXwmiaYPIt2ga64=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-07T18:22:48.0030964+00:00"}, "uzq3i8z0UlZG29R1FnaeNoA5I1YgcB0zacwPbMvgdLM=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-07T18:22:48.0040962+00:00"}, "dUxiZQpUtqTFyq5S7d5w0J0DXrDPoVZ7a4HPAXYzlqc=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-07T18:22:48.0040962+00:00"}, "R6c4WUIG1WMC8k0nIL1abeoXlRFZev2FdIOOBX5XwwM=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-07T18:22:48.0050935+00:00"}, "iJSYJpkmSlKR/hQJXd3ZygLduxFejFEJZzOx0fqSMfk=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "jHwFiOG7G4c8OCXT0WtCZHkPfAytXRu5m4WoZH+4IV0=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "Dy+it7321xFP4PexoMfUhJUsGXcfz3py6Y1lBdpqDqg=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "Lzz3YBPq3ogZ32t3LPiY/WrP6lVGL7Mril0XrFMvwds=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "7sBUBPQ3Mkvk5j1tEcV60FbD25s78v+HFwzkLtVhb6Q=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "t0Xth9PDfdzXCSrX2eofNNwz12E4KtDZZtwHe29l1nM=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "QEvGtPqPDzugJgeL2vQnW/5+x3OEEUKz9UJUgza8zIA=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "SCjt2qJ9erCsDDz0zXsebqeQpGyROg0bUxDVW1nGzlM=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "2Tol7InSGMU5SeNAuuKY/5z1SSY0n8/wzfciwJXcf0g=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "b104Otw11zXpgk8aAVvG2PSdBYxe3xXAryh4Myto1h0=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "5uuRQ8DkzoO0fnCYD7sLa7QZbb5As/SzvtdGKuuvOcA=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "z1zxzwxtEaJVPSJ/54Af+qfMEoLb6lzaDDfwHXr/ed4=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "3Wk4DkTdwpLg+y2O6RLE3Sn6cEJT/iiVl3klh4vn5ak=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "qcLki9uIMVriwFG8yd8GtDbjXad64TRLskuOfTFvlGY=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-07T18:22:48.0186963+00:00"}, "2QyYRgrY8/RzMhA3dygjSmuPoqCkRCGyRNdMuzXvtg8=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-07T18:22:48.0197053+00:00"}, "/6czZhNm//UNM13lrrWe6nUYECc9OAD3pW/xng/s6pM=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-07T18:22:48.0207066+00:00"}, "1P61yc/h2lL40TqnpjGdqh+o1gCDON7RxHBozSUNgNY=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-07T18:22:48.0217038+00:00"}, "rYa/gD2+rmQBu5Evguz6XfOhlLVWeH4uGeMr5yfFev8=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "hL0r/uestWAQTdil0iWfIa343Lj6mLw9B+ttHbvDZ5U=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "UZ8VsjTBVAJY7saNZmGnZxNBEtoD4swwh2e01CTxR5I=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "ZlmhJnS5SP8qX4gigT8Ticm/FupIufRSUPCG5W/GEd0=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "EsOI8aO+rKcCssVl471bbk94MakRGoKnAPGelwQ5R38=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "DHJlGiI+CEF9+YgLZSwoWr2jocXRK3GzYk08UvrRc0k=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "UnA63tff5N200efcBugz0AtqjBvhI8vEQCQpXC14Tc8=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "EtMMx+RjHxZC4+MSRG5IbZhDEtO1CGO+wgA3A+GBrCU=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "LF19V4HGjn2ttNntxPWEDayCzA7kMK0QkW3I1vuHE4A=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "hZGprgIfpDDtuFe5m9rRCUrM3aAXYrt2tChULQW3uv4=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "IGRT42/TdP2E3uj2g7kiPu/YXLGHSTbbCCOtmN9R+Tw=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "U78tBJq2e3f9Fmd6nfTGFWN1t74AV1MToSXi6l2if1E=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "9Zq0hmmoVcHpd6m42k06hk6ilip908Wu4o98N2sFbOs=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "DrmHt5q0BDdgaHCz/QUTy8Pz371lgjmV2x2b+SZZXiU=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "6sIMdUfMeeAOm0ygzckk0GrEH8iSZ77Fx2wWYc4/VcM=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "4KVed3u0/UBSXOcQcDh60OwYI319xOD7ivRIbbp+NMU=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "t2MmtLd+06ybaIoQS8yityquooyBvFHDTzzZEaccGnU=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-07T18:22:48.0373776+00:00"}, "XKIMSRyFBRZPlh0Pjb4XnnmUcRrVMt0u9mU87EkZXVY=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-07T18:22:48.0549302+00:00"}, "v8liwon63FnJoWWhWC1UmXTrjs9TJ9TYOJL5xu/hPRs=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}}, "CachedCopyCandidates": {}}