-- Create tblGLConfig table for GL Configuration
-- This table stores the mapping between system modules and GL accounts

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tblGLConfig]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[tblGLConfig](
        [ConfigID] [int] IDENTITY(1,1) NOT NULL,
        [EntryReferenceModule] [nvarchar](100) NOT NULL,
        [AccountNo] [int] NOT NULL,
        [Description] [nvarchar](200) NULL,
        [IsActive] [bit] NOT NULL DEFAULT(1),
        CONSTRAINT [PK_tblGLConfig] PRIMARY KEY CLUSTERED ([ConfigID] ASC)
    )
END

-- Add index for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_tblGLConfig_EntryReferenceModule')
BEGIN
    CREATE UNIQUE INDEX [IX_tblGLConfig_EntryReferenceModule] ON [dbo].[tblGLConfig] ([EntryReferenceModule])
END

-- Insert default module configurations
IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'مبيعات')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('مبيعات', 0, 'إيرادات المبيعات', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'مشتريات')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('مشتريات', 0, 'تكلفة المشتريات', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'نقدية')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('نقدية', 0, 'النقدية والصندوق', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'مخزون أول المدة')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('مخزون أول المدة', 0, 'مخزون أول المدة', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'عملاء')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('عملاء', 0, 'حسابات العملاء', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'موردون')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('موردون', 0, 'حسابات الموردين', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'خصم مبيعات')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('خصم مبيعات', 0, 'خصم المبيعات', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'خصم مشتريات')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('خصم مشتريات', 0, 'خصم المشتريات', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'القيمة المضافة المحصلة')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('القيمة المضافة المحصلة', 0, 'القيمة المضافة المحصلة', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'القيمة المضافة المدفوعة')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('القيمة المضافة المدفوعة', 0, 'القيمة المضافة المدفوعة', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'مرتجعات المشتريات')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('مرتجعات المشتريات', 0, 'مرتجعات المشتريات', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'مرتجعات المبيعات')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('مرتجعات المبيعات', 0, 'مرتجعات المبيعات', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'الشريك')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('الشريك', 0, 'الشريك', 1)
END

IF NOT EXISTS (SELECT * FROM [dbo].[tblGLConfig] WHERE [EntryReferenceModule] = 'تكلفة المبيعات')
BEGIN
    INSERT INTO [dbo].[tblGLConfig] ([EntryReferenceModule], [AccountNo], [Description], [IsActive])
    VALUES ('تكلفة المبيعات', 0, 'تكلفة المبيعات', 1)
END

PRINT 'tblGLConfig table created and populated with default data successfully!' 