using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Web.Models;
using AccountingSystem.Services;

namespace AccountingSystem.Web.Controllers;

[Authorize]
public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly IPOSService _posService;

    public HomeController(ILogger<HomeController> logger, IPOSService posService)
    {
        _logger = logger;
        _posService = posService;
    }

    public async Task<IActionResult> Index()
    {
        // Get user information from claims
        var username = User.Identity?.Name ?? "Unknown";
        var userGroup = User.FindFirst("UserGroup")?.Value ?? "";
        var fullName = User.FindFirst("FullName")?.Value ?? username;

        ViewBag.Username = username;
        ViewBag.UserGroup = userGroup;
        ViewBag.FullName = fullName;
        ViewBag.LoginTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm");

        // Get today's total sales
        var today = DateTime.Today;
        var sales = await _posService.SearchInvoicesAsync(null, null, null, null, today);
        var totalSales = sales.Sum(i => i.TrxNetAmount);
        ViewBag.TodayTotalSales = totalSales;

        return View();
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
