using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Services.ViewModels;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class POSSessionsController : Controller
    {
        private readonly IPOSSessionService _posSessionService;
        private readonly ILogger<POSSessionsController> _logger;

        public POSSessionsController(IPOSSessionService posSessionService, ILogger<POSSessionsController> logger)
        {
            _posSessionService = posSessionService;
            _logger = logger;
        }

        public async Task<IActionResult> Index(POSSessionFilterViewModel filter)
        {
            try
            {
                var sessions = await _posSessionService.GetAllSessionsAsync(filter);
                var shops = await _posSessionService.GetAllShopsAsync();
                var devices = await _posSessionService.GetAllDevicesAsync();
                var shifts = await _posSessionService.GetAllShiftsAsync();

                ViewBag.Shops = shops ?? new List<ShopViewModel>();
                ViewBag.Devices = devices ?? new List<POSDeviceViewModel>();
                ViewBag.Shifts = shifts ?? new List<POSShiftViewModel>();
                ViewBag.Filter = filter;

                return View(sessions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading POS sessions");
                TempData["Error"] = "حدث خطأ أثناء تحميل الجلسات";
                
                // Provide empty collections to prevent null reference exceptions
                ViewBag.Shops = new List<ShopViewModel>();
                ViewBag.Devices = new List<POSDeviceViewModel>();
                ViewBag.Shifts = new List<POSShiftViewModel>();
                ViewBag.Filter = filter;
                
                return View(new List<POSSessionViewModel>());
            }
        }

        public async Task<IActionResult> Create()
        {
            try
            {
                var shops = await _posSessionService.GetAllShopsAsync();
                var devices = await _posSessionService.GetAllDevicesAsync();
                var shifts = await _posSessionService.GetAllShiftsAsync();

                ViewBag.Shops = shops;
                ViewBag.Devices = devices;
                ViewBag.Shifts = shifts;

                return View(new POSSessionCreateViewModel());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error preparing create session form");
                TempData["Error"] = "حدث خطأ أثناء تحضير نموذج إنشاء الجلسة";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(POSSessionCreateViewModel model)
        {
            if (!ModelState.IsValid)
            {
                try
                {
                    var shops = await _posSessionService.GetAllShopsAsync();
                    var devices = await _posSessionService.GetAllDevicesAsync();
                    var shifts = await _posSessionService.GetAllShiftsAsync();

                    ViewBag.Shops = shops;
                    ViewBag.Devices = devices;
                    ViewBag.Shifts = shifts;

                    return View(model);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error preparing create session form");
                    TempData["Error"] = "حدث خطأ أثناء تحضير النموذج";
                    return RedirectToAction(nameof(Index));
                }
            }

            try
            {
                var openedBy = User.Identity?.Name ?? "Unknown";
                var success = await _posSessionService.CreateSessionAsync(model, openedBy);

                if (success)
                {
                    TempData["Success"] = "تم فتح الجلسة الجديدة بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["Error"] = "فشل في فتح الجلسة الجديدة";
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating POS session");
                TempData["Error"] = "حدث خطأ أثناء إنشاء الجلسة";
                return RedirectToAction(nameof(Index));
            }
        }

        public async Task<IActionResult> Close(int id)
        {
            try
            {
                var session = await _posSessionService.GetSessionByIdAsync(id);
                if (session == null)
                {
                    TempData["Error"] = "الجلسة غير موجودة";
                    return RedirectToAction(nameof(Index));
                }

                if (session.IsClosed)
                {
                    TempData["Warning"] = "هذه الجلسة مغلقة بالفعل";
                    return RedirectToAction(nameof(Index));
                }

                var closeModel = new POSSessionCloseViewModel
                {
                    SessionID = session.SessionID,
                    SessionSN = session.SessionSN
                };

                return View(closeModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error preparing close session form");
                TempData["Error"] = "حدث خطأ أثناء تحضير نموذج إغلاق الجلسة";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Close(POSSessionCloseViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var closedBy = User.Identity?.Name ?? "Unknown";
                var success = await _posSessionService.CloseSessionAsync(model, closedBy);

                if (success)
                {
                    TempData["Success"] = "تم إغلاق الجلسة بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["Error"] = "فشل في إغلاق الجلسة";
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error closing POS session");
                TempData["Error"] = "حدث خطأ أثناء إغلاق الجلسة";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        public IActionResult Filter(POSSessionFilterViewModel filter)
        {
            return RedirectToAction(nameof(Index), filter);
        }
    }
} 