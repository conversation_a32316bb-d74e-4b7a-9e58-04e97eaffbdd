{"RootPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\ALSULTAN", "ProjectFileName": "ALSULTAN.vbproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "CommunFunctions.vb"}, {"SourceFile": "FormPermissionInfo.vb"}, {"SourceFile": "connection.vb"}, {"SourceFile": "frmAccountSearch.Designer.vb"}, {"SourceFile": "frmAccountSearch.vb"}, {"SourceFile": "frmActivation.Designer.vb"}, {"SourceFile": "frmActivation.vb"}, {"SourceFile": "frmBalanceUpload.Designer.vb"}, {"SourceFile": "frmBalanceUpload.vb"}, {"SourceFile": "frmBarcodeSettings.Designer.vb"}, {"SourceFile": "frmBarcodeSettings.vb"}, {"SourceFile": "frmCOAManagement.Designer.vb"}, {"SourceFile": "frmCOAManagement.vb"}, {"SourceFile": "frmCustomerBalances.Designer.vb"}, {"SourceFile": "frmCustomerBalances.vb"}, {"SourceFile": "frmCustomerInvoices.Designer.vb"}, {"SourceFile": "frmCustomerInvoices.vb"}, {"SourceFile": "frmCustomerSearch.Designer.vb"}, {"SourceFile": "frmCustomerSearch.vb"}, {"SourceFile": "frmCustomersUpload.Designer.vb"}, {"SourceFile": "frmCustomersUpload.vb"}, {"SourceFile": "frmDBCon.Designer.vb"}, {"SourceFile": "frmDBCon.vb"}, {"SourceFile": "frmGroupPermissions.Designer.vb"}, {"SourceFile": "frmGroupPermissions.vb"}, {"SourceFile": "frmItemStock.Designer.vb"}, {"SourceFile": "frmItemStock.vb"}, {"SourceFile": "frmItemsTree.Designer.vb"}, {"SourceFile": "frmItemsTree.vb"}, {"SourceFile": "frmItemsUpload.Designer.vb"}, {"SourceFile": "frmItemsUpload.vb"}, {"SourceFile": "frmLoginNew.Designer.vb"}, {"SourceFile": "frmLoginNew.vb"}, {"SourceFile": "frmMassUpload.Designer.vb"}, {"SourceFile": "frmMassUpload.vb"}, {"SourceFile": "frmOpenStockInvoice.Designer.vb"}, {"SourceFile": "frmOpenStockInvoice.vb"}, {"SourceFile": "frmPOSNew.Designer.vb"}, {"SourceFile": "frmPOSNew.vb"}, {"SourceFile": "frmPurchaseInvoiceTrx.Designer.vb"}, {"SourceFile": "frmPurchaseInvoiceTrx.vb"}, {"SourceFile": "frmPayMethod.Designer.vb"}, {"SourceFile": "frmPayMethod.vb"}, {"SourceFile": "frmPaySplit.Designer.vb"}, {"SourceFile": "frmPaySplit.vb"}, {"SourceFile": "frmPOS.Designer.vb"}, {"SourceFile": "frmPOS.vb"}, {"SourceFile": "frmPOSDevices.Designer.vb"}, {"SourceFile": "frmPOSDevices.vb"}, {"SourceFile": "frmPOSSessions.Designer.vb"}, {"SourceFile": "frmPOSSessions.vb"}, {"SourceFile": "frmPOSSettings.Designer.vb"}, {"SourceFile": "frmPOSSettings.vb"}, {"SourceFile": "frmPOSShifts.Designer.vb"}, {"SourceFile": "frmPOSShifts.vb"}, {"SourceFile": "frmPurchaseInvoiceTrxNew.Designer.vb"}, {"SourceFile": "frmPurchaseInvoiceTrxNew.vb"}, {"SourceFile": "frmPurchasingReturnInvoice.Designer.vb"}, {"SourceFile": "frmPurchasingReturnInvoice.vb"}, {"SourceFile": "frmSearchJE.Designer.vb"}, {"SourceFile": "frmSearchJE.vb"}, {"SourceFile": "frmSearchPurchase.Designer.vb"}, {"SourceFile": "frmSearchPurchase.vb"}, {"SourceFile": "frmSearchStock.Designer.vb"}, {"SourceFile": "frmSearchStock.vb"}, {"SourceFile": "frmSelectItem.Designer.vb"}, {"SourceFile": "frmSelectItem.vb"}, {"SourceFile": "frmShopsMaster.Designer.vb"}, {"SourceFile": "frmShopsMaster.vb"}, {"SourceFile": "frmStockAdjustment.Designer.vb"}, {"SourceFile": "frmStockAdjustment.vb"}, {"SourceFile": "frmStockMovement.Designer.vb"}, {"SourceFile": "frmStockMovement.vb"}, {"SourceFile": "frmStockMovements.Designer.vb"}, {"SourceFile": "frmStockMovements.vb"}, {"SourceFile": "frmStoresMaster.Designer.vb"}, {"SourceFile": "frmStoresMaster.vb"}, {"SourceFile": "frmTafqeet.Designer.vb"}, {"SourceFile": "frmTafqeet.vb"}, {"SourceFile": "frmTest.Designer.vb"}, {"SourceFile": "frmTest.vb"}, {"SourceFile": "frmTestPOS.Designer.vb"}, {"SourceFile": "frmTestPOS.vb"}, {"SourceFile": "frmToolsCOA.Designer.vb"}, {"SourceFile": "frmToolsCOA.vb"}, {"SourceFile": "frmTrxExpenses.Designer.vb"}, {"SourceFile": "frmTrxExpenses.vb"}, {"SourceFile": "frmUserPOSItems.Designer.vb"}, {"SourceFile": "frmUserPOSItems.vb"}, {"SourceFile": "frmVendorBalances.Designer.vb"}, {"SourceFile": "frmVendorBalances.vb"}, {"SourceFile": "frmVendorInvoices.Designer.vb"}, {"SourceFile": "frmVendorInvoices.vb"}, {"SourceFile": "frmVendors.Designer.vb"}, {"SourceFile": "frmVendors.vb"}, {"SourceFile": "frmVendorSearch.Designer.vb"}, {"SourceFile": "frmVendorSearch.vb"}, {"SourceFile": "frmVendorStatement.Designer.vb"}, {"SourceFile": "frmVendorStatement.vb"}, {"SourceFile": "frmVendorsUpload.Designer.vb"}, {"SourceFile": "frmVendorsUpload.vb"}, {"SourceFile": "frmZATCACustomerInfo.Designer.vb"}, {"SourceFile": "frmZATCACustomerInfo.vb"}, {"SourceFile": "Module2.vb"}, {"SourceFile": "AuthorizationModule.vb"}, {"SourceFile": "rptCashStatement.vb"}, {"SourceFile": "rptCustomerBalances.vb"}, {"SourceFile": "rptPOSInvoiceNoQR.vb"}, {"SourceFile": "rptSalesInvoice-NoQR.vb"}, {"SourceFile": "rptVendorBalances.vb"}, {"SourceFile": "rptVendorStatement.vb"}, {"SourceFile": "rptExpenses - Copy.vb"}, {"SourceFile": "rptCustStatement.vb"}, {"SourceFile": "rptJEStatement.vb"}, {"SourceFile": "rptPOSInvoice.vb"}, {"SourceFile": "rptPOSInvoice-test.vb"}, {"SourceFile": "rptPurchaseHeader.vb"}, {"SourceFile": "rptPurchaseLines.vb"}, {"SourceFile": "rptSalesLines.vb"}, {"SourceFile": "rptSalesHeader.vb"}, {"SourceFile": "rptSalesInvoice.vb"}, {"SourceFile": "rptSalesInvoice - Backup.vb"}, {"SourceFile": "rptSalesInvoice-Test.vb"}, {"SourceFile": "rptPurchaseReturnInvoice.vb"}, {"SourceFile": "rptPurchaseInvoice.vb"}, {"SourceFile": "rptPurchaseReturnInvoice-1.vb"}, {"SourceFile": "frmSearchInvoices.Designer.vb"}, {"SourceFile": "frmSearchInvoices.vb"}, {"SourceFile": "rptNonSNStock2.vb"}, {"SourceFile": "rptNonSNStock.vb"}, {"SourceFile": "frmEmployees.Designer.vb"}, {"SourceFile": "frmEmployees.vb"}, {"SourceFile": "rptExpenses.vb"}, {"SourceFile": "rptGLTrx.vb"}, {"SourceFile": "rptCashRec.vb"}, {"SourceFile": "rptCashPay.vb"}, {"SourceFile": "frmJETrx.Designer.vb"}, {"SourceFile": "frmJETrx.vb"}, {"SourceFile": "frmCashPrintPreview.Designer.vb"}, {"SourceFile": "frmCashPrintPreview.vb"}, {"SourceFile": "frmCashStatement.Designer.vb"}, {"SourceFile": "frmCashStatement.vb"}, {"SourceFile": "frmCashStatementAllPrint.Designer.vb"}, {"SourceFile": "frmCashStatementAllPrint.vb"}, {"SourceFile": "frmCashStatementPrint.Designer.vb"}, {"SourceFile": "frmCashStatementPrint.vb"}, {"SourceFile": "frmChangeMyPass.Designer.vb"}, {"SourceFile": "frmChangeMyPass.vb"}, {"SourceFile": "frmCompanies.Designer.vb"}, {"SourceFile": "frmCompanies.vb"}, {"SourceFile": "frmCreditStatement.Designer.vb"}, {"SourceFile": "frmCreditStatement.vb"}, {"SourceFile": "frmCreditStatementPrint.Designer.vb"}, {"SourceFile": "frmCreditStatementPrint.vb"}, {"SourceFile": "frmCustomers.Designer.vb"}, {"SourceFile": "frmCustomers.vb"}, {"SourceFile": "frmCustomerStatement.Designer.vb"}, {"SourceFile": "frmCustomerStatement.vb"}, {"SourceFile": "frmCustomerStatementPrint.Designer.vb"}, {"SourceFile": "frmCustomerStatementPrint.vb"}, {"SourceFile": "frmDBMaint.Designer.vb"}, {"SourceFile": "frmDBMaint.vb"}, {"SourceFile": "frmJEStatement.Designer.vb"}, {"SourceFile": "frmJEStatement.vb"}, {"SourceFile": "frmInvoiceReference.Designer.vb"}, {"SourceFile": "frmInvoiceReference.vb"}, {"SourceFile": "frmItems.Designer.vb"}, {"SourceFile": "frmItems.vb"}, {"SourceFile": "frmItemsCategory.Designer.vb"}, {"SourceFile": "frmItemsCategory.vb"}, {"SourceFile": "frmItemSearch.Designer.vb"}, {"SourceFile": "frmItemSearch.vb"}, {"SourceFile": "frmLogin.Designer.vb"}, {"SourceFile": "frmLogin.vb"}, {"SourceFile": "frmMain.Designer.vb"}, {"SourceFile": "frmMain.vb"}, {"SourceFile": "frmPrintPreview.Designer.vb"}, {"SourceFile": "frmPrintPreview.vb"}, {"SourceFile": "frmSalesInvoiceTrx.Designer.vb"}, {"SourceFile": "frmSalesInvoiceTrx.vb"}, {"SourceFile": "frmSalesReturnInvoiceTrx.Designer.vb"}, {"SourceFile": "frmSalesReturnInvoiceTrx.vb"}, {"SourceFile": "frmSetup.Designer.vb"}, {"SourceFile": "frmSetup.vb"}, {"SourceFile": "frmToolsChartOfAccounts.Designer.vb"}, {"SourceFile": "frmToolsChartOfAccounts.vb"}, {"SourceFile": "frmToolsGL.Designer.vb"}, {"SourceFile": "frmToolsGL.vb"}, {"SourceFile": "frmToolsInvoice.Designer.vb"}, {"SourceFile": "frmToolsInvoice.vb"}, {"SourceFile": "frmToolsPrint.Designer.vb"}, {"SourceFile": "frmToolsPrint.vb"}, {"SourceFile": "frmTrxCashPay.Designer.vb"}, {"SourceFile": "frmTrxCashPay.vb"}, {"SourceFile": "frmTrxCashRec.Designer.vb"}, {"SourceFile": "frmTrxCashRec.vb"}, {"SourceFile": "frmUsers.Designer.vb"}, {"SourceFile": "frmUsers.vb"}, {"SourceFile": "Module1.vb"}, {"SourceFile": "My Project\\AssemblyInfo.vb"}, {"SourceFile": "My Project\\Application.Designer.vb"}, {"SourceFile": "My Project\\Resources.Designer.vb"}, {"SourceFile": "My Project\\Settings.Designer.vb"}, {"SourceFile": "rptCustomerStatement.vb"}, {"SourceFile": "rptEmployeeSalary.vb"}, {"SourceFile": "rptInAnalysis.vb"}, {"SourceFile": "rptInAnalysis2.vb"}, {"SourceFile": "rptLoss.vb"}, {"SourceFile": "rptLossAnalysis.vb"}, {"SourceFile": "rptOpenStockInvoice.vb"}, {"SourceFile": "rptOutAnalysis.vb"}, {"SourceFile": "rptOutAnalysis2.vb"}, {"SourceFile": "rptPartnersBalance.vb"}, {"SourceFile": "rptPurchasing.vb"}, {"SourceFile": "rptPurchasingItem.vb"}, {"SourceFile": "rptSalesLoss.vb"}, {"SourceFile": "rptSalesNonBenfit.vb"}, {"SourceFile": "rptSold.vb"}, {"SourceFile": "rptSoldItem.vb"}, {"SourceFile": "rptSoldSearch.vb"}, {"SourceFile": "rptItemStock.vb"}, {"SourceFile": "rptStockMovements.vb"}, {"SourceFile": "rptStocks.vb"}, {"SourceFile": "rptStocks2.vb"}, {"SourceFile": "rptStocks3.vb"}, {"SourceFile": "rptStocksSearch.vb"}, {"SourceFile": "rptStocksSearchAll.vb"}, {"SourceFile": "rptStocksSearchOld.vb"}, {"SourceFile": "rptSummary.vb"}, {"SourceFile": "SplashScreen1.Designer.vb"}, {"SourceFile": "SplashScreen1.vb"}, {"SourceFile": "‫rptCreditStatement.vb"}, {"SourceFile": "‫rptDamage.vb"}, {"SourceFile": "‫rptVendorCreditPayments.vb"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.vb"}], "References": [{"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Augment.3.0.0\\lib\\net471\\Augment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\CrystalReports.AxShockwaveFlashObjects.13.0.4003\\lib\\net40\\AxShockwaveFlashObjects.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Azure.Core.1.45.0\\lib\\net472\\Azure.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Azure.Identity.1.13.2\\lib\\netstandard2.0\\Azure.Identity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\SAP BusinessObjects\\Crystal Reports for .NET Framework 4.0\\Common\\SAP BusinessObjects Enterprise XI 4.0\\win64_x64\\dotnet\\CrystalDecisions.CrystalReports.Engine.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\SAP BusinessObjects\\Crystal Reports for .NET Framework 4.0\\Common\\SAP BusinessObjects Enterprise XI 4.0\\win64_x64\\dotnet\\CrystalDecisions.ReportSource.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\SAP BusinessObjects\\Crystal Reports for .NET Framework 4.0\\Common\\SAP BusinessObjects Enterprise XI 4.0\\win64_x64\\dotnet\\CrystalDecisions.Shared.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\SAP BusinessObjects\\Crystal Reports for .NET Framework 4.0\\Common\\SAP BusinessObjects Enterprise XI 4.0\\win64_x64\\dotnet\\CrystalDecisions.VSDesigner.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\SAP BusinessObjects\\Crystal Reports for .NET Framework 4.0\\Common\\SAP BusinessObjects Enterprise XI 4.0\\win64_x64\\dotnet\\CrystalDecisions.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Ensure.That.8.0.0\\lib\\net451\\Ensure.That.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\EPPlusFree.4.5.3.8\\lib\\net40\\EPPlusFree.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\ALSULTAN\\bin\\Debug\\FlashControlV71.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Genesis.QRCodeLib.1.0.0\\lib\\net45\\Genesis.QRCodeLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Guna.UI2.WinForms.2.0.4.7\\lib\\net472\\Guna.UI2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\log4net.3.0.4\\lib\\net462\\log4net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\MaterialSkin.2.2.3.1\\lib\\net461\\MaterialSkin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Bcl.AsyncInterfaces.9.0.3\\lib\\net462\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Bcl.Cryptography.9.0.3\\lib\\net462\\Microsoft.Bcl.Cryptography.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Bcl.Memory.9.0.3\\lib\\net462\\Microsoft.Bcl.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Bcl.TimeProvider.9.0.3\\lib\\net462\\Microsoft.Bcl.TimeProvider.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Data.SqlClient.6.0.1\\lib\\net462\\Microsoft.Data.SqlClient.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.Data.Tools.Sql.BatchParser.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Extensions.Caching.Abstractions.9.0.3\\lib\\net462\\Microsoft.Extensions.Caching.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Extensions.Caching.Memory.9.0.3\\lib\\net462\\Microsoft.Extensions.Caching.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.3\\lib\\net462\\Microsoft.Extensions.DependencyInjection.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Extensions.Logging.Abstractions.9.0.3\\lib\\net462\\Microsoft.Extensions.Logging.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Extensions.Options.9.0.3\\lib\\net462\\Microsoft.Extensions.Options.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Extensions.Primitives.9.0.3\\lib\\net462\\Microsoft.Extensions.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Identity.Client.4.69.1\\lib\\net472\\Microsoft.Identity.Client.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.Identity.Client.Extensions.Msal.4.69.1\\lib\\netstandard2.0\\Microsoft.Identity.Client.Extensions.Msal.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.IdentityModel.Abstractions.8.6.1\\lib\\net472\\Microsoft.IdentityModel.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.IdentityModel.JsonWebTokens.8.6.1\\lib\\net472\\Microsoft.IdentityModel.JsonWebTokens.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.IdentityModel.Logging.8.6.1\\lib\\net472\\Microsoft.IdentityModel.Logging.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.IdentityModel.Protocols.8.6.1\\lib\\net472\\Microsoft.IdentityModel.Protocols.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.IdentityModel.Protocols.OpenIdConnect.8.6.1\\lib\\net472\\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.IdentityModel.Tokens.8.6.1\\lib\\net472\\Microsoft.IdentityModel.Tokens.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.Assessment.1.1.17\\lib\\net462\\Microsoft.SqlServer.Assessment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.Assessment.Authoring.1.1.0\\lib\\net462\\Microsoft.SqlServer.Assessment.Types.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.ConnectionInfo.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Dmf.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Dmf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Management.Collector.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Management.CollectorEnum.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Management.HadrData.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Management.HadrModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Management.RegisteredServers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Management.Sdk.Sfc.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Management.SqlScriptPublish.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Management.XEvent.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Management.XEventDbScoped.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Management.XEventDbScopedEnum.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Management.XEventEnum.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.PolicyEnum.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.RegSvrEnum.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.ServiceBrokerEnum.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Smo.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.Smo.Notebook.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.SmoExtended.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.SqlClrProvider.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.SqlEnum.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.SqlWmiManagement.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Microsoft.SqlServer.SqlManagementObjects.172.64.0\\lib\\net472\\Microsoft.SqlServer.WmiEnum.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\QRCoder.1.6.0\\lib\\net40\\QRCoder.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\SAP BusinessObjects\\Crystal Reports for .NET Framework 4.0\\Common\\SAP BusinessObjects Enterprise XI 4.0\\win64_x64\\dotnet\\SAPBusinessObjects.WPF.ViewerShared.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\CrystalReports.ShockwaveFlashObjects.13.0.4003\\lib\\net40\\ShockwaveFlashObjects.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Buffers.4.6.0\\lib\\net462\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.ClientModel.1.3.0\\lib\\netstandard2.0\\System.ClientModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.Composition.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Configuration.ConfigurationManager.9.0.3\\lib\\net462\\System.Configuration.ConfigurationManager.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.OracleClient.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Diagnostics.DiagnosticSource.9.0.3\\lib\\net462\\System.Diagnostics.DiagnosticSource.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.DirectoryServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Formats.Asn1.9.0.3\\lib\\net462\\System.Formats.Asn1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.IdentityModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.IdentityModel.Tokens.Jwt.8.6.1\\lib\\net472\\System.IdentityModel.Tokens.Jwt.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.IO.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.IO.FileSystem.AccessControl.5.0.0\\lib\\net461\\System.IO.FileSystem.AccessControl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.IO.Pipelines.9.0.3\\lib\\net462\\System.IO.Pipelines.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Memory.Data.9.0.3\\lib\\net462\\System.Memory.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Memory.4.6.0\\lib\\net462\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Numerics.Vectors.4.6.0\\lib\\net462\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Runtime.CompilerServices.Unsafe.6.1.0\\lib\\net462\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Security.AccessControl.6.0.1\\lib\\net461\\System.Security.AccessControl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Security.Cryptography.Algorithms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Security.Cryptography.Encoding.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Security.Cryptography.Pkcs.9.0.3\\lib\\net462\\System.Security.Cryptography.Pkcs.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Security.Cryptography.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Security.Cryptography.ProtectedData.9.0.3\\lib\\net462\\System.Security.Cryptography.ProtectedData.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Security.Cryptography.X509Certificates.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Security.Permissions.9.0.3\\lib\\net462\\System.Security.Permissions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ServiceProcess.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Text.Encodings.Web.9.0.3\\lib\\net462\\System.Text.Encodings.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Text.Json.9.0.3\\lib\\net462\\System.Text.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.Threading.Tasks.Extensions.4.6.0\\lib\\net462\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Transactions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\System.ValueTuple.4.5.0\\lib\\net47\\System.ValueTuple.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Services.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\RibbonWinForms.5.1.0-beta\\lib\\net48\\System.Windows.Forms.Ribbon.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Zen.Barcode.Core.2.0.0\\lib\\net45\\Zen.Barcode.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Zen.Barcode.Rendering.Framework.Web.3.1.10729.1\\lib\\Zen.Barcode.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Zen.Barcode.Rendering.Framework.Web.3.1.10729.1\\lib\\Zen.Barcode.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\Zen.Barcode.Rendering.Framework.Web.3.1.10729.1\\lib\\Zen.Barcode.Web.Mvc.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\ZXing.Net.0.16.10\\lib\\net48\\zxing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\packages\\ZXing.Net.0.16.10\\lib\\net48\\zxing.presentation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\Dose Solutions\\Systems\\AlsultanSoft\\ALSULTAN\\bin\\Debug\\ALSULTAN.exe", "OutputItemRelativePath": "ALSULTAN.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}