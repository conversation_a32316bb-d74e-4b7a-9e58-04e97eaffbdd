@model AccountingSystem.Web.Controllers.GLConfigIndexViewModel
@{
    ViewData["Title"] = "إعدادات ربط الحسابات";
    Layout = "_Layout";
}

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-link me-2"></i>
                        إعدادات ربط الحسابات
                    </h4>
                    <small class="d-block mt-1">ربط وحدات النظام بحسابات دليل الحسابات</small>
                </div>
                <div class="card-body">
                    <!-- Alert Messages -->
                    @if (TempData["Success"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["Success"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["Error"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["Error"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <div id="alertContainer"></div>

                    <!-- GL Configuration Form -->
                    <form id="glConfigForm">
                        @Html.AntiForgeryToken()
                        
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th style="width: 30%;">الوحدة</th>
                                                <th style="width: 25%;">رقم الحساب</th>
                                                <th style="width: 45%;">اسم الحساب</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for (int i = 0; i < Model.GLConfigs.Count; i++)
                                            {
                                                var config = Model.GLConfigs[i];
                                                <tr data-module="@config.EntryReferenceModule">
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-cog me-2 text-primary"></i>
                                                            <strong>@config.ModuleDisplayName</strong>
                                                        </div>
                                                        <input type="hidden" name="configs[@i].Id" value="@config.Id" />
                                                        <input type="hidden" name="configs[@i].EntryReferenceModule" value="@config.EntryReferenceModule" />
                                                        <input type="hidden" name="configs[@i].ModuleDisplayName" value="@config.ModuleDisplayName" />
                                                        <input type="hidden" name="configs[@i].IsActive" value="@config.IsActive" />
                                                    </td>
                                                    <td>
                                                        <select name="configs[@i].AccountNo" 
                                                                class="form-select account-select" 
                                                                data-index="@i"
                                                                data-module="@config.EntryReferenceModule">
                                                            <option value="0">-- اختر الحساب --</option>
                                                                                                                         @foreach (var account in Model.AvailableAccounts)
                                                             {
                                                                 if (account.Key == config.AccountNo.ToString())
                                                                 {
                                                                     <option value="@account.Key" selected>@account.Value</option>
                                                                 }
                                                                 else
                                                                 {
                                                                     <option value="@account.Key">@account.Value</option>
                                                                 }
                                                             }
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <input type="text" 
                                                               name="configs[@i].AccountName" 
                                                               class="form-control account-name" 
                                                               value="@config.AccountName" 
                                                               readonly 
                                                               placeholder="اسم الحساب سيظهر هنا تلقائياً" />
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-info-circle me-2"></i>
                                            معلومات الوحدات
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="module-info">
                                            <h6 class="text-primary">وحدات النظام:</h6>
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="mb-2">
                                                        <span class="badge bg-primary me-2">المبيعات</span>
                                                        <small class="text-muted">إيرادات المبيعات</small>
                                                    </div>
                                                    <div class="mb-2">
                                                        <span class="badge bg-success me-2">المشتريات</span>
                                                        <small class="text-muted">تكلفة المشتريات</small>
                                                    </div>
                                                    <div class="mb-2">
                                                        <span class="badge bg-warning me-2">النقدية</span>
                                                        <small class="text-muted">النقدية والصندوق</small>
                                                    </div>
                                                    <div class="mb-2">
                                                        <span class="badge bg-info me-2">العملاء</span>
                                                        <small class="text-muted">حسابات العملاء</small>
                                                    </div>
                                                    <div class="mb-2">
                                                        <span class="badge bg-secondary me-2">الموردون</span>
                                                        <small class="text-muted">حسابات الموردين</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            تنبيهات مهمة
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled mb-0">
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                اختر حسابات الترحيل فقط
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                تأكد من صحة ربط الحسابات
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                احفظ التغييرات بعد الانتهاء
                                            </li>
                                            <li class="mb-0">
                                                <i class="fas fa-check text-success me-2"></i>
                                                راجع الربط مع المحاسب
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="/SimpleDashboard" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                    <button type="button" class="btn btn-primary" id="saveBtn">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ الإعدادات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Handle account selection change
            $('.account-select').change(function() {
                var accountNo = $(this).val();
                var accountNameInput = $(this).closest('tr').find('.account-name');
                
                if (accountNo && accountNo !== '0') {
                    // Get account name
                    $.get('@Url.Action("GetAccountName", "GLConfig")', { accountNo: accountNo })
                        .done(function(response) {
                            if (response.success) {
                                accountNameInput.val(response.accountName);
                            } else {
                                accountNameInput.val('');
                                showAlert('error', 'حدث خطأ في جلب اسم الحساب');
                            }
                        })
                        .fail(function() {
                            accountNameInput.val('');
                            showAlert('error', 'حدث خطأ في الاتصال بالخادم');
                        });
                } else {
                    accountNameInput.val('');
                }
            });

            // Handle save button
            $('#saveBtn').click(function() {
                var btn = $(this);
                var originalText = btn.html();
                
                // Disable button and show loading
                btn.prop('disabled', true);
                btn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...');

                // Collect form data
                var formData = {
                    configs: []
                };

                $('#glConfigForm tbody tr').each(function() {
                    var row = $(this);
                    var config = {
                        Id: parseInt(row.find('input[name$=".Id"]').val()) || 0,
                        EntryReferenceModule: row.find('input[name$=".EntryReferenceModule"]').val(),
                        ModuleDisplayName: row.find('input[name$=".ModuleDisplayName"]').val(),
                        AccountNo: parseInt(row.find('.account-select').val()) || 0,
                        AccountName: row.find('.account-name').val(),
                        Description: '',
                        IsActive: row.find('input[name$=".IsActive"]').val() === 'true'
                    };
                    formData.configs.push(config);
                });

                // Send data to server
                $.ajax({
                    url: '@Url.Action("Save", "GLConfig")',
                    type: 'POST',
                    data: JSON.stringify(formData),
                    contentType: 'application/json',
                    headers: {
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            showAlert('success', response.message || 'تم حفظ الإعدادات بنجاح');
                        } else {
                            showAlert('error', response.message || 'حدث خطأ أثناء حفظ الإعدادات');
                        }
                    },
                    error: function(xhr, status, error) {
                        showAlert('error', 'حدث خطأ في الاتصال بالخادم');
                    },
                    complete: function() {
                        // Re-enable button
                        btn.prop('disabled', false);
                        btn.html(originalText);
                    }
                });
            });

            // Show alert function
            function showAlert(type, message) {
                var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                var icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
                
                var alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <i class="fas ${icon} me-2"></i>
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                
                $('#alertContainer').html(alertHtml);
                
                // Auto-hide success alerts after 5 seconds
                if (type === 'success') {
                    setTimeout(function() {
                        $('#alertContainer .alert-success').fadeOut();
                    }, 5000);
                }
            }

            // Initialize account names on page load
            $('.account-select').each(function() {
                var accountNo = $(this).val();
                if (accountNo && accountNo !== '0') {
                    $(this).trigger('change');
                }
            });
        });
    </script>

    <style>
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            border-radius: 12px 12px 0 0 !important;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table th {
            background-color: #2c3e50;
            color: white;
            font-weight: 600;
            border: none;
        }

        .table td {
            vertical-align: middle;
            border-color: #e9ecef;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .form-select, .form-control {
            border-radius: 8px;
            border: 1px solid #ced4da;
            transition: all 0.3s ease;
        }

        .form-select:focus, .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.35em 0.65em;
        }

        .module-info {
            font-size: 0.9rem;
        }

        .alert {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .shadow-lg {
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
        }

        .table-responsive {
            border-radius: 8px;
        }

        .account-name {
            background-color: #f8f9fa;
            font-weight: 500;
        }

        .account-select {
            font-weight: 500;
        }

        .text-muted {
            font-size: 0.85rem;
        }
    </style>
} 