using System.ComponentModel.DataAnnotations;

namespace AccountingSystem.Services.ViewModels
{
    public class ChartOfAccountViewModel
    {
        public string AccountCode { get; set; } = string.Empty;
        public string? SegmentCode { get; set; }
        public string AccountName { get; set; } = string.Empty;
        public string? ParentAccountCode { get; set; }
        public int AccountLevel { get; set; }
        public bool IsPosting { get; set; }
        public string? AccountType { get; set; }
        public string? AccountNature { get; set; }
        public decimal OpeningBalance { get; set; }
        public string? Notes { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public List<ChartOfAccountViewModel> SubAccounts { get; set; } = new();
        public List<ChartOfAccountViewModel> Children { get; set; } = new();
        public bool HasChildren => Children.Any();
    }

    public class ChartOfAccountCreateViewModel
    {
        [Required(ErrorMessage = "رمز الحساب مطلوب")]
        [StringLength(50, ErrorMessage = "رمز الحساب لا يمكن أن يتجاوز 50 حرف")]
        public string AccountCode { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "رمز القطاع لا يمكن أن يتجاوز 20 حرف")]
        public string? SegmentCode { get; set; }

        [Required(ErrorMessage = "اسم الحساب مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الحساب لا يمكن أن يتجاوز 200 حرف")]
        public string AccountName { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "رمز الحساب الأب لا يمكن أن يتجاوز 50 حرف")]
        public string? ParentAccountCode { get; set; }

        public bool IsPosting { get; set; } = true;

        [StringLength(50, ErrorMessage = "نوع الحساب لا يمكن أن يتجاوز 50 حرف")]
        public string? AccountType { get; set; }

        [StringLength(20, ErrorMessage = "طبيعة الحساب لا يمكن أن تتجاوز 20 حرف")]
        public string? AccountNature { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الرصيد الافتتاحي يجب أن يكون موجب")]
        public decimal OpeningBalance { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات لا يمكن أن تتجاوز 500 حرف")]
        public string? Notes { get; set; }
    }

    public class ChartOfAccountEditViewModel
    {
        [Required(ErrorMessage = "رمز الحساب مطلوب")]
        [StringLength(50, ErrorMessage = "رمز الحساب لا يمكن أن يتجاوز 50 حرف")]
        public string AccountCode { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "رمز القطاع لا يمكن أن يتجاوز 20 حرف")]
        public string? SegmentCode { get; set; }

        [Required(ErrorMessage = "اسم الحساب مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الحساب لا يمكن أن يتجاوز 200 حرف")]
        public string AccountName { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "رمز الحساب الأب لا يمكن أن يتجاوز 50 حرف")]
        public string? ParentAccountCode { get; set; }

        public bool IsPosting { get; set; } = true;

        [StringLength(50, ErrorMessage = "نوع الحساب لا يمكن أن يتجاوز 50 حرف")]
        public string? AccountType { get; set; }

        [StringLength(20, ErrorMessage = "طبيعة الحساب لا يمكن أن تتجاوز 20 حرف")]
        public string? AccountNature { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الرصيد الافتتاحي يجب أن يكون موجب")]
        public decimal OpeningBalance { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات لا يمكن أن تتجاوز 500 حرف")]
        public string? Notes { get; set; }
    }
} 