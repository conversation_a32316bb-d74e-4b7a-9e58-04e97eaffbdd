@model AccountingSystem.Models.PurchaseInvoiceViewModel
@{
    ViewData["Title"] = ViewBag.PageTitle;
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">@ViewBag.PageTitle</h3>
                </div>
                <div class="card-body">
                    <form id="purchaseInvoiceForm">
                        <!-- Invoice Header -->
                        <div class="row mb-3">
                            <div class="col-md-2">
                                <label for="TrxNo" class="form-label">رقم الفاتورة</label>
                                <input type="text" class="form-control" id="TrxNo" name="TrxNo" value="@Model.TrxNo" readonly />
                            </div>
                            <div class="col-md-2">
                                <label for="TrxDate" class="form-label">تاريخ الفاتورة</label>
                                <input type="date" class="form-control" id="TrxDate" name="TrxDate" value="@(Model.TrxDate?.ToString("yyyy-MM-dd") ?? DateTime.Now.ToString("yyyy-MM-dd"))" />
                            </div>
                            <div class="col-md-2">
                                <label for="ReferenceInvoice" class="form-label">رقم المرجع</label>
                                <input type="text" class="form-control" id="ReferenceInvoice" name="ReferenceInvoice" value="@Model.ReferenceInvoice" />
                            </div>
                            <div class="col-md-3">
                                <label for="PartnerNo" class="form-label">المورد</label>
                                <select class="form-control" id="PartnerNo" name="PartnerNo" @(!Model.UserAuthorization.IsAdmin && !Model.UserAuthorization.CustomerChange ? "disabled" : "")>
                                    <option value="">اختر المورد</option>
                                    @foreach (var vendor in Model.Vendors)
                                    {
                                        <option value="@vendor.AccountCode">@vendor.AccountName - @vendor.AccountCode</option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="PartnerName" class="form-label">اسم المورد</label>
                                <input type="text" class="form-control" id="PartnerName" name="PartnerName" value="@Model.PartnerName" readonly />
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-2">
                                <label for="PaymentMethod" class="form-label">طريقة الدفع</label>
                                <select class="form-control" id="PaymentMethod" name="PaymentMethod">
                                    <option value="نقدي">نقدي</option>
                                    <option value="آجل">آجل</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="Cashier" class="form-label">الصندوق</label>
                                <select class="form-control" id="Cashier" name="Cashier" @(!Model.UserAuthorization.IsAdmin && !Model.UserAuthorization.CashierChange ? "disabled" : "")>
                                    <option value="">اختر الصندوق</option>
                                    @foreach (var cashier in Model.Cashiers)
                                    {
                                        <option value="@cashier.AccountCode">@cashier.AccountName</option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="Store" class="form-label">المستودع</label>
                                <select class="form-control" id="Store" name="Store" @(!Model.UserAuthorization.IsAdmin && !Model.UserAuthorization.StoreChange ? "disabled" : "")>
                                    <option value="">اختر المستودع</option>
                                    @foreach (var store in Model.Stores)
                                    {
                                        <option value="@store.SN">@store.StoreName</option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="PartnerReference" class="form-label">رقم أمر الشراء</label>
                                <input type="text" class="form-control" id="PartnerReference" name="PartnerReference" value="@Model.PartnerReference" />
                            </div>
                            <div class="col-md-3">
                                <label for="TrxNote" class="form-label">ملاحظات</label>
                                <input type="text" class="form-control" id="TrxNote" name="TrxNote" value="@Model.TrxNote" />
                            </div>
                        </div>

                        <!-- VAT Settings -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="VATIncluded" name="VATIncluded" @(Model.VATIncluded ? "checked" : "") />
                                    <label class="form-check-label" for="VATIncluded">
                                        السعر يتضمن الضريبة
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Invoice Items -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <h5>أصناف الفاتورة</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="itemsTable">
                                        <thead>
                                            <tr>
                                                <th>م</th>
                                                <th>رقم الصنف</th>
                                                <th>وصف الصنف</th>
                                                <th>الكمية</th>
                                                <th>الوحدة</th>
                                                <th>سعر الوحدة</th>
                                                <th>السعر الإجمالي</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var item in Model.Items)
                                            {
                                                <tr data-line-sn="@item.LineSN">
                                                    <td>@item.LineSN</td>
                                                    <td>@item.ItemNo</td>
                                                    <td>@item.ItemDescription</td>
                                                    <td>@item.TrxQTY.ToString("N2")</td>
                                                    <td>@item.UofM</td>
                                                    <td>@item.UnitPrice.ToString("N2")</td>
                                                    <td>@item.LineTotal.ToString("N2")</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-warning edit-item">تعديل</button>
                                                        <button type="button" class="btn btn-sm btn-danger delete-item">حذف</button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Add Item Form -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6>إضافة صنف جديد</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <label for="ItemNo" class="form-label">رقم الصنف</label>
                                                <input type="number" class="form-control" id="ItemNo" name="ItemNo" />
                                            </div>
                                            <div class="col-md-3">
                                                <label for="ItemDescription" class="form-label">وصف الصنف</label>
                                                <input type="text" class="form-control" id="ItemDescription" name="ItemDescription" readonly />
                                            </div>
                                            <div class="col-md-1">
                                                <label for="TrxQTY" class="form-label">الكمية</label>
                                                <input type="number" class="form-control" id="TrxQTY" name="TrxQTY" step="0.01" />
                                            </div>
                                            <div class="col-md-1">
                                                <label for="UofM" class="form-label">الوحدة</label>
                                                <select class="form-control" id="UofM" name="UofM">
                                                    <option value="">اختر الوحدة</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label for="UnitPrice" class="form-label">سعر الوحدة</label>
                                                <input type="number" class="form-control" id="UnitPrice" name="UnitPrice" step="0.01" @(!Model.UserAuthorization.IsAdmin && !Model.UserAuthorization.ChangeInvoicePrice ? "readonly" : "") />
                                            </div>
                                            <div class="col-md-2">
                                                <label for="LineTotal" class="form-label">السعر الإجمالي</label>
                                                <input type="number" class="form-control" id="LineTotal" name="LineTotal" step="0.01" readonly />
                                            </div>
                                            <div class="col-md-1">
                                                <label>&nbsp;</label>
                                                <button type="button" class="btn btn-primary form-control" id="addItemBtn">إضافة</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Invoice Totals -->
                        <div class="row mb-3">
                            <div class="col-md-6 offset-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6>إجماليات الفاتورة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">عدد الأصناف</label>
                                                <input type="text" class="form-control" id="ItemCount" value="@Model.Items.Count" readonly />
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">إجمالي الكمية</label>
                                                <input type="text" class="form-control" id="TotalQTY" value="@Model.Items.Sum(i => i.TrxQTY).ToString("N2")" readonly />
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-6">
                                                <label class="form-label">إجمالي المبيعات</label>
                                                <input type="text" class="form-control" id="Total" value="@((Model.TrxTotal ?? 0).ToString("N2"))" readonly />
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">الخصم %</label>
                                                <input type="number" class="form-control" id="Discount" name="TrxDiscount" value="@((Model.TrxDiscount ?? 0).ToString("N2"))" step="0.01" max="@Model.UserAuthorization.MaxDiscountPercent" />
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-6">
                                                <label class="form-label">مبلغ الخصم</label>
                                                <input type="text" class="form-control" id="DiscountAmount" value="@((Model.TrxDiscountValue ?? 0).ToString("N2"))" readonly />
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">ضريبة القيمة المضافة</label>
                                                <input type="text" class="form-control" id="VATAmount" value="@((Model.TrxVAT ?? 0).ToString("N2"))" readonly />
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-12">
                                                <label class="form-label">صافي المبلغ</label>
                                                <input type="text" class="form-control" id="NetAmount" value="@((Model.TrxNetAmount ?? 0).ToString("N2"))" readonly />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <button type="button" class="btn btn-success" id="saveInvoiceBtn">حفظ الفاتورة</button>
                                <button type="button" class="btn btn-secondary" id="clearBtn">مسح</button>
                                <a href="@Url.Action("Index")" class="btn btn-info">العودة</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Item Search Modal -->
<div class="modal fade" id="itemSearchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">البحث عن الأصناف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="input-group mb-3">
                    <input type="text" class="form-control" id="itemSearchInput" placeholder="ابحث عن الصنف...">
                    <button class="btn btn-outline-secondary" type="button" id="searchItemsBtn">بحث</button>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped" id="itemSearchTable">
                        <thead>
                            <tr>
                                <th>رقم الصنف</th>
                                <th>الوصف</th>
                                <th>سعر الشراء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            let currentInvoiceNo = @Model.TrxNo;
            let currentLineSN = @(Model.Items.Count > 0 ? Model.Items.Max(i => i.LineSN) + 1 : 1);

            // Partner selection change
            $('#PartnerNo').change(function () {
                var selectedOption = $(this).find('option:selected');
                var partnerName = selectedOption.text().split(' - ')[0];
                $('#PartnerName').val(partnerName);
            });

            // Payment method change
            $('#PaymentMethod').change(function () {
                if ($(this).val() === 'آجل') {
                    $('#Cashier').prop('disabled', true);
                } else {
                    $('#Cashier').prop('disabled', false);
                }
            });

            // Item number input
            $('#ItemNo').on('keypress', function (e) {
                if (e.which === 13) {
                    loadItemDetails();
                }
            });

            $('#ItemNo').on('blur', function () {
                loadItemDetails();
            });

            // Quantity and unit price change
            $('#TrxQTY, #UnitPrice').on('input', function () {
                calculateLineTotal();
            });

            // Unit change
            $('#UofM').change(function () {
                updateUnitPrice();
            });

            // Discount change
            $('#Discount').on('input', function () {
                calculateTotals();
            });

            // VAT included change
            $('#VATIncluded').change(function () {
                calculateTotals();
            });

            // Add item button
            $('#addItemBtn').click(function () {
                addItem();
            });

            // Save invoice button
            $('#saveInvoiceBtn').click(function () {
                saveInvoice();
            });

            // Clear button
            $('#clearBtn').click(function () {
                clearForm();
            });

            // Search items button
            $('#searchItemsBtn').click(function () {
                searchItems();
            });

            // Edit item
            $(document).on('click', '.edit-item', function () {
                editItem($(this));
            });

            // Delete item
            $(document).on('click', '.delete-item', function () {
                deleteItem($(this));
            });

            function loadItemDetails() {
                var itemNo = $('#ItemNo').val();
                if (!itemNo) return;

                $.get('/Purchase/GetItemDetails', { itemNo: itemNo })
                    .done(function (data) {
                        if (data.item) {
                            $('#ItemDescription').val(data.item.itemDescription);
                            $('#UnitPrice').val(data.purchasePrice);
                            
                            // Load units
                            $('#UofM').empty().append('<option value="">اختر الوحدة</option>');
                            data.units.forEach(function (unit) {
                                $('#UofM').append(`<option value="${unit.unitName}" data-conversion="${unit.conversionFactor}">${unit.unitName}</option>`);
                            });
                            
                            calculateLineTotal();
                        } else {
                            // Show item search modal
                            $('#itemSearchModal').modal('show');
                            searchItems();
                        }
                    })
                    .fail(function () {
                        alert('حدث خطأ أثناء تحميل بيانات الصنف');
                    });
            }

            function updateUnitPrice() {
                var selectedUnit = $('#UofM option:selected');
                var conversionFactor = selectedUnit.data('conversion');
                var basePrice = parseFloat($('#UnitPrice').val()) || 0;
                
                if (conversionFactor) {
                    $('#UnitPrice').val((basePrice * conversionFactor).toFixed(2));
                    calculateLineTotal();
                }
            }

            function calculateLineTotal() {
                var qty = parseFloat($('#TrxQTY').val()) || 0;
                var price = parseFloat($('#UnitPrice').val()) || 0;
                var total = qty * price;
                $('#LineTotal').val(total.toFixed(2));
            }

            function calculateTotals() {
                var total = 0;
                $('#itemsTable tbody tr').each(function () {
                    var lineTotal = parseFloat($(this).find('td:eq(6)').text().replace(',', '')) || 0;
                    total += lineTotal;
                });

                var discount = parseFloat($('#Discount').val()) || 0;
                var discountAmount = (total * discount / 100);
                var netAmount = total - discountAmount;
                var vatAmount = 0;

                if ($('#VATIncluded').is(':checked')) {
                    vatAmount = (netAmount * 15 / 115);
                } else {
                    vatAmount = (netAmount * 15 / 100);
                }

                $('#Total').val(total.toFixed(2));
                $('#DiscountAmount').val(discountAmount.toFixed(2));
                $('#VATAmount').val(vatAmount.toFixed(2));
                $('#NetAmount').val(netAmount.toFixed(2));
                $('#ItemCount').val($('#itemsTable tbody tr').length);
                
                var totalQty = 0;
                $('#itemsTable tbody tr').each(function () {
                    totalQty += parseFloat($(this).find('td:eq(3)').text().replace(',', '')) || 0;
                });
                $('#TotalQTY').val(totalQty.toFixed(2));
            }

            function addItem() {
                var itemNo = $('#ItemNo').val();
                var qty = $('#TrxQTY').val();
                var unitPrice = $('#UnitPrice').val();
                var uofM = $('#UofM').val();
                var conversionFactor = $('#UofM option:selected').data('conversion') || 1;

                if (!itemNo || !qty || !unitPrice || !uofM) {
                    alert('يرجى ملء جميع البيانات المطلوبة');
                    return;
                }

                $.post('/Purchase/AddItem', {
                    invoiceNo: currentInvoiceNo,
                    itemNo: itemNo,
                    quantity: qty,
                    unitPrice: unitPrice,
                    uofM: uofM,
                    uofMConversion: conversionFactor
                })
                .done(function (response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                })
                .fail(function () {
                    alert('حدث خطأ أثناء إضافة الصنف');
                });
            }

            function editItem(button) {
                var row = button.closest('tr');
                var lineSN = row.data('line-sn');
                
                // Populate form with item data
                $('#ItemNo').val(row.find('td:eq(1)').text());
                $('#TrxQTY').val(row.find('td:eq(3)').text().replace(',', ''));
                $('#UnitPrice').val(row.find('td:eq(5)').text().replace(',', ''));
                
                // Load item details to get units
                loadItemDetails();
                
                // Set the unit
                setTimeout(function () {
                    $('#UofM').val(row.find('td:eq(4)').text());
                }, 500);
                
                // Change add button to update
                $('#addItemBtn').text('تحديث').off('click').click(function () {
                    updateItem(lineSN);
                });
            }

            function updateItem(lineSN) {
                var itemNo = $('#ItemNo').val();
                var qty = $('#TrxQTY').val();
                var unitPrice = $('#UnitPrice').val();
                var uofM = $('#UofM').val();
                var conversionFactor = $('#UofM option:selected').data('conversion') || 1;

                $.post('/Purchase/UpdateItem', {
                    invoiceNo: currentInvoiceNo,
                    lineSN: lineSN,
                    quantity: qty,
                    unitPrice: unitPrice,
                    uofM: uofM,
                    uofMConversion: conversionFactor
                })
                .done(function (response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                })
                .fail(function () {
                    alert('حدث خطأ أثناء تحديث الصنف');
                });
            }

            function deleteItem(button) {
                if (!confirm('هل أنت متأكد من حذف هذا الصنف؟')) return;

                var row = button.closest('tr');
                var lineSN = row.data('line-sn');

                $.post('/Purchase/DeleteItem', {
                    invoiceNo: currentInvoiceNo,
                    lineSN: lineSN
                })
                .done(function (response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                })
                .fail(function () {
                    alert('حدث خطأ أثناء حذف الصنف');
                });
            }

            function searchItems() {
                var searchTerm = $('#itemSearchInput').val();
                
                $.get('/Purchase/SearchItems', { term: searchTerm })
                    .done(function (items) {
                        var tbody = $('#itemSearchTable tbody');
                        tbody.empty();
                        
                        items.forEach(function (item) {
                            tbody.append(`
                                <tr>
                                    <td>${item.itemNo}</td>
                                    <td>${item.itemDescription || ''}</td>
                                    <td>${item.unitPurchasePrice || 0}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary select-item" data-item-no="${item.itemNo}">اختيار</button>
                                    </td>
                                </tr>
                            `);
                        });
                    })
                    .fail(function () {
                        alert('حدث خطأ أثناء البحث عن الأصناف');
                    });
            }

            // Select item from search results
            $(document).on('click', '.select-item', function () {
                var itemNo = $(this).data('item-no');
                $('#ItemNo').val(itemNo);
                $('#itemSearchModal').modal('hide');
                loadItemDetails();
            });

            function saveInvoice() {
                var formData = {
                    TrxNo: currentInvoiceNo,
                    TrxDate: $('#TrxDate').val(),
                    PartnerNo: $('#PartnerNo').val(),
                    PartnerName: $('#PartnerName').val(),
                    PaymentMethod: $('#PaymentMethod').val(),
                    Cashier: $('#Cashier').val(),
                    Store: $('#Store').val(),
                    PartnerReference: $('#PartnerReference').val(),
                    ReferenceInvoice: $('#ReferenceInvoice').val(),
                    TrxNote: $('#TrxNote').val(),
                    TrxDiscount: parseFloat($('#Discount').val()) || 0,
                    VATIncluded: $('#VATIncluded').is(':checked'),
                    Items: []
                };

                // Add items
                $('#itemsTable tbody tr').each(function () {
                    formData.Items.push({
                        ItemNo: parseInt($(this).find('td:eq(1)').text()),
                        TrxQTY: parseFloat($(this).find('td:eq(3)').text().replace(',', '')),
                        UofM: $(this).find('td:eq(4)').text(),
                        UnitPrice: parseFloat($(this).find('td:eq(5)').text().replace(',', '')),
                        UofMConversion: 1
                    });
                });

                $.ajax({
                    url: '/Purchase/SaveAndCompleteInvoice',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function (response) {
                        if (response.success) {
                            alert(response.message);

                            // Clear the form and prepare for next invoice
                            clearFormAndCreateNew(response.newInvoiceNo);
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function () {
                        alert('حدث خطأ أثناء حفظ الفاتورة');
                    }
                });
            }

            function clearForm() {
                if (confirm('هل أنت متأكد من مسح البيانات؟')) {
                    $('#purchaseInvoiceForm')[0].reset();
                    $('#itemsTable tbody').empty();
                    calculateTotals();
                }
            }

            function clearFormAndCreateNew(newInvoiceNo) {
                // Update the current invoice number
                currentInvoiceNo = newInvoiceNo;
                currentLineSN = 1;

                // Clear all form fields
                $('#TrxDate').val(new Date().toISOString().split('T')[0]);
                $('#PartnerNo').val('').trigger('change');
                $('#PartnerName').val('');
                $('#PaymentMethod').val('').trigger('change');
                $('#Cashier').val('');
                $('#Store').val('');
                $('#PartnerReference').val('');
                $('#ReferenceInvoice').val('');
                $('#TrxNote').val('');
                $('#Discount').val('0');
                $('#VATIncluded').prop('checked', false);

                // Clear items table
                $('#itemsTable tbody').empty();

                // Clear item input fields
                $('#ItemNo').val('');
                $('#ItemDescription').val('');
                $('#Quantity').val('1');
                $('#UnitPrice').val('0');
                $('#UofM').val('');

                // Update totals
                updateTotals();

                // Focus on first field
                $('#PartnerNo').focus();

                console.log('Form cleared. New invoice number: ' + newInvoiceNo);
            }

            // Initial calculations
            calculateTotals();
        });
    </script>
} 