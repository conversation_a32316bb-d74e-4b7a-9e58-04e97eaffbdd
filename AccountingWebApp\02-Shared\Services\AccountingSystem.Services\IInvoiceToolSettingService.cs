using AccountingSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public interface IInvoiceToolSettingService
    {
        Task<InvoiceToolSetting> GetSettingsByInvoiceTypeAsync(string invoiceType);
        Task<bool> UpdateSettingsAsync(InvoiceToolSetting settings, string currentUser);
        Task<List<string>> GetInvoiceTypesAsync();
        Task<List<string>> GetPrinterNamesAsync();
    }
} 