-- Add POS-specific columns to tblUsers table
-- This script adds the missing columns that are needed for the new POS user authorization features

USE SULTDB;
GO

-- Check if columns exist before adding them
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tblUsers' AND COLUMN_NAME = 'DefaultStore')
BEGIN
    ALTER TABLE tblUsers ADD DefaultStore NVARCHAR(50) NULL;
    PRINT 'Added DefaultStore column to tblUsers';
END
ELSE
    PRINT 'DefaultStore column already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tblUsers' AND COLUMN_NAME = 'StoreChange')
BEGIN
    ALTER TABLE tblUsers ADD StoreChange BIT NOT NULL DEFAULT 1;
    PRINT 'Added StoreChange column to tblUsers';
END
ELSE
    PRINT 'StoreChange column already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tblUsers' AND COLUMN_NAME = 'DefaultCustomer')
BEGIN
    ALTER TABLE tblUsers ADD DefaultCustomer BIGINT NULL;
    PRINT 'Added DefaultCustomer column to tblUsers';
END
ELSE
    PRINT 'DefaultCustomer column already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tblUsers' AND COLUMN_NAME = 'CustomerChange')
BEGIN
    ALTER TABLE tblUsers ADD CustomerChange BIT NOT NULL DEFAULT 1;
    PRINT 'Added CustomerChange column to tblUsers';
END
ELSE
    PRINT 'CustomerChange column already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tblUsers' AND COLUMN_NAME = 'DefaultCashier')
BEGIN
    ALTER TABLE tblUsers ADD DefaultCashier NVARCHAR(50) NULL;
    PRINT 'Added DefaultCashier column to tblUsers';
END
ELSE
    PRINT 'DefaultCashier column already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tblUsers' AND COLUMN_NAME = 'CashierChange')
BEGIN
    ALTER TABLE tblUsers ADD CashierChange BIT NOT NULL DEFAULT 1;
    PRINT 'Added CashierChange column to tblUsers';
END
ELSE
    PRINT 'CashierChange column already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tblUsers' AND COLUMN_NAME = 'ChangeInvoicePrice')
BEGIN
    ALTER TABLE tblUsers ADD ChangeInvoicePrice BIT NOT NULL DEFAULT 0;
    PRINT 'Added ChangeInvoicePrice column to tblUsers';
END
ELSE
    PRINT 'ChangeInvoicePrice column already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tblUsers' AND COLUMN_NAME = 'MaxDiscountPercent')
BEGIN
    ALTER TABLE tblUsers ADD MaxDiscountPercent DECIMAL(5,2) NULL;
    PRINT 'Added MaxDiscountPercent column to tblUsers';
END
ELSE
    PRINT 'MaxDiscountPercent column already exists';

-- Update admin user to have full permissions
UPDATE tblUsers 
SET 
    StoreChange = 1,
    CustomerChange = 1,
    CashierChange = 1,
    ChangeInvoicePrice = 1,
    MaxDiscountPercent = 99.99
WHERE Username = 'admin';

PRINT 'Updated admin user with full POS permissions';

-- Show the updated table structure
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'tblUsers' 
ORDER BY ORDINAL_POSITION;

PRINT 'Script completed successfully!'; 