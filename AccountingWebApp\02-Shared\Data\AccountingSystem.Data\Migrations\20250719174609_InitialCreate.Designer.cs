﻿// <auto-generated />
using System;
using AccountingSystem.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AccountingSystem.Data.Migrations
{
    [DbContext(typeof(AccountingDbContext))]
    [Migration("20250719174609_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("AccountingSystem.Models.BarcodeSettings", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Barcode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Barcode");

                    b.Property<string>("BarcodeType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("BarcodeType");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedOn");

                    b.Property<int?>("CurrencyDivisor")
                        .HasColumnType("int")
                        .HasColumnName("CurrencyDivisor");

                    b.Property<string>("EmbeddedFormat")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("EmbeddedFormat");

                    b.Property<bool>("EnableEmbeddedWeight")
                        .HasColumnType("bit")
                        .HasColumnName("EnableEmbeddedWeight");

                    b.Property<string>("FixedCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("FixedCode");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedOn");

                    b.Property<string>("Notes")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Notes");

                    b.Property<string>("Shop")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Shop");

                    b.Property<string>("Weight")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Weight");

                    b.Property<int?>("WeightDivisor")
                        .HasColumnType("int")
                        .HasColumnName("WeightDivisor");

                    b.HasKey("ID");

                    b.ToTable("tblBarcodeSettings", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.ChartOfAccount", b =>
                {
                    b.Property<string>("AccountCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("AccountCode");

                    b.Property<int>("AccountLevel")
                        .HasColumnType("int")
                        .HasColumnName("AccountLevel");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("AccountName");

                    b.Property<string>("AccountNature")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("AccountNature");

                    b.Property<string>("AccountType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("AccountType");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedOn");

                    b.Property<bool>("IsPosting")
                        .HasColumnType("bit")
                        .HasColumnName("IsPosting");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedOn");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Notes");

                    b.Property<decimal>("OpeningBalance")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("OpeningBalance");

                    b.Property<string>("ParentAccountCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ParentAccountCode");

                    b.Property<string>("SegmentCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("SegmentCode");

                    b.HasKey("AccountCode");

                    b.ToTable("tbl_Acc_Accounts", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.Customer", b =>
                {
                    b.Property<long>("CustomerNo")
                        .HasColumnType("bigint")
                        .HasColumnName("CustomerNo");

                    b.Property<int?>("AdditionalNo")
                        .HasColumnType("int")
                        .HasColumnName("AdditionalNo");

                    b.Property<int?>("BuildingNo")
                        .HasColumnType("int")
                        .HasColumnName("BuildingNo");

                    b.Property<string>("CR")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("CR");

                    b.Property<string>("City")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("City");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("ContactPerson");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedOn");

                    b.Property<double?>("CreditLimit")
                        .HasColumnType("float")
                        .HasColumnName("CreditLimit");

                    b.Property<string>("CustomerName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("CustomerName");

                    b.Property<string>("District")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("District");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Email");

                    b.Property<int?>("EmployeeNo")
                        .HasColumnType("int")
                        .HasColumnName("EmployeeNo");

                    b.Property<string>("FirstName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("FirstName");

                    b.Property<string>("LastName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("LastName");

                    b.Property<string>("LocalCustomer")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("LocalCustomer");

                    b.Property<int?>("Mobile")
                        .HasColumnType("int")
                        .HasColumnName("Mobile");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedOn");

                    b.Property<string>("Notes")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Notes");

                    b.Property<int?>("OldCode")
                        .HasColumnType("int")
                        .HasColumnName("OldCode");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PaymentMethod");

                    b.Property<string>("PaymentTerm")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("PaymentTerm");

                    b.Property<int?>("Phone")
                        .HasColumnType("int")
                        .HasColumnName("Phone");

                    b.Property<int?>("PostalCode")
                        .HasColumnType("int")
                        .HasColumnName("PostalCode");

                    b.Property<string>("Region")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Region");

                    b.Property<string>("Shop")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Shop");

                    b.Property<string>("Status")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Status");

                    b.Property<string>("StreetAddress1")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("StreetAddress1");

                    b.Property<string>("StreetAddress2")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("StreetAddress2");

                    b.Property<string>("VATRegNo")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("VATRegNo");

                    b.HasKey("CustomerNo");

                    b.HasIndex("CustomerNo")
                        .IsUnique();

                    b.HasIndex("EmployeeNo");

                    b.ToTable("tblCustomers", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.Employee", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("EmployeeNo");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedOn");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedOn");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Emp_Name");

                    b.Property<decimal?>("Salary")
                        .HasColumnType("money")
                        .HasColumnName("Emp_Salary");

                    b.HasKey("Id");

                    b.ToTable("tblEmployees");
                });

            modelBuilder.Entity("AccountingSystem.Models.FormPermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("FormID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FormName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("FormName");

                    b.Property<string>("FormTitle")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("FormTitle");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModuleName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ModuleName");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.ToTable("tblForms", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.GLConfig", b =>
                {
                    b.Property<string>("EntryReferenceModule")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("EntryReferenceModule");

                    b.Property<int>("AccountNo")
                        .HasColumnType("int")
                        .HasColumnName("AccountNo");

                    b.HasKey("EntryReferenceModule");

                    b.ToTable("tblGLConfig", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.GroupFormPermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PermissionID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("CanAdd")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("CanAdd");

                    b.Property<bool>("CanDelete")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("CanDelete");

                    b.Property<bool>("CanEdit")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("CanEdit");

                    b.Property<bool>("CanPrint")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("CanPrint");

                    b.Property<bool>("CanView")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("CanView");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("FormID")
                        .HasColumnType("int")
                        .HasColumnName("FormID");

                    b.Property<int>("GroupID")
                        .HasColumnType("int")
                        .HasColumnName("GroupID");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("FormID");

                    b.HasIndex("GroupID");

                    b.ToTable("tblGroupFormPermissions", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.GroupPermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PermissionID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("CanAdd")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("CanAdd");

                    b.Property<bool>("CanDelete")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("CanDelete");

                    b.Property<bool>("CanEdit")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("CanEdit");

                    b.Property<bool>("CanPrint")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("CanPrint");

                    b.Property<bool>("CanView")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("CanView");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<int>("GroupID")
                        .HasColumnType("int")
                        .HasColumnName("GroupID");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModuleName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModuleName");

                    b.Property<string>("PermissionName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("PermissionName");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("GroupID");

                    b.ToTable("tblGroupPermissions", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.InvoiceToolSetting", b =>
                {
                    b.Property<string>("InvoiceType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("InvoiceType");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedOn");

                    b.Property<int?>("DefAccountNo")
                        .HasColumnType("int")
                        .HasColumnName("DefAccountNo");

                    b.Property<int?>("DefCashier")
                        .HasColumnType("int")
                        .HasColumnName("DefCashier");

                    b.Property<string>("DefNonVATInvoice")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DefNonVATInvoice");

                    b.Property<string>("DefPaymentType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DefPaymentType");

                    b.Property<string>("DefPriceIncludeVAT")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DefPriceIncludeVAT");

                    b.Property<string>("DefStores")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DefStores");

                    b.Property<int?>("DiscountAccountNo")
                        .HasColumnType("int")
                        .HasColumnName("DiscountAccountNo");

                    b.Property<string>("InvoicePrinter")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("InvoicePrinter");

                    b.Property<string>("MandatoryVendorVATReg")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("MandatoryVendorVATReg");

                    b.Property<int?>("MaterialAccountNo")
                        .HasColumnType("int")
                        .HasColumnName("MaterialAccountNo");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedOn");

                    b.Property<string>("NonVatInvoiceChangeable")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NonVatInvoiceChangeable");

                    b.Property<int?>("PrintOption")
                        .HasColumnType("int")
                        .HasColumnName("PrintOption");

                    b.Property<string>("ReferenceMandatory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ReferenceMandatory");

                    b.Property<string>("VATIncludedChangeable")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("VATIncludedChangeable");

                    b.HasKey("InvoiceType");

                    b.ToTable("tblToolsInvoice");
                });

            modelBuilder.Entity("AccountingSystem.Models.Item", b =>
                {
                    b.Property<long>("SN")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SN");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("SN"));

                    b.Property<string>("AUofM")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("AUofM");

                    b.Property<string>("AUofM2")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("AUofM2");

                    b.Property<decimal?>("AUofM2_Price")
                        .HasColumnType("decimal(10, 2)")
                        .HasColumnName("AUofM2_Price");

                    b.Property<string>("AUofM3")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("AUofM3");

                    b.Property<decimal?>("AUofM3_Price")
                        .HasColumnType("decimal(10, 2)")
                        .HasColumnName("AUofM3_Price");

                    b.Property<decimal?>("AUofMX")
                        .HasColumnType("decimal(10, 4)")
                        .HasColumnName("AUofMX");

                    b.Property<decimal?>("AUofMX2")
                        .HasColumnType("decimal(10, 4)")
                        .HasColumnName("AUofMX2");

                    b.Property<decimal?>("AUofMX3")
                        .HasColumnType("decimal(10, 4)")
                        .HasColumnName("AUofMX3");

                    b.Property<decimal?>("AUofM_Price")
                        .HasColumnType("decimal(10, 2)")
                        .HasColumnName("AUofM_Price");

                    b.Property<string>("Barcode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Barcode");

                    b.Property<string>("Brand")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Brand");

                    b.Property<int?>("CategoryId")
                        .HasColumnType("int")
                        .HasColumnName("ItemCategory");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedOn");

                    b.Property<byte?>("EnableSN")
                        .HasColumnType("tinyint")
                        .HasColumnName("SNEnable");

                    b.Property<string>("ItemDescription")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ItemDescription");

                    b.Property<string>("ItemDescription2")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ItemDescription2");

                    b.Property<long?>("ItemNo")
                        .HasColumnType("bigint")
                        .HasColumnName("ItemNo");

                    b.Property<string>("ItemType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ItemType");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedOn");

                    b.Property<byte?>("NegativeEnable")
                        .HasColumnType("tinyint")
                        .HasColumnName("NegativeStock");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<string>("OldCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("OldCode");

                    b.Property<byte[]>("Photo")
                        .HasColumnType("varbinary(max)")
                        .HasColumnName("Item_Photo");

                    b.Property<string>("SalesUofM")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("SalesUofM");

                    b.Property<string>("Shop")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Shop");

                    b.Property<byte?>("Status")
                        .HasColumnType("tinyint")
                        .HasColumnName("Status");

                    b.Property<decimal?>("Tax_Percent")
                        .HasColumnType("decimal(5, 2)")
                        .HasColumnName("Tax_Percent");

                    b.Property<decimal?>("UnitAveragePrice")
                        .HasColumnType("decimal(10, 0)")
                        .HasColumnName("UnitAveragePrice");

                    b.Property<decimal?>("UnitPurchasePrice")
                        .HasColumnType("decimal(10, 2)")
                        .HasColumnName("UnitPurchasePrice");

                    b.Property<decimal?>("UnitSalesPrice")
                        .HasColumnType("decimal(10, 2)")
                        .HasColumnName("UnitSalesPrice");

                    b.Property<string>("UofM")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("UofM");

                    b.HasKey("SN");

                    b.ToTable("tblItems", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.ItemsCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("RootID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedOn");

                    b.Property<int?>("Level")
                        .HasColumnType("int")
                        .HasColumnName("RootLevel");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedOn");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("RootName");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("ParentID");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("tblItemsCategory");
                });

            modelBuilder.Entity("AccountingSystem.Models.POSDevice", b =>
                {
                    b.Property<int>("DeviceID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("DeviceID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DeviceID"));

                    b.Property<string>("DeviceName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("DeviceName");

                    b.HasKey("DeviceID");

                    b.ToTable("tblPOSDevices", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.POSInvoice", b =>
                {
                    b.Property<int>("TrxNo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("TrxNo");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TrxNo"));

                    b.Property<string>("Cashier")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Cashier");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedOn");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedOn");

                    b.Property<string>("PartnerName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("PartnerName");

                    b.Property<int?>("PartnerNo")
                        .HasColumnType("int")
                        .HasColumnName("PartnerNo");

                    b.Property<string>("PartnerPhoneNo")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("PartnerPhoneNo");

                    b.Property<string>("PartnerReference")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("PartnerReference");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PaymentMethod");

                    b.Property<byte[]>("QRCodeImage")
                        .HasColumnType("varbinary(max)")
                        .HasColumnName("QRCodeImage");

                    b.Property<string>("ReadyForUse")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("ReadyForUse");

                    b.Property<string>("Store")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Store");

                    b.Property<DateTime>("TrxDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("TrxDate");

                    b.Property<decimal>("TrxDiscount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("TrxDiscount");

                    b.Property<decimal>("TrxDiscountValue")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("TrxDiscountValue");

                    b.Property<decimal>("TrxNetAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("TrxNetAmount");

                    b.Property<string>("TrxNote")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("TrxNote");

                    b.Property<decimal>("TrxTotal")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("TrxTotal");

                    b.Property<string>("TrxType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("TrxType");

                    b.Property<decimal>("TrxVAT")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("TrxVAT");

                    b.Property<bool>("VOIDSTTS")
                        .HasColumnType("bit")
                        .HasColumnName("VOIDSTTS");

                    b.HasKey("TrxNo");

                    b.ToTable("tblStockMovHeader", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.POSInvoiceItem", b =>
                {
                    b.Property<int>("DocNo")
                        .HasColumnType("int")
                        .HasColumnName("DocNo");

                    b.Property<int>("LineSN")
                        .HasColumnType("int")
                        .HasColumnName("LineSN");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedOn");

                    b.Property<int>("ItemNo")
                        .HasColumnType("int")
                        .HasColumnName("ItemNo");

                    b.Property<decimal>("LineAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("LineAmount");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedOn");

                    b.Property<string>("Store")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Store");

                    b.Property<DateTime>("TrxDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("TrxDate");

                    b.Property<decimal>("TrxQTY")
                        .HasColumnType("decimal(18,3)")
                        .HasColumnName("TrxQTY");

                    b.Property<string>("TrxType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("TrxType");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("UnitPrice");

                    b.Property<string>("UofM")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("UofM");

                    b.Property<decimal>("UofMConversion")
                        .HasColumnType("decimal(18,3)")
                        .HasColumnName("UofMConversion");

                    b.Property<decimal>("VATAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("VATAmount");

                    b.HasKey("DocNo", "LineSN");

                    b.ToTable("tblStockMovement", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.POSPayment", b =>
                {
                    b.Property<int>("TrxNo")
                        .HasColumnType("int")
                        .HasColumnName("TrxNo");

                    b.Property<int>("Pay_mthd")
                        .HasColumnType("int")
                        .HasColumnName("Pay_mthd");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedOn");

                    b.Property<decimal>("Pay_amnt")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Pay_amnt");

                    b.HasKey("TrxNo", "Pay_mthd");

                    b.ToTable("tblPayMethodTrx", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.POSSession", b =>
                {
                    b.Property<int>("SessionID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SessionID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SessionID"));

                    b.Property<DateTime?>("CloseTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CloseTime");

                    b.Property<string>("ClosedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ClosedBy");

                    b.Property<decimal?>("ClosingCash")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("ClosingCash");

                    b.Property<int?>("DeviceID")
                        .HasColumnType("int")
                        .HasColumnName("DeviceID");

                    b.Property<string>("Note")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Note");

                    b.Property<DateTime?>("OpenTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("OpenTime");

                    b.Property<string>("OpenedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("OpenedBy");

                    b.Property<string>("SessionSN")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("SessionSN");

                    b.Property<int?>("ShiftID")
                        .HasColumnType("int")
                        .HasColumnName("ShiftID");

                    b.Property<int?>("ShopID")
                        .HasColumnType("int")
                        .HasColumnName("ShopID");

                    b.Property<string>("Status")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Status");

                    b.HasKey("SessionID");

                    b.HasIndex("DeviceID");

                    b.HasIndex("ShiftID");

                    b.HasIndex("ShopID");

                    b.ToTable("tblPOSSessions", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.POSSessionDetail", b =>
                {
                    b.Property<int>("DetailID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DetailID"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("SessionID")
                        .HasColumnType("int");

                    b.HasKey("DetailID");

                    b.HasIndex("SessionID");

                    b.ToTable("POSSessionDetails");
                });

            modelBuilder.Entity("AccountingSystem.Models.POSShift", b =>
                {
                    b.Property<int>("ShiftID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ShiftID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ShiftID"));

                    b.Property<string>("ShiftName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ShiftName");

                    b.HasKey("ShiftID");

                    b.ToTable("tblPOSShifts", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.SessionLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LogID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FailureReason")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("FailureReason");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)")
                        .HasColumnName("IPAddress");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<string>("LoginStatus")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("LoginStatus");

                    b.Property<DateTime>("LoginTime")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("LoginTime")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<DateTime?>("LogoutTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LogoutTime");

                    b.Property<string>("MachineName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("MachineName");

                    b.Property<string>("MachineUser")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("MachineUser");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int>("SessionID")
                        .HasColumnType("int")
                        .HasColumnName("SessionID");

                    b.Property<string>("SessionToken")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("SessionToken");

                    b.Property<string>("SystemUsername")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("SystemUsername");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("UserAgent");

                    b.Property<string>("Username")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Username");

                    b.HasKey("Id");

                    b.HasIndex("Username");

                    b.ToTable("tblSessionLogs", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.Store", b =>
                {
                    b.Property<int>("SN")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("StoreName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Shop_Text");

                    b.HasKey("SN");

                    b.ToTable("tblShops");
                });

            modelBuilder.Entity("AccountingSystem.Models.SystemConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ConfigID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConfigKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ConfigKey");

                    b.Property<string>("ConfigValue")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ConfigValue");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Description");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.HasKey("Id");

                    b.HasIndex("ConfigKey")
                        .IsUnique();

                    b.ToTable("tblConfig", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("UserID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Email");

                    b.Property<int>("FailedLoginAttempts")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0)
                        .HasColumnName("FailedLoginAttempts");

                    b.Property<string>("FullName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("FullName");

                    b.Property<int?>("GroupID")
                        .HasColumnType("int")
                        .HasColumnName("GroupID");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsLocked")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsLocked");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastLoginDate");

                    b.Property<DateTime?>("LastPasswordChange")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastPasswordChange");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Password");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Username");

                    b.HasKey("Id");

                    b.HasIndex("GroupID");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("tblUsers", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.UserGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Description");

                    b.Property<int>("GroupID")
                        .HasColumnType("int")
                        .HasColumnName("GroupID");

                    b.Property<string>("GroupName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("GroupName");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("GroupID")
                        .IsUnique();

                    b.ToTable("tblGroupsAuth", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.UserPOSItem", b =>
                {
                    b.Property<string>("Username")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Username");

                    b.Property<long>("ItemNo")
                        .HasColumnType("bigint")
                        .HasColumnName("ItemNo");

                    b.HasKey("Username", "ItemNo");

                    b.ToTable("tblUserPOSItems", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.UserStore", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("StoreId")
                        .HasColumnType("int")
                        .HasColumnName("StoreId");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Username");

                    b.HasKey("Id");

                    b.ToTable("UserStores");
                });

            modelBuilder.Entity("AccountingSystem.Models.Vendor", b =>
                {
                    b.Property<long>("VendorNo")
                        .HasColumnType("bigint")
                        .HasColumnName("VendorNo");

                    b.Property<string>("CR")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("CR");

                    b.Property<string>("City")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("City");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("ContactPerson");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedOn");

                    b.Property<double?>("CreditLimit")
                        .HasColumnType("float")
                        .HasColumnName("CreditLimit");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Email");

                    b.Property<string>("FirstName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("FirstName");

                    b.Property<string>("LastName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("LastName");

                    b.Property<string>("LocalVendor")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("LocalVendor");

                    b.Property<int?>("Mobile")
                        .HasColumnType("int")
                        .HasColumnName("Mobile");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedOn");

                    b.Property<string>("Notes")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Notes");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PaymentMethod");

                    b.Property<string>("PaymentTerm")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("PaymentTerm");

                    b.Property<int?>("Phone")
                        .HasColumnType("int")
                        .HasColumnName("Phone");

                    b.Property<int?>("PostalCode")
                        .HasColumnType("int")
                        .HasColumnName("PostalCode");

                    b.Property<string>("Region")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Region");

                    b.Property<string>("Shop")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Shop");

                    b.Property<string>("Status")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Status");

                    b.Property<string>("StreetAddress1")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("StreetAddress1");

                    b.Property<string>("StreetAddress2")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("StreetAddress2");

                    b.Property<string>("VATRegNo")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("VATRegNo");

                    b.Property<string>("VendorName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("VendorName");

                    b.HasKey("VendorNo");

                    b.HasIndex("VendorNo")
                        .IsUnique();

                    b.ToTable("tblVendors", (string)null);
                });

            modelBuilder.Entity("AccountingSystem.Models.Customer", b =>
                {
                    b.HasOne("AccountingSystem.Models.Employee", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeNo")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("AccountingSystem.Models.GroupFormPermission", b =>
                {
                    b.HasOne("AccountingSystem.Models.FormPermission", "Form")
                        .WithMany("GroupPermissions")
                        .HasForeignKey("FormID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AccountingSystem.Models.UserGroup", null)
                        .WithMany()
                        .HasForeignKey("GroupID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Form");
                });

            modelBuilder.Entity("AccountingSystem.Models.GroupPermission", b =>
                {
                    b.HasOne("AccountingSystem.Models.UserGroup", "Group")
                        .WithMany("Permissions")
                        .HasForeignKey("GroupID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");
                });

            modelBuilder.Entity("AccountingSystem.Models.ItemsCategory", b =>
                {
                    b.HasOne("AccountingSystem.Models.ItemsCategory", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("AccountingSystem.Models.POSSession", b =>
                {
                    b.HasOne("AccountingSystem.Models.POSDevice", "Device")
                        .WithMany()
                        .HasForeignKey("DeviceID")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("AccountingSystem.Models.POSShift", "Shift")
                        .WithMany()
                        .HasForeignKey("ShiftID")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("AccountingSystem.Models.Store", "Shop")
                        .WithMany()
                        .HasForeignKey("ShopID")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Device");

                    b.Navigation("Shift");

                    b.Navigation("Shop");
                });

            modelBuilder.Entity("AccountingSystem.Models.POSSessionDetail", b =>
                {
                    b.HasOne("AccountingSystem.Models.POSSession", "Session")
                        .WithMany()
                        .HasForeignKey("SessionID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Session");
                });

            modelBuilder.Entity("AccountingSystem.Models.SessionLog", b =>
                {
                    b.HasOne("AccountingSystem.Models.User", "User")
                        .WithMany("SessionLogs")
                        .HasForeignKey("Username")
                        .HasPrincipalKey("Username")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("User");
                });

            modelBuilder.Entity("AccountingSystem.Models.User", b =>
                {
                    b.HasOne("AccountingSystem.Models.UserGroup", "Group")
                        .WithMany("Users")
                        .HasForeignKey("GroupID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Group");
                });

            modelBuilder.Entity("AccountingSystem.Models.FormPermission", b =>
                {
                    b.Navigation("GroupPermissions");
                });

            modelBuilder.Entity("AccountingSystem.Models.ItemsCategory", b =>
                {
                    b.Navigation("Children");
                });

            modelBuilder.Entity("AccountingSystem.Models.User", b =>
                {
                    b.Navigation("SessionLogs");
                });

            modelBuilder.Entity("AccountingSystem.Models.UserGroup", b =>
                {
                    b.Navigation("Permissions");

                    b.Navigation("Users");
                });
#pragma warning restore 612, 618
        }
    }
}
