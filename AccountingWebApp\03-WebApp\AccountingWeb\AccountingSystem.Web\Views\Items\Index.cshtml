@model IEnumerable<AccountingSystem.Models.Item>

@{
    ViewData["Title"] = "الأصناف";
}

<h1>@ViewData["Title"]</h1>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success">
        @TempData["SuccessMessage"]
    </div>
}

<p>
    <a asp-action="Create" class="btn btn-primary">إنشاء صنف جديد</a>
</p>

<form asp-action="Index" method="get">
    <div class="form-actions no-color">
        <p>
            بحث بالاسم أو الرقم: <input type="text" name="SearchTerm" value="@ViewBag.SearchTerm" />
            <input type="submit" value="بحث" class="btn btn-default" /> |
            <a asp-action="Index">عرض الكل</a>
        </p>
    </div>
</form>

<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.ItemNo)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.ItemDescription)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.UofM)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.UnitSalesPrice)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Status)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.ItemNo)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.ItemDescription)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.UofM)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.UnitSalesPrice)
                </td>
                <td>
                    @(item.Status == 1 ? "نشط" : "غير نشط")
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.SN">تعديل</a> |
                    <a asp-action="Details" asp-route-id="@item.SN">تفاصيل</a> |
                    <a asp-action="Delete" asp-route-id="@item.SN">حذف</a>
                </td>
            </tr>
        }
    </tbody>
</table> 