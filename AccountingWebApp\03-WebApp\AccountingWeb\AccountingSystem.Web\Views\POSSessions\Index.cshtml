@model List<AccountingSystem.Services.ViewModels.POSSessionViewModel>
@{
    ViewData["Title"] = "إدارة جلسات نقاط البيع";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-cash-register me-2"></i>
                        إدارة جلسات نقاط البيع
                    </h3>
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        فتح جلسة جديدة
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-filter me-1"></i>
                                        فلترة الجلسات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form asp-action="Filter" method="post" id="filterForm">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">المتجر</label>
                                                    <select name="ShopID" class="form-select">
                                                        <option value="">-- جميع المتاجر --</option>
                                                        @if (ViewBag.Shops != null)
                                                        {
                                                            @foreach (var shop in ViewBag.Shops)
                                                            {
                                                                if (ViewBag.Filter?.ShopID == shop.SN)
                                                                {
                                                                    <option value="@shop.SN" selected>@shop.StoreName</option>
                                                                }
                                                                else
                                                                {
                                                                    <option value="@shop.SN">@shop.StoreName</option>
                                                                }
                                                            }
                                                        }
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">الجهاز</label>
                                                    <select name="DeviceID" class="form-select">
                                                        <option value="">-- جميع الأجهزة --</option>
                                                        @if (ViewBag.Devices != null)
                                                        {
                                                            @foreach (var device in ViewBag.Devices)
                                                            {
                                                                if (ViewBag.Filter?.DeviceID == device.DeviceID)
                                                                {
                                                                    <option value="@device.DeviceID" selected>@device.DeviceName</option>
                                                                }
                                                                else
                                                                {
                                                                    <option value="@device.DeviceID">@device.DeviceName</option>
                                                                }
                                                            }
                                                        }
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">الوردية</label>
                                                    <select name="ShiftID" class="form-select">
                                                        <option value="">-- جميع الورديات --</option>
                                                                                                                @if (ViewBag.Shifts != null)
                                                        {
                                                            @foreach (var shift in ViewBag.Shifts)
                                                            {
                                                                if (ViewBag.Filter?.ShiftID == shift.ShiftID)
                                                                {
                                                                    <option value="@shift.ShiftID" selected>@shift.ShiftName</option>
                                                                }
                                                                else
                                                                {
                                                                    <option value="@shift.ShiftID">@shift.ShiftName</option>
                                                                }
                                                            }
                                                        }
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">الحالة</label>
                                                    <select name="Status" class="form-select">
                                                        @if (ViewBag.Filter?.Status == "All" || ViewBag.Filter?.Status == null)
                                                        {
                                                            <option value="All" selected>جميع الحالات</option>
                                                        }
                                                        else
                                                        {
                                                            <option value="All">جميع الحالات</option>
                                                        }
                                                        @if (ViewBag.Filter?.Status == "Open")
                                                        {
                                                            <option value="Open" selected>مفتوحة</option>
                                                        }
                                                        else
                                                        {
                                                            <option value="Open">مفتوحة</option>
                                                        }
                                                        @if (ViewBag.Filter?.Status == "Closed")
                                                        {
                                                            <option value="Closed" selected>مغلقة</option>
                                                        }
                                                        else
                                                        {
                                                            <option value="Closed">مغلقة</option>
                                                        }
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <button type="submit" class="btn btn-info">
                                                    <i class="fas fa-search me-1"></i>
                                                    تطبيق الفلتر
                                                </button>
                                                <a href="@Url.Action("Index")" class="btn btn-secondary">
                                                    <i class="fas fa-times me-1"></i>
                                                    إلغاء الفلتر
                                                </a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sessions Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="sessionsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الجلسة</th>
                                    <th>المتجر</th>
                                    <th>الجهاز</th>
                                    <th>الوردية</th>
                                    <th>فتح بواسطة</th>
                                    <th>وقت الفتح</th>
                                    <th>إغلاق بواسطة</th>
                                    <th>وقت الإغلاق</th>
                                    <th>المبلغ النهائي</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Any())
                                {
                                    @foreach (var session in Model)
                                    {
                                        <tr>
                                            <td>@session.SessionSN</td>
                                            <td>@session.ShopName</td>
                                            <td>@session.DeviceName</td>
                                            <td>@session.ShiftName</td>
                                            <td>@session.OpenedBy</td>
                                            <td>@(session.OpenTime?.ToString("dd/MM/yyyy HH:mm"))</td>
                                            <td>@session.ClosedBy</td>
                                            <td>@(session.CloseTime?.ToString("dd/MM/yyyy HH:mm"))</td>
                                            <td>@(session.ClosingCash?.ToString("N2"))</td>
                                            <td>
                                                <span class="@session.StatusBadgeClass">@session.Status</span>
                                            </td>
                                            <td>
                                                @if (session.IsOpen)
                                                {
                                                    <a href="@Url.Action("Close", new { id = session.SessionID })" 
                                                       class="btn btn-warning btn-sm" 
                                                       title="إغلاق الجلسة">
                                                        <i class="fas fa-lock"></i>
                                                    </a>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="11" class="text-center text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            لا توجد جلسات لعرضها
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Initialize DataTable
            $('#sessionsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
                },
                order: [[0, 'desc']],
                pageLength: 25,
                responsive: true
            });

            // Auto-submit filter form on select change
            $('select[name="ShopID"], select[name="DeviceID"], select[name="ShiftID"], select[name="Status"]').change(function() {
                $('#filterForm').submit();
            });
        });
    </script>
} 