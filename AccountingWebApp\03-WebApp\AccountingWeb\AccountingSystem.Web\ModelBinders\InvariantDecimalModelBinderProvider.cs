using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace AccountingSystem.Web.ModelBinders
{
    /// <summary>
    /// Supplies the InvariantDecimalModelBinder for decimal and nullable decimal properties.
    /// </summary>
    public sealed class InvariantDecimalModelBinderProvider : IModelBinderProvider
    {
        public IModelBinder? GetBinder(ModelBinderProviderContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }

            var modelType = context.Metadata.ModelType;
            if (modelType == typeof(decimal) || modelType == typeof(decimal?))
            {
                return new InvariantDecimalModelBinder();
            }

            return null;
        }
    }
}

