using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Web.Utilities;
using AccountingSystem.Models;
using AccountingSystem.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic; // Added missing import for List

namespace AccountingSystem.Web.Controllers
{
    [Authorize(Roles = "admin")]
    [Route("Admin/QuickActions")]
    public class AdminQuickActionsController : Controller
    {
        private readonly AccountingDbContext _context;
        public AdminQuickActionsController(AccountingDbContext context)
        {
            _context = context;
        }

        [HttpGet("")]
        public IActionResult Index(string role = null)
        {
            var allItems = SidebarItemRegistry.AllItems;
            var groups = _context.UserGroups.ToList();
            if (string.IsNullOrEmpty(role) && groups.Count > 0)
                role = groups[0].GroupName;
            var enabledRoutes = _context.DashboardQuickActionConfigs
                .Where(q => q.RoleName == role && q.IsEnabled)
                .Select(q => q.SidebarItemRoute)
                .ToHashSet();
            ViewBag.Role = role;
            ViewBag.Groups = groups;
            return View((allItems, enabledRoutes));
        }

        [HttpPost("Save")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Save(string role, List<string> enabledRoutes)
        {
            var configs = _context.DashboardQuickActionConfigs.Where(q => q.RoleName == role).ToList();
            // Disable all first
            foreach (var config in configs)
            {
                config.IsEnabled = enabledRoutes.Contains(config.SidebarItemRoute);
            }
            // Add new enabled routes if not exist
            var existingRoutes = configs.Select(c => c.SidebarItemRoute).ToHashSet();
            foreach (var route in enabledRoutes)
            {
                if (!existingRoutes.Contains(route))
                {
                    _context.DashboardQuickActionConfigs.Add(new DashboardQuickActionConfig
                    {
                        RoleName = role,
                        SidebarItemRoute = route,
                        IsEnabled = true
                    });
                }
            }
            await _context.SaveChangesAsync();
            TempData["SuccessMessage"] = "تم حفظ الإعدادات بنجاح.";
            return RedirectToAction("Index", new { role });
        }
    }
} 