# ALSULTAN Accounting Web Migration – Progress Log

This file records incremental migration progress. Append new entries at the end; do not edit prior entries.

---

## [Session 2025-08-10]
- Summary: Resolved EF Core table-sharing issue for purchase/pos items using TPH; fixed nullable DateTime crashes; updated sidebar to point purchase to real pages; added dashboard deep-link for today's sales; application builds and runs.
- Changes:
  - Introduced `BaseInvoiceItem` and TPH mapping; updated `AccountingDbContext`.
  - Made `TrxDate` nullable on invoices/items; guarded views and queries.
  - Updated sidebar `_Layout.cshtml` and `SidebarItemRegistry.cs` for purchases.
  - Linked dashboard cards to `/Sales/Search?date=today` in `SimpleDashboard` and `Dashboard` views.
- Decisions:
  - Use EF Core TPH with `TrxType` as discriminator.
  - Prefer EF Core over ADO.NET; keep legacy ADO in dashboard temporarily.
- Open Issues:
  - Purchase Return workflow pending.
  - Dashboard totals still use ADO.NET; unify on EF.
  - Search endpoints need projection/paging to avoid N+1.
- Next Actions:
  - Implement Purchase Return flow and wire sidebar.
  - Refactor dashboard totals to EF (create `DashboardService`).
  - Optimize `POSService`/`PurchaseService` search queries.
- Artifacts Changed:
  - `02-Shared/Models/AccountingSystem.Models/BaseInvoiceItem.cs`
  - `02-Shared/Data/AccountingSystem.Data/AccountingDbContext.cs`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Views/Shared/_Layout.cshtml`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Utilities/SidebarItemRegistry.cs`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Views/SimpleDashboard/Index.cshtml`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Views/Dashboard/Index.cshtml`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Views/POS/Receipt.cshtml`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Views/POS/ThermalReceipt.cshtml`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Views/Purchase/Create.cshtml`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Views/Sales/Search.cshtml`

---

## [Session 2025-01-27 15:30]
- Summary: Fixed critical SqlNullValueException issues preventing purchase invoice page from loading; resolved type mismatches between C# models and database schema; addressed nullable decimal properties causing runtime crashes; fixed CS1501 build errors from ToString() calls on nullable decimals.
- Changes:
  - Changed `DefaultCashier` property from `int?` to `string?` in User model and PurchaseUserAuthorization to match database NVARCHAR(50) schema.
  - Made decimal properties nullable (`decimal?`) in BaseInvoice and PurchaseInvoiceViewModel: TrxVAT, TrxTotal, TrxDiscount, TrxDiscountValue, TrxNetAmount.
  - Updated DbContext mapping for DefaultCashier to use HasMaxLength(50) instead of HasColumnType("int").
  - Fixed ToString() calls on nullable decimals using null-coalescing operator (?? 0) in POSService, POSController, and Purchase Create view.
  - Enhanced error handling in PurchaseController.Create() to render Error view instead of redirecting to prevent infinite loops.
  - Updated Error.cshtml view to be Arabic RTL-friendly with detailed error display.
- Decisions:
  - Use nullable decimal types to match database schema allowing NULL values.
  - Handle null decimals with null-coalescing operator before formatting operations.
  - Render Error view directly instead of redirecting to prevent navigation loops.
- Open Issues:
  - Remaining CS1501 errors in other views (Receipt.cshtml, ThermalReceipt.cshtml, Search.cshtml) need similar nullable decimal fixes.
  - Purchase invoice page navigation working but functionality still needs verification.
- Next Actions:
  - Fix remaining CS1501 errors in other views with nullable decimal properties.
  - Rebuild application to verify all compilation errors resolved.
  - Test purchase invoice page navigation and basic functionality.
- Artifacts Changed:
  - `02-Shared/Models/AccountingSystem.Models/BaseEntity.cs`
  - `02-Shared/Models/AccountingSystem.Models/BaseInvoice.cs`
  - `02-Shared/Models/AccountingSystem.Models/PurchaseInvoiceModels.cs`
  - `02-Shared/Data/AccountingSystem.Data/AccountingDbContext.cs`
  - `02-Shared/Services/AccountingSystem.Services/POSService.cs`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Controllers/POSController.cs`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Controllers/PurchaseController.cs`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Views/Purchase/Create.cshtml`
  - `03-WebApp/AccountingWeb/AccountingSystem.Web/Views/Shared/Error.cshtml`
