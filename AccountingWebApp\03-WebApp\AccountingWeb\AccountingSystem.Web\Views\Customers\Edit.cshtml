@model AccountingSystem.Models.Customer

@{
    ViewData["Title"] = "تعديل العميل";
}

<h1>@ViewData["Title"]</h1>

<h4>بيانات العميل</h4>
<hr />

<div class="row">
    <div class="col-md-12">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="CustomerNo" />
            <input type="hidden" asp-for="CreatedBy" />
            <input type="hidden" asp-for="CreatedOn" />
            
            <div class="row">
                <!-- العمود الأول - معلومات العميل الأساسية -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">معلومات العميل الأساسية</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-group mb-3">
                                <label asp-for="CustomerNo" class="control-label"></label>
                                <input asp-for="CustomerNo" class="form-control" />
                                <span asp-validation-for="CustomerNo" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="CustomerName" class="control-label"></label>
                                <input asp-for="CustomerName" class="form-control" />
                                <span asp-validation-for="CustomerName" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="FirstName" class="control-label"></label>
                                <input asp-for="FirstName" class="form-control" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="LastName" class="control-label"></label>
                                <input asp-for="LastName" class="form-control" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="Mobile" class="control-label"></label>
                                <input asp-for="Mobile" class="form-control" />
                                <span asp-validation-for="Mobile" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="Phone" class="control-label"></label>
                                <input asp-for="Phone" class="form-control" />
                                <span asp-validation-for="Phone" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="Email" class="control-label"></label>
                                <input asp-for="Email" class="form-control" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- العمود الثاني - معلومات الدفع والأعمال -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">معلومات الدفع والأعمال</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-group mb-3">
                                <label asp-for="PaymentMethod" class="control-label"></label>
                                <select asp-for="PaymentMethod" class="form-control" asp-items="new SelectList(ViewBag.PaymentMethods)">
                                    <option value="">-- اختر طريقة الدفع --</option>
                                </select>
                                <span asp-validation-for="PaymentMethod" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="CreditLimit" class="control-label"></label>
                                <input asp-for="CreditLimit" class="form-control" />
                                <span asp-validation-for="CreditLimit" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="PaymentTerm" class="control-label"></label>
                                <input asp-for="PaymentTerm" class="form-control" />
                                <span asp-validation-for="PaymentTerm" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="ContactPerson" class="control-label"></label>
                                <input asp-for="ContactPerson" class="form-control" />
                                <span asp-validation-for="ContactPerson" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="CR" class="control-label"></label>
                                <input asp-for="CR" class="form-control" />
                                <span asp-validation-for="CR" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="VATRegNo" class="control-label"></label>
                                <input asp-for="VATRegNo" class="form-control" />
                                <span asp-validation-for="VATRegNo" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="Shop" class="control-label"></label>
                                <select asp-for="Shop" class="form-control" asp-items='new SelectList(ViewBag.Shops, "SN", "StoreName")'>
                                    <option value="">-- اختر المتجر --</option>
                                </select>
                                <span asp-validation-for="Shop" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="Status" class="control-label"></label>
                                <select asp-for="Status" class="form-control" asp-items="new SelectList(ViewBag.StatusOptions)">
                                    <option value="">-- اختر الحالة --</option>
                                </select>
                                <span asp-validation-for="Status" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="LocalCustomer" class="control-label"></label>
                                <select asp-for="LocalCustomer" class="form-control" asp-items="new SelectList(ViewBag.LocalCustomerOptions)">
                                    <option value="">-- اختر النوع --</option>
                                </select>
                                <span asp-validation-for="LocalCustomer" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="EmployeeNo" class="control-label"></label>
                                <select asp-for="EmployeeNo" class="form-control" asp-items='new SelectList(ViewBag.Employees, "EmployeeNo", "EmployeeName")'>
                                    <option value="">-- اختر المندوب --</option>
                                </select>
                                <span asp-validation-for="EmployeeNo" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- العمود الثالث - العنوان والملاحظات -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">العنوان والملاحظات</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-group mb-3">
                                <label asp-for="StreetAddress1" class="control-label"></label>
                                <input asp-for="StreetAddress1" class="form-control" />
                                <span asp-validation-for="StreetAddress1" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="StreetAddress2" class="control-label"></label>
                                <input asp-for="StreetAddress2" class="form-control" />
                                <span asp-validation-for="StreetAddress2" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="City" class="control-label"></label>
                                <input asp-for="City" class="form-control" />
                                <span asp-validation-for="City" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="Region" class="control-label"></label>
                                <input asp-for="Region" class="form-control" />
                                <span asp-validation-for="Region" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="PostalCode" class="control-label"></label>
                                <input asp-for="PostalCode" class="form-control" />
                                <span asp-validation-for="PostalCode" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="BuildingNo" class="control-label"></label>
                                <input asp-for="BuildingNo" class="form-control" />
                                <span asp-validation-for="BuildingNo" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="AdditionalNo" class="control-label"></label>
                                <input asp-for="AdditionalNo" class="form-control" />
                                <span asp-validation-for="AdditionalNo" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="District" class="control-label"></label>
                                <input asp-for="District" class="form-control" />
                                <span asp-validation-for="District" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="Notes" class="control-label"></label>
                                <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                                <span asp-validation-for="Notes" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="OldCode" class="control-label"></label>
                                <input asp-for="OldCode" class="form-control" />
                                <span asp-validation-for="OldCode" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group mt-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ التغييرات
                </button>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة إلى القائمة
                </a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 