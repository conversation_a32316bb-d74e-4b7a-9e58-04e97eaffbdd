using System.ComponentModel.DataAnnotations;

namespace AccountingSystem.Models
{
    public class DashboardQuickActionConfig
    {
        [Key]
        public int Id { get; set; }
        [Required]
        public string RoleName { get; set; } = string.Empty; // e.g., "admin", "cashier"
        [Required]
        public string SidebarItemRoute { get; set; } = string.Empty; // e.g., "/POS/Index"
        public bool IsEnabled { get; set; } = true;
    }
} 