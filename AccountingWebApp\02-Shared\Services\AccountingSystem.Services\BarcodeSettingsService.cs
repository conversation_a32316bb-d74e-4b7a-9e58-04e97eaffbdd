using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public class BarcodeSettingsService : IBarcodeSettingsService
    {
        private readonly AccountingDbContext _context;

        public BarcodeSettingsService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<List<BarcodeSettings>> GetBarcodeSettingsAsync()
        {
            return await _context.BarcodeSettings.ToListAsync();
        }

        public async Task<BarcodeSettings?> GetBarcodeSettingsByIdAsync(int id)
        {
            return await _context.BarcodeSettings.FindAsync(id);
        }

        public async Task<BarcodeSettings?> GetBarcodeSettingsByShopAsync(string shop)
        {
            return await _context.BarcodeSettings.FirstOrDefaultAsync(s => s.Shop == shop);
        }

        public async Task<BarcodeSettings> CreateBarcodeSettingsAsync(BarcodeSettings settings)
        {
            _context.BarcodeSettings.Add(settings);
            await _context.SaveChangesAsync();
            return settings;
        }

        public async Task<BarcodeSettings> UpdateBarcodeSettingsAsync(BarcodeSettings settings)
        {
            _context.Entry(settings).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return settings;
        }

        public async Task DeleteBarcodeSettingsAsync(int id)
        {
            var settings = await _context.BarcodeSettings.FindAsync(id);
            if (settings != null)
            {
                _context.BarcodeSettings.Remove(settings);
                await _context.SaveChangesAsync();
            }
        }
    }
} 