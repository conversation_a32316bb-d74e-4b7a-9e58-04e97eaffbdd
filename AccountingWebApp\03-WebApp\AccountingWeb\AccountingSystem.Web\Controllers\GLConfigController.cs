using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class GLConfigController : Controller
    {
        private readonly IGLConfigService _glConfigService;
        private readonly ILogger<GLConfigController> _logger;

        public GLConfigController(IGLConfigService glConfigService, ILogger<GLConfigController> logger)
        {
            _glConfigService = glConfigService;
            _logger = logger;
        }

        /// <summary>
        /// Display GL Configuration form
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                var configs = await _glConfigService.GetAllGLConfigsAsync();
                var accounts = await _glConfigService.GetAccountsForDropdownAsync();

                var model = new GLConfigIndexViewModel
                {
                    GLConfigs = configs,
                    AvailableAccounts = accounts
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading GL configurations");
                TempData["Error"] = "حدث خطأ أثناء تحميل إعدادات ربط الحسابات";
                return View(new GLConfigIndexViewModel());
            }
        }

        /// <summary>
        /// Save GL Configuration
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Save([FromBody] SaveGLConfigRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = "البيانات المدخلة غير صحيحة" });
                }

                var configs = request.Configs.Select(c => new GLConfigViewModel
                {
                    Id = c.Id,
                    EntryReferenceModule = c.EntryReferenceModule,
                    ModuleDisplayName = c.ModuleDisplayName,
                    AccountNo = c.AccountNo,
                    AccountName = c.AccountName,
                    Description = c.Description,
                    IsActive = c.IsActive
                }).ToList();

                var result = await _glConfigService.SaveAllGLConfigsAsync(configs);

                if (result)
                {
                    _logger.LogInformation("GL configurations saved successfully by user {Username}", User.Identity?.Name);
                    return Json(new { success = true, message = "تم حفظ إعدادات ربط الحسابات بنجاح" });
                }
                else
                {
                    return Json(new { success = false, message = "حدث خطأ أثناء حفظ إعدادات ربط الحسابات" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving GL configurations");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ إعدادات ربط الحسابات" });
            }
        }

        /// <summary>
        /// Get account name by account number
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAccountName(int accountNo)
        {
            try
            {
                var accountName = await _glConfigService.GetAccountNameAsync(accountNo);
                return Json(new { success = true, accountName = accountName });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting account name for account: {AccountNo}", accountNo);
                return Json(new { success = false, message = "حدث خطأ أثناء جلب اسم الحساب" });
            }
        }

        /// <summary>
        /// Get accounts for dropdown
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAccounts()
        {
            try
            {
                var accounts = await _glConfigService.GetAccountsForDropdownAsync();
                return Json(new { success = true, accounts = accounts });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting accounts for dropdown");
                return Json(new { success = false, message = "حدث خطأ أثناء جلب الحسابات" });
            }
        }
    }

    /// <summary>
    /// View model for GL Configuration index page
    /// </summary>
    public class GLConfigIndexViewModel
    {
        public List<GLConfigViewModel> GLConfigs { get; set; } = new();
        public Dictionary<string, string> AvailableAccounts { get; set; } = new();
    }

    /// <summary>
    /// Request model for saving GL configurations
    /// </summary>
    public class SaveGLConfigRequest
    {
        [Required]
        public List<GLConfigRequestModel> Configs { get; set; } = new();
    }

    /// <summary>
    /// GL Configuration request model
    /// </summary>
    public class GLConfigRequestModel
    {
        public int Id { get; set; }
        
        [Required]
        public string EntryReferenceModule { get; set; } = string.Empty;
        
        [Required]
        public string ModuleDisplayName { get; set; } = string.Empty;
        
        [Range(0, int.MaxValue)]
        public int AccountNo { get; set; }
        
        public string AccountName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
    }
} 