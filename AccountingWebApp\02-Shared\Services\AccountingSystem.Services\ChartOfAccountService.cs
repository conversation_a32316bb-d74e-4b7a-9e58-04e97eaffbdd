using Microsoft.EntityFrameworkCore;
using AccountingSystem.Data;
using AccountingSystem.Models;
using AccountingSystem.Services.ViewModels;

namespace AccountingSystem.Services
{
    public interface IChartOfAccountService
    {
        Task<List<ChartOfAccountViewModel>> GetAllAccountsAsync();
        Task<ChartOfAccountViewModel?> GetAccountByCodeAsync(string accountCode);
        Task<ChartOfAccountViewModel?> GetAccountByIdAsync(string accountCode);
        Task<bool> CreateAccountAsync(ChartOfAccountCreateViewModel model, string createdBy);
        Task<bool> UpdateAccountAsync(ChartOfAccountEditViewModel model, string modifiedBy);
        Task<bool> DeleteAccountAsync(string accountCode);
        Task<string> GenerateNextSegmentCodeAsync(string parentCode);
        Task<List<ChartOfAccountViewModel>> GetParentAccountsAsync();
        Task<bool> AccountExistsAsync(string accountCode);
        Task<bool> HasSubAccountsAsync(string accountCode);
    }

    public class ChartOfAccountService : IChartOfAccountService
    {
        private readonly AccountingDbContext _context;

        public ChartOfAccountService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<List<ChartOfAccountViewModel>> GetAllAccountsAsync()
        {
            try
            {
                // Use direct ADO.NET to bypass Entity Framework discriminator issues
                var accounts = new List<ChartOfAccount>();
                var connectionString = _context.Database.GetConnectionString();
                
                using (var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var command = new Microsoft.Data.SqlClient.SqlCommand(
                        "SELECT AccountCode, SegmentCode, AccountName, ParentAccountCode, AccountLevel, IsPosting, AccountType, AccountNature, OpeningBalance, Notes, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn FROM tbl_Acc_Accounts ORDER BY AccountCode", 
                        connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var account = new ChartOfAccount
                                {
                                    AccountCode = reader["AccountCode"].ToString() ?? "",
                                    SegmentCode = reader["SegmentCode"]?.ToString(),
                                    AccountName = reader["AccountName"].ToString() ?? "",
                                    ParentAccountCode = reader["ParentAccountCode"]?.ToString(),
                                    AccountLevel = Convert.ToInt32(reader["AccountLevel"]),
                                    IsPosting = Convert.ToBoolean(reader["IsPosting"]),
                                    AccountType = reader["AccountType"]?.ToString(),
                                    AccountNature = reader["AccountNature"]?.ToString(),
                                    OpeningBalance = Convert.ToDecimal(reader["OpeningBalance"]),
                                    Notes = reader["Notes"]?.ToString(),
                                    CreatedBy = reader["CreatedBy"]?.ToString(),
                                    CreatedOn = reader["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(reader["CreatedOn"]) : null,
                                    ModifiedBy = reader["ModifiedBy"]?.ToString(),
                                    ModifiedOn = reader["ModifiedOn"] != DBNull.Value ? Convert.ToDateTime(reader["ModifiedOn"]) : null
                                };
                                accounts.Add(account);
                            }
                        }
                    }
                }

                return accounts.Select(a => ToViewModel(a)).ToList();
            }
            catch (Exception ex)
            {
                // Log the specific error
                System.Diagnostics.Debug.WriteLine($"ADO.NET query failed: {ex.Message}");
                
                // Fallback to regular Entity Framework query if ADO.NET fails
                try
                {
                    var accounts = await _context.ChartOfAccounts
                        .OrderBy(a => a.AccountCode)
                        .ToListAsync();

                    return accounts.Select(a => ToViewModel(a)).ToList();
                }
                catch (Exception efEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Entity Framework query also failed: {efEx.Message}");
                    throw; // Re-throw the original exception
                }
            }
        }

        public async Task<ChartOfAccountViewModel?> GetAccountByCodeAsync(string accountCode)
        {
            try
            {
                // Use direct ADO.NET to bypass Entity Framework discriminator issues
                var connectionString = _context.Database.GetConnectionString();
                
                using (var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var command = new Microsoft.Data.SqlClient.SqlCommand(
                        "SELECT AccountCode, SegmentCode, AccountName, ParentAccountCode, AccountLevel, IsPosting, AccountType, AccountNature, OpeningBalance, Notes, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn FROM tbl_Acc_Accounts WHERE AccountCode = @AccountCode", 
                        connection))
                    {
                        command.Parameters.AddWithValue("@AccountCode", accountCode);
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                var account = new ChartOfAccount
                                {
                                    AccountCode = reader["AccountCode"].ToString() ?? "",
                                    SegmentCode = reader["SegmentCode"]?.ToString(),
                                    AccountName = reader["AccountName"].ToString() ?? "",
                                    ParentAccountCode = reader["ParentAccountCode"]?.ToString(),
                                    AccountLevel = Convert.ToInt32(reader["AccountLevel"]),
                                    IsPosting = Convert.ToBoolean(reader["IsPosting"]),
                                    AccountType = reader["AccountType"]?.ToString(),
                                    AccountNature = reader["AccountNature"]?.ToString(),
                                    OpeningBalance = Convert.ToDecimal(reader["OpeningBalance"]),
                                    Notes = reader["Notes"]?.ToString(),
                                    CreatedBy = reader["CreatedBy"]?.ToString(),
                                    CreatedOn = reader["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(reader["CreatedOn"]) : null,
                                    ModifiedBy = reader["ModifiedBy"]?.ToString(),
                                    ModifiedOn = reader["ModifiedOn"] != DBNull.Value ? Convert.ToDateTime(reader["ModifiedOn"]) : null
                                };
                                return ToViewModel(account);
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                // Log the specific error
                System.Diagnostics.Debug.WriteLine($"GetAccountByCodeAsync ADO.NET query failed: {ex.Message}");
                
                // Fallback to regular Entity Framework query if ADO.NET fails
                try
                {
                    var account = await _context.ChartOfAccounts
                        .FirstOrDefaultAsync(a => a.AccountCode == accountCode);

                    if (account == null) return null;

                    return ToViewModel(account);
                }
                catch (Exception efEx)
                {
                    System.Diagnostics.Debug.WriteLine($"GetAccountByCodeAsync Entity Framework query also failed: {efEx.Message}");
                    throw; // Re-throw the original exception
                }
            }
        }

        public async Task<ChartOfAccountViewModel?> GetAccountByIdAsync(string accountCode)
        {
            try
            {
                // Use direct ADO.NET to bypass Entity Framework discriminator issues
                var connectionString = _context.Database.GetConnectionString();
                
                using (var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var command = new Microsoft.Data.SqlClient.SqlCommand(
                        "SELECT AccountCode, SegmentCode, AccountName, ParentAccountCode, AccountLevel, IsPosting, AccountType, AccountNature, OpeningBalance, Notes, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn FROM tbl_Acc_Accounts WHERE AccountCode = @AccountCode", 
                        connection))
                    {
                        command.Parameters.AddWithValue("@AccountCode", accountCode);
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                var account = new ChartOfAccount
                                {
                                    AccountCode = reader["AccountCode"].ToString() ?? "",
                                    SegmentCode = reader["SegmentCode"]?.ToString(),
                                    AccountName = reader["AccountName"].ToString() ?? "",
                                    ParentAccountCode = reader["ParentAccountCode"]?.ToString(),
                                    AccountLevel = Convert.ToInt32(reader["AccountLevel"]),
                                    IsPosting = Convert.ToBoolean(reader["IsPosting"]),
                                    AccountType = reader["AccountType"]?.ToString(),
                                    AccountNature = reader["AccountNature"]?.ToString(),
                                    OpeningBalance = Convert.ToDecimal(reader["OpeningBalance"]),
                                    Notes = reader["Notes"]?.ToString(),
                                    CreatedBy = reader["CreatedBy"]?.ToString(),
                                    CreatedOn = reader["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(reader["CreatedOn"]) : null,
                                    ModifiedBy = reader["ModifiedBy"]?.ToString(),
                                    ModifiedOn = reader["ModifiedOn"] != DBNull.Value ? Convert.ToDateTime(reader["ModifiedOn"]) : null
                                };
                                return ToViewModel(account);
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                // Fallback to regular Entity Framework query if ADO.NET fails
                var account = await _context.ChartOfAccounts
                    .FirstOrDefaultAsync(a => a.AccountCode == accountCode);

                if (account == null) return null;

                return ToViewModel(account);
            }
        }

        public async Task<bool> CreateAccountAsync(ChartOfAccountCreateViewModel model, string createdBy)
        {
            try
            {
                var account = new ChartOfAccount
                {
                    AccountCode = model.AccountCode,
                    SegmentCode = model.SegmentCode,
                    AccountName = model.AccountName,
                    ParentAccountCode = string.IsNullOrEmpty(model.ParentAccountCode) ? null : model.ParentAccountCode,
                    AccountLevel = model.AccountCode.Length / 4,
                    IsPosting = model.IsPosting,
                    AccountType = model.AccountType,
                    AccountNature = model.AccountNature,
                    OpeningBalance = model.OpeningBalance,
                    Notes = model.Notes,
                    CreatedBy = createdBy,
                    CreatedOn = DateTime.Now
                };

                _context.ChartOfAccounts.Add(account);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateAccountAsync(ChartOfAccountEditViewModel model, string modifiedBy)
        {
            try
            {
                var account = await _context.ChartOfAccounts.FirstOrDefaultAsync(a => a.AccountCode == model.AccountCode);
                if (account == null) return false;

                account.AccountName = model.AccountName;
                account.ParentAccountCode = string.IsNullOrEmpty(model.ParentAccountCode) ? null : model.ParentAccountCode;
                account.AccountLevel = model.AccountCode.Length / 4;
                account.IsPosting = model.IsPosting;
                account.AccountType = model.AccountType;
                account.AccountNature = model.AccountNature;
                account.OpeningBalance = model.OpeningBalance;
                account.Notes = model.Notes;
                account.ModifiedBy = modifiedBy;
                account.ModifiedOn = DateTime.Now;

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteAccountAsync(string accountCode)
        {
            try
            {
                var account = await _context.ChartOfAccounts.FirstOrDefaultAsync(a => a.AccountCode == accountCode);
                if (account == null) return false;

                // Check if account has sub-accounts
                var hasSubAccounts = await _context.ChartOfAccounts
                    .AnyAsync(a => a.ParentAccountCode == account.AccountCode);

                if (hasSubAccounts) return false;

                _context.ChartOfAccounts.Remove(account);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> GenerateNextSegmentCodeAsync(string parentCode)
        {
            try
            {
                var parentLength = parentCode.Length;
                var segmentLength = Math.Max(7 - parentLength, 4); // fallback to 4 if calculation is negative

                // Use direct ADO.NET to bypass Entity Framework discriminator issues
                var connectionString = _context.Database.GetConnectionString();
                var maxSegments = new List<string>();
                
                using (var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var command = new Microsoft.Data.SqlClient.SqlCommand(
                        "SELECT SegmentCode FROM tbl_Acc_Accounts WHERE ParentAccountCode = @ParentCode AND SegmentCode IS NOT NULL", 
                        connection))
                    {
                        command.Parameters.AddWithValue("@ParentCode", parentCode);
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var segmentCode = reader["SegmentCode"]?.ToString();
                                if (!string.IsNullOrEmpty(segmentCode))
                                {
                                    maxSegments.Add(segmentCode);
                                }
                            }
                        }
                    }
                }

                if (!maxSegments.Any())
                {
                    return "1".PadLeft(segmentLength, '0');
                }

                var maxValue = maxSegments
                    .Where(s => int.TryParse(s, out _))
                    .Select(s => int.Parse(s))
                    .DefaultIfEmpty(0)
                    .Max();

                return (maxValue + 1).ToString($"D{segmentLength}");
            }
            catch (Exception ex)
            {
                // Log the specific error
                System.Diagnostics.Debug.WriteLine($"GenerateNextSegmentCodeAsync ADO.NET query failed: {ex.Message}");
                
                // Fallback to regular Entity Framework query if ADO.NET fails
                try
                {
                    var parentLength = parentCode.Length;
                    var segmentLength = Math.Max(7 - parentLength, 4); // fallback to 4 if calculation is negative

                    var maxSegment = await _context.ChartOfAccounts
                        .Where(a => a.ParentAccountCode == parentCode)
                        .Select(a => a.SegmentCode)
                        .Where(s => s != null)
                        .ToListAsync();

                    if (!maxSegment.Any())
                    {
                        return "1".PadLeft(segmentLength, '0');
                    }

                    var maxValue = maxSegment
                        .Where(s => int.TryParse(s, out _))
                        .Select(s => int.Parse(s!))
                        .DefaultIfEmpty(0)
                        .Max();

                    return (maxValue + 1).ToString($"D{segmentLength}");
                }
                catch (Exception efEx)
                {
                    System.Diagnostics.Debug.WriteLine($"GenerateNextSegmentCodeAsync Entity Framework query also failed: {efEx.Message}");
                    return "0001"; // Final fallback
                }
            }
        }

        public async Task<List<ChartOfAccountViewModel>> GetParentAccountsAsync()
        {
            try
            {
                // Use direct ADO.NET to bypass Entity Framework discriminator issues
                var accounts = new List<ChartOfAccount>();
                var connectionString = _context.Database.GetConnectionString();
                
                using (var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var command = new Microsoft.Data.SqlClient.SqlCommand(
                        "SELECT AccountCode, SegmentCode, AccountName, ParentAccountCode, AccountLevel, IsPosting, AccountType, AccountNature, OpeningBalance, Notes, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn FROM tbl_Acc_Accounts ORDER BY AccountCode", 
                        connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var account = new ChartOfAccount
                                {
                                    AccountCode = reader["AccountCode"].ToString() ?? "",
                                    SegmentCode = reader["SegmentCode"]?.ToString(),
                                    AccountName = reader["AccountName"].ToString() ?? "",
                                    ParentAccountCode = reader["ParentAccountCode"]?.ToString(),
                                    AccountLevel = Convert.ToInt32(reader["AccountLevel"]),
                                    IsPosting = Convert.ToBoolean(reader["IsPosting"]),
                                    AccountType = reader["AccountType"]?.ToString(),
                                    AccountNature = reader["AccountNature"]?.ToString(),
                                    OpeningBalance = Convert.ToDecimal(reader["OpeningBalance"]),
                                    Notes = reader["Notes"]?.ToString(),
                                    CreatedBy = reader["CreatedBy"]?.ToString(),
                                    CreatedOn = reader["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(reader["CreatedOn"]) : null,
                                    ModifiedBy = reader["ModifiedBy"]?.ToString(),
                                    ModifiedOn = reader["ModifiedOn"] != DBNull.Value ? Convert.ToDateTime(reader["ModifiedOn"]) : null
                                };
                                accounts.Add(account);
                            }
                        }
                    }
                }

                return accounts.Select(a => ToViewModel(a)).ToList();
            }
            catch (Exception ex)
            {
                // Log the specific error
                System.Diagnostics.Debug.WriteLine($"GetParentAccountsAsync ADO.NET query failed: {ex.Message}");
                
                // Fallback to regular Entity Framework query if ADO.NET fails
                try
                {
                    var accounts = await _context.ChartOfAccounts
                        .OrderBy(a => a.AccountCode)
                        .ToListAsync();

                    return accounts.Select(a => ToViewModel(a)).ToList();
                }
                catch (Exception efEx)
                {
                    System.Diagnostics.Debug.WriteLine($"GetParentAccountsAsync Entity Framework query also failed: {efEx.Message}");
                    throw; // Re-throw the original exception
                }
            }
        }

        public async Task<bool> AccountExistsAsync(string accountCode)
        {
            try
            {
                // Use direct ADO.NET to bypass Entity Framework discriminator issues
                var connectionString = _context.Database.GetConnectionString();
                
                using (var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var command = new Microsoft.Data.SqlClient.SqlCommand(
                        "SELECT COUNT(*) FROM tbl_Acc_Accounts WHERE AccountCode = @AccountCode", 
                        connection))
                    {
                        command.Parameters.AddWithValue("@AccountCode", accountCode);
                        var count = (int)await command.ExecuteScalarAsync();
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the specific error
                System.Diagnostics.Debug.WriteLine($"AccountExistsAsync ADO.NET query failed: {ex.Message}");
                
                // Fallback to regular Entity Framework query if ADO.NET fails
                try
                {
                    return await _context.ChartOfAccounts
                        .AnyAsync(a => a.AccountCode == accountCode);
                }
                catch (Exception efEx)
                {
                    System.Diagnostics.Debug.WriteLine($"AccountExistsAsync Entity Framework query also failed: {efEx.Message}");
                    throw; // Re-throw the original exception
                }
            }
        }

        public async Task<bool> HasSubAccountsAsync(string accountCode)
        {
            try
            {
                // Use direct ADO.NET to bypass Entity Framework discriminator issues
                var connectionString = _context.Database.GetConnectionString();
                
                using (var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var command = new Microsoft.Data.SqlClient.SqlCommand(
                        "SELECT COUNT(*) FROM tbl_Acc_Accounts WHERE ParentAccountCode = @AccountCode", 
                        connection))
                    {
                        command.Parameters.AddWithValue("@AccountCode", accountCode);
                        var count = (int)await command.ExecuteScalarAsync();
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the specific error
                System.Diagnostics.Debug.WriteLine($"HasSubAccountsAsync ADO.NET query failed: {ex.Message}");
                
                // Fallback to regular Entity Framework query if ADO.NET fails
                try
                {
                    return await _context.ChartOfAccounts
                        .AnyAsync(a => a.ParentAccountCode == accountCode);
                }
                catch (Exception efEx)
                {
                    System.Diagnostics.Debug.WriteLine($"HasSubAccountsAsync Entity Framework query also failed: {efEx.Message}");
                    throw; // Re-throw the original exception
                }
            }
        }



        private ChartOfAccountViewModel ToViewModel(ChartOfAccount account)
        {
            return new ChartOfAccountViewModel
            {
                AccountCode = account.AccountCode,
                SegmentCode = account.SegmentCode,
                AccountName = account.AccountName,
                ParentAccountCode = account.ParentAccountCode,
                AccountLevel = account.AccountLevel,
                IsPosting = account.IsPosting,
                AccountType = account.AccountType,
                AccountNature = account.AccountNature,
                OpeningBalance = account.OpeningBalance,
                Notes = account.Notes,
                CreatedBy = account.CreatedBy,
                CreatedOn = account.CreatedOn,
                ModifiedBy = account.ModifiedBy,
                ModifiedOn = account.ModifiedOn
            };
        }
    }
} 