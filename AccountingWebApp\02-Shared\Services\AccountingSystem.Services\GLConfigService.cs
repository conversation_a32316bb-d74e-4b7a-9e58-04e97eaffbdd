using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AccountingSystem.Services
{
    public interface IGLConfigService
    {
        Task<List<GLConfigViewModel>> GetAllGLConfigsAsync();
        Task<GLConfigViewModel?> GetGLConfigByModuleAsync(string moduleName);
        Task<bool> SaveGLConfigAsync(GLConfigViewModel config);
        Task<bool> SaveAllGLConfigsAsync(List<GLConfigViewModel> configs);
        Task<Dictionary<string, string>> GetAccountsForDropdownAsync();
        Task<string> GetAccountNameAsync(int accountNo);
    }

    public class GLConfigService : IGLConfigService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<GLConfigService> _logger;

        public GLConfigService(AccountingDbContext context, ILogger<GLConfigService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<GLConfigViewModel>> GetAllGLConfigsAsync()
        {
            try
            {
                // Check if the table exists first
                if (!await _context.Database.CanConnectAsync())
                {
                    throw new InvalidOperationException("Cannot connect to the database. Please check your connection string.");
                }

                // Try to get existing configs
                List<GLConfig> configs = new();
                try
                {
                    configs = await _context.GLConfigs.ToListAsync();
                    
                    _logger.LogInformation($"Found {configs.Count} GL configurations in database");
                    
                    // Log the first few configs for debugging
                    foreach (var config in configs.Take(3))
                    {
                        _logger.LogInformation($"Config: Module={config.EntryReferenceModule}, AccountNo={config.AccountNo}");
                    }
                }
                catch (Exception tableEx)
                {
                    _logger.LogError(tableEx, "Error reading GLConfigs table");
                    // Continue with empty configs list
                }

                var viewModels = new List<GLConfigViewModel>();
                
                // Define all modules that should be configured
                var moduleDefinitions = GetModuleDefinitions();

                foreach (var module in moduleDefinitions)
                {
                    var existingConfig = configs.FirstOrDefault(c => c.EntryReferenceModule == module.Key);
                    
                    viewModels.Add(new GLConfigViewModel
                    {
                        Id = 0, // No ID in the actual table
                        EntryReferenceModule = module.Key,
                        ModuleDisplayName = module.Value,
                        AccountNo = existingConfig?.AccountNo ?? 0,
                        AccountName = existingConfig?.AccountNo > 0 ? await GetAccountNameAsync(existingConfig.AccountNo) : "",
                        Description = module.Value, // Use module display name as description
                        IsActive = true // Assume active since we can't determine from DB
                    });
                }

                return viewModels;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting GL configurations");
                throw;
            }
        }

        public async Task<GLConfigViewModel?> GetGLConfigByModuleAsync(string moduleName)
        {
            try
            {
                var config = await _context.GLConfigs
                    .FirstOrDefaultAsync(g => g.EntryReferenceModule == moduleName);

                if (config == null) return null;

                var moduleDefinitions = GetModuleDefinitions();
                var displayName = moduleDefinitions.GetValueOrDefault(moduleName, moduleName);

                return new GLConfigViewModel
                {
                    Id = 0, // No ID in actual table
                    EntryReferenceModule = config.EntryReferenceModule,
                    ModuleDisplayName = displayName,
                    AccountNo = config.AccountNo,
                    AccountName = await GetAccountNameAsync(config.AccountNo),
                    Description = displayName, // Use display name as description
                    IsActive = true // Assume active
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting GL configuration for module: {ModuleName}", moduleName);
                throw;
            }
        }

        public async Task<bool> SaveGLConfigAsync(GLConfigViewModel config)
        {
            try
            {
                var existingConfig = await _context.GLConfigs
                    .FirstOrDefaultAsync(g => g.EntryReferenceModule == config.EntryReferenceModule);

                if (existingConfig != null)
                {
                    existingConfig.AccountNo = config.AccountNo;
                    _context.GLConfigs.Update(existingConfig);
                }
                else
                {
                    var newConfig = new GLConfig
                    {
                        EntryReferenceModule = config.EntryReferenceModule,
                        AccountNo = config.AccountNo
                    };
                    _context.GLConfigs.Add(newConfig);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving GL configuration for module: {ModuleName}", config.EntryReferenceModule);
                return false;
            }
        }

        public async Task<bool> SaveAllGLConfigsAsync(List<GLConfigViewModel> configs)
        {
            try
            {
                using var transaction = await _context.Database.BeginTransactionAsync();

                foreach (var config in configs)
                {
                    if (config.AccountNo > 0) // Only save if account is selected
                    {
                        await SaveGLConfigAsync(config);
                    }
                }

                await transaction.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving all GL configurations");
                return false;
            }
        }

        public async Task<Dictionary<string, string>> GetAccountsForDropdownAsync()
        {
            try
            {
                // Check if the table exists first
                if (!await _context.Database.CanConnectAsync())
                {
                    _logger.LogWarning("Cannot connect to database for accounts dropdown");
                    return new Dictionary<string, string>();
                }

                var accounts = await _context.ChartOfAccounts
                    .Where(a => a.IsPosting) // Only posting accounts
                    .OrderBy(a => a.AccountCode)
                    .Select(a => new { a.AccountCode, a.AccountName })
                    .ToListAsync();

                return accounts.ToDictionary(
                    a => a.AccountCode,
                    a => $"{a.AccountCode} - {a.AccountName}"
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting accounts for dropdown");
                return new Dictionary<string, string>();
            }
        }

        public async Task<string> GetAccountNameAsync(int accountNo)
        {
            try
            {
                var account = await _context.ChartOfAccounts
                    .FirstOrDefaultAsync(a => a.AccountCode == accountNo.ToString());

                return account?.AccountName ?? "";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting account name for account: {AccountNo}", accountNo);
                return "";
            }
        }

        private Dictionary<string, string> GetModuleDefinitions()
        {
            return new Dictionary<string, string>
            {
                { "مبيعات", "المبيعات" },
                { "مشتريات", "المشتريات" },
                { "نقدية", "النقدية" },
                { "مخزون أول المدة", "مخزون أول المدة" },
                { "عملاء", "العملاء" },
                { "موردون", "الموردون" },
                { "خصم مبيعات", "خصم المبيعات" },
                { "خصم مشتريات", "خصم المشتريات" },
                { "القيمة المضافة المحصلة", "القيمة المضافة المحصلة" },
                { "القيمة المضافة المدفوعة", "القيمة المضافة المدفوعة" },
                { "مرتجعات المشتريات", "مرتجعات المشتريات" },
                { "مرتجعات المبيعات", "مرتجعات المبيعات" },
                { "الشريك", "الشريك" },
                { "تكلفة المبيعات", "تكلفة المبيعات" }
            };
        }

        private async Task CreateGLConfigTableWithDefaultData()
        {
            try
            {
                _logger.LogInformation("This method is no longer needed as the table already has data.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreateGLConfigTableWithDefaultData");
            }
        }
    }

    public class GLConfigViewModel
    {
        public int Id { get; set; }
        public string EntryReferenceModule { get; set; } = string.Empty;
        public string ModuleDisplayName { get; set; } = string.Empty;
        public int AccountNo { get; set; }
        public string AccountName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
    }
} 