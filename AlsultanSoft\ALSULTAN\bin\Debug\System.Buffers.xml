<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Buffers</name>
    </assembly>
    <members>
        <member name="T:System.Buffers.ArrayPool`1">
            <summary>
            Provides a resource pool that enables reusing instances of type <see cref="T:T[]"/>. 
            </summary>
            <remarks>
            <para>
            Renting and returning buffers with an <see cref="T:System.Buffers.ArrayPool`1"/> can increase performance
            in situations where arrays are created and destroyed frequently, resulting in significant
            memory pressure on the garbage collector.
            </para>
            <para>
            This class is thread-safe.  All members may be used by multiple threads concurrently.
            </para>
            </remarks>
        </member>
        <member name="F:System.Buffers.ArrayPool`1.s_sharedInstance">
            <summary>The lazily-initialized shared pool instance.</summary>
        </member>
        <member name="P:System.Buffers.ArrayPool`1.Shared">
            <summary>
            Retrieves a shared <see cref="T:System.Buffers.ArrayPool`1"/> instance.
            </summary>
            <remarks>
            The shared pool provides a default implementation of <see cref="T:System.Buffers.ArrayPool`1"/>
            that's intended for general applicability.  It maintains arrays of multiple sizes, and 
            may hand back a larger array than was actually requested, but will never hand back a smaller 
            array than was requested. Renting a buffer from it with <see cref="M:System.Buffers.ArrayPool`1.Rent(System.Int32)"/> will result in an 
            existing buffer being taken from the pool if an appropriate buffer is available or in a new 
            buffer being allocated if one is not available.
            </remarks>
        </member>
        <member name="M:System.Buffers.ArrayPool`1.EnsureSharedCreated">
            <summary>Ensures that <see cref="F:System.Buffers.ArrayPool`1.s_sharedInstance"/> has been initialized to a pool and returns it.</summary>
        </member>
        <member name="M:System.Buffers.ArrayPool`1.Create">
            <summary>
            Creates a new <see cref="T:System.Buffers.ArrayPool`1"/> instance using default configuration options.
            </summary>
            <returns>A new <see cref="T:System.Buffers.ArrayPool`1"/> instance.</returns>
        </member>
        <member name="M:System.Buffers.ArrayPool`1.Create(System.Int32,System.Int32)">
            <summary>
            Creates a new <see cref="T:System.Buffers.ArrayPool`1"/> instance using custom configuration options.
            </summary>
            <param name="maxArrayLength">The maximum length of array instances that may be stored in the pool.</param>
            <param name="maxArraysPerBucket">
            The maximum number of array instances that may be stored in each bucket in the pool.  The pool
            groups arrays of similar lengths into buckets for faster access.
            </param>
            <returns>A new <see cref="T:System.Buffers.ArrayPool`1"/> instance with the specified configuration options.</returns>
            <remarks>
            The created pool will group arrays into buckets, with no more than <paramref name="maxArraysPerBucket"/>
            in each bucket and with those arrays not exceeding <paramref name="maxArrayLength"/> in length.
            </remarks>
        </member>
        <member name="M:System.Buffers.ArrayPool`1.Rent(System.Int32)">
            <summary>
            Retrieves a buffer that is at least the requested length.
            </summary>
            <param name="minimumLength">The minimum length of the array needed.</param>
            <returns>
            An <see cref="T:T[]"/> that is at least <paramref name="minimumLength"/> in length.
            </returns>
            <remarks>
            This buffer is loaned to the caller and should be returned to the same pool via 
            <see cref="M:System.Buffers.ArrayPool`1.Return(`0[],System.Boolean)"/> so that it may be reused in subsequent usage of <see cref="M:System.Buffers.ArrayPool`1.Rent(System.Int32)"/>.  
            It is not a fatal error to not return a rented buffer, but failure to do so may lead to 
            decreased application performance, as the pool may need to create a new buffer to replace
            the one lost.
            </remarks>
        </member>
        <member name="M:System.Buffers.ArrayPool`1.Return(`0[],System.Boolean)">
            <summary>
            Returns to the pool an array that was previously obtained via <see cref="M:System.Buffers.ArrayPool`1.Rent(System.Int32)"/> on the same 
            <see cref="T:System.Buffers.ArrayPool`1"/> instance.
            </summary>
            <param name="array">
            The buffer previously obtained from <see cref="M:System.Buffers.ArrayPool`1.Rent(System.Int32)"/> to return to the pool.
            </param>
            <param name="clearArray">
            If <c>true</c> and if the pool will store the buffer to enable subsequent reuse, <see cref="M:System.Buffers.ArrayPool`1.Return(`0[],System.Boolean)"/>
            will clear <paramref name="array"/> of its contents so that a subsequent consumer via <see cref="M:System.Buffers.ArrayPool`1.Rent(System.Int32)"/> 
            will not see the previous consumer's content.  If <c>false</c> or if the pool will release the buffer,
            the array's contents are left unchanged.
            </param>
            <remarks>
            Once a buffer has been returned to the pool, the caller gives up all ownership of the buffer 
            and must not use it. The reference returned from a given call to <see cref="M:System.Buffers.ArrayPool`1.Rent(System.Int32)"/> must only be
            returned via <see cref="M:System.Buffers.ArrayPool`1.Return(`0[],System.Boolean)"/> once.  The default <see cref="T:System.Buffers.ArrayPool`1"/>
            may hold onto the returned buffer in order to rent it again, or it may release the returned buffer
            if it's determined that the pool already has enough buffers stored.
            </remarks>
        </member>
        <member name="T:System.Buffers.ArrayPoolEventSource.BufferAllocatedReason">
            <summary>The reason for a BufferAllocated event.</summary>
        </member>
        <member name="F:System.Buffers.ArrayPoolEventSource.BufferAllocatedReason.Pooled">
            <summary>The pool is allocating a buffer to be pooled in a bucket.</summary>
        </member>
        <member name="F:System.Buffers.ArrayPoolEventSource.BufferAllocatedReason.OverMaximumSize">
            <summary>The requested buffer size was too large to be pooled.</summary>
        </member>
        <member name="F:System.Buffers.ArrayPoolEventSource.BufferAllocatedReason.PoolExhausted">
            <summary>The pool has already allocated for pooling as many buffers of a particular size as it's allowed.</summary>
        </member>
        <member name="M:System.Buffers.ArrayPoolEventSource.BufferRented(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Event for when a buffer is rented.  This is invoked once for every successful call to Rent,
            regardless of whether a buffer is allocated or a buffer is taken from the pool.  In a
            perfect situation where all rented buffers are returned, we expect to see the number
            of BufferRented events exactly match the number of BuferReturned events, with the number
            of BufferAllocated events being less than or equal to those numbers (ideally significantly
            less than).
            </summary>
        </member>
        <member name="M:System.Buffers.ArrayPoolEventSource.BufferAllocated(System.Int32,System.Int32,System.Int32,System.Int32,System.Buffers.ArrayPoolEventSource.BufferAllocatedReason)">
            <summary>
            Event for when a buffer is allocated by the pool.  In an ideal situation, the number
            of BufferAllocated events is significantly smaller than the number of BufferRented and
            BufferReturned events.
            </summary>
        </member>
        <member name="M:System.Buffers.ArrayPoolEventSource.BufferReturned(System.Int32,System.Int32,System.Int32)">
            <summary>
            Event raised when a buffer is returned to the pool.  This event is raised regardless of whether
            the returned buffer is stored or dropped.  In an ideal situation, the number of BufferReturned
            events exactly matches the number of BufferRented events.
            </summary>
        </member>
        <member name="F:System.Buffers.DefaultArrayPool`1.DefaultMaxArrayLength">
            <summary>The default maximum length of each array in the pool (2^20).</summary>
        </member>
        <member name="F:System.Buffers.DefaultArrayPool`1.DefaultMaxNumberOfArraysPerBucket">
            <summary>The default maximum number of arrays per bucket that are available for rent.</summary>
        </member>
        <member name="F:System.Buffers.DefaultArrayPool`1.s_emptyArray">
            <summary>Lazily-allocated empty array used when arrays of length 0 are requested.</summary>
        </member>
        <member name="P:System.Buffers.DefaultArrayPool`1.Id">
            <summary>Gets an ID for the pool to use with events.</summary>
        </member>
        <member name="T:System.Buffers.DefaultArrayPool`1.Bucket">
            <summary>Provides a thread-safe bucket containing buffers that can be Rent'd and Return'd.</summary>
        </member>
        <member name="M:System.Buffers.DefaultArrayPool`1.Bucket.#ctor(System.Int32,System.Int32,System.Int32)">
            <summary>
            Creates the pool with numberOfBuffers arrays where each buffer is of bufferLength length.
            </summary>
        </member>
        <member name="P:System.Buffers.DefaultArrayPool`1.Bucket.Id">
            <summary>Gets an ID for the bucket to use with events.</summary>
        </member>
        <member name="M:System.Buffers.DefaultArrayPool`1.Bucket.Rent">
            <summary>Takes an array from the bucket.  If the bucket is empty, returns null.</summary>
        </member>
        <member name="M:System.Buffers.DefaultArrayPool`1.Bucket.Return(`0[])">
            <summary>
            Attempts to return the buffer to the bucket.  If successful, the buffer will be stored
            in the bucket and true will be returned; otherwise, the buffer won't be stored, and false
            will be returned.
            </summary>
        </member>
        <member name="P:System.SR.ArgumentException_BufferNotFromPool">
            <summary>The buffer is not associated with this pool and may not be returned to it.</summary>
        </member>
    </members>
</doc>
