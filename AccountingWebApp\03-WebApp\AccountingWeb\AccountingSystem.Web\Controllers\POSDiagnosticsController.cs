using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using System.Security.Claims;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class POSDiagnosticsController : Controller
    {
        private readonly IPOSService _posService;
        private readonly ILogger<POSDiagnosticsController> _logger;

        public POSDiagnosticsController(IPOSService posService, ILogger<POSDiagnosticsController> logger)
        {
            _posService = posService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";
                
                var diagnostics = new
                {
                    Username = username,
                    IsAuthenticated = User.Identity?.IsAuthenticated ?? false,
                    
                    // Check basic data
                    StoresCount = (await _posService.GetStoresAsync()).Count,
                    CustomersCount = (await _posService.GetCustomersAsync()).Count,
                    FavoriteItemsCount = (await _posService.GetFavoriteItemsAsync(username)).Count,
                    
                    // Check session
                    HasActiveSession = await _posService.ValidateActiveSessionAsync(username),
                    ActiveSession = await _posService.GetActiveSessionAsync(username),
                    
                    // Check current invoice
                    CurrentInvoice = await _posService.GetCurrentInvoiceAsync(),
                    
                    // Check settings
                    POSSettings = await _posService.GetPOSSettingsAsync()
                };

                return Json(new { success = true, diagnostics });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "Error in POS diagnostics");
                return Json(new { success = false, error = ex.Message });
            }
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult Test()
        {
            return Content($"POS Diagnostics Controller is working! Time: {System.DateTime.Now}");
        }
    }
}
