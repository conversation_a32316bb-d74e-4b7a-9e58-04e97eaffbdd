using AccountingSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public interface IStoreServiceV2
    {
        Task<List<Store>> GetStoresAsync();
        Task<Store?> GetStoreByIdAsync(int id);
        Task<(bool success, Store? createdStore)> CreateStoreAsync(Store store, string currentUser);
        Task<bool> UpdateStoreAsync(Store store, string currentUser);
        Task<(bool success, string errorMessage)> DeleteStoreAsync(int id);
        Task<bool> IsStoreUsedInTransactionsAsync(int storeId);
    }
} 