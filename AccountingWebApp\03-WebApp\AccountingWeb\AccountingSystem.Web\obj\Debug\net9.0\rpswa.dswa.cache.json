{"GlobalPropertiesHash": "QjKeZ+xDyimWk6u++TjVVfgrl+4XluY0jyH/00XTiRc=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["VHvek9qfFCXpiJu0DNtfH6btjmq7xEADzMmT5fENz4k=", "CjYPWYHdexnaJ/Yxeg/n2j/RhgHhbsb+x7rLFz6TOq4=", "h+eoWBNOWIPW7eFQLoKHHiceNhN9/HBuCOnt7JjP7bE=", "Aq4zkkD0BxG0kDiM7bDLyKdHmh53mfVfcIIrzTy2dLg=", "ruDuLzti8BI7/qIuIrVxrtycsjU9VCLaGZQF7SKuAds=", "uIhgI+ysuVCxCsrH9Ertd0HA9YdzCc6irLQ46XT4Gq4=", "okruoBkYWOI8L5NMLwOYHziAHcyBgOEGHQU4IAPF4Hg=", "Dr/x2FKY5WtJaldVoYQGPWUaqmz5fus8OqML/rgVzWk=", "SvQW3HxV+2O/TSBfmzVpRUxJTLXJiixK0zK0rcWFTCg=", "5+f2jk47XKHc0+L0d2c2Z79CJiqqzaBQR7XPx4rTDbU=", "PWdAGrx4kkUljHfMynhtdVGXJCNYmYSbwIF9x0SPDBo=", "XldBbQfO3gWIj6FtKaz2Y2aYezisImfiJUJJQLVR/7A=", "k3qOKytlqobhMVKOP3CfSrIQru6cKK4Vi9EVretukls=", "ZWFaQKeR2AyL1N3FFEFt9JKHSg4S4qBSty4dAqe8yj4=", "KkWGZ8pLQ4TJJXjvSTQAewb9ksSg6tr737EVlJi3gUY=", "dEIuFLZiGlOpB8frFwICHcLIeBk9gr1KScqAPAv5aiw=", "rrRUo+gLmZuZZMVCMt5beXY85TD5Xg97kkbAo80KAig=", "ikvYCLkkacmyk5QMBz3a2y3CYXvuH338WQOxyTkCQXQ=", "907uDB1axluzARLb1YV7+C0prBqPc5qRmlrzc9lLEVI=", "dWOb9mGhIWSgBU9yXZ3SluSprmrpgRfcZkf7JiPT2hw=", "rYEaJNWlXbhsLIfY45nwtqp3xJPPZ7J8Dmb9UHUTxeg=", "LA5QE3Ff5PmS1j2b7Ej6eB0K6c3Aat0By2lpYquzgn0=", "Jy+eHBajj83Hj+Xy1xlMHlMTljQoo8E848gtL/SOinU=", "+69yfCc9j+xBxVMXIAP3v8r5Lo3nz2rqRKOjFWtxsWw=", "C7KqdSLjmvb26ffobfRakPP5hDbc7owRGL6SP9In7/Q=", "+Srio85R+tRG60mm+6IYDwyqcyhcTrVsVQKHP/PpmFo=", "HLnApQmYYE+RGaUGnA0G8pcYbonsMk4bH6/xz+Eg58A=", "jL/R+4hpMvU/l9I/cIC5tL3Nnz0EJMoUWVlK9BF3/Gw=", "WD1+uExwL5/TluOTBAUrIvmx+q5hgnzeFcvQYtdhY/A=", "yicXJeS0DvIujop91K5xpKISkRqt+dRc07Np/1RK2dE=", "7f4IRt+8tiFGCbw+zD4Wm+Pi7l3dSAQQokP27TVNWcw=", "pQ4oHW4jsctMKJLuIa6wi6w5fbQ4u+oWFJYzC1RA+DE=", "Zfa+dZ+JEvJDglKhomREnVXwb4caOutTQN0lCXjgzcA=", "Bz8sSnsBg/DHspnneDRUet/k26atRnVUl6aah+gui9s=", "RA2RAbkZANkppb2CG/nciAQMXStNWi0w0LLxCa7VlXY=", "xlPObcXBSqR/I+RDKR3VUEQyvBMFizTmEpKZG5yB3Bw=", "fJ957KA4y4sFvDYWz/pa4PKHeke08KTeLJI+cgZ1vMk=", "9IkVoHlgS6JoeQUL5ar2CJesc80g31/kdB5pZewZCW8=", "OfZKHsF6xqcXTGweI0RwK715fwhpToT1Bsw7IG9Fqco=", "Gd2CxewmR/+MPL/i7ytWFLI8+ZR7rtGhKO5FVa5GiaA=", "TLRKEjm1XNl8AbqW/QUzOKIQjvZgne9ONAdKZDFjJNs=", "tBEryW0i3VePuKz64299XMPIC3OIisUJvCd1RLMYfXk=", "6ZX6/I2BP7IfJ6Qppu2kBxkpdap0Rfpx0y+Dy8vmylA=", "O9OKEjz+fvTXdFCsawVD5ngb4FLh//9tLEy1ehe+beY=", "u2aEVKbP4FGFsHooNz7N8gCvqUL+kVQ6ZTEWAMWcRtA=", "ZnlXxMHs5ukxKOB62wM+Q31P853CaFaDkufewoKaIng=", "5KLvAtpcdp1DSvmIve6nsY9Qh24QrR8te1s42w7/S+w=", "augBaOkacmNeA0Oi8elcYdr7C8s/P8e7OD/VMgHlDAA=", "ttslcNixMUdm5iYLHoz+rwCMpSl+rOwyxAQP2i+tnfM=", "O9I+ynX+TP+mnmrXPU3I1w5C5+6i51UAfDeLp/dBGfM=", "2hJYIvMDfFjfNbIZJ+UHwChiHdAfvfjO9Yq8AhDqhMo=", "FQUSTNeGo3Q/yMtycUA+bxrFcqN6XxAsonPvhApCAPg=", "RxVzDtrYn9ez2Jjd7Znmc1HKFq8GjZxIoRE6txd+35Q=", "tzcd3QGXWBbdjaTAHpkyGQHY18KaEqV7rvvifu3sEpA=", "zQ9To76uDWntNMnt4sGwb5eYzn5iH69S8uVoJfb9l7w=", "68rncSPAqSx7cXluBhcgTSUdRK5RvjgEKRpwidKBfwI=", "B7t9P9wSreSRk8lMwu/ttNMHFCYtbXuwy/kdHpcZNb8=", "5jbZ+oN4h+OCrPFqwu7nXeF+Dla3SL46L3oGzrMAkJ0=", "/CH3t64kNKIYkbS7C5AXSesyaTJnsMSi5IaJW9MDswg=", "ranuryiZOPUCcI9ziDtxcRxOm9/dnI0u+Ha/m4VZwD8=", "HiQfzRn4vvl9zXFRoUZWITprRi2EM9XTKKmupdwTcEA=", "g/uhn6YS3fdtfO4kmMhfjbN8yNrwMNC2fc/b3MLvxR4=", "dcGeTaZgFKVxrY/AGeJVIFk1suOMcT2OsCvFsGeYyIA=", "OZ6SH68BWJkLDFTdpapxWZv3l5akzfXQqXGNqh5JW8E=", "gSKenKsnAxwZrKCZ9heNU4v+RnruT9KcdAn6hW9xR0E=", "YuHsw9f3sJwXk4oMwXkBDFsZuXqx7s++SMysYRJYxZg=", "I8ARog2tPwOTznAyjOBVZ1Wq8mir591ZqaHcXOOyvhs=", "Lbj87L4iZfXxraXZBAD+zdkZWP13He7ZjH5sjHCWLHs=", "0IOe3dRBSCZKeXuAvtTVTEUPtrL3nAbbeIXHPIZPaJk=", "ppHowB21hESlsTNYcxmd3HgOiZf/Tc8Geo0b8jsQdOA=", "vdOaASNQIsClGs/eeuSznz0SRRyZoXUeKvxfeyUV/Mc=", "UZMi9jDu2B+9VIqRGnGGJwCDGpa1MTgcyY3bO6NN96A=", "lpvwyosCnESYhzgAwTndyNtWX0UNLuevbeDC0+cQRuk=", "tWNxyrAFeG0TygyAFod4T6Yx570EdYYfIfUPHGbirfA=", "KMXjxDQIrp+NvFVJM8Yu2gPyYlI4YPIE7LS4r5oJ8HQ=", "f1TFYhwMhcKBBYqKcb5k+hALbn0DBJ/nf+SWMPJJFu0=", "+awK7aupjahOy4PzNEJ6skkBLF9a6nIYiETcfOEgM/Q=", "K5gf1r5TQenhtw8l9DkhRkpmmYPpAQQIQg7tju4h1AM=", "Mu+jvriYFzvqT6Hkj4TGLDPVPbbY8sOM3mW2D93mQv8=", "bm9FTICDZqCPXDAJ+vb76C3qStLVDEuAPKtXbixdQMg=", "0F07mo+6oKpF2jzGqp0yd7RcliK/gnYYfsSvlBgHIMU=", "/Q1w9v+FGsBF8oJ2ogRLBmE0xVrTP4n6Rpf76m414DQ=", "rrDk8mtm4TUxB7cl+kLLZBdvpmJfiMa6pUIhRUwjhOY=", "UmmDPELSoINVj7m+9mFyhmIJtxeisKTu8ClmB/AgI0o=", "TbziOLYH8n9iq2JrOXGtLQGzjk6orPxeTDhEFZixHmk=", "tm9FEuUpG+lsKcD4UIFVU0+n/QY265jOo6l7+XX0VWg=", "U8h2DWz2BiGa+/0TyV5FFtKvyzbed1oyXY5MFEBGRUg=", "9AoqXo2YY87rWnNBMOjgMcByqh+Rwx6QnzMN6O0gHsg=", "1lC8dIMUAKK5Ov6ksXM4Fd7oG5Pxy2D+4BixDTeoJso=", "v55cUrLVYQkkOZw2e1eqCZB5jrDGBXfyHLnpx8zOkfA=", "zj7qzEeZI1JVT8/Mx46RPDbya1+WaQJxrn7ntSXujgU=", "uOj7wgn2YV4kZdOLdLDthR9CiI+AoMA0NvgcQO75kbM=", "eWhVU1rcZNmGc5DE0d/pMMeggbeZPOYIgY7Xzc1Zj9I=", "91eYoXwLFhMBTTQYg37E3oaQJ7FAZRfgvtaEkRBAJ8M=", "MggWydS4Vd8WB1NAqtKjICDI0Uc4BrpTdi73gTT6tn4=", "5hOH72WOyzrjdvszCi2CjmrHMHtJZJ3ZmyLXut0wfsk=", "O2zXdcdel4k3QHo3CGUW9/NjMpnewaizYK+o7wQ7Br8=", "8S9SgAgLa61Ad4+UlKVuE8vTb/UsNIcYd7+bYKYGLLw=", "sm2L7zmc4hrwZALUD7XME7/X/egUJDf95XLEbe9O1hU=", "3F7WV1xrx6UWTrYO5Bnq73Ku943vYYZ8UmXBM2QKCr8=", "J/jOxiUmeZ2fXYVoeDCy7j4KJR5FhPM1u9T/O0AMOf0=", "GQWIHACP87Ck9DxVee7nLr7VYIfrthLFFKtjklpgHAY=", "uupuLIAYGIIPPsGrD2asJe+FoBvpcnr7iODjZ6H9W5c=", "PPuvdbXvg03C6vTd7Ke1aP6i2egAdytlpYCfWXJgIzs=", "F+l32ZtviVbw18LPcJV5aW/k99VT9GZSog1inZ+h/os=", "VkZ3D/iHr6L9auuX9xCxTgSGDjj/uzCMmskb0wlP+7Y=", "PcquWuXrExVjqeRLqp+WAS+Yf8A8z/zWkc1Dbg2Cm6w=", "oe3yM/Ad4M0ZkCwulAK34PBQuG3kYhirBp1gxpZ1YrE=", "meVHR5PdzKmCPHVu3FrfRzbQyhpIfWXLXUl+/l7FHgw=", "vMBvbWKnIWSkwLucHFlAhqXbr7Q381X7B40dbfX6kcA=", "mu+6eIkk+uQh0F0FSujIwtwrF8TAoxn8BKyuLuU1rxo=", "Yd1c0Q828tPr45dzJbOQgg4OEpY8DS9DAElAjH6BYyQ=", "OMkbZf60sTEjSx35twY9w3H71eH/Kzl6vRLtC3qnrlY=", "eqy3VCvi88EMBg0SE1E/RrxXGBoGnovzx4rh31RvtLY=", "3UeNoQN3g+8RsBFj15u45lXJ2stlCaPpJzgEL/wdJmo=", "15ZiWtw4KXXW9+PhlHHDcbNKjWwcRjSr310zZqbVPcM=", "ctVVze601HJmInMcv5ImhTOZq2+IUxnwFMVBghATO6A=", "FQBpH1EaUz2pKIkScv6Yxijs8vkEgvV+oG2BCj2diq4=", "N16v8Rc/jKHJK6sqRZ/K41zTOO0wNXM3sq4rvFLhIq8=", "UM0jVMLiRik+PywVuIwEs5219W9VuH5bSokkwyu+hBs=", "cq0FOUDoEH3nDSLopi6KdmQyu5+xtTZfqD/JdpBY4yU=", "86U3ay418pPmybRUz2ZSQd+cIYnGTectnbUeHxgAY3U=", "hGgnvyXqNOXk/fYT6VVBK3zie1MltqVT3oC4OGsXiHI=", "smW7xC14ZFzUydXcF0RMMZQFR6opJX7aiJmy+N+Zxug=", "BOsJVE355F5QWmZIdGREbEn0vKVYIGJfx+Qp9cDLBu4=", "3bPKbmyN8ei5sL5QQeBCimYm3r2Q005yj91oGieCxYY=", "Eh6LoNkift0EoFk2Xn9K2SZn56tGPi0TMQWgAlIARQU=", "49GsvUH/l4ZspsVRezUdKhxj5JZACE/4GaQJKFRHkFc=", "IJFcNDlKMdMGz6GjRvQV6v814fuxL6+5wsNAvcEqs9g=", "z/aBxHweriTnZxoEfwqXbT97xS7lKpi4dCy5SAgdvMY=", "qDrRPLKfaDFf4p+Llfea328ERh9lmTV5kmeTSb4ohdA=", "gQXeuwqzK5XdbyEARp9j181iqjJZOQK15cgjE0dkf7k=", "ySlhpJy/7m3P8K+646/2Cx/PqORjJCRuK5H20eNiDPs=", "vz+ixDuernVvvVtF8rTY1ZlMuIyWG3C1YFBEHei7/MU=", "MiyWIsC2GR5KXYUJEQ9EUfkdsMoXgp9aKnTXuYNcwQ0=", "kB9sWPlDeuIShzYjRXDKIoXuNXX5Xbc62F4Ja3sFeHU=", "Wz8qCliqN5CiN0GIEuUBLRJ9kthkT7mvPdHSu6FRAsE=", "01iNxdAl4z6VNeV0KXF1mkTOTSnL+c5GEnmvxqRbUds=", "bbBR9o9mVt8rKj7NJNt47C19R4it+ak+dXfzBuQqfSA="], "CachedAssets": {"VHvek9qfFCXpiJu0DNtfH6btjmq7xEADzMmT5fENz4k=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\css\\site.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hsrfjvnq1z", "Integrity": "cfn14Tmg4sAy4irvFiAShMG69r+VWLJS//ZCf/uvNeg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 865, "LastWriteTime": "2025-07-14T22:07:23.6589377+00:00"}, "CjYPWYHdexnaJ/Yxeg/n2j/RhgHhbsb+x7rLFz6TOq4=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\favicon.ico", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "h+eoWBNOWIPW7eFQLoKHHiceNhN9/HBuCOnt7JjP7bE=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\images\\Alsultan-logo.jpg", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "images/Alsultan-logo#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ahdim8u5fj", "Integrity": "QtUzywDQE62QFxITh5qwpE1/gkdAHcIjo5vr90Jyga4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\Alsultan-logo.jpg", "FileLength": 96296, "LastWriteTime": "2019-05-11T15:56:20+00:00"}, "Aq4zkkD0BxG0kDiM7bDLyKdHmh53mfVfcIIrzTy2dLg=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\js\\qz-tray.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "js/qz-tray#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3sv8558lwh", "Integrity": "HZTMlTnPoQPhfb70q4e4iM8GU7NRrRtrjD4+r4Pbboc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\qz-tray.js", "FileLength": 140, "LastWriteTime": "2025-07-23T13:19:53.2467028+00:00"}, "ruDuLzti8BI7/qIuIrVxrtycsjU9VCLaGZQF7SKuAds=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\js\\site.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-07T18:22:48.070244+00:00"}, "uIhgI+ysuVCxCsrH9Ertd0HA9YdzCc6irLQ46XT4Gq4=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "okruoBkYWOI8L5NMLwOYHziAHcyBgOEGHQU4IAPF4Hg=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "Dr/x2FKY5WtJaldVoYQGPWUaqmz5fus8OqML/rgVzWk=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "SvQW3HxV+2O/TSBfmzVpRUxJTLXJiixK0zK0rcWFTCg=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "5+f2jk47XKHc0+L0d2c2Z79CJiqqzaBQR7XPx4rTDbU=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "PWdAGrx4kkUljHfMynhtdVGXJCNYmYSbwIF9x0SPDBo=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "XldBbQfO3gWIj6FtKaz2Y2aYezisImfiJUJJQLVR/7A=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "k3qOKytlqobhMVKOP3CfSrIQru6cKK4Vi9EVretukls=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "ZWFaQKeR2AyL1N3FFEFt9JKHSg4S4qBSty4dAqe8yj4=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "KkWGZ8pLQ4TJJXjvSTQAewb9ksSg6tr737EVlJi3gUY=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "dEIuFLZiGlOpB8frFwICHcLIeBk9gr1KScqAPAv5aiw=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "rrRUo+gLmZuZZMVCMt5beXY85TD5Xg97kkbAo80KAig=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "ikvYCLkkacmyk5QMBz3a2y3CYXvuH338WQOxyTkCQXQ=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "907uDB1axluzARLb1YV7+C0prBqPc5qRmlrzc9lLEVI=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "dWOb9mGhIWSgBU9yXZ3SluSprmrpgRfcZkf7JiPT2hw=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "rYEaJNWlXbhsLIfY45nwtqp3xJPPZ7J8Dmb9UHUTxeg=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "LA5QE3Ff5PmS1j2b7Ej6eB0K6c3Aat0By2lpYquzgn0=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "Jy+eHBajj83Hj+Xy1xlMHlMTljQoo8E848gtL/SOinU=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-07T18:22:48.0020805+00:00"}, "+69yfCc9j+xBxVMXIAP3v8r5Lo3nz2rqRKOjFWtxsWw=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-07T18:22:48.0020805+00:00"}, "C7KqdSLjmvb26ffobfRakPP5hDbc7owRGL6SP9In7/Q=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-07T18:22:48.0030964+00:00"}, "+Srio85R+tRG60mm+6IYDwyqcyhcTrVsVQKHP/PpmFo=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-07T18:22:48.0030964+00:00"}, "HLnApQmYYE+RGaUGnA0G8pcYbonsMk4bH6/xz+Eg58A=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-07T18:22:48.0040962+00:00"}, "jL/R+4hpMvU/l9I/cIC5tL3Nnz0EJMoUWVlK9BF3/Gw=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-07T18:22:48.0040962+00:00"}, "WD1+uExwL5/TluOTBAUrIvmx+q5hgnzeFcvQYtdhY/A=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-07T18:22:48.0050935+00:00"}, "yicXJeS0DvIujop91K5xpKISkRqt+dRc07Np/1RK2dE=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "7f4IRt+8tiFGCbw+zD4Wm+Pi7l3dSAQQokP27TVNWcw=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "pQ4oHW4jsctMKJLuIa6wi6w5fbQ4u+oWFJYzC1RA+DE=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "Zfa+dZ+JEvJDglKhomREnVXwb4caOutTQN0lCXjgzcA=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "Bz8sSnsBg/DHspnneDRUet/k26atRnVUl6aah+gui9s=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "RA2RAbkZANkppb2CG/nciAQMXStNWi0w0LLxCa7VlXY=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "xlPObcXBSqR/I+RDKR3VUEQyvBMFizTmEpKZG5yB3Bw=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "fJ957KA4y4sFvDYWz/pa4PKHeke08KTeLJI+cgZ1vMk=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "9IkVoHlgS6JoeQUL5ar2CJesc80g31/kdB5pZewZCW8=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "OfZKHsF6xqcXTGweI0RwK715fwhpToT1Bsw7IG9Fqco=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "Gd2CxewmR/+MPL/i7ytWFLI8+ZR7rtGhKO5FVa5GiaA=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "TLRKEjm1XNl8AbqW/QUzOKIQjvZgne9ONAdKZDFjJNs=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "tBEryW0i3VePuKz64299XMPIC3OIisUJvCd1RLMYfXk=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "6ZX6/I2BP7IfJ6Qppu2kBxkpdap0Rfpx0y+Dy8vmylA=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-07T18:22:48.0186963+00:00"}, "O9OKEjz+fvTXdFCsawVD5ngb4FLh//9tLEy1ehe+beY=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-07T18:22:48.0197053+00:00"}, "u2aEVKbP4FGFsHooNz7N8gCvqUL+kVQ6ZTEWAMWcRtA=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-07T18:22:48.0207066+00:00"}, "ZnlXxMHs5ukxKOB62wM+Q31P853CaFaDkufewoKaIng=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-07T18:22:48.0217038+00:00"}, "5KLvAtpcdp1DSvmIve6nsY9Qh24QrR8te1s42w7/S+w=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "augBaOkacmNeA0Oi8elcYdr7C8s/P8e7OD/VMgHlDAA=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "ttslcNixMUdm5iYLHoz+rwCMpSl+rOwyxAQP2i+tnfM=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "O9I+ynX+TP+mnmrXPU3I1w5C5+6i51UAfDeLp/dBGfM=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "2hJYIvMDfFjfNbIZJ+UHwChiHdAfvfjO9Yq8AhDqhMo=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "FQUSTNeGo3Q/yMtycUA+bxrFcqN6XxAsonPvhApCAPg=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "RxVzDtrYn9ez2Jjd7Znmc1HKFq8GjZxIoRE6txd+35Q=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "tzcd3QGXWBbdjaTAHpkyGQHY18KaEqV7rvvifu3sEpA=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "zQ9To76uDWntNMnt4sGwb5eYzn5iH69S8uVoJfb9l7w=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "68rncSPAqSx7cXluBhcgTSUdRK5RvjgEKRpwidKBfwI=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "B7t9P9wSreSRk8lMwu/ttNMHFCYtbXuwy/kdHpcZNb8=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "5jbZ+oN4h+OCrPFqwu7nXeF+Dla3SL46L3oGzrMAkJ0=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "/CH3t64kNKIYkbS7C5AXSesyaTJnsMSi5IaJW9MDswg=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "ranuryiZOPUCcI9ziDtxcRxOm9/dnI0u+Ha/m4VZwD8=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "HiQfzRn4vvl9zXFRoUZWITprRi2EM9XTKKmupdwTcEA=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "g/uhn6YS3fdtfO4kmMhfjbN8yNrwMNC2fc/b3MLvxR4=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "dcGeTaZgFKVxrY/AGeJVIFk1suOMcT2OsCvFsGeYyIA=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-07T18:22:48.0373776+00:00"}, "OZ6SH68BWJkLDFTdpapxWZv3l5akzfXQqXGNqh5JW8E=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-07T18:22:48.0549302+00:00"}, "gSKenKsnAxwZrKCZ9heNU4v+RnruT9KcdAn6hW9xR0E=": {"Identity": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}}, "CachedCopyCandidates": {}}