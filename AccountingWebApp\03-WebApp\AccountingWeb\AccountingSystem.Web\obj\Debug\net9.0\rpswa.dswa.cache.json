{"GlobalPropertiesHash": "EJEDztg8zuPiGdeARQOENsnrdSzpCnjXl2aucDcRut8=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["tI1szlh7VCCOHxEDOsq/4iRElcFJjagev7EDC3nx3+I=", "+SFW1juKea6NWGL/MudYPZN82sDnpD39Cyi3f3URswo=", "bqHYt98u29dZhxLwYtM7/cpukwwi+8+h/jC0IeJ0uvQ=", "ANKx0Rbs4uyyNvjRTakvQZNvvfOw6IKxW8MYNn1b9Bc=", "p7paXKTZnEfFed2hrNbM4Ebg0ez56lqLb3vL0lSmn28=", "jfFs3MW7NmxGpk8uS0DkmIQe14I1XSlGzAFvjlYvdjM=", "7iMiQvN6EHcHALImPvDgDGOCKUwMM7PDIjN+wOxBqFs=", "IdQode7umclInMf/L/5w+PQG5T/g5NImfBvqcEJApYE=", "eV3iKsXXHizRRwIv1/4mynzIINy4gG9aAgloeLideIE=", "i1pKoewcOK0p0RNSPyFwqDGgYCgyg8cvxHAUJQhSooQ=", "a8vMxR4iZ+j0+4gcKmaamd7qFnVVuAnauvvd1+X7ZoA=", "PEAGW6jnlBEVHi6eVVGFccoHiT1gjlpLUDcX5DuencY=", "gxSMVeDmrUmo9NB7GtF6TBsG+aeeQMMiiEMv1Kunf6Y=", "jn8bANNp2sx+Sp+Z7Y433YhNqKtTKZT031XopxxZv0M=", "eoLitadCnDVV5OSKtcbPzXKAfiUyAhZfKVp5GbK3iF8=", "nbwu4eFaTEohQspL5ZIJ3eyRHNPfSuxnVNz9Xu5TpZc=", "az1CKfkWLF/cCQ8P3sdN0R5kGNYtD71zknzX+tvY8Ak=", "CpMua9cXACziqHz6aTUrjDkqKpUsUT1DcTG5x1DaMnU=", "UIeE8oD+yJQq+Q1R5jEvzFFSau5zDZrsS/mVUyRLTaA=", "+5PDSCfH73a+Mh+M52pD/ibeV/+6Jx1iR3sQ8n4HbTg=", "VqaXCufgl/hXe/P8gIkngnSMWKXwOC6k86OzWvueMhA=", "DbfejiecIDhwi2wm7IbQZx6VC7e3HW0N1gDQPSclOCA=", "qpSf+Qe/gsv/P5CvtXPpmX2+yMu31d+7pec8GbFrNAE=", "SyupbOyj47ILK0Y8rgp1wGW5kgiwdjiHQ9qiYIT54Gk=", "mXnTCnuthDqPnLzCC0qK/aFv+2nP/2gFjriBSCXkb0I=", "qUntMIqyJQT/nCBxlembhckoRx/MiL9WTWduBrB37wo=", "mLwlynBqKdGYJnTq/cb07TDLXKlFyjCyMqDIYCUBX54=", "9aQWFdeXf4PpakEzJZKSULUauKavQvKf/aNcx4J6FS4=", "ZzDDpshUksNL1UpJ7gPFLiHmFR0R+caGqAJw03KplO4=", "m0XPluJjFg0WIMTIQDXkwN6JHqpQvKtWqyQRZqJ52io=", "qDhXQ2lzdSF+l9JkHy6D9O2gMCj32VVyctJPott+BNI=", "QA6a2PqF3K3axyclhR9tx0fNY4RtzhVIe2MNduilGH8=", "hEqWM0oA36uIG/J2BCXByZVAc/sfcIlH1RPWsEduh80=", "QeyIaTWs7P6pzBCXAMgXB1jQI8DdMlHwd4X9kYdXPt0=", "Meg2IdltfLCDh9UjmAHB9o61sRNOGfVhOc1SKWh03ag=", "bJoKkw4txbzmr57w6EHn+Mx8r3K2PPet50FYUXFCqG8=", "IEezjvzcd6VsU/T/yqw9NY1ZVeREkOgFjBHtKvX/tJQ=", "tvp8HqtbZaJTjIAru2pdyWwAMg01vujf/GxIX1vZowg=", "kx2hsV+sEo6lsbsH2/M0USk6Csc3MQGcC5bahKIG70Y=", "/SWrG343zBFtWZMg4CeaOSBsDcMQE1SuOM2Hy8ccWGA=", "UeK0nOm7TNupdxra2eJTm1Bbm/w2yeVhUb8xAZjtLwI=", "DQ2dhXZZFiiK3bQguQxEzxj1nBYT+m3xjeZb8OePzNc=", "HtWe5K5DJL1MMKkfe64b0XaCp4OCXvOd4nsrlKB77VM=", "ukJkwEoe/gn199EP6X+JDnJaRzTwL6N3XqW5bdpAUC8=", "6ksRjYXcDCmm2if6jHr/BgtjA5v15v/DmQEso9TzH/Q=", "oD44dJzI55XkiflF2tuw2ZnuS4Xvj3RXYmcxVNGFT50=", "bZfOtFF4jwMQY6BFJAuaF1/e0frbwmsUaxxcG65CScw=", "exdZc5PzpDmFEUN7srrrkUDuhW7TP24tMnD3K9fJw4o=", "E7ZxNnBOd3JSIiPuHQKsDt4AbP1Sh6w1tLFKrXP7r8g=", "kHCXUFzj6uAK3hRPyw8YQqNOqNo7OQ2d7rNulc1v3VQ=", "2KhIKMvV/SHRVExj4y4s3ARnKEnmipgH1Z//KNug+/Y=", "kSCs0k+yqb1qoht3ZOPH8RcyfuJV1kP60cU3ZeHkwcU=", "ijtFuREa+ppa8rTho2789devJIr/g75SfHwL9EnyIJU=", "gISMOCyvyUdnjCPyf85AsOWRf+6burhfPFqi0eljDz8=", "BowduY4+zP4lJjqT0BAMp6YJKGfgVTX4G4cQGolKi4g=", "FO1GI5335j2VxZvBlQHGV1bTvOhyeCJddPv/IaWRF4w=", "wegFIODA38AQjYG32+922/ZvptkFUQMpfFa/8qnNGyo=", "O3vrX4hovujZJOQ2+bnQ66+DB55ic8Cxoq9bfVbecnk=", "BPTLx4Faxh+K++P2YySjR+9mb13LOXfFuL40MagzN58=", "nbPdhgFV6N9ToMR8HirNuwGKtXTRaueswoRzLDzSu1g=", "hw++vDacSOiCKJUDcFJ3WpaBztzsGk6MJUBb8ezCl04=", "/wvXYkg6BRKlBrSbP0taMXpqxrrIuI6h0Ds8dOzmd5k=", "2O1wXTV55hdGSY2NqHMIEKZwG3Nd+eebWH7YmpU3Syg=", "E7MMBksF7IkdDYVIRPT7+r/CjO85W2qjjWFJOKr8LHg=", "Gj2IfwD1VewicnHA4+eWYqO8BtcthRs92c5ltPqA7wM=", "2AknU60jGjYqOS205aj46JbCmiJ54goHRl6WVN4EVDc=", "KpA65YN/Qj/xE5DF25U855c5av7OWeB6P4Y9eIK4M00=", "G2i0Su5UTqbNPhrE35ld7HGt8PQJe9wobWHX7UJBnAY=", "P7pg/BsCLA6TNkNl4meSrRRH+8pGKIc5nNs8SA/6uIs=", "ZTY4qXW5GiFOATGpErCM8nIlJf1wnIoDv3bPKVBUKD8=", "/k0COjN6GCE0pQRFg3R7wMEyTlWZ2bJ8VIhc3580k1c=", "sT8Q9eAagvvdAz8/U1YJuJhXEohbPknzBObj65EZZp4=", "wjangiACUGcEi/8i4cey+MB5JxSsIF5/+Fv/4DQzyrA=", "CeaYDgnjJMSOE1x96lrVkSQvmfGUGm/5Wtc5ml8RNRY=", "BIGA/rszAuUvvRTVatSZXipYcrsRzjEq3Yd6iQbIoj8=", "CXi1pR9bmNL3x98STukmCBFbFE8bopeg6ffIZmyjs6A=", "mLeE9URSy70mTW6syl1u9XmhSa2gGALmElpiddAZp3A=", "fNWZJJNTWp9qnfk0VU6SMlDLinZE90xuDV+uqcSU9kc=", "3Uh3dy3A4TiHdyItZI6XBAia2xi+wodb7o6KQWkyE04=", "6geMD3RZ2PejDP03IcBxzomuqUfXeigS1p72MEi7ISg=", "enQ26tScXFvxFH2JPAFWurEIa2kwMpBKYnYGMvJ3VYI=", "mRTGTcSzWsbq6K7SsgzjGzkRc/6xn7yAKlsjXZ8q+Uk=", "KD2UZ3Hi/2hHERlIicWvKhUjsb9twRDm1GaCprAN+J8=", "tOvYsYkzx3Gavz5pED3IHRtfJtWghwqPEsFKZRPrJRY=", "QLsLyY1X+NZb43zEcuRCTBQlnwPSDgyA/27edf0hTfY=", "FVt5sr5hrZdNDVcfwi9eCKF+bO65LcFutgTTsWlYfP0=", "y277yn6emAI8H8+znhTHVIwLcwg/jFxYRzSB726rjHQ=", "FCaao7BG5PjLeF8Pklm3hlXljhKhYeAmgsKMqLs2/wQ=", "DWIN1z/hIBD4Njz3uMVa9ahqPiwYuF68Y01kf6RNTx0=", "tJZi4bhihA8E0HbzozmHLn22Imrh7ZQ5ndoFjW4eQUI=", "ttripV/vl3Oux4VytGyZAbzHgaRQaOKqjjB/s3/9q6c=", "7U0TNoJh+KGT+FTjwrRwV0z4EnPrBLpoqlrwZxPjdfQ=", "+d4RvDmGuMg7Kk08ngwzySEBBOhBvUdBhX7svx3pD48=", "e17VaXElJAfuKTpRBdx3D7EGPeZyTv2geFGcXoH2T08=", "W+p2FYsC8/tazECeeRmTjgf0KyJgtp1n+0uFc/kqkdo=", "p0rIMiIFOF3rMmHvmHApzDz7sIN5iDgXxdbwQP94J9g=", "ZRQhtIU3eXgvprCwtPYRvUPP3stQ+xqV95xl1J6mZ7Y=", "lQ7A6h3jHGx+KEqZShk+LyMgllzJ3AyQkpw76Fb8FE0=", "hBFUvWI+0QS6X8YZiebHyA/4La6dcsS7KaHmOUxCsro=", "x8hefoPwIvpQJ4EiIRytg+f0Ps3MHEQLZ6McpEf6DHI=", "iqjj2nVR0pCUYSR6c6ku/9SCUNOlC3+eKo7jLRY2bBM=", "TKhLXKkN9rwebIhQBrT4Zi0c6+jvZdJ+Xw8+OitNc30=", "3A+y0FhDex8jLescbuLPOzqEmtPSLm0MHXRTlXSWk14=", "fvEq/0LCxBOHlqakqf2YD1CmIE6IloxfJA14P3btvI8=", "atfxjcYwi5cOT6Uft4zwbX/F/nKyUHLgNUMPkMb1DU8=", "vXNHUYfXh6Xfa+JCtm6h376pvhEXCj22QFLIvoGXrzw=", "qvYRkj3cDO7ZMdeq/zp+3iR09DxWsHkEi9/GiCR6MUg=", "rh0OF6rBn6cu1ntOs+/6JT9K4RuC1muUx0BRXdBH7TQ=", "1F+vsYmLbBmam9KV3J/+V09Yu1v3xQDyy199udZUrHc=", "PE7VuRL3XHkqkMgdb+w4Umx4TjXvUsq6Sc5xdRfDDew=", "xlDauab67Ab/ecaVp/EBhFKo3uPUCT3HCmGE82xtrpw=", "pOpHwCmWticWujQKhKpn+Xw6EMdRq7lnSoiBA8rNdQg=", "2YgGjq+vQ8HUpL11R02BOxIx5ixxerk6vzFI+zBthbM=", "tCspCDs0qd3DPupG7OBzt6+n2fxBe4oyt5zHCcTcATE=", "9Zyu+IrFyqbw4k/zbqy8Pp7O/RQ903Fs2sYrz8yR+5M=", "wPqUPTYGVxumTqnRGwZSgXtz1T05ZrrY7Qduojnma+o=", "hkI6LOI/7tioZVzYvHsAO/GLZejxEOAM8WBk++i5ktI=", "DDw0m9aMLQmGtMAVppDQPgT7veF2plp7YNnBEmRPBxo=", "n2vj8NQuMU0XVgchFgH9Jhz3U5NTaDNVZjNtUfIN/AQ=", "zbCwwPEMvsjlEKcSjvtZsADaPKW1IglKoLSMfs0qR20=", "OLilLpECo5xGRKbX0m4WXPbJOUG5+SbLWyKTvx2iHq0=", "3GZ3qJwodJr/pXWqApKzZY3GUVakpwfiDzFn2h/FBUo=", "gScN2MwVrKWzGwjz6PG+0miQff+bfOaIgF+gPrk4OqU=", "+Dy+xZKrVbdd/SPtJw3vMjZPwCdIndpQKWmabBbdvhs=", "5TrumlOQiB+az9MfYpqTLrVij0TCjSpOk22+ENbKXP4=", "GRO5XASENYXhlXQY+5SEOACeb30pbXZtRkS/qTcqsto=", "22WuKfnnkuyeaUrRvPltN3Tdjkr91m9L7pSW0JPuWag=", "vQY4Tor+WQ69qQOqGiUDzpymkeHVIUV38rvK0zpH6t0=", "Cxn66vlNiqei3lDu9ScVCs1DsL61mLhxNYFudX/4iJ4=", "1EfpCO3u7eio0/O/AQ3YhjGVXo9ZPY5o2W7F3X7+7C0=", "5+ddyNOAxk9gXywbKvBgLTx84HnnHOx7wSY+DzZuTBU=", "SswTtQeu1UosfM0uAD72LOTOW+ZWhiSRAoAOdXJtJuo=", "8LnUXhngs1fnx3lBC1cOJXkmhVIiVihEuhR002LdSVw=", "lPyGTqyvxrthKLkPmKBeGCHZf39F3pim3LHhx/Qa77w=", "elTNnCLj5h++mEKosNFJ+mVj/a6d3Wx2SjyCI0/65EY=", "FkaHUc17DxffiH+wF2ntktGuI4+PTLe7AYY3r+pwWkc=", "gFOWTvARm9OT2X6tCgv4cA/8ZAYgJSVuXkhE30UhAMw=", "6X5u3jMCwGigEyx3rkQK2rBKlsSYXB6yWR5OI6HG7TY=", "QBlV78QHNgGwDEvybyeuuPYNoxnm9ck3kVG+04YPstY="], "CachedAssets": {"tI1szlh7VCCOHxEDOsq/4iRElcFJjagev7EDC3nx3+I=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\css\\site.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hsrfjvnq1z", "Integrity": "cfn14Tmg4sAy4irvFiAShMG69r+VWLJS//ZCf/uvNeg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 865, "LastWriteTime": "2025-07-14T22:07:23.6589377+00:00"}, "+SFW1juKea6NWGL/MudYPZN82sDnpD39Cyi3f3URswo=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\favicon.ico", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "bqHYt98u29dZhxLwYtM7/cpukwwi+8+h/jC0IeJ0uvQ=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\images\\Alsultan-logo.jpg", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "images/Alsultan-logo#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ahdim8u5fj", "Integrity": "QtUzywDQE62QFxITh5qwpE1/gkdAHcIjo5vr90Jyga4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\Alsultan-logo.jpg", "FileLength": 96296, "LastWriteTime": "2019-05-11T15:56:20+00:00"}, "ANKx0Rbs4uyyNvjRTakvQZNvvfOw6IKxW8MYNn1b9Bc=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\js\\qz-tray.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "js/qz-tray#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3sv8558lwh", "Integrity": "HZTMlTnPoQPhfb70q4e4iM8GU7NRrRtrjD4+r4Pbboc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\qz-tray.js", "FileLength": 140, "LastWriteTime": "2025-07-23T13:19:53.2467028+00:00"}, "p7paXKTZnEfFed2hrNbM4Ebg0ez56lqLb3vL0lSmn28=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\js\\site.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-07T18:22:48.070244+00:00"}, "jfFs3MW7NmxGpk8uS0DkmIQe14I1XSlGzAFvjlYvdjM=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "7iMiQvN6EHcHALImPvDgDGOCKUwMM7PDIjN+wOxBqFs=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "IdQode7umclInMf/L/5w+PQG5T/g5NImfBvqcEJApYE=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "eV3iKsXXHizRRwIv1/4mynzIINy4gG9aAgloeLideIE=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "i1pKoewcOK0p0RNSPyFwqDGgYCgyg8cvxHAUJQhSooQ=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "a8vMxR4iZ+j0+4gcKmaamd7qFnVVuAnauvvd1+X7ZoA=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "PEAGW6jnlBEVHi6eVVGFccoHiT1gjlpLUDcX5DuencY=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "gxSMVeDmrUmo9NB7GtF6TBsG+aeeQMMiiEMv1Kunf6Y=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "jn8bANNp2sx+Sp+Z7Y433YhNqKtTKZT031XopxxZv0M=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "eoLitadCnDVV5OSKtcbPzXKAfiUyAhZfKVp5GbK3iF8=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "nbwu4eFaTEohQspL5ZIJ3eyRHNPfSuxnVNz9Xu5TpZc=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "az1CKfkWLF/cCQ8P3sdN0R5kGNYtD71zknzX+tvY8Ak=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "CpMua9cXACziqHz6aTUrjDkqKpUsUT1DcTG5x1DaMnU=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "UIeE8oD+yJQq+Q1R5jEvzFFSau5zDZrsS/mVUyRLTaA=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "+5PDSCfH73a+Mh+M52pD/ibeV/+6Jx1iR3sQ8n4HbTg=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "VqaXCufgl/hXe/P8gIkngnSMWKXwOC6k86OzWvueMhA=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "DbfejiecIDhwi2wm7IbQZx6VC7e3HW0N1gDQPSclOCA=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-07T18:22:47.9877265+00:00"}, "qpSf+Qe/gsv/P5CvtXPpmX2+yMu31d+7pec8GbFrNAE=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-07T18:22:48.0020805+00:00"}, "SyupbOyj47ILK0Y8rgp1wGW5kgiwdjiHQ9qiYIT54Gk=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-07T18:22:48.0020805+00:00"}, "mXnTCnuthDqPnLzCC0qK/aFv+2nP/2gFjriBSCXkb0I=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-07T18:22:48.0030964+00:00"}, "qUntMIqyJQT/nCBxlembhckoRx/MiL9WTWduBrB37wo=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-07T18:22:48.0030964+00:00"}, "mLwlynBqKdGYJnTq/cb07TDLXKlFyjCyMqDIYCUBX54=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-07T18:22:48.0040962+00:00"}, "9aQWFdeXf4PpakEzJZKSULUauKavQvKf/aNcx4J6FS4=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-07T18:22:48.0040962+00:00"}, "ZzDDpshUksNL1UpJ7gPFLiHmFR0R+caGqAJw03KplO4=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-07T18:22:48.0050935+00:00"}, "m0XPluJjFg0WIMTIQDXkwN6JHqpQvKtWqyQRZqJ52io=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "qDhXQ2lzdSF+l9JkHy6D9O2gMCj32VVyctJPott+BNI=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "QA6a2PqF3K3axyclhR9tx0fNY4RtzhVIe2MNduilGH8=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "hEqWM0oA36uIG/J2BCXByZVAc/sfcIlH1RPWsEduh80=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "QeyIaTWs7P6pzBCXAMgXB1jQI8DdMlHwd4X9kYdXPt0=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "Meg2IdltfLCDh9UjmAHB9o61sRNOGfVhOc1SKWh03ag=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "bJoKkw4txbzmr57w6EHn+Mx8r3K2PPet50FYUXFCqG8=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "IEezjvzcd6VsU/T/yqw9NY1ZVeREkOgFjBHtKvX/tJQ=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "tvp8HqtbZaJTjIAru2pdyWwAMg01vujf/GxIX1vZowg=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "kx2hsV+sEo6lsbsH2/M0USk6Csc3MQGcC5bahKIG70Y=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "/SWrG343zBFtWZMg4CeaOSBsDcMQE1SuOM2Hy8ccWGA=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "UeK0nOm7TNupdxra2eJTm1Bbm/w2yeVhUb8xAZjtLwI=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "DQ2dhXZZFiiK3bQguQxEzxj1nBYT+m3xjeZb8OePzNc=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "HtWe5K5DJL1MMKkfe64b0XaCp4OCXvOd4nsrlKB77VM=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-07T18:22:48.0186963+00:00"}, "ukJkwEoe/gn199EP6X+JDnJaRzTwL6N3XqW5bdpAUC8=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-07T18:22:48.0197053+00:00"}, "6ksRjYXcDCmm2if6jHr/BgtjA5v15v/DmQEso9TzH/Q=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-07T18:22:48.0207066+00:00"}, "oD44dJzI55XkiflF2tuw2ZnuS4Xvj3RXYmcxVNGFT50=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-07T18:22:48.0217038+00:00"}, "bZfOtFF4jwMQY6BFJAuaF1/e0frbwmsUaxxcG65CScw=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "exdZc5PzpDmFEUN7srrrkUDuhW7TP24tMnD3K9fJw4o=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "E7ZxNnBOd3JSIiPuHQKsDt4AbP1Sh6w1tLFKrXP7r8g=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "kHCXUFzj6uAK3hRPyw8YQqNOqNo7OQ2d7rNulc1v3VQ=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "2KhIKMvV/SHRVExj4y4s3ARnKEnmipgH1Z//KNug+/Y=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "kSCs0k+yqb1qoht3ZOPH8RcyfuJV1kP60cU3ZeHkwcU=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "ijtFuREa+ppa8rTho2789devJIr/g75SfHwL9EnyIJU=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "gISMOCyvyUdnjCPyf85AsOWRf+6burhfPFqi0eljDz8=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "BowduY4+zP4lJjqT0BAMp6YJKGfgVTX4G4cQGolKi4g=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "FO1GI5335j2VxZvBlQHGV1bTvOhyeCJddPv/IaWRF4w=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "wegFIODA38AQjYG32+922/ZvptkFUQMpfFa/8qnNGyo=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-07T18:22:48.0572065+00:00"}, "O3vrX4hovujZJOQ2+bnQ66+DB55ic8Cxoq9bfVbecnk=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}, "BPTLx4Faxh+K++P2YySjR+9mb13LOXfFuL40MagzN58=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "nbPdhgFV6N9ToMR8HirNuwGKtXTRaueswoRzLDzSu1g=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "hw++vDacSOiCKJUDcFJ3WpaBztzsGk6MJUBb8ezCl04=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "/wvXYkg6BRKlBrSbP0taMXpqxrrIuI6h0Ds8dOzmd5k=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-07T18:22:48.0227046+00:00"}, "2O1wXTV55hdGSY2NqHMIEKZwG3Nd+eebWH7YmpU3Syg=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-07T18:22:48.0373776+00:00"}, "E7MMBksF7IkdDYVIRPT7+r/CjO85W2qjjWFJOKr8LHg=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-07T18:22:48.0549302+00:00"}, "Gj2IfwD1VewicnHA4+eWYqO8BtcthRs92c5ltPqA7wM=": {"Identity": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "AccountingSystem.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AlsultanWeb\\accountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\wwwroot\\", "BasePath": "_content/AccountingSystem.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-07T18:22:48.006094+00:00"}}, "CachedCopyCandidates": {}}