﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AccountingSystem.Data.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {

        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "POSSessionDetails");

            migrationBuilder.DropTable(
                name: "tbl_Acc_Accounts");

            migrationBuilder.DropTable(
                name: "tblBarcodeSettings");

            migrationBuilder.DropTable(
                name: "tblConfig");

            migrationBuilder.DropTable(
                name: "tblCustomers");

            migrationBuilder.DropTable(
                name: "tblGLConfig");

            migrationBuilder.DropTable(
                name: "tblGroupFormPermissions");

            migrationBuilder.DropTable(
                name: "tblGroupPermissions");

            migrationBuilder.DropTable(
                name: "tblItems");

            migrationBuilder.DropTable(
                name: "tblItemsCategory");

            migrationBuilder.DropTable(
                name: "tblPayMethodTrx");

            migrationBuilder.DropTable(
                name: "tblSessionLogs");

            migrationBuilder.DropTable(
                name: "tblStockMovement");

            migrationBuilder.DropTable(
                name: "tblStockMovHeader");

            migrationBuilder.DropTable(
                name: "tblToolsInvoice");

            migrationBuilder.DropTable(
                name: "tblUserPOSItems");

            migrationBuilder.DropTable(
                name: "tblVendors");

            migrationBuilder.DropTable(
                name: "UserStores");

            migrationBuilder.DropTable(
                name: "tblPOSSessions");

            migrationBuilder.DropTable(
                name: "tblEmployees");

            migrationBuilder.DropTable(
                name: "tblForms");

            migrationBuilder.DropTable(
                name: "tblUsers");

            migrationBuilder.DropTable(
                name: "tblPOSDevices");

            migrationBuilder.DropTable(
                name: "tblPOSShifts");

            migrationBuilder.DropTable(
                name: "tblShops");

            migrationBuilder.DropTable(
                name: "tblGroupsAuth");
        }
    }
}
