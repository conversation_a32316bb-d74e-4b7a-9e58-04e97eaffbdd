@model IEnumerable<AccountingSystem.Models.POSInvoice>
@{
    ViewData["Title"] = "بحث المبيعات";
    var totalSales = ViewBag.TotalSales ?? 0M;
}

<div class="container mt-4">
    <h2 class="mb-4"><i class="fas fa-search"></i> بحث المبيعات</h2>
    <form method="get" class="row g-3 mb-4">
        <div class="col-md-2">
            <label class="form-label">رقم الفاتورة</label>
            <input type="text" name="invoiceNo" class="form-control" value="@ViewBag.InvoiceNo" />
        </div>
        <div class="col-md-2">
            <label class="form-label">العميل</label>
            <input type="text" name="customer" class="form-control" value="@ViewBag.Customer" />
        </div>
        <div class="col-md-2">
            <label class="form-label">المستخدم</label>
            <input type="text" name="user" class="form-control" value="@ViewBag.User" />
        </div>
        <div class="col-md-2">
            <label class="form-label">المستودع</label>
            <input type="text" name="warehouse" class="form-control" value="@ViewBag.Warehouse" />
        </div>
        <div class="col-md-2">
            <label class="form-label">التاريخ</label>
            <input type="date" name="date" class="form-control" value="@ViewBag.Date" />
        </div>
        <div class="col-md-2 d-flex align-items-end">
            <button type="submit" class="btn btn-primary w-100"><i class="fas fa-search"></i> بحث</button>
        </div>
    </form>

    <div class="mb-3">
        <span class="badge bg-success fs-5">إجمالي المبيعات: @totalSales.ToString("N2") ريال</span>
    </div>

    <div id="searchResults">
        @if (Model != null && Model.Any())
        {
            <table class="table table-bordered table-striped">
                <thead class="table-dark">
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>العميل</th>
                        <th>المستخدم</th>
                        <th>المستودع</th>
                        <th>الإجمالي النهائي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                @foreach (var invoice in Model)
                {
                    <tr>
                        <td>@invoice.TrxNo</td>
                        <td>@(invoice.TrxDate?.ToString("yyyy/MM/dd") ?? "غير محدد")</td>
                        <td>@invoice.PartnerName</td>
                        <td>@invoice.Cashier</td>
                        <td>@invoice.Store</td>
                        <td>@((invoice.TrxNetAmount ?? 0).ToString("N2"))</td>
                        <td>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="showPrintout(@invoice.TrxNo)">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </td>
                    </tr>
                }
                </tbody>
            </table>
        }
        else
        {
            <div class="alert alert-info">لا توجد نتائج للبحث الحالي.</div>
        }
    </div>

    <!-- Printout Modal -->
    <div class="modal fade" id="printoutModal" tabindex="-1" aria-labelledby="printoutModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="printoutModalLabel">معاينة الفاتورة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <iframe id="printoutFrame" src="" width="100%" height="600" style="border:none;"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function showPrintout(invoiceNo) {
            console.log('showPrintout called with invoiceNo:', invoiceNo);
            
            // Clear any previous content
            document.getElementById('printoutFrame').src = 'about:blank';
            
            // Show modal first
            var modal = new bootstrap.Modal(document.getElementById('printoutModal'));
            modal.show();
            
            // Set the iframe source with a small delay to ensure modal is shown
            setTimeout(function() {
                console.log('Setting iframe src to: /POS/ThermalReceipt/' + invoiceNo);
                document.getElementById('printoutFrame').src = '/POS/ThermalReceipt/' + invoiceNo;
            }, 100);
        }
    </script>
} 
