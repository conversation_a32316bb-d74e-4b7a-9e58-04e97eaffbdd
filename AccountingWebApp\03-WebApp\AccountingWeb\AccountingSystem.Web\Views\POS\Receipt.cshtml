@model AccountingSystem.Web.Models.ReceiptViewModel
@{
    ViewData["Title"] = "إيصال الفاتورة";
    Layout = null; // No layout for printing
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>إيصال الفاتورة رقم @Model.Invoice.TrxNo</title>
    <style>
        media print {
            body { margin: 0; padding: 10px; }
            .no-print { display: none !important; }
            .receipt { page-break-inside: avoid; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .receipt {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .store-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .customer-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        .items-table th {
            background-color: #007bff;
            color: white;
            padding: 8px;
            text-align: right;
            font-size: 12px;
        }
        
        .items-table td {
            padding: 6px 8px;
            border-bottom: 1px solid #eee;
            font-size: 12px;
        }
        
        .totals {
            border-top: 2px solid #007bff;
            padding-top: 15px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .final-total {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            margin-top: 10px;
        }
        
        .payment-info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }
        
        .qr-code {
            text-align: center;
            margin: 15px 0;
        }
        
        .qr-code img {
            max-width: 150px;
            height: auto;
        }
        
        .print-buttons {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="print-buttons no-print">
        <button class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button class="btn btn-secondary" onclick="window.close()">
            <i class="fas fa-times"></i> إغلاق
        </button>
        <a href="/POS/ThermalReceipt/@Model.Invoice.TrxNo" class="btn btn-secondary" target="_blank">
            <i class="fas fa-receipt"></i> إيصال حراري
        </a>
    </div>

    <div class="receipt">
        <!-- Header -->
        <div class="header">
            <div class="store-name">نظام السلطان المحاسبي</div>
            <div style="font-size: 14px; color: #666;">نقاط البيع</div>
            <div style="font-size: 12px; color: #999; margin-top: 5px;">
                المملكة العربية السعودية
            </div>
        </div>

        <!-- Invoice Information -->
        <div class="invoice-info">
            <div>
                <strong>رقم الفاتورة:</strong> @Model.Invoice.TrxNo
            </div>
            <div>
                <strong>التاريخ:</strong> @(Model.Invoice.TrxDate?.ToString("yyyy/MM/dd") ?? "غير محدد")
            </div>
        </div>
        <div class="invoice-info">
            <div>
                <strong>الوقت:</strong> @(Model.Invoice.TrxDate?.ToString("HH:mm") ?? "غير محدد")
            </div>
            <div>
                <strong>المتجر:</strong> @Model.Invoice.Store
            </div>
        </div>
        <div class="invoice-info">
            <div>
                <strong>الكاشير:</strong> @Model.Invoice.Cashier
            </div>
        </div>

        <!-- Customer Information -->
        @if (!string.IsNullOrEmpty(Model.Invoice.PartnerName))
        {
            <div class="customer-info">
                <div><strong>العميل:</strong> @Model.Invoice.PartnerName</div>
                @if (!string.IsNullOrEmpty(Model.Invoice.PartnerPhoneNo))
                {
                    <div><strong>الهاتف:</strong> @Model.Invoice.PartnerPhoneNo</div>
                }
            </div>
        }

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>الصنف</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.Items)
                {
                    <tr>
                        <td>@(await GetItemDescription(item.ItemNo))</td>
                        <td>@item.TrxQTY.ToString("N3") @item.UofM</td>
                        <td>@item.UnitPrice.ToString("N2")</td>
                        <td>@item.LineAmount.ToString("N2")</td>
                    </tr>
                }
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals">
            <div class="total-row">
                <span>عدد الأصناف:</span>
                <span>@Model.Items.Count</span>
            </div>
            <div class="total-row">
                <span>المجموع:</span>
                <span>@((Model.Invoice.TrxTotal ?? 0).ToString("N2")) ريال</span>
            </div>
            @if ((Model.Invoice.TrxDiscount ?? 0) > 0)
            {
                <div class="total-row">
                    <span>الخصم (@((Model.Invoice.TrxDiscount ?? 0).ToString("N2"))%):</span>
                    <span>-@((Model.Invoice.TrxDiscountValue ?? 0).ToString("N2")) ريال</span>
                </div>
            }
            <div class="total-row">
                <span>ضريبة القيمة المضافة (15%):</span>
                <span>@((Model.Invoice.TrxVAT ?? 0).ToString("N2")) ريال</span>
            </div>
            <div class="total-row final-total">
                <span>الإجمالي النهائي:</span>
                <span>@((Model.Invoice.TrxNetAmount ?? 0).ToString("N2")) ريال</span>
            </div>
        </div>

        <!-- Payment Information -->
        @if (Model.Payments.Any())
        {
            <div class="payment-info">
                <div style="font-weight: bold; margin-bottom: 8px;">طرق الدفع:</div>
                @foreach (var payment in Model.Payments)
                {
                    <div class="total-row">
                        <span>@GetPaymentMethodName(payment.Pay_mthd):</span>
                        <span>@payment.Pay_amnt.ToString("N2") ريال</span>
                    </div>
                }
            </div>
        }

        <!-- QR Code -->
        <div class="qr-code">
            <img src="/POS/QRCode/@Model.Invoice.TrxNo" alt="QR Code" style="width: 100px; height: 100px;" />
        </div>

        <!-- Footer -->
        <div class="footer">
            <div>شكراً لزيارتكم</div>
            <div>نتمنى لكم يوماً سعيداً</div>
            <div style="margin-top: 10px; font-size: 10px;">
                هذا الإيصال صالح للضريبة
            </div>
        </div>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() {
        //     window.print();
        // };
    </script>
</body>
</html>

@functions {
    private async Task<string> GetItemDescription(long itemNo)
    {
        // This would typically be injected as a service
        // For now, we'll return a placeholder
        return $"صنف رقم {itemNo}";
    }

    private string GetPaymentMethodName(int paymentMethod)
    {
        return paymentMethod switch
        {
            1 => "نقدي",
            2 => "بطاقة ائتمان",
            3 => "تحويل بنكي",
            _ => "غير محدد"
        };
    }
} 