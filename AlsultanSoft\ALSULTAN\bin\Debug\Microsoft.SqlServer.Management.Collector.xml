<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.SqlServer.Management.Collector</name>
    </assembly>
    <members>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectionItem">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionItem.Id">
            <summary>
            Collection item id
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionItem.Name">
            <summary>
            Collection item name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionItem.TypeName">
            <summary>
            The collector type name that this collection item implements
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionItem.CollectionFrequency">
            <summary>
            The collection frequency for this item if the set is running in CollectionMode "continuous"
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionItem.Parameters">
            <summary>
            An xml fragment representing the specific parameters for this collection item. It has to conform to the 
            schema of the collector type that this item implements
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.#ctor(Microsoft.SqlServer.Management.Collector.CollectionSet,System.String)">
            <summary>
            Create an instance of a collection item given the collection set and an item name
            <param name='set'>The parent collection set</param>
            <param name='name'>The name of the collection item</param>
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Alter">
            <summary>
            Alter a Collection Item by applying the changes to the configuration store
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Rename(System.String)">
            <summary>
            
            </summary>
            <param name="newName"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcRenamable#Rename(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="newKey"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.MarkForDrop(System.Boolean)">
            <summary>
            
            </summary>
            <param name="dropOnAlter"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Discover(Microsoft.SqlServer.Management.Sdk.Sfc.ISfcDependencyDiscoveryObjectSink)">
            <summary>
            Unused for now. Discovery for this should have been taken care of by the parent.
            </summary>
            <param name="sink"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.GetCreateScript(System.String)">
            <summary>
            Get a specific script to create a collection item that belongs to this set.
            It can either be called when the set is altered, in which case the set_id will be known,
            or when the set is being created, in which case it assumes the script for creating the 
            collection set has already stored its id in a declared variable with its name stored
            in the setParamName argument.
            </summary>
            <param name="setParamName">parameter name created for holding the set id, null if not needed</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.GetAlterScript">
            <summary>
            We will need this script as part of the alter action in collection set Alter(), hence the 
            "internal" modifier.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.GetDropScript">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.SetId(System.Int32)">
            <summary>
            This is needed for the collection set create method to be able to modify the item id
            </summary>
            <param name="id"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.CheckNewName(System.String)">
            <summary>
            Throws an exception if the new name is more than the maximum length, or if it is null
            </summary>
            <param name="newName"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectionItem.Key">
            <summary>
            A key class for identification
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.#ctor(Microsoft.SqlServer.Management.Collector.CollectionItem.Key)">
            <summary>
            
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.Name">
            <summary>
            Name is the main key property for a collection item
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.Equals(System.Object)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Collector.CollectionItem.Key)">
            <summary>
            Both sides are equal if they both null, 
            or Both are not null AND rhs is a Key AND the keys are equal
            </summary>
            <param name="obj"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.op_Equality(Microsoft.SqlServer.Management.Collector.CollectionItem.Key,System.Object)">
            <summary>
            Both sides are equal if they both null, 
            or Both are not null AND lhs is a Key AND the keys are equal
            </summary>
            <param name="lhs"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.op_Equality(Microsoft.SqlServer.Management.Collector.CollectionItem.Key,Microsoft.SqlServer.Management.Collector.CollectionItem.Key)">
            <summary>
            
            </summary>
            <param name="lhs"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Collector.CollectionItem.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.op_Inequality(Microsoft.SqlServer.Management.Collector.CollectionItem.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="lhs"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.op_Inequality(Microsoft.SqlServer.Management.Collector.CollectionItem.Key,Microsoft.SqlServer.Management.Collector.CollectionItem.Key)">
            <summary>
            
            </summary>
            <param name="lhs"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.GetHashCode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.ToString">
            <summary>
            Returns the appropriate string representation of the key for the purpose of default comparison
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.IsEqual(Microsoft.SqlServer.Management.Collector.CollectionItem.Key)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.Key.GetUrnFragment">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionItem.IdentityKey">
            <summary>
            Key property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionItem.Parent">
            <summary>
            Parent object in the om hierarchy; CollectionSet
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.GetObjectFactory">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.#ctor">
            <summary>
            This is only for the factory
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItem.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectionItemCollection">
            <summary>
            SFC Collection class for collection item objects
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItemCollection.#ctor(Microsoft.SqlServer.Management.Collector.CollectionSet)">
            <summary>
            Initializes a new instance of a CollectionItemCollection given a CollectionSet
            </summary>
            <param name="parent"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionItemCollection.Item(System.String)">
            <summary>
            Get the CollectionItem by name
            </summary>
            <param name="name">Item name</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItemCollection.GetElementFactoryImpl">
            <summary>
            Returns an object factory for CollectionItemCollection class
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItemCollection.AddCollectionItem(Microsoft.SqlServer.Management.Collector.CollectionItem)">
            <summary>
            Explicitly add an item to the collection.
            TODO: This is a temporary solution until SFC v2.
            </summary>
            <param name="item"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionItemCollection.RemoveCollectionItem(Microsoft.SqlServer.Management.Collector.CollectionItem)">
            <summary>
            Explicitly remove an item from the collection
            TODO: This is a temporary solution until SFC v2
            </summary>
            <param name="item"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectionSet">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectionSet.CollectionModes">
            <summary>
            Different execution modes for a collection set.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectionSet.CollectionModes.Cached">
            <summary>
            The collection part of the collection set executes continuously, while the upload part is 
            driven by a SQL Agent Schedule. The data is cached locally between scheduled uploads
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectionSet.CollectionModes.NonCached">
            <summary>
            Execution of the collection set is driven by a SQL Agent schedule. Data is collected and uploaded to the 
            Management Data Warehouse on the spot.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectionSet.ExecutionStatus">
            <summary>
            The status for an execution log entry.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectionSet.ExecutionStatus.Running">
            <summary>
            The operation is still running.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectionSet.ExecutionStatus.Finished">
            <summary>
            The operation has completed successfully.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectionSet.ExecutionStatus.Failed">
            <summary>
            The operation has completed with a failure.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectionSet.ExecutionStatus.Warning">
            <summary>
            The operation had a failure during upload but the most recent upload succeeded
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectionSet.RuntimeExecutionMode">
            <summary>
            Indicate whether the execution log entry corresponds to a collection or an upload 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectionSet.RuntimeExecutionMode.Collection">
            <summary>
            A collection operation.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectionSet.RuntimeExecutionMode.Upload">
            <summary>
            An upload operation.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.CollectionItems">
            <summary>
            The collection items belonging to this collection set
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.Id">
            <summary>
            Id of the collection set in the config store
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.UId">
            <summary>
            A GUID uniqely identifying the collection set
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.Name">
            <summary>
            Collection set name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.TargetName">
            <summary>
            Name of the target from which the collection set is collecting data
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.ScheduleName">
            <summary>
            Name of the SQL Agent schedule for the collection set. Used to drive the collection set upload
            in "Cached" colleciton mode, and to drive the snampshot collection in "Snapshot" collection mode
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.ProxyName">
            <summary>
            Name of the SQL Agent proxy account the collection set uses to connect to target and the Management Data 
            Warehouse
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.CollectionMode">
            <summary>
            Collection mode for the collection set. <see cref="T:Microsoft.SqlServer.Management.Collector.CollectionSet.CollectionModes"/>
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.LoggingLevel">
            <summary>
            Logging level for the collection set. 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.IsRunning">
            <summary>
            True if the collection set is currently executing
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.IsSystem">
            <summary>
            True if the set is a system collection set
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.IsTransactionPerformance">
            <summary>
            True if the set is one of defined Transaction Performance collection sets
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.Description">
            <summary>
            Free text description of the collection set
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.DaysUntilExpiration">
            <summary>
            Defines how long the collected data should be kept in the repository
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.State">
            <summary>
            Object state of collection set
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.#ctor">
            <summary>
            Default constructor used for deserialization and for the factory
            
            CollectionSet must have a public parameterless constructor in order to use it as parameter 'TDcInstance' in the generic type or 
            method 'Microsoft.SqlServer.Management.DataCollectionUITasks.DcTaskFormComponent TDcInstance
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.#ctor(Microsoft.SqlServer.Management.Collector.CollectorConfigStore,System.String)">
            <summary>
            Initialize an instance of a CollectionSet given a CollectorConfigStore object as a parent and a name
            <param name='store'>The parent config store</param>
            <param name='name'>The name of the collection set</param>
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Create">
            <summary>
            Create a collection set on the config store 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.ScriptAlter">
            <summary>
            Script alter for collection set objects 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Alter">
            <summary>
            Alter the collection set on the config store. Only the schedule, the target, the proxy, 
            and the description could be updated. Items can also be added dropped or altered
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Drop">
            <summary>
            Drop the collectoin set from the config store
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Rename(System.String)">
            <summary>
            
            </summary>
            <param name="newName"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcRenamable#Rename(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="newKey"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Stop">
            <summary>
            Stop collection from a running collection set
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Start">
            <summary>
            Start collection from a collection set
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Upload">
            <summary>
            Do an on demand upload on the collection set to the Management Data Warehouse. 
            Only meaningful if the collection mode is "Cached" and the set is currently running.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.RunOnce">
            <summary>
            Run a collection and upload once, on demand. A single snapshot of data will be collected and uploaded to the 
            Management Data Warehouse. Can only be used if the collection mode is "NonCached".
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Cleanup">
            <summary>
            This method cleans up all activities done while configuring/starting/running this collection set
            Cleanup tasks:
                Delete collect, upload jobs 
                Change set's state to out of the box settings
                Set Data collector to non-configured state if this collection set is the last running set
                Delete all collection set logs
            </summary>
            <remarks> This method does not delete the collection set from MSDB tables </remarks>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.PostCreate(System.Object)">
            <summary>
            A call back with the result of executing the create script.
            The execution result is a data set of tables.
            The first table contains the id and the uid of the newly created set,
            the following tables each contain an id of one of the collection items
            </summary>
            <param name="executionResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.PostAlter(System.Object)">
            <summary>
            This is called with the result of executing the alter script.
            Since we have indicated it to return a data set, we expect it to be so.
            The data set consists of a table of singletons, each with the id of a newly
            added items.
            </summary>
            <param name="executionResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.PostDrop(System.Object)">
            <summary>
            Called after drop is done.
            </summary>
            <param name="executionResult"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.StartStopUpload(System.String,System.Boolean,System.Boolean,System.String)">
            <summary>
            Will perform the main function for start/stop/upload.
            </summary>
            <param name="script"></param>
            <param name="modifyIsRunning"></param>
            <param name="isRunningVal"></param>
            <param name="exptnString"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.GetCreateScript">
            <summary>
            Scripting a collection set requires creating the set and all its collection items in one
            batch. So, it is comprised of several small sub-scripts, one for creating the set and one 
            for each of its items. 
            To preserve the data integrity, everything is lumped into one transaction. If any of the 
            sub-scripts fails the whole transactions is rolled back.
            The script return multiple rowsets. Each rowset represents the output id of the object that 
            created on the backend store. In the furture, it might also include the object GUID.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.GetCreateSetScript(System.String@)">
            <summary>
            
            </summary>
            <param name="setParamName">variable name in the script that holds the value of the set id</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.GetAlterScript">
            <summary>
            We need transactional symantics here for the same reasons we need them on the Create() interface.
            See comments for GetCreateScript()
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.GetAlterItemScript(Microsoft.SqlServer.Management.Collector.CollectionItem)">
            <summary>
            The script for a child item depends on its state, which can be one of three things:
            1- Pending: newly created, get a create script and mark the item as existing
            2- Existing: already existing on the backend, get an alter script and don't change state
            3- ToBeDropped: will be removed from the collection when parent altered or dropped, get a delete script and mark the item as dropped
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.GetDropScript">
            <summary>
            In the drop script, we don't need to have transactional symantics since the server
            will guarantee that all items belonging to a collection set are removed when the 
            set is removed. This is by virtue of the cascading delete constraint on the item.
            
            So, we only need to generate a drop script for the collection set
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.GetStartScript">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.GetCleanupScript">
            <summary>
            Gets script to cleanup a collection set
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.CreateRemoveScript">
            <summary>
            Creates script to cleanup a collection set
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.CheckNewName(System.String)">
            <summary>S
            Throws an exception if the new name is more than the maximum length, or if it is null
            </summary>
            <param name="newName"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.AssertCorrectNumberOfTables(System.Data.DataSet)">
            <summary>
            makes sure the alter script returned the correct number of tables
            </summary>
            <param name="ds"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.EnumCollectionSetExecutionHistory">
            <summary>
            Gets the top-level history records for given collection set
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.EnumCollectionSetExecutionHistory(System.Int64)">
            <summary>
            Gets history records for given collection set for a given parent log id
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.EnumCollectionSetExecutionHistoryDetail(System.Int64)">
            <summary>
            Gets detail records for given collection set execution (logId)
            </summary>
            <param name="logId"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectionSet.Key">
            <summary>
            A key class for identification
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.#ctor(Microsoft.SqlServer.Management.Collector.CollectionSet.Key)">
            <summary>
            
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.Name">
            <summary>
            Name is the key property for a collection set
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.Equals(System.Object)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Collector.CollectionSet.Key)">
            <summary>
            Both sides are equal if they both null, 
            or Both are not null AND rhs is a Key AND the keys are equal
            </summary>
            <param name="obj"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.op_Equality(Microsoft.SqlServer.Management.Collector.CollectionSet.Key,System.Object)">
            <summary>
            Both sides are equal if they both null, 
            or Both are not null AND lhs is a Key AND the keys are equal
            </summary>
            <param name="lhs"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.op_Equality(Microsoft.SqlServer.Management.Collector.CollectionSet.Key,Microsoft.SqlServer.Management.Collector.CollectionSet.Key)">
            <summary>
            
            </summary>
            <param name="lhs"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Collector.CollectionSet.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.op_Inequality(Microsoft.SqlServer.Management.Collector.CollectionSet.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="lhs"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.op_Inequality(Microsoft.SqlServer.Management.Collector.CollectionSet.Key,Microsoft.SqlServer.Management.Collector.CollectionSet.Key)">
            <summary>
            
            </summary>
            <param name="lhs"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.GetHashCode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.ToString">
            <summary>
            Returns the appropriate string representation of the key for the purpose of default comparison
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.IsEqual(Microsoft.SqlServer.Management.Collector.CollectionSet.Key)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.Key.GetUrnFragment">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.IdentityKey">
            <summary>
            Key property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSet.Parent">
            <summary>
            Parent object in the object model hierarchy; CollectorConfigStore
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.GetObjectFactory">
            <summary>
            Returns an object factory for the CollectionSet class
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSet.GetLastUploadTime">
            <summary>
            Gets the time last successful upload finished
            </summary>
            <returns>The time last upload finished successfully or DateTime.MinValue if none</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectionSetCollection">
            <summary>
            SFC Collection class for collection sets
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSetCollection.#ctor(Microsoft.SqlServer.Management.Collector.CollectorConfigStore)">
            <summary>
            Instantiate a new collection given a config store object as a parent
            </summary>
            <param name="parent"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectionSetCollection.Item(System.String)">
            <summary>
            Returns a collection set by name
            </summary>
            <param name="name">Name of the collection set</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSetCollection.GetElementFactoryImpl">
            <summary>
            Returns an object factory for the CollectionSet class
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectionSetExtender">
            <summary>
            Extender of CollectionSet for UI
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSetExtender.#ctor">
            <summary>
            default ctor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSetExtender.#ctor(Microsoft.SqlServer.Management.Collector.CollectionSet)">
            <summary>
            ctor. Takes parent CollectionSet object to aggregate on
            </summary>
            <param name="collectionSet"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectionSetExtender.#ctor(Microsoft.SqlServer.Management.Collector.CollectorConfigStore,System.String)">
            <summary>
            ctor. Create a new CollectionSet object and aggregates on it.
            </summary>
            <param name="collectorConfigStore"></param>
            <param name="name"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectorConfigStore">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.TransactionPerformanceCollectors">
            <summary>
            Transaction performance collectors are marked in OM by their GUIDs
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.MDWInstance">
            <summary>
            The name of the instance hosting the Management Data Warehouse
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.MDWDatabase">
            <summary>
            The name of the database hosting the Management Data Warehouse
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.CacheDirectory">
            <summary>
            The temporary directory where the data between the collection and upload packages
            of each collector type is stored.
            If this value is Null, it will default at execution time to the value stored in the
            %TEMP% environment variable.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.CacheWindow">
            <summary>
            This is the retention window of the locally collected data in the staging area
            between the collection and upload packages. 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Enabled">
            <summary>
            True if the Collector is enabled
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.ConfigureTransactionPerformanceCollectors(Microsoft.Data.SqlClient.SqlConnection)">
            <summary>
            Method creates transaction performance collection sets in case they don't exist
            </summary>
            <exception cref="T:Microsoft.SqlServer.Management.Collector.CollectorException"></exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.#ctor">
            <summary>
            Creates a new instance of the config store
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.#ctor(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Creates a new instance of the config store given a connection to the server hosting the collector
            </summary>
            <param name="connection"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.CollectionSets">
            <summary>
            Returns a collection of all the collection sets currently defined in the config store
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Alter">
            <summary>
            Update the config store parameters on the server
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.EnumTypes">
            <summary>
            Return a collection of all the defined collector types on th server
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.EnableCollector">
            <summary>
            Enable the collector
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.DisableCollector">
            <summary>
            Disable the collector
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.RethrowErrorScript">
            <summary>
            returns a script that will rethrow the error that brought the execution into the catch block
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.IsCleanupProcSupportedOnServer">
            <summary>
            Returns true if SQL Version supports cleanup of one collection set
            using msdb stored procedures
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.AlterUID(System.Guid,System.Guid)">
            <summary>
            Modifies syscollector_collection_sets_internal and changes a uid of a given collector
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.name">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.#ctor">
            <summary>
            Default constructor for generic Key generation. Caller has to remember to set Root.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.#ctor(Microsoft.SqlServer.Management.Sdk.Sfc.ISfcDomain)">
            <summary>
            Internal constructor
            </summary>
            <param name="root"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.Name">
            <summary>
            Name is the key property for a collection set
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.Equals(System.Object)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key)">
            <summary>
            Both sides are equal if they both null, 
            or Both are not null AND rhs is a Key AND the keys are equal
            </summary>
            <param name="obj"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.op_Equality(Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key,System.Object)">
            <summary>
            Both sides are equal if they both null, 
            or Both are not null AND lhs is a Key AND the keys are equal
            </summary>
            <param name="lhs"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.op_Equality(Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key,Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key)">
            <summary>
            
            </summary>
            <param name="lhs"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.IsEqual(Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.op_Inequality(Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="lhs"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.op_Inequality(Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key,Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key)">
            <summary>
            
            </summary>
            <param name="lhs"></param>
            <param name="rhs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.GetHashCode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Key.GetUrnFragment">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.CreateIdentityKey">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.IdentityKey">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.GetChildCollection(System.String)">
            <summary>
            
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#GetConnection">
            <summary>
            Get the current connection to query on.
            Return a connection supporting a single serial query, so the query must end before another one may begin.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#SetConnection(Microsoft.SqlServer.Management.Common.ISfcConnection)">
            <summary>
            Sets the active connection.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#GetConnection(Microsoft.SqlServer.Management.Sdk.Sfc.SfcObjectQueryMode)">
            <summary>
            Get the current connection to query on.
            Return a connection supporting either a single serial query or multiple simultaneously open queries as requested.
            </summary>
            <param name="activeQueriesMode"></param>
            <returns>The connection to use, or null to use Cache mode. Cache mode avoids connection and open data reader issues.</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#ConnectionContext">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetType(System.String)">
            <summary>
            
            </summary>
            <param name="typeName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetExecutionEngine">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetKey(Microsoft.SqlServer.Management.Sdk.Sfc.IUrnFragment)">
            <summary>
            
            </summary>
            <param name="urnFragment"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetTypeMetadata(System.String)">
            <summary>
            
            </summary>
            <param name="typeName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#UseSfcStateManagement">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomainLite#GetLogicalVersion">
            <summary>
            Returns the logical version of the domain
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomainLite#DomainName">
            <summary>
            The name of the domain used to distinguish it from other domains. This is usually the end of the namespace qualifier.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomainLite#DomainInstanceName">
            <summary>
            The logical name of a domain instance usually derived from the connection and domain information.
            This name does not have to be unique on the client, but should be different whenever the server representation would be.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.CreateMDWTable(Microsoft.Data.SqlClient.SqlConnection,System.String,System.Collections.Generic.List{Microsoft.Data.SqlClient.SqlParameter})">
            <summary>
            Creates mdw tables, indexes and foreign keys for a given dbname and a set of columns
            </summary>
            <remarks>
            Method creates a table on the MDW database using SQLCommand. This results in CA2100 since a string is used 
            for command text. But, this string has no user input in it, so it's safe to suppress the warning.
            </remarks>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.CreateStoredProcCollector(Microsoft.Data.SqlClient.SqlConnection)">
            <summary>
            Create stored procedure usage analysis collector
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorConfigStore.CreateTableAnalysisCollector(Microsoft.Data.SqlClient.SqlConnection)">
            <summary>
            Create table analysis collector
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.CollectorException">
             <summary>
             This is the base class for all Collector exceptions. 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorException.#ctor">
            <summary>
            Create an instance of the exception
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorException.#ctor(System.String)">
            <summary>
            Create an instance of the exception given an error message
            </summary>
            <param name="message">Error message</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorException.#ctor(System.String,System.Exception)">
            <summary>
            Creates an instance of the exception given an error message and another throwing exception instance
            </summary>
            <param name="message">Error message</param>
            <param name="innerException">A throwing exception</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Creates an instance of the exception
            </summary>
            <param name="info"></param>
            <param name="context"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorException.ProdVer">
            <summary>
            Version of the product
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.CollectorException.SetHelpContext(System.String)">
            <summary>
            
            </summary>
            <param name="resource"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.CollectorException.HelpLink">
            <summary>
            will output a link to the help web site
            <!--http://www.microsoft.com/products/ee/transform.aspx?ProdName=Microsoft%20SQL%20Server&ProdVer=09.00.0000.00&EvtSrc=MSSQLServer&EvtID=15401-->
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.Helpers">
            <summary>
            This class contains a number of helpers methods and constants that are useful for all Collector classes
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.Helpers.CheckPropertyValueLength(System.String,System.Int32,System.String)">
            <summary>
            Checks the value of a certain string property to make sure it is within its specified length.
            If the check fails, throws a collector exception.
            </summary>
            <param name="value">string value of the properyt</param>
            <param name="maxLength"></param>
            <param name="propertyName"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter">
            <summary>
            This is copied from the Dmf proc formatter class with many addtions/modifications
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.SprocArg">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.SprocArg.#ctor(System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            
            </summary>
            <param name="name"></param>
            <param name="property"></param>
            <param name="required"></param>
            <param name="output"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.SprocArg.#ctor(System.String,System.Boolean)">
            <summary>
            This constructor is used when a parameter is not a
            property on an object but will be provided during the
            call to GenerateScript as a RuntimeArg.
            </summary>
            <param name="name"></param>
            <param name="required"></param>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.SprocArg.argName">
            
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.SprocArg.property">
            
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.SprocArg.required">
            
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.SprocArg.output">
            
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.SprocArg.outputParamName">
            
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.RuntimeArg">
            <summary>
            This struct is used for arguments that are not based on a
            Property inside of the object, but on some arbitrary
            value.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.RuntimeArg.#ctor(System.Type,System.Object)">
            <summary>
            </summary>
            <param name="type"></param>
            <param name="value"></param>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.RuntimeArg.type">
            
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.RuntimeArg.value">
            
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.RuntimeArg.isScript">
            <summary>
            true if added because of a script type argument, if so, ignore the type and print out the value literally
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.ScriptTypeArg">
            <summary>
            This struct is used for arguments that are generated using an arbitrary script.
            The script is assumed to be correct and properly escaped.
            The script is a string format that takes one parameter to be filled by the name of 
            the generated variable name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.ScriptTypeArg.#ctor(System.String,System.Type,System.String)">
            <summary>
            Init with the script
            </summary>
            <param name="name"></param>
            <param name="type"></param>
            <param name="script"></param>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.ScriptTypeArg.argName">
            <summary>
            The name of the argument should match the name of the procedure argument name
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.ScriptTypeArg.type">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.Procedure">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.Arguments">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.HasOutput">
            <summary>
            Procedure defines OUTPUT parameter and the query returns a value
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.GenerateScript(Microsoft.SqlServer.Management.Sdk.Sfc.SfcInstance)">
            <summary>
            
            </summary>
            <param name="sfcObject"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.GenerateScript(Microsoft.SqlServer.Management.Sdk.Sfc.SfcInstance,System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.RuntimeArg})">
            <summary>
            
            </summary>
            <param name="sfcObject"></param>
            <param name="runtimeArgs"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.GenerateScript(Microsoft.SqlServer.Management.Sdk.Sfc.SfcInstance,System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.RuntimeArg},System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Collector.TsqlProcFormatter.ScriptTypeArg})">
            <summary>
            
            </summary>
            <param name="sfcObject"></param>
            <param name="runtimeArgs"></param>
            <param name="scriptArgs"></param>
            <returns></returns>
        </member>
        <member name="T:HighestIncompatibleMDWVersion">
            <summary>
            This is a singleton class that includes the highest version of the MDW database that is 
            incompatible with the current collector.
            Whenever an MDW ships with a breaking schema/API change from the previous version, the value
            has to change to reflect this.
            
            </summary>
        </member>
        <member name="P:HighestIncompatibleMDWVersion.Version">
            <summary>
            The highest MDW database version not compatible with this version of the collector
            </summary>
        </member>
    </members>
</doc>
