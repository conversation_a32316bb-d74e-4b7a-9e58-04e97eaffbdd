using System.ComponentModel.DataAnnotations;

namespace AccountingSystem.Services.ViewModels
{
    public class POSSessionViewModel
    {
        public int SessionID { get; set; }

        [Display(Name = "رقم الجلسة")]
        public string SessionSN { get; set; } = string.Empty;

        [Display(Name = "المتجر")]
        public string? ShopName { get; set; }

        [Display(Name = "الجهاز")]
        public string? DeviceName { get; set; }

        [Display(Name = "الوردية")]
        public string? ShiftName { get; set; }

        [Display(Name = "فتح بواسطة")]
        public string? OpenedBy { get; set; }

        [Display(Name = "وقت الفتح")]
        public DateTime? OpenTime { get; set; }

        [Display(Name = "إغلاق بواسطة")]
        public string? ClosedBy { get; set; }

        [Display(Name = "وقت الإغلاق")]
        public DateTime? CloseTime { get; set; }

        [Display(Name = "الحالة")]
        public string? Status { get; set; }

        [Display(Name = "المبلغ النهائي")]
        [DataType(DataType.Currency)]
        public decimal? ClosingCash { get; set; }

        [Display(Name = "ملاحظات")]
        public string? Note { get; set; }

        // Helper properties
        public bool IsOpen => Status == "Open";
        public bool IsClosed => Status == "Closed";
        public string StatusBadgeClass => Status switch
        {
            "Open" => "badge bg-success",
            "Closed" => "badge bg-secondary",
            _ => "badge bg-warning"
        };
    }

    public class POSSessionCreateViewModel
    {
        [Required(ErrorMessage = "يرجى اختيار المتجر")]
        [Display(Name = "المتجر")]
        public int ShopID { get; set; }

        [Required(ErrorMessage = "يرجى اختيار الجهاز")]
        [Display(Name = "الجهاز")]
        public int DeviceID { get; set; }

        [Required(ErrorMessage = "يرجى اختيار الوردية")]
        [Display(Name = "الوردية")]
        public int ShiftID { get; set; }

        [Display(Name = "الرصيد الافتتاحي")]
        [DataType(DataType.Currency)]
        public decimal OpeningCash { get; set; }
    }

    public class POSSessionCloseViewModel
    {
        public int SessionID { get; set; }

        [Display(Name = "رقم الجلسة")]
        public string SessionSN { get; set; } = string.Empty;

        [Required(ErrorMessage = "يرجى إدخال المبلغ النهائي")]
        [Display(Name = "المبلغ النهائي")]
        [DataType(DataType.Currency)]
        public decimal ClosingCash { get; set; }

        [Display(Name = "ملاحظات")]
        public string? Note { get; set; }
    }

    public class POSSessionFilterViewModel
    {
        [Display(Name = "المتجر")]
        public int? ShopID { get; set; }

        [Display(Name = "الجهاز")]
        public int? DeviceID { get; set; }

        [Display(Name = "الوردية")]
        public int? ShiftID { get; set; }

        [Display(Name = "الحالة")]
        public string Status { get; set; } = "All";
    }

    public class ShopViewModel
    {
        public int SN { get; set; }
        public string StoreName { get; set; }
    }

    public class POSDeviceViewModel
    {
        public int DeviceID { get; set; }
        public string DeviceName { get; set; } = string.Empty;
    }

    public class POSShiftViewModel
    {
        public int ShiftID { get; set; }
        public string ShiftName { get; set; } = string.Empty;
    }
} 