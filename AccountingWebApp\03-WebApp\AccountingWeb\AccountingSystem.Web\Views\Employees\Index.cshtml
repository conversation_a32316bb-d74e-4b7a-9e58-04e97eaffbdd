@model IEnumerable<AccountingSystem.Models.Employee>
@{
    ViewData["Title"] = "إدارة الموظفين";
    Layout = "_Layout";
}

<div class="container-fluid" dir="rtl">
    <div class="page-header">
        <h1 class="page-title"><i class="fas fa-users-cog"></i> @ViewData["Title"]</h1>
        <div class="page-options">
            <button type="button" class="btn btn-primary" onclick="openCreateModal()">
                <i class="fas fa-plus"></i> إضافة موظف جديد
            </button>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show">@TempData["SuccessMessage"]<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show">@TempData["ErrorMessage"]<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>
        }
    </div>

    <div class="card">
        <div class="card-header"><h3 class="card-title">قائمة الموظفين</h3></div>
        <div class="card-body">
            <table class="table table-striped table-hover" id="employeesTable">
                <thead class="table-dark">
                    <tr>
                        <th>رقم الموظف</th>
                        <th>اسم الموظف</th>
                        <th>الراتب</th>
                        <th>أنشئ بواسطة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var emp in Model)
                    {
                        <tr>
                            <td>@emp.Id</td>
                            <td>@emp.Name</td>
                            <td>@emp.Salary?.ToString("C")</td>
                            <td>@emp.CreatedBy</td>
                            <td>@emp.CreatedOn?.ToString("yyyy/MM/dd")</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="openEditModal(@emp.Id)" title="تعديل"><i class="fas fa-edit"></i></button>
                                <button class="btn btn-sm btn-outline-danger" onclick="confirmDelete(@emp.Id)" title="حذف"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Employee Modal -->
<div class="modal fade" id="employeeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="employeeForm" method="post">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="employeeId" class="form-label">رقم الموظف</label>
                        <input type="number" class="form-control" id="employeeId" name="Id" readonly />
                    </div>
                    <div class="mb-3">
                        <label for="employeeName" class="form-label">اسم الموظف</label>
                        <input type="text" class="form-control" id="employeeName" name="Name" required />
                    </div>
                    <div class="mb-3">
                        <label for="employeeSalary" class="form-label">الراتب</label>
                        <input type="number" step="0.01" class="form-control" id="employeeSalary" name="Salary" />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Form -->
<form id="deleteForm" asp-controller="Employees" asp-action="DeleteEmployee" method="post" class="d-none">
    <input type="hidden" id="deleteEmployeeId" name="id" />
</form>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#employeesTable').DataTable({
                language: { url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json' }
            });
        });

        var myModal = new bootstrap.Modal(document.getElementById('employeeModal'));

        function openCreateModal() {
            $('#employeeForm').attr('action', '/Employees/Create');
            $('#modalTitle').text('إضافة موظف جديد');
            $('#employeeForm')[0].reset();

            $.get('/Employees/NextEmployeeNo', function (data) {
                $('#employeeId').val(data.nextNo);
            });

            myModal.show();
        }

        function openEditModal(id) {
            $('#employeeForm').attr('action', '/Employees/Edit');
            $('#modalTitle').text('تعديل بيانات الموظف');
            $('#employeeForm')[0].reset();

            $.get(`/Employees/Details/${id}`, function (data) {
                $('#employeeId').val(data.id);
                $('#employeeName').val(data.name);
                $('#employeeSalary').val(data.salary);
                myModal.show();
            });
        }

        function confirmDelete(id) {
            if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                $('#deleteEmployeeId').val(id);
                $('#deleteForm').submit();
            }
        }
    </script>
} 