using Microsoft.EntityFrameworkCore;
using AccountingSystem.Data;
using AccountingSystem.Models;
using AccountingSystem.Services.ViewModels;

namespace AccountingSystem.Services
{
    public interface IPOSSessionService
    {
        Task<List<POSSessionViewModel>> GetAllSessionsAsync(POSSessionFilterViewModel filter);
        Task<POSSessionViewModel?> GetSessionByIdAsync(int sessionId);
        Task<bool> CreateSessionAsync(POSSessionCreateViewModel model, string openedBy);
        Task<bool> CloseSessionAsync(POSSessionCloseViewModel model, string closedBy);
        Task<List<ShopViewModel>> GetAllShopsAsync();
        Task<List<POSDeviceViewModel>> GetAllDevicesAsync();
        Task<List<POSShiftViewModel>> GetAllShiftsAsync();
        Task<string> GenerateSessionSNAsync(int shiftId, int deviceId);
        Task<bool> SessionExistsAsync(int sessionId);
        Task<bool> IsSessionOpenAsync(int sessionId);
    }

    public class POSSessionService : IPOSSessionService
    {
        private readonly AccountingDbContext _context;

        public POSSessionService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<List<POSSessionViewModel>> GetAllSessionsAsync(POSSessionFilterViewModel filter)
        {
            var query = _context.POSSessions
                .Include(s => s.Shop)
                .Include(s => s.Device)
                .Include(s => s.Shift)
                .AsQueryable();

            // Apply filters
            if (filter.ShopID.HasValue)
                query = query.Where(s => s.ShopID == filter.ShopID.Value);

            if (filter.DeviceID.HasValue)
                query = query.Where(s => s.DeviceID == filter.DeviceID.Value);

            if (filter.ShiftID.HasValue)
                query = query.Where(s => s.ShiftID == filter.ShiftID.Value);

            if (filter.Status != "All")
                query = query.Where(s => s.Status == filter.Status);

            var sessions = await query
                .OrderByDescending(s => s.SessionID)
                .ToListAsync();

            return sessions.Select(s => ToViewModel(s)).ToList();
        }

        public async Task<POSSessionViewModel?> GetSessionByIdAsync(int sessionId)
        {
            var session = await _context.POSSessions
                .Include(s => s.Shop)
                .Include(s => s.Device)
                .Include(s => s.Shift)
                .FirstOrDefaultAsync(s => s.SessionID == sessionId);

            return session != null ? ToViewModel(session) : null;
        }

        public async Task<bool> CreateSessionAsync(POSSessionCreateViewModel model, string openedBy)
        {
            try
            {
                var sessionSN = await GenerateSessionSNAsync(model.ShiftID, model.DeviceID);

                var session = new POSSession
                {
                    SessionSN = sessionSN,
                    ShopID = model.ShopID,
                    DeviceID = model.DeviceID,
                    ShiftID = model.ShiftID,
                    OpenedBy = openedBy,
                    OpenTime = DateTime.Now,
                    Status = "Open"
                };

                _context.POSSessions.Add(session);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> CloseSessionAsync(POSSessionCloseViewModel model, string closedBy)
        {
            try
            {
                var session = await _context.POSSessions.FirstOrDefaultAsync(s => s.SessionID == model.SessionID);
                if (session == null || session.Status == "Closed") return false;

                session.CloseTime = DateTime.Now;
                session.ClosedBy = closedBy;
                session.ClosingCash = model.ClosingCash;
                session.Note = model.Note;
                session.Status = "Closed";

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<ShopViewModel>> GetAllShopsAsync()
        {
            var shops = await _context.Shops
                .OrderBy(s => s.StoreName)
                .ToListAsync();

            return shops.Select(s => new ShopViewModel
            {
                SN = s.SN,
                StoreName = s.StoreName
            }).ToList();
        }

        public async Task<List<POSDeviceViewModel>> GetAllDevicesAsync()
        {
            var devices = await _context.POSDevices
                .OrderBy(d => d.DeviceName)
                .ToListAsync();

            return devices.Select(d => new POSDeviceViewModel
            {
                DeviceID = d.DeviceID,
                DeviceName = d.DeviceName
            }).ToList();
        }

        public async Task<List<POSShiftViewModel>> GetAllShiftsAsync()
        {
            var shifts = await _context.POSShifts
                .OrderBy(s => s.ShiftName)
                .ToListAsync();

            return shifts.Select(s => new POSShiftViewModel
            {
                ShiftID = s.ShiftID,
                ShiftName = s.ShiftName
            }).ToList();
        }

        public async Task<string> GenerateSessionSNAsync(int shiftId, int deviceId)
        {
            var datePart = DateTime.Now.ToString("yyyyMMdd");
            var seqNum = 1;

            var existingCount = await _context.POSSessions
                .Where(s => s.OpenTime.HasValue && 
                           s.OpenTime.Value.Date == DateTime.Today &&
                           s.ShiftID == shiftId &&
                           s.DeviceID == deviceId)
                .CountAsync();

            seqNum = existingCount + 1;

            return $"{datePart}-{shiftId:D2}-{deviceId:D2}-{seqNum:D3}";
        }

        public async Task<bool> SessionExistsAsync(int sessionId)
        {
            return await _context.POSSessions
                .AnyAsync(s => s.SessionID == sessionId);
        }

        public async Task<bool> IsSessionOpenAsync(int sessionId)
        {
            var session = await _context.POSSessions
                .FirstOrDefaultAsync(s => s.SessionID == sessionId);

            return session?.Status == "Open";
        }

        private POSSessionViewModel ToViewModel(POSSession session)
        {
            return new POSSessionViewModel
            {
                SessionID = session.SessionID,
                SessionSN = session.SessionSN,
                ShopName = session.Shop != null ? session.Shop.StoreName : "N/A",
                DeviceName = session.Device != null ? session.Device.DeviceName : "N/A",
                ShiftName = session.Shift != null ? session.Shift.ShiftName : "N/A",
                OpenedBy = session.OpenedBy,
                OpenTime = session.OpenTime,
                ClosedBy = session.ClosedBy,
                CloseTime = session.CloseTime,
                Status = session.Status,
                ClosingCash = session.ClosingCash,
                Note = session.Note
            };
        }
    }
} 