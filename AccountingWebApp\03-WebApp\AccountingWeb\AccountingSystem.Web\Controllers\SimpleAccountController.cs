using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using System.Security.Claims;
using Microsoft.Data.SqlClient;
using AccountingSystem.Web.Models;
using AccountingSystem.Web.Utilities;

namespace AccountingSystem.Web.Controllers
{
    public class SimpleAccountController : Controller
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SimpleAccountController> _logger;

        public SimpleAccountController(IConfiguration configuration, ILogger<SimpleAccountController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        [HttpGet]
        public IActionResult Login(string? returnUrl = null)
        {
            // If user is already authenticated, redirect to main page
            if (User.Identity?.IsAuthenticated == true)
            {
                return RedirectToAction("Index", "SimpleDashboard");
            }

            ViewData["ReturnUrl"] = returnUrl;
            return View(new LoginViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginViewModel model, string? returnUrl = null)
        {
            ViewData["ReturnUrl"] = returnUrl;

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var connectionString = _configuration.GetConnectionString("AccountingDatabase");
                
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Simple authentication query similar to VB.NET
                    var query = @"
                        SELECT u.Username, u.Password, u.GroupID, g.GroupName, u.SN
                        FROM tblUsers u 
                        LEFT JOIN tblGroupsAuth g ON u.GroupID = g.GroupID 
                        WHERE u.Username = @Username";
                    
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Username", model.Username);
                        
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                var storedPassword = reader["Password"]?.ToString() ?? "";

                                // Use VB.NET hash compatibility utility for password verification
                                bool passwordMatches = VBNetHashCompatibility.VerifyVBNetPassword(model.Password, storedPassword);

                                // For debugging: log the hash comparison
                                _logger.LogInformation("Password verification for user {Username}: Stored={StoredPassword}, Match={PasswordMatches}",
                                    model.Username, storedPassword, passwordMatches);

                                if (passwordMatches)
                                {
                                    // Login successful
                                    var username = reader["Username"]?.ToString() ?? "";
                                    var groupName = reader["GroupName"]?.ToString() ?? "";
                                    var groupId = reader["GroupID"]?.ToString() ?? "0";
                                    var userId = reader["SN"]?.ToString() ?? "0";
                                    
                                    // Create claims for the authenticated user
                                    var claims = new List<Claim>
                                    {
                                        new Claim(ClaimTypes.Name, username),
                                        new Claim(ClaimTypes.NameIdentifier, userId),
                                        new Claim("FullName", username), // Use username as full name for now
                                        new Claim("UserGroup", groupName),
                                        new Claim("GroupID", groupId),
                                        new Claim("LoginTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                                    };
                                    if (groupId == "1")
                                    {
                                        claims.Add(new Claim(ClaimTypes.Role, "admin"));
                                    }

                                    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                                    var authProperties = new AuthenticationProperties
                                    {
                                        IsPersistent = model.RememberMe,
                                        ExpiresUtc = DateTimeOffset.UtcNow.AddHours(8)
                                    };

                                    await HttpContext.SignInAsync(
                                        CookieAuthenticationDefaults.AuthenticationScheme,
                                        new ClaimsPrincipal(claimsIdentity),
                                        authProperties);

                                    _logger.LogInformation("User {Username} logged in successfully", model.Username);

                                    // Redirect to return URL or dashboard
                                    if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
                                    {
                                        return Redirect(returnUrl);
                                    }

                                    return RedirectToAction("Index", "SimpleDashboard");
                                }
                                else
                                {
                                    ModelState.AddModelError(string.Empty, "اسم المستخدم او كلمة المرور خطأ");
                                    _logger.LogWarning("Failed login attempt for user {Username}: Invalid password", model.Username);
                                }
                            }
                            else
                            {
                                ModelState.AddModelError(string.Empty, "اسم المستخدم او كلمة المرور خطأ");
                                _logger.LogWarning("Failed login attempt for user {Username}: User not found", model.Username);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "حدث خطأ أثناء تسجيل الدخول");
                _logger.LogError(ex, "Login error for user {Username}", model.Username);
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout()
        {
            var username = User.Identity?.Name;
            
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            
            _logger.LogInformation("User {Username} logged out", username);
            
            return RedirectToAction("Login");
        }

        public IActionResult AccessDenied()
        {
            return View();
        }


    }
}
