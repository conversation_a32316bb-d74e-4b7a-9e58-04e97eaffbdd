using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class ReportsController : Controller
    {
        public IActionResult Index()
        {
            ViewBag.PageTitle = "التقارير";
            return View();
        }

        public IActionResult Financial()
        {
            ViewBag.PageTitle = "التقارير المالية";
            ViewBag.Message = "صفحة التقارير المالية - قيد التطوير";
            return View();
        }

        public IActionResult Inventory()
        {
            ViewBag.PageTitle = "تقارير المخزون";
            ViewBag.Message = "صفحة تقارير المخزون - قيد التطوير";
            return View();
        }

        public IActionResult Sales()
        {
            ViewBag.PageTitle = "تقارير المبيعات";
            ViewBag.Message = "صفحة تقارير المبيعات - قيد التطوير";
            return View();
        }

        public IActionResult Purchase()
        {
            ViewBag.PageTitle = "تقارير المشتريات";
            ViewBag.Message = "صفحة تقارير المشتريات - قيد التطوير";
            return View();
        }

        public IActionResult CustomerStatement()
        {
            ViewBag.PageTitle = "كشف حساب العملاء";
            ViewBag.Message = "صفحة كشف حساب العملاء - قيد التطوير";
            return View();
        }

        public IActionResult VendorStatement()
        {
            ViewBag.PageTitle = "كشف حساب الموردين";
            ViewBag.Message = "صفحة كشف حساب الموردين - قيد التطوير";
            return View();
        }
    }
}
