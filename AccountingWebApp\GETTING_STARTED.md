# Getting Started with Accounting System Web Application

## Quick Start Guide

### 1. Initial Setup
Run the development setup script to configure all projects and dependencies:
```powershell
.\05-Scripts\setup-development.ps1
```

### 2. Configure Database Connection
1. Open `03-WebApp\AccountingWeb\AccountingSystem.Web\appsettings.json`
2. Update the connection strings with your actual database information:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=YOUR_SERVER;Database=YOUR_DB;Trusted_Connection=true;TrustServerCertificate=true;",
       "AccountingDatabase": "Server=YOUR_SERVER;Database=YOUR_DB;Trusted_Connection=true;TrustServerCertificate=true;"
     }
   }
   ```

### 3. Copy Reference Materials
1. Copy your VB.NET source code to `01-Reference\VB.NET-Source\`
2. Export your database schema to `01-Reference\Database-Schema\`
3. Document business rules in `01-Reference\Business-Logic\`

### 4. Generate Entity Framework Models
Navigate to the web project and scaffold your existing database:
```bash
cd 03-WebApp\AccountingWeb\AccountingSystem.Web
dotnet ef dbcontext scaffold "YOUR_CONNECTION_STRING" Microsoft.EntityFrameworkCore.SqlServer -o Models\Generated -c AccountingDbContext --force
```

### 5. Configure Startup
Update `Program.cs` in the web project to register the DbContext:
```csharp
builder.Services.AddDbContext<AccountingDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("AccountingDatabase")));
```

### 6. Test the Setup
1. Build the solution: `dotnet build`
2. Run the web application: `dotnet run --project 03-WebApp\AccountingWeb\AccountingSystem.Web`
3. Navigate to `https://localhost:5001` to see the application

## Project Structure Overview

```
AccountingWebApp/
├── 01-Reference/           # VB.NET reference materials
│   ├── VB.NET-Source/     # Original VB.NET code
│   ├── Database-Schema/   # Database documentation
│   ├── Business-Logic/    # Business rules documentation
│   └── Reports/           # Report templates
├── 02-Shared/             # Shared libraries
│   ├── Core/              # Core business logic
│   ├── Data/              # Data access layer
│   ├── Models/            # Data models
│   ├── Services/          # Business services
│   └── Utilities/         # Common utilities
├── 03-WebApp/             # Web application
│   ├── AccountingWeb/     # Main web project
│   └── Tests/             # Unit tests
├── 04-Documentation/      # Project documentation
└── 05-Scripts/            # Build and deployment scripts
```

## Development Workflow

### Phase 1: Foundation (Week 1-2)
1. Set up database connection and test connectivity
2. Generate Entity Framework models from existing database
3. Create basic authentication and user management
4. Set up basic navigation and layout

### Phase 2: Core Features (Week 3-6)
1. Implement Chart of Accounts management
2. Create Journal Entry functionality
3. Build Customer/Vendor management
4. Develop basic reporting

### Phase 3: Advanced Features (Week 7-12)
1. Implement invoicing system
2. Add payment processing
3. Create financial reports
4. Build dashboard and analytics

### Phase 4: Testing & Deployment (Week 13-16)
1. Comprehensive testing with both applications
2. User training and documentation
3. Production deployment
4. Gradual migration from Windows app

## Key Considerations

### Database Compatibility
- ✅ Uses same database as Windows application
- ✅ No schema changes required
- ✅ Both applications can run simultaneously
- ✅ Optimistic concurrency control prevents conflicts

### Security
- Connection strings stored in appsettings.json
- Use environment variables for production
- Implement proper authentication and authorization
- Audit logging for all transactions

### Performance
- Entity Framework with proper indexing
- Lazy loading for navigation properties
- Caching for frequently accessed data
- Pagination for large datasets

## Current Migration Status

### ✅ Completed Features
1. **Authentication System**: Cookie-based authentication with VB.NET compatibility
2. **Chart of Accounts**: Complete CRUD operations with hierarchical display
3. **POS System**: Full point-of-sale functionality migrated from VB.NET
4. **Database Integration**: Entity Framework Core models matching existing schema
5. **UI/UX**: Modern Arabic RTL responsive design with Bootstrap 5

### 🔧 Current Issues (Being Resolved)
1. **POS Sessions Controller**: NullReferenceException in POSSessions/Index view
   - **Issue**: Navigation properties causing dependency injection errors
   - **Status**: Under investigation and fix in progress
   - **Impact**: POS Sessions page not accessible

2. **Service Registration**: Some services may have circular dependencies
   - **Issue**: POSSessionService causing DI container issues
   - **Status**: Need to review service registration order

### 🚧 In Progress
1. **Error Handling**: Implementing proper exception handling for database operations
2. **Service Layer**: Refactoring services to avoid navigation property issues
3. **View Models**: Ensuring all view models have required properties

### 📋 Next Priorities
1. Fix POS Sessions runtime errors
2. Complete POS system testing
3. Implement remaining VB.NET screens
4. Add comprehensive error handling
5. Performance optimization

## Known Issues and Solutions

### Build Issues (Resolved)
- ✅ Missing using directives in view models
- ✅ Razor syntax errors in views
- ✅ Database schema mismatches
- ✅ Missing properties in models

### Runtime Issues (Current)
- ❌ POS Sessions page crashes with NullReferenceException
- ❌ Some navigation properties causing DI issues
- ⚠️ Need to implement proper null checking in views

### Database Issues (Resolved)
- ✅ Entity Framework models now match actual database schema
- ✅ Removed invalid columns (IsActive, AccountID, RowVersion)
- ✅ Fixed property mappings for CreatedDate/ModifiedDate

## Troubleshooting Guide

### If Build Fails
1. Check for missing using directives in view models
2. Verify Razor syntax in .cshtml files
3. Ensure all required properties exist in models
4. Run `dotnet clean` followed by `dotnet build`

### If Runtime Errors Occur
1. Check application logs for specific error messages
2. Verify database connection string is correct
3. Ensure all services are properly registered in Program.cs
4. Check for null reference exceptions in views

### If Database Issues Arise
1. Verify Entity Framework models match actual database schema
2. Check for missing or incorrectly named columns
3. Ensure navigation properties are properly configured
4. Review DbContext configuration

## Next Steps

1. **Immediate**: Configure database connection and test
2. **Short-term**: Generate models and implement authentication
3. **Medium-term**: Build core accounting features
4. **Long-term**: Complete feature parity with Windows app

## Support and Documentation

- **Project Structure**: See `04-Documentation/ProjectStructure.md`
- **Database Setup**: See `04-Documentation/DatabaseConfiguration.md`
- **Development Guide**: See `README.md`

## Common Commands

```bash
# Build solution
dotnet build

# Run web application
dotnet run --project 03-WebApp\AccountingWeb\AccountingSystem.Web

# Run tests
dotnet test

# Add new migration (when needed)
dotnet ef migrations add MigrationName --project 02-Shared\Data\AccountingSystem.Data

# Update database (when needed)
dotnet ef database update --project 03-WebApp\AccountingWeb\AccountingSystem.Web
```

Remember: The goal is to create a modern web interface for your existing accounting system while maintaining full compatibility with your current VB.NET Windows application.
