<?xml version="1.0" encoding="utf-8"?>
<doc>
    <assembly>
        <name>System.ClientModel</name>
    </assembly>
    <members>
        <member name="T:System.ClientModel.ApiKeyCredential">
            <summary>
            A credential used to authenticate to a service that accepts an API key.
            This type provides the ability to update the key's value without creating
            a new client.
            </summary>
        </member>
        <member name="M:System.ClientModel.ApiKeyCredential.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.ClientModel.ApiKeyCredential" /> class.
            </summary>
            <param name="key">The API key to used to authenticate with the service.
            </param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when the <paramref name="key" /> is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            Thrown when the <paramref name="key" /> is empty.
            </exception>
        </member>
        <member name="M:System.ClientModel.ApiKeyCredential.Deconstruct(System.String@)">
            <summary>
            Deconstructs the credential into component values.
            </summary>
            <param name="key">When this method returns, contains the value of the
            API key this instance represents.</param>
        </member>
        <member name="M:System.ClientModel.ApiKeyCredential.Update(System.String)">
            <summary>
            Updates the API key to used to authenticate with the service.
            This method is intended to be called when the API key has been
            regenerated and long-lived clients need to be updated to send the new
            value.
            </summary>
            <param name="key">The new value for the API key to used to authenticate
            with the service.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown when the <paramref name="key" /> is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            Thrown when the <paramref name="key" /> is empty.
            </exception>
        </member>
        <member name="T:System.ClientModel.Primitives.AsyncCollectionResult">
            <summary>
            Represents a collection of values returned from a cloud service operation.
            The collection values may be delivered over one or more service responses.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.AsyncCollectionResult.#ctor">
            <summary>
            Creates a new instance of <see cref="T:System.ClientModel.Primitives.AsyncCollectionResult" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.AsyncCollectionResult.GetRawPagesAsync">
            <summary>
            Gets the collection of page responses that contain the items in this
            collection.
            </summary>
            <returns>A collection of service responses where each
            <see cref="T:System.ClientModel.ClientResult" /> holds a subset of items in the full
            collection.
            </returns>
            <remarks>This method does not take a <see cref="T:System.Threading.CancellationToken" />
            parameter.  <see cref="T:System.ClientModel.Primitives.AsyncCollectionResult" /> implementations must
            store the <see cref="T:System.Threading.CancellationToken" /> passed to the service method
            that creates them and pass that token to any <c>async</c> methods
            called from this method.  For protocol methods, this
            <see cref="T:System.Threading.CancellationToken" /> will come from the
            <see cref="P:System.ClientModel.Primitives.RequestOptions.CancellationToken" /> property.</remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.AsyncCollectionResult.GetContinuationToken(System.ClientModel.ClientResult)">
            <summary>
            Gets a <see cref="T:System.ClientModel.ContinuationToken" /> that can be passed to a client
            method to obtain a collection holding the items remaining in this
            <see cref="T:System.ClientModel.Primitives.AsyncCollectionResult" />.
            </summary>
            <param name="page">The raw page to obtain a continuation token for.
            </param>
            <returns>A <see cref="T:System.ClientModel.ContinuationToken" /> that a client can use to
            obtain an <see cref="T:System.ClientModel.Primitives.AsyncCollectionResult" /> whose items start at the
            first item after the last item in <paramref name="page" />, or
            <c>null</c> if <paramref name="page" /> is the last page in the sequence
            of page responses delivering the items in the collection.</returns>
        </member>
        <member name="T:System.ClientModel.Primitives.CollectionResult">
            <summary>
            Represents a collection of values returned from a cloud service operation.
            The collection values may be delivered over one or more service responses.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.CollectionResult.#ctor">
            <summary>
            Creates a new instance of <see cref="T:System.ClientModel.Primitives.CollectionResult" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.CollectionResult.GetRawPages">
            <summary>
            Gets the collection of page responses that contain the items in this
            collection.
            </summary>
            <returns>A collection of service responses where each
            <see cref="T:System.ClientModel.ClientResult" /> holds a subset of items in the full
            collection.
            </returns>
            <remarks><see cref="T:System.ClientModel.Primitives.CollectionResult" /> implementations are expected
            to store the <see cref="T:System.Threading.CancellationToken" /> passed to the service
            method that creates them and pass that token to any methods making
            service calls that are called from this method.  For protocol methods,
            this <see cref="T:System.Threading.CancellationToken" /> will come from the
            <see cref="P:System.ClientModel.Primitives.RequestOptions.CancellationToken" /> property.</remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.CollectionResult.GetContinuationToken(System.ClientModel.ClientResult)">
            <summary>
            Gets a <see cref="T:System.ClientModel.ContinuationToken" /> that can be passed to a client
            method to obtain a collection holding the remaining items in this
            <see cref="T:System.ClientModel.Primitives.CollectionResult" />.
            </summary>
            <param name="page">The raw page to obtain a continuation token for.
            </param>
            <returns>A <see cref="T:System.ClientModel.ContinuationToken" /> that a client can use to
            obtain an <see cref="T:System.ClientModel.Primitives.CollectionResult" /> whose items start at the
            first item after the last item in <paramref name="page" />, or
            <c>null</c> if <paramref name="page" /> is the last page in the sequence
            of page responses delivering the items in the collection.</returns>
        </member>
        <member name="T:System.ClientModel.Primitives.OperationResult">
            <summary>
            Represents an operation that runs asynchronously on a cloud service.  Cloud
            services use long-running operations to allow users to start an operation
            with one request and then monitor progress of the operation until it has
            completed.  <see cref="T:System.ClientModel.Primitives.OperationResult" /> enables waiting for completion of
            long-running operations.  Client libraries provide derived types that add
            properties such as <code>Value</code> or <code>Status</code> as applicable
            for a given service operation.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.OperationResult.#ctor(System.ClientModel.Primitives.PipelineResponse)">
            <summary>
            Creates a new instance of <see cref="T:System.ClientModel.Primitives.OperationResult" />.
            </summary>
            <param name="response">The <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> received from
            the service in response to the request that started the operation.</param>
        </member>
        <member name="P:System.ClientModel.Primitives.OperationResult.HasCompleted">
            <summary>
            Gets a value that indicates whether the operation has completed.
            </summary>
            <value><c>true</c> if the operation has reached a terminal state
            (that is, it has finished successfully, ended due to an error condition,
            or has been cancelled by a user); otherwise, <c>false</c>.
            </value>
            <remarks><see cref="P:System.ClientModel.Primitives.OperationResult.HasCompleted" /> is updated by the
            <see cref="M:System.ClientModel.Primitives.OperationResult.UpdateStatus(System.ClientModel.Primitives.RequestOptions)" /> method, based on the response received from
            the service regarding the operation's status.  Users must call
            <see cref="M:System.ClientModel.Primitives.OperationResult.WaitForCompletion(System.Threading.CancellationToken)" />, <see cref="M:System.ClientModel.Primitives.OperationResult.UpdateStatus(System.ClientModel.Primitives.RequestOptions)" />, or other
            method provided by the derived type to ensure that the value of the
            <see cref="P:System.ClientModel.Primitives.OperationResult.HasCompleted" /> property reflects the current status of the
            operation running on the service.
            </remarks>
        </member>
        <member name="P:System.ClientModel.Primitives.OperationResult.RehydrationToken">
            <summary>
            Gets a token that can be used to rehydrate the operation.
            </summary>
            <value>A token that can be used to rehydrate the operation, for example
            to monitor its progress or to obtain its final result, from a process
            different than the one that started the operation.</value>
            <remarks>This property is abstract so that derived types that do not
            support rehydration can return null without using a backing field for
            an unused <see cref="T:System.ClientModel.ContinuationToken" />.</remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.OperationResult.UpdateStatusAsync(System.ClientModel.Primitives.RequestOptions)">
            <summary>
            Sends a request to the service to get the current status of the
            operation and updates <see cref="P:System.ClientModel.Primitives.OperationResult.HasCompleted" /> and other relevant
            properties.
            </summary>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.RequestOptions" /> to be used when
            sending the request to the service.</param>
            <returns>The <see cref="T:System.ClientModel.ClientResult" /> returned from the service call.
            </returns>
            <remarks>This method updates the value returned from
            <see cref="M:System.ClientModel.ClientResult.GetRawResponse" /> and will update
            <see cref="P:System.ClientModel.Primitives.OperationResult.HasCompleted" /> to <c>true</c> once the operation has finished
            running on the service.  It will also update <c>Value</c> or
            <c>Status</c> properties if present on the <see cref="T:System.ClientModel.Primitives.OperationResult" />
            derived type.</remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.OperationResult.UpdateStatus(System.ClientModel.Primitives.RequestOptions)">
            <summary>
            Sends a request to the service to get the current status of the
            operation and updates <see cref="P:System.ClientModel.Primitives.OperationResult.HasCompleted" /> and other relevant
            properties.
            </summary>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.RequestOptions" /> to be used when
            sending the request to the service.</param>
            <returns>The <see cref="T:System.ClientModel.ClientResult" /> returned from the service call.
            </returns>
            <remarks>This method updates the value returned from
            <see cref="M:System.ClientModel.ClientResult.GetRawResponse" /> and will update
            <see cref="P:System.ClientModel.Primitives.OperationResult.HasCompleted" /> to <c>true</c> once the operation has finished
            running on the service.  It will also update <c>Value</c> or
            <c>Status</c> properties if present on the <see cref="T:System.ClientModel.Primitives.OperationResult" />
            derived type.</remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.OperationResult.WaitForCompletionAsync(System.Threading.CancellationToken)">
            <summary>
            Waits for the operation to complete processing on the service.
            </summary>
            <remarks>Derived types may override <see cref="M:System.ClientModel.Primitives.OperationResult.WaitForCompletionAsync(System.Threading.CancellationToken)" />
            to implement different mechanisms for obtaining updates from the service
            regarding the progress of the operation. For example, if the derived
            type polls for status updates, it may provides overloads of
            <see cref="M:System.ClientModel.Primitives.OperationResult.WaitForCompletionAsync(System.Threading.CancellationToken)" />
            that allow the caller to specify the polling interval or delay strategy
            used to wait between sending request for updates.  By default,
            <see cref="M:System.ClientModel.Primitives.OperationResult.WaitForCompletionAsync(System.Threading.CancellationToken)" /> waits a default interval between
            calling <see cref="M:System.ClientModel.Primitives.OperationResult.UpdateStatusAsync(System.ClientModel.Primitives.RequestOptions)" /> to obtain a status updates, so
            if updates are delivered via streaming or another mechanism where a wait
            time is not required, derived types can override this method to update
            the status more frequently.
            </remarks>
            <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" />
            was cancelled.</exception>
        </member>
        <member name="M:System.ClientModel.Primitives.OperationResult.WaitForCompletion(System.Threading.CancellationToken)">
            <summary>
            Waits for the operation to complete processing on the service.
            </summary>
            <remarks>Derived types may override <see cref="M:System.ClientModel.Primitives.OperationResult.WaitForCompletion(System.Threading.CancellationToken)" />
            to implement different mechanisms for obtaining updates from the service
            regarding the progress of the operation. For example, if the derived
            type polls for status updates, it may provides overloads of
            <see cref="M:System.ClientModel.Primitives.OperationResult.WaitForCompletion(System.Threading.CancellationToken)" />
            that allow the caller to specify the polling interval or delay strategy
            used to wait between sending request for updates.  By default,
            <see cref="M:System.ClientModel.Primitives.OperationResult.WaitForCompletion(System.Threading.CancellationToken)" /> waits a default interval between
            calling <see cref="M:System.ClientModel.Primitives.OperationResult.UpdateStatus(System.ClientModel.Primitives.RequestOptions)" /> to obtain a status updates, so
            if updates are delivered via streaming or another mechanism where a wait
            time is not required, derived types can override this method to update
            the status more frequently.
            </remarks>
            <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" />
            was cancelled.</exception>
        </member>
        <member name="M:System.ClientModel.Primitives.OperationResult.GetRawResponse">
            <summary>
            Gets the <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> corresponding to the most
            recent update received from the service.
            </summary>
            <returns>The most recent <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> received
            from the service.
            </returns>
        </member>
        <member name="M:System.ClientModel.Primitives.OperationResult.SetRawResponse(System.ClientModel.Primitives.PipelineResponse)">
            <summary>
            Update the value returned from <see cref="M:System.ClientModel.Primitives.OperationResult.GetRawResponse" />.
            </summary>
            <param name="response">The <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> to return
            from <see cref="M:System.ClientModel.Primitives.OperationResult.GetRawResponse" />.</param>
        </member>
        <member name="T:System.ClientModel.Primitives.PipelineMessage">
            <summary>
            Represents an HTTP message that can be sent from a
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.  <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> holds the request sent
            to the cloud service, and <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" /> holds the response received
            from the service.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineMessage.#ctor(System.ClientModel.Primitives.PipelineRequest)">
            <summary>
            Creates an instance of <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> with
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> set to the provided <paramref name="request" />.
            </summary>
            <param name="request">The <see cref="T:System.ClientModel.Primitives.PipelineRequest" /> to set on this
            instance's <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> property.</param>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineMessage.Request">
            <summary>
            Gets the <see cref="T:System.ClientModel.Primitives.PipelineRequest" /> to send to the service during
            the call to <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineMessage.Response">
            <summary>
            Gets the <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> received from the service during
            the call to <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineMessage.ExtractResponse">
            <summary>
            Returns the value of the <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" /> property and transfers
            dispose ownership of the response to the caller. After calling this
            method, the <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" /> property will be <code>null</code>
            and the caller will be responsible for disposing the returned value,
            which may hold a live network stream.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineMessage.CancellationToken">
            <summary>
            Gets or sets the <see cref="P:System.ClientModel.Primitives.PipelineMessage.CancellationToken" /> used for the duration
            of the call to <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineMessage.ResponseClassifier">
             <summary>
             Gets or sets the <see cref="T:System.ClientModel.Primitives.PipelineMessageClassifier" /> used by the
             <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> to determine whether the response received
             from the service is considered an error and populate the
             <see cref="P:System.ClientModel.Primitives.PipelineResponse.IsError" /> on <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />.
            
             This property is intended to be set in a client's service method to a
             a <see cref="T:System.ClientModel.Primitives.PipelineMessageClassifier" /> that classifies responses as
             success responses based on the service API's published success codes.
             Setting this value outside the service method will override the
             client-specified classifier and may result in undesired behavior.
             </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineMessage.Apply(System.ClientModel.Primitives.RequestOptions)">
            <summary>
            Apply the options from the provided <see cref="T:System.ClientModel.Primitives.RequestOptions" /> to
            this <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> instance.
            </summary>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.RequestOptions" /> to apply to this
            <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> instance.  If <c>null</c> is passed,
            no changes will be made to this message instance.</param>
            <remarks> This method is intended to be called after the creation of
            the <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> and its <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> is
            complete, and prior to the call to
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />.
            </remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineMessage.TryGetProperty(System.Type,System.Object@)">
            <summary>
            Attempts to get a property from the property bag for this
            <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> instance. Message properties are used to
            govern the behavior of specific policies in the
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" />. Please refer to documentation for a
            specific <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> to understand what properties it
            supports.
            </summary>
            <param name="key">The key for the property in the message's property
            bag.</param>
            <param name="value">The value of the property.</param>
            <returns><c>true</c> if property exists; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineMessage.SetProperty(System.Type,System.Object)">
            <summary>
            Set a property in the property bag for this <see cref="T:System.ClientModel.Primitives.PipelineMessage" />
            instance. Message properties are used to govern the behavior of specific
            policies in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" />. Please refer to
            documentation for a specific <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> to understand
            what properties it supports.
            </summary>
            <param name="key">The key for the property in the message's property
            bag.</param>
            <param name="value">The value of the property.</param>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineMessage.BufferResponse">
            <summary>
            Gets or sets the value indicating whether the response should be buffered
            in-memory by the pipeline. Defaults to true.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineMessage.NetworkTimeout">
            <summary>
            Gets or sets the network timeout value for this message.
            If <c>null</c>, the value set on the client's options will be used.
            Defaults to <c>null</c>.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineMessage.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineMessage.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the
            <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> and optionally disposes of the managed
            resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and
            unmanaged resources; <c>false</c> to release only unmanaged resources.
            </param>
        </member>
        <member name="T:System.ClientModel.Primitives.PipelineRequest">
            <summary>
            Represents an HTTP request to be sent to a cloud service.
            The type of a <see cref="T:System.ClientModel.Primitives.PipelineRequest" /> is specific to the type of the
            <see cref="T:System.ClientModel.Primitives.PipelineTransport" /> used by the <see cref="T:System.ClientModel.Primitives.ClientPipeline" />
            that sends the request.  Because of this,
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.CreateMessage" /> is used to create an instance of
            <see cref="T:System.ClientModel.Primitives.PipelineRequest" /> for a given pipeline.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineRequest.Method">
            <summary>
            Gets or sets the HTTP method used by the HTTP request.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineRequest.MethodCore">
            <summary>
            Gets or sets the derived-type's value of the request's
            <see cref="P:System.ClientModel.Primitives.PipelineRequest.Method" />.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineRequest.Uri">
            <summary>
            Gets or sets the <see cref="T:System.Uri" /> used for the HTTP request.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineRequest.UriCore">
            <summary>
            Gets or sets the derived-type's value of the request's <see cref="P:System.ClientModel.Primitives.PipelineRequest.Uri" />.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineRequest.Headers">
            <summary>
            Gets the collection of HTTP request headers.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineRequest.HeadersCore">
            <summary>
            Gets or sets the derived-type's value of the request's
            <see cref="P:System.ClientModel.Primitives.PipelineRequest.Headers" /> collection.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineRequest.Content">
            <summary>
            Gets or sets the contents of the HTTP request.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineRequest.ContentCore">
            <summary>
            Gets or sets the derived-type's value of the request's
            <see cref="P:System.ClientModel.Primitives.PipelineRequest.Content" />.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineRequest.ClientRequestId">
            <summary>
            The client request id to include in log entries.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineRequest.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
        </member>
        <member name="T:System.ClientModel.Primitives.PipelineRequestHeaders">
            <summary>
            Represents the collection of HTTP headers on a <see cref="T:System.ClientModel.Primitives.PipelineRequest" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineRequestHeaders.Add(System.String,System.String)">
            <summary>
            Adds the specified header and its value to the request's header
            collection. If a header with this name is already present in the
            collection, the value will be added to the comma-separated list of
            values associated with the header.
            </summary>
            <param name="name">The name of the header to add.</param>
            <param name="value">The value of the header.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineRequestHeaders.Set(System.String,System.String)">
            <summary>
            Sets the specified header and its value in the request's header
            collection. If a header with this name is already present in the
            collection, the header's value will be replaced with the specified value.
            </summary>
            <param name="name">The name of the header to set.</param>
            <param name="value">The value of the header.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineRequestHeaders.Remove(System.String)">
            <summary>
            Removes the specified header from the request's header collection.
            </summary>
            <param name="name">The name of the header to remove.</param>
            <returns><c>true</c> if the header was successfully removed; otherwise
            <c>false</c>.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineRequestHeaders.TryGetValue(System.String,System.String@)">
            <summary>
            Attempts to retrieve the value associated with the specified header name.
            </summary>
            <param name="name">The name of the header to retrieve.</param>
            <param name="value">The specified header value.</param>
            <returns><c>true</c> if the specified header name and value are stored
            in the collection; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineRequestHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
            <summary>
            Attempts to retrieve the values associated with the specified header name.
            </summary>
            <param name="name">The name of the header to retrieve.</param>
            <param name="values">The specified header values.</param>
            <returns><c>true</c> if the specified header name and values are stored
            in the collection; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineRequestHeaders.GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns>
        </member>
        <member name="T:System.ClientModel.Primitives.PipelineResponse">
            <summary>
            Represents an HTTP response received from a cloud service.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineResponse.Status">
            <summary>
            Gets the status code of the HTTP response.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineResponse.ReasonPhrase">
            <summary>
            Gets the reason phrase that accompanies the status code on the HTTP
            response.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineResponse.Headers">
            <summary>
            Gets the collection of HTTP response headers.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineResponse.HeadersCore">
            <summary>
            Gets or sets the derived-type's value of the response's
            <see cref="P:System.ClientModel.Primitives.PipelineResponse.Headers" /> collection.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineResponse.ContentStream">
            <summary>
            Gets or sets the contents of the HTTP response.
            </summary>
            <remarks>
            <see cref="P:System.ClientModel.Primitives.PipelineResponse.ContentStream" /> may be a stream that contains the buffered
            contents transferred from the network stream that originally held the
            contents of the service response; or it may be the live network stream
            itself, depending on the value of
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.BufferResponse" /> on the message sent via
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />. Please refer to the
            documentation for a client's service method if needed to understand
            whether this <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> instance must be disposed
            to close the network stream.
            </remarks>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineResponse.Content">
            <summary>
            Gets the contents of the HTTP response.
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown if the response is
            not buffered.</exception>
            <remarks>
            <see cref="P:System.ClientModel.Primitives.PipelineResponse.Content" /> holds the in-memory contents of the HTTP response
            when <see cref="P:System.ClientModel.Primitives.PipelineMessage.BufferResponse" /> is <c>true</c> on
            the message sent via <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />.
            If this <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> instance was obtained from the
            return value of a client's service method, please refer to the
            documentation for the service method to understand whether this property
            can be accessed without throwing an exception. If this instance is
            accessed from a <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />, please check the value of
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.BufferResponse" /> to determine whether to
            obtain the response content from <see cref="P:System.ClientModel.Primitives.PipelineResponse.Content" /> or
            <see cref="P:System.ClientModel.Primitives.PipelineResponse.ContentStream" />.
            </remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineResponse.BufferContent(System.Threading.CancellationToken)">
            <summary>
            Transfer the contents of the response network stream from
            <see cref="P:System.ClientModel.Primitives.PipelineResponse.ContentStream" /> to a buffered cache on this
            <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> instance.
            </summary>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to
            use while buffering the content.</param>
            <returns>The buffered content.</returns>
            <remarks>
            This method will read the full content from the response content
            network stream into memory. Please be sure the contents will fit into
            memory before calling this method.
            </remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineResponse.BufferContentAsync(System.Threading.CancellationToken)">
            <summary>
            Transfer the contents of the response network stream from
            <see cref="P:System.ClientModel.Primitives.PipelineResponse.ContentStream" /> to a buffered cache on this
            <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> instance.
            </summary>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> to
            use while buffering the content.</param>
            <returns>The buffered content.</returns>
            <remarks>
            This method will read the full content from the response content
            network stream into memory. Please be sure the contents will fit into
            memory before calling this method.
            </remarks>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineResponse.IsError">
            <summary>
            Indicates whether the status code of the returned response is considered
            an error code.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineResponse.IsErrorCore">
            <summary>
            Gets or sets the derived-type's value of <see cref="P:System.ClientModel.Primitives.PipelineResponse.IsError" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineResponse.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
        </member>
        <member name="T:System.ClientModel.Primitives.PipelineResponseHeaders">
            <summary>
            A collection of HTTP response headers and their values.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineResponseHeaders.TryGetValue(System.String,System.String@)">
            <summary>
            Attempts to retrieve the value associated with the specified header
            name.
            </summary>
            <param name="name">The name of the header to retrieve.</param>
            <param name="value">The specified header value.</param>
            <returns><c>true</c> if the specified header name and value are stored
            in the collection; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineResponseHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
            <summary>
            Attempts to retrieve the values associated with the specified header name.
            </summary>
            <param name="name">The name of the header to retrieve.</param>
            <param name="values">The specified header values.</param>
            <returns><c>true</c> if the specified header name and values are stored
            in the collection; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineResponseHeaders.GetEnumerator">
            <summary>
            Gets an enumerator that iterates through the <see cref="T:System.ClientModel.Primitives.PipelineResponseHeaders" />.
            </summary>
            <returns>An enumerator that iterates through the <see cref="T:System.ClientModel.Primitives.PipelineResponseHeaders" />.</returns>
        </member>
        <member name="T:System.ClientModel.Primitives.IJsonModel`1">
            <summary>
            Allows an object to control its own JSON writing and reading.
            </summary>
            <typeparam name="T">The type the model can be converted into.</typeparam>
        </member>
        <member name="M:System.ClientModel.Primitives.IJsonModel`1.Write(System.Text.Json.Utf8JsonWriter,System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Writes the model to the provided <see cref="T:System.Text.Json.Utf8JsonWriter" />.
            </summary>
            <param name="writer">The <see cref="T:System.Text.Json.Utf8JsonWriter" /> to write into.</param>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" /> to use.</param>
            <exception cref="T:System.FormatException">If the model does not support the requested <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format" />.</exception>
        </member>
        <member name="M:System.ClientModel.Primitives.IJsonModel`1.Create(System.Text.Json.Utf8JsonReader@,System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Reads one JSON value (including objects or arrays) from the provided reader and converts it to a model.
            </summary>
            <param name="reader">The <see cref="T:System.Text.Json.Utf8JsonReader" /> to read.</param>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" /> to use.</param>
            <returns>A <typeparamref name="T" /> representation of the JSON value.</returns>
            <exception cref="T:System.FormatException">If the model does not support the requested <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format" />.</exception>
        </member>
        <member name="T:System.ClientModel.Primitives.IPersistableModel`1">
            <summary>
            Allows an object to control its own writing and reading.
            The format is determined by the implementer.
            </summary>
            <typeparam name="T">The type the model can be converted into.</typeparam>
        </member>
        <member name="M:System.ClientModel.Primitives.IPersistableModel`1.Write(System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Writes the model into a <see cref="T:System.BinaryData" />.
            </summary>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" /> to use.</param>
            <returns>A binary representation of the written model.</returns>
            <exception cref="T:System.FormatException">If the model does not support the requested <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format" />.</exception>
        </member>
        <member name="M:System.ClientModel.Primitives.IPersistableModel`1.Create(System.BinaryData,System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Converts the provided <see cref="T:System.BinaryData" /> into a model.
            </summary>
            <param name="data">The <see cref="T:System.BinaryData" /> to parse.</param>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" /> to use.</param>
            <returns>A <typeparamref name="T" /> representation of the data.</returns>
            <exception cref="T:System.FormatException">If the model does not support the requested <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format" />.</exception>
        </member>
        <member name="M:System.ClientModel.Primitives.IPersistableModel`1.GetFormatFromOptions(System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Gets the data interchange format (JSON, Xml, etc) that the model uses when communicating with the service.
            </summary>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" /> to consider when serializing and deserializing the model.</param>
            <returns>The format that the model uses when communicating with the serivce.</returns>
        </member>
        <member name="T:System.ClientModel.Primitives.JsonModelConverter">
            <summary>
            A generic converter which allows <see cref="T:System.Text.Json.JsonSerializer" /> to be able to write and read any models that implement <see cref="T:System.ClientModel.Primitives.IJsonModel`1" />.
            </summary>
            <remarks>
            Since <see cref="T:System.ClientModel.Primitives.IJsonModel`1" /> defines what the serialized shape should look like the <see cref="T:System.Text.Json.JsonSerializerOptions" /> are ignored
            except for those pertaining to indentation formatting.
            </remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.JsonModelConverter.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:System.ClientModel.Primitives.JsonModelConverter" /> with a default options of <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Json" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.JsonModelConverter.#ctor(System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Initializes a new instance of <see cref="T:System.ClientModel.Primitives.JsonModelConverter" />.
            </summary>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" /> to use.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.JsonModelConverter.CanConvert(System.Type)">
            <summary>
            Determines whether the type can be converted.
            </summary><remarks>
            The default implementation is to return True when <paramref name="typeToConvert" /> equals typeof(T).
            </remarks><param name="typeToConvert"></param><returns>True if the type can be converted, False otherwise.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.JsonModelConverter.Read(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
            <summary>
            Read and convert the JSON to T.
            </summary><remarks>
            A converter may throw any Exception, but should throw <cref>JsonException</cref> when the JSON is invalid.
            </remarks><param name="reader">The <see cref="T:System.Text.Json.Utf8JsonReader" /> to read from.</param><param name="typeToConvert">The <see cref="T:System.Type" /> being converted.</param><param name="options">The <see cref="T:System.Text.Json.JsonSerializerOptions" /> being used.</param><returns>The value that was converted.</returns><remarks>Note that the value of <seealso cref="P:System.Text.Json.Serialization.JsonConverter`1.HandleNull" /> determines if the converter handles null JSON tokens.</remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.JsonModelConverter.Write(System.Text.Json.Utf8JsonWriter,System.ClientModel.Primitives.IJsonModel{System.Object},System.Text.Json.JsonSerializerOptions)">
            <summary>
            Write the value as JSON.
            </summary><remarks>
            A converter may throw any Exception, but should throw <cref>JsonException</cref> when the JSON
            cannot be created.
            </remarks><param name="writer">The <see cref="T:System.Text.Json.Utf8JsonWriter" /> to write to.</param><param name="value">The value to convert. Note that the value of <seealso cref="P:System.Text.Json.Serialization.JsonConverter`1.HandleNull" /> determines if the converter handles <see langword="null" /> values.</param><param name="options">The <see cref="T:System.Text.Json.JsonSerializerOptions" /> being used.</param>
        </member>
        <member name="T:System.ClientModel.Primitives.ModelReaderWriter">
            <summary>
            Provides functionality to read and write <see cref="T:System.ClientModel.Primitives.IPersistableModel`1" /> and <see cref="T:System.ClientModel.Primitives.IJsonModel`1" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.ModelReaderWriter.Write``1(``0,System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Converts the value of a model into a <see cref="T:System.BinaryData" />.
            </summary>
            <typeparam name="T">The type of the value to write.</typeparam>
            <param name="model">The model to convert.</param>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" /> to use.</param>
            <returns>A <see cref="T:System.BinaryData" /> representation of the model in the <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format" /> specified by the <paramref name="options" />.</returns>
            <exception cref="T:System.FormatException">If the model does not support the requested <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format" />.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="model" /> is null.</exception>
        </member>
        <member name="M:System.ClientModel.Primitives.ModelReaderWriter.Write(System.Object,System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Converts the value of a model into a <see cref="T:System.BinaryData" />.
            </summary>
            <param name="model">The model to convert.</param>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" /> to use.</param>
            <returns>A <see cref="T:System.BinaryData" /> representation of the model in the <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format" /> specified by the <paramref name="options" />.</returns>
            <exception cref="T:System.InvalidOperationException">Throws if <paramref name="model" /> does not implement <see cref="T:System.ClientModel.Primitives.IPersistableModel`1" />.</exception>
            <exception cref="T:System.FormatException">If the model does not support the requested <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format" />.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="model" /> is null.</exception>
        </member>
        <member name="M:System.ClientModel.Primitives.ModelReaderWriter.Read``1(System.BinaryData,System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Converts the <see cref="T:System.BinaryData" /> into a <typeparamref name="T" />.
            </summary>
            <param name="data">The <see cref="T:System.BinaryData" /> to convert.</param>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" /> to use.</param>
            <returns>A <typeparamref name="T" /> representation of the <see cref="T:System.BinaryData" />.</returns>
            <exception cref="T:System.InvalidOperationException">Throws if <typeparamref name="T" /> does not have a public or internal parameterless constructor.</exception>
            <exception cref="T:System.FormatException">If the model does not support the requested <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format" />.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="data" /> is null.</exception>
            <exception cref="T:System.MissingMethodException">If <typeparamref name="T" /> does not have a public or non public empty constructor.</exception>
        </member>
        <member name="M:System.ClientModel.Primitives.ModelReaderWriter.Read(System.BinaryData,System.Type,System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Converts the <see cref="T:System.BinaryData" /> into a <paramref name="returnType" />.
            </summary>
            <param name="data">The <see cref="T:System.BinaryData" /> to convert.</param>
            <param name="returnType">The type of the objec to convert and return.</param>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" /> to use.</param>
            <returns>A <paramref name="returnType" /> representation of the <see cref="T:System.BinaryData" />.</returns>
            <exception cref="T:System.InvalidOperationException">Throws if <paramref name="returnType" /> does not implement <see cref="T:System.ClientModel.Primitives.IPersistableModel`1" />.</exception>
            <exception cref="T:System.InvalidOperationException">Throws if <paramref name="returnType" /> does not have a public or internal parameterless constructor.</exception>
            <exception cref="T:System.FormatException">If the model does not support the requested <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format" />.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="data" /> or <paramref name="returnType" /> are null.</exception>
            <exception cref="T:System.MissingMethodException">If <paramref name="returnType" /> does not have a public or non public empty constructor.</exception>
        </member>
        <member name="T:System.ClientModel.Primitives.ModelReaderWriterOptions">
            <summary>
            Provides the client options for reading and writing models.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Json">
            <summary>
            Default options for writing models into JSON format.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Xml">
            <summary>
            Default options for writing models into XML format.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.ModelReaderWriterOptions.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" />.
            </summary>
            <param name="format">The format to read and write models.</param>
        </member>
        <member name="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format">
            <summary>
            Gets the format to read and write the model.
            </summary>
        </member>
        <member name="T:System.ClientModel.Primitives.PersistableModelProxyAttribute">
            <summary>
            Attribute that indicates a proxy <see cref="T:System.Type" /> to use for reading a model.
            The proxy <see cref="T:System.Type" /> must implement <see cref="T:System.ClientModel.Primitives.IPersistableModel`1" /> and have a public or non-public parameterless constructor.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PersistableModelProxyAttribute.#ctor(System.Type)">
            <summary>
            Instantiates a new instance of the <see cref="T:System.ClientModel.Primitives.PersistableModelProxyAttribute" /> class.
            </summary>
            <param name="proxyType">
            The <see cref="T:System.Type" /> to create and call read on.
            The <see cref="T:System.Type" /> must have a public or non-public parameterless constructor.
            The <see cref="T:System.Type" /> must implement <see cref="T:System.ClientModel.Primitives.IPersistableModel`1" /> where T is the type of the abstract class.
            </param>
        </member>
        <member name="P:System.ClientModel.Primitives.PersistableModelProxyAttribute.ProxyType">
            <summary>
            Gets the <see cref="T:System.Type" /> to create and call read on.
            The <see cref="T:System.Type" /> must have a public or non-public parameterless constructor.
            The <see cref="T:System.Type" /> must implement <see cref="T:System.ClientModel.Primitives.IPersistableModel`1" /> where T is the type of the abstract class.
            </summary>
        </member>
        <member name="T:System.ClientModel.Primitives.ClientErrorBehaviors">
            <summary>
            ClientErrorBehaviors controls the behavior of a service method when an unexpected
            response status code is received.
            </summary>
        </member>
        <member name="F:System.ClientModel.Primitives.ClientErrorBehaviors.Default">
            <summary>
            The client will throw an exception from a service method if the service
            returns an error response.
            </summary>
        </member>
        <member name="F:System.ClientModel.Primitives.ClientErrorBehaviors.NoThrow">
            <summary>
            The client will not throw an exception from a service method if the service
            returns an error response. Callers of the service method must check the
            Response.IsError property before accessing the response content.
            </summary>
        </member>
        <member name="T:System.ClientModel.Primitives.ClientLoggingOptions">
            <summary>
            Exposes client options for logging within a <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientLoggingOptions.LoggerFactory">
            <summary>
            Gets or sets the implementation of <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory" /> to use to
            create <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instances for logging.
            </summary>
            <remarks>If an ILoggerFactory is not provided, logs will be written to Event Source
            instead. If an ILoggerFactory is provided, logs will be written to ILogger only and not
            Event Source.</remarks>
            <value>Defaults to <see cref="T:Microsoft.Extensions.Logging.Abstractions.NullLoggerFactory" />.</value>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientLoggingOptions.EnableLogging">
            <summary>
            Gets or sets value indicating if logging should be enabled in this client pipeline.
            </summary>
            <value>Defaults to <c>null</c>. If <c>null</c>, this value will be treated as <c>true</c>.</value>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientLoggingOptions.EnableMessageLogging">
            <summary>
            Gets or sets value indicating if request and response uri and header information should be logged.
            </summary>
            <value>Defaults to <c>null</c>. If <c>null</c>, the value
            of <see cref="P:System.ClientModel.Primitives.ClientLoggingOptions.EnableLogging" /> will be used instead.</value>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientLoggingOptions.EnableMessageContentLogging">
            <summary>
            Gets or sets value indicating if request and response content should be logged.
            </summary>
            <value>Defaults to <c>null</c>. If <c>null</c>, this value will be treated as <c>false</c>.</value>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientLoggingOptions.MessageContentSizeLimit">
            <summary>
            Gets or sets value indicating maximum size of content to log in bytes.
            </summary>
            <value>Defaults to <c>null</c>. If <c>null</c>, this value will be treated as </value>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientLoggingOptions.AllowedHeaderNames">
            <summary>
            Gets or sets a list of header names that are not redacted during logging.
            </summary>
            <value>Defaults to a list of common header names that do not
            typically hold sensitive information.</value>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientLoggingOptions.AllowedQueryParameters">
            <summary>
            Gets or sets a list of query parameter names that are not redacted during logging.
            </summary>
            <value>Defaults to a list of common query parameters that do not
            typically hold sensitive information.</value>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientLoggingOptions.Freeze">
            <summary>
            Freeze this instance of <see cref="T:System.ClientModel.Primitives.ClientLoggingOptions" />.  After this method
            has been called, any attempt to set properties on the instance or call
            methods that would change its state will throw <see cref="T:System.InvalidOperationException" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientLoggingOptions.AssertNotFrozen">
            <summary>
            Assert that <see cref="M:System.ClientModel.Primitives.ClientLoggingOptions.Freeze" /> has not been called on this
            <see cref="T:System.ClientModel.Primitives.ClientLoggingOptions" /> instance.
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown when an attempt is
            made to change the state of this <see cref="T:System.ClientModel.Primitives.ClientLoggingOptions" /> instance
            after <see cref="M:System.ClientModel.Primitives.ClientLoggingOptions.Freeze" /> has been called.</exception>
        </member>
        <member name="T:System.ClientModel.Primitives.ClientPipelineOptions">
            <summary>
            Options that control the creation of a <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> used
            by a service client to send and receive HTTP messages.
            Service clients must create a client-specific subtype of this class
            to pass to their constructors to allow for service-specific options
            with a client-wide scope.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientPipelineOptions.RetryPolicy">
            <summary>
            Gets or sets the <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> to be used by the
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> for handling retry logic.
            </summary>
            <remarks>
            In most cases, this property will be set to an instance of
            <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" />.
            </remarks>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientPipelineOptions.MessageLoggingPolicy">
            <summary>
            Gets or sets the <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> to be used by the
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> for logging.
            </summary>
            <remarks>
            In most cases, this property will be set to an instance of
            <see cref="P:System.ClientModel.Primitives.ClientPipelineOptions.MessageLoggingPolicy" />.
            </remarks>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientPipelineOptions.Transport">
            <summary>
            Gets or sets the <see cref="T:System.ClientModel.Primitives.PipelineTransport" /> to be used by the
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> for sending and receiving HTTP messages.
            </summary>
            <remarks>
            In most cases, this property will be set to an instance of
            <see cref="T:System.ClientModel.Primitives.HttpClientPipelineTransport" />.
            </remarks>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientPipelineOptions.NetworkTimeout">
            <summary>
            The timeout applied to an individual network operation.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientPipelineOptions.ClientLoggingOptions">
            <summary>
            The options to be used to configure logging within the
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientPipelineOptions.AddPolicy(System.ClientModel.Primitives.PipelinePolicy,System.ClientModel.Primitives.PipelinePosition)">
            <summary>
            Adds the provided <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> to the default
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.
            </summary>
            <param name="policy">The <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> to add to the
            pipeline.</param>
            <param name="position">The position of the policy in the pipeline.</param>
            <exception cref="T:System.ArgumentException">Thrown when the provided policy
            is <c>null</c>.</exception>
            <remarks>
            For a policy to run once per invocation of
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />, use
            <see cref="F:System.ClientModel.Primitives.PipelinePosition.PerCall" />, which will insert the policy
            before the pipeline's <see cref="P:System.ClientModel.Primitives.ClientPipelineOptions.RetryPolicy" />. For a policy to run
            once per retry attempt, use <see cref="F:System.ClientModel.Primitives.PipelinePosition.PerTry" />, which
            will insert the policy after the pipeline's <see cref="P:System.ClientModel.Primitives.ClientPipelineOptions.RetryPolicy" />.
            To ensure that a policy runs after all other policies in the pipeline
            have viewed the <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> and before all
            other policies view the <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />, use
            <see cref="F:System.ClientModel.Primitives.PipelinePosition.BeforeTransport" />. Changes made to
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> by a before-transport policy will
            not be visible to any logging policies that come before it in the
            pipeline.
            </remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientPipelineOptions.Freeze">
            <summary>
            Freeze this instance of <see cref="T:System.ClientModel.Primitives.ClientPipelineOptions" />.  After
            this method has been called, any attempt to set properties on the
            instance or call methods that would change its state will throw
            <see cref="T:System.InvalidOperationException" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientPipelineOptions.AssertNotFrozen">
            <summary>
            Assert that <see cref="M:System.ClientModel.Primitives.ClientPipelineOptions.Freeze" /> has not been called on this
            <see cref="T:System.ClientModel.Primitives.ClientPipelineOptions" /> instance.
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown when an attempt is
            made to change the state of this <see cref="T:System.ClientModel.Primitives.ClientPipelineOptions" />
            instance after <see cref="M:System.ClientModel.Primitives.ClientPipelineOptions.Freeze" /> has been called.</exception>
        </member>
        <member name="T:System.ClientModel.Primitives.PipelineMessageClassifier">
            <summary>
            A classifier that can evaluate a <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> in two ways.
            First, given an HTTP message, the <see cref="T:System.ClientModel.Primitives.PipelineMessageClassifier" />
            can determine whether the service response it holds should be considered an
            error response.  Second, given an HTTP message and an optional pipeline
            exception, the classifier can determine whether or not the
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> should retry the request.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.PipelineMessageClassifier.Default">
            <summary>
            A default classifier instance. This classifier will classify a
            <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> with a status code of <c>4xx</c> or
            <c>5xx</c> as an error, and with a status code of <c>408</c>,
            <c>429</c>, <c>500</c>, <c>502</c>, <c>503</c> and <c>504</c> as
            retriable.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineMessageClassifier.Create(System.ReadOnlySpan{System.UInt16})">
            <summary>
            Create an instance of a <see cref="T:System.ClientModel.Primitives.PipelineMessageClassifier" /> from a
            collection of success status codes.
            </summary>
            <param name="successStatusCodes">The status codes that the returned
            classifier instance will classifiy as success codes.</param>
            <returns>A <see cref="T:System.ClientModel.Primitives.PipelineMessageClassifier" /> instance that
            classifies the status codes provided by
            <paramref name="successStatusCodes" /> as success codes.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineMessageClassifier.TryClassify(System.ClientModel.Primitives.PipelineMessage,System.Boolean@)">
            <summary>
            Attempt to evaluate whether the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" />
            contains a <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" /> that the client should
            consider an error response.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to classify.</param>
            <param name="isError"><c>true</c> if the
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" /> should be considered an error
            response.</param>
            <returns><c>true</c> if the classifier had an opinion regarding whether
            the service response was an error response; <c>false</c> otherwise.
            </returns>
            <remarks>
            Not all classifiers derived from <see cref="T:System.ClientModel.Primitives.PipelineMessageClassifier" />
            will classify a given <see cref="T:System.ClientModel.Primitives.PipelineMessage" />. Returning
            <c>false</c> from <see cref="M:System.ClientModel.Primitives.PipelineMessageClassifier.TryClassify(System.ClientModel.Primitives.PipelineMessage,System.Boolean@)" />
            allows a classifier instance to compose with other classifiers by
            passing the classification decision to a later instance.
            <see cref="P:System.ClientModel.Primitives.PipelineMessageClassifier.Default" /> will always return <c>true</c> from
            <see cref="M:System.ClientModel.Primitives.PipelineMessageClassifier.TryClassify(System.ClientModel.Primitives.PipelineMessage,System.Boolean@)" /> and may be used as
            the last classifier in any composed classifier collection.
            </remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineMessageClassifier.TryClassify(System.ClientModel.Primitives.PipelineMessage,System.Exception,System.Boolean@)">
            <summary>
            Attempt to evaluate whether the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" />
            contains a <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" /> that indicates the
            client should retry the <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to classify.</param>
            <param name="exception">An <see cref="T:System.Exception" />, if any, that will
            also be used in the retry classification. Callers are intended to
            provide any exception thrown during the attempt to send the prior
            request.</param>
            <param name="isRetriable"><c>true</c> if the
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> should be retried.</param>
            <returns><c>true</c> if the classifier had an opinion regarding whether
            the service request should be retried; <c>false</c> otherwise.
            </returns>
            <remarks>
            Not all classifiers derived from <see cref="T:System.ClientModel.Primitives.PipelineMessageClassifier" />
            will classify a given <see cref="T:System.ClientModel.Primitives.PipelineMessage" />. Returning
            <c>false</c> from <see cref="M:System.ClientModel.Primitives.PipelineMessageClassifier.TryClassify(System.ClientModel.Primitives.PipelineMessage,System.Boolean@)" />
            allows a classifier instance to compose with other classifiers by
            passing the classification decision to a later instance.
            <see cref="P:System.ClientModel.Primitives.PipelineMessageClassifier.Default" /> will always return <c>true</c> from
            <see cref="M:System.ClientModel.Primitives.PipelineMessageClassifier.TryClassify(System.ClientModel.Primitives.PipelineMessage,System.Boolean@)" /> and may be used as
            the last classifier in any composed classifier collection.
            </remarks>
        </member>
        <member name="T:System.ClientModel.Primitives.RequestOptions">
            <summary>
            Options that can be used to control the behavior of a request sent by a client.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.RequestOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.ClientModel.Primitives.RequestOptions" /> class
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.RequestOptions.CancellationToken">
            <summary>
            Gets or sets the <see cref="P:System.ClientModel.Primitives.RequestOptions.CancellationToken" /> used for the duration
            of the call to <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.RequestOptions.ErrorOptions">
            <summary>
            Gets or sets a value that describes when a client's service method will
            raise an exception if the underlying response is considered an error.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.RequestOptions.BufferResponse">
            <summary>
            Gets or sets a value indicating whether the response content should be
            buffered in-memory by the pipeline. This value defaults to <c>true</c>.
            </summary>
            <remarks>Please note that setting this value to <c>false</c> will result
            in the <see cref="P:System.ClientModel.Primitives.PipelineResponse.ContentStream" /> obtained from
            <see cref="M:System.ClientModel.ClientResult.GetRawResponse" /> holding a live network stream.
            It is the responsibility of the caller to ensure the stream is disposed.
            </remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.RequestOptions.AddHeader(System.String,System.String)">
            <summary>
            Adds the specified header and its value to the request's header
            collection. If a header with this name is already present in the
            collection, the value will be added to the comma-separated list of
            values associated with the header.
            </summary>
            <param name="name">The name of the header to add.</param>
            <param name="value">The value of the header.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.RequestOptions.SetHeader(System.String,System.String)">
            <summary>
            Sets the specified header and its value in the request's header
            collection. If a header with this name is already present in the
            collection, the header's value will be replaced with the specified value.
            </summary>
            <param name="name">The name of the header to set.</param>
            <param name="value">The value of the header.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.RequestOptions.AddPolicy(System.ClientModel.Primitives.PipelinePolicy,System.ClientModel.Primitives.PipelinePosition)">
            <summary>
            Adds a <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> into the pipeline for the duration
            of this request.
            </summary>
            <param name="policy">The <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> to add to the
            pipeline.</param>
            <param name="position">The position of the policy in the pipeline.</param>
            <exception cref="T:System.ArgumentException">Thrown when the provided policy
            is <c>null</c>.</exception>
        </member>
        <member name="M:System.ClientModel.Primitives.RequestOptions.Apply(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            Apply the options provided in this <see cref="T:System.ClientModel.Primitives.RequestOptions" />
            instance to the <paramref name="message" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to apply the
            options to.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.RequestOptions.Freeze">
            <summary>
            Freeze this instance of <see cref="T:System.ClientModel.Primitives.RequestOptions" />.  After this method
            has been called, any attempt to set properties on the instance or call
            methods that would change its state will throw <see cref="T:System.InvalidOperationException" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.RequestOptions.AssertNotFrozen">
            <summary>
            Assert that <see cref="M:System.ClientModel.Primitives.RequestOptions.Freeze" /> has not been called on this
            <see cref="T:System.ClientModel.Primitives.RequestOptions" /> instance.
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown when an attempt is
            made to change the state of this <see cref="T:System.ClientModel.Primitives.RequestOptions" /> instance
            after <see cref="M:System.ClientModel.Primitives.RequestOptions.Freeze" /> has been called.</exception>
        </member>
        <member name="M:System.ClientModel.Primitives.ResponseStatusClassifier.#ctor(System.ReadOnlySpan{System.UInt16})">
            <summary>
            Creates a new instance of <see cref="T:System.ClientModel.Primitives.ResponseStatusClassifier" />.
            </summary>
            <param name="successStatusCodes">The status codes that this classifier
            will consider not to be errors.</param>
        </member>
        <member name="T:System.ClientModel.Primitives.ApiKeyAuthenticationPolicy">
            <summary>
            A <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> that uses an <see cref="T:System.ClientModel.ApiKeyCredential" />
            to set a value on a <see cref="T:System.ClientModel.Primitives.PipelineRequest" /> to authenticate with the
            cloud service.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.ApiKeyAuthenticationPolicy.CreateHeaderApiKeyPolicy(System.ClientModel.ApiKeyCredential,System.String,System.String)">
            <summary>
            Create a new instance of the <see cref="T:System.ClientModel.Primitives.ApiKeyAuthenticationPolicy" />
            class, where the credential value will be specified in a request header.
            </summary>
            <param name="credential">The <see cref="T:System.ClientModel.ApiKeyCredential" /> used to
            authenticate requests.</param>
            <param name="headerName">The name of the request header used to send
            the key credential in the request.</param>
            <param name="keyPrefix">A prefix to prepend before the key credential in
            the header value.
            If provided, the prefix string will be followed by a space and then the
            credential string.  For example, setting <c>valuePrefix</c> to
            "SharedAccessKey" will result in the header value being set to
            "SharedAccessKey {credential.Key}".</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ApiKeyAuthenticationPolicy.CreateBasicAuthorizationPolicy(System.ClientModel.ApiKeyCredential)">
            <summary>
            Create a new instance of the <see cref="T:System.ClientModel.Primitives.ApiKeyAuthenticationPolicy" />
            class, where the credential value will be set in the <c>Authorization</c>
            header on the <see cref="T:System.ClientModel.Primitives.PipelineRequest" /> with a <c>Basic</c> prefix.
            </summary>
            <param name="credential">The <see cref="T:System.ClientModel.ApiKeyCredential" /> used to
            authenticate requests.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ApiKeyAuthenticationPolicy.CreateBearerAuthorizationPolicy(System.ClientModel.ApiKeyCredential)">
            <summary>
            Create a new instance of the <see cref="T:System.ClientModel.Primitives.ApiKeyAuthenticationPolicy" />
            class, where the credential value will be set in the <c>Authorization</c>
            header on the <see cref="T:System.ClientModel.Primitives.PipelineRequest" /> with a <c>Bearer</c> prefix.
            </summary>
            <param name="credential">The <see cref="T:System.ClientModel.ApiKeyCredential" /> used to
            authenticate requests.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ApiKeyAuthenticationPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Process the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> according to the
            intended purpose of this <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />instance.
            Derived types must pass control to the next
            <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> in the pipeline by calling
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to process.</param>
            <param name="pipeline">The collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> instance whose
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" /> method was called to invoke
            this method.</param>
            <param name="currentIndex">The index of this policy in the
            <paramref name="pipeline" /> policy list. This value should be passed to
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            to pass control to the next policy in the pipeline.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ApiKeyAuthenticationPolicy.ProcessAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Process the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> according to the
            intended purpose of this <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />instance.
            Derived types must pass control to the next
            <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> in the pipeline by calling
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNextAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to process.</param>
            <param name="pipeline">The collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> instance whose
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.SendAsync(System.ClientModel.Primitives.PipelineMessage)" /> method was called to invoke
            this method.</param>
            <param name="currentIndex">The index of this policy in the
            <paramref name="pipeline" /> policy list. This value should be passed to
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNextAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            to pass control to the next policy in the pipeline.</param>
        </member>
        <member name="T:System.ClientModel.Primitives.ClientPipeline">
            <summary>
            Represents an extensible pipeline used by clients that call cloud services
            to send and receive HTTP request and responses. Creators of
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> can modify how it process a
            <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> by adding <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances at various points in the default pipeline.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientPipeline.Create(System.ClientModel.Primitives.ClientPipelineOptions)">
            <summary>
            Create an instance of a <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> from the provided
            <see cref="T:System.ClientModel.Primitives.ClientPipelineOptions" />.
            </summary>
            <param name="options">If provided, the
            <see cref="T:System.ClientModel.Primitives.ClientPipelineOptions" /> to use to construct the
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.</param>
            <returns>The created <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> instance.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientPipeline.Create(System.ClientModel.Primitives.ClientPipelineOptions,System.ReadOnlySpan{System.ClientModel.Primitives.PipelinePolicy},System.ReadOnlySpan{System.ClientModel.Primitives.PipelinePolicy},System.ReadOnlySpan{System.ClientModel.Primitives.PipelinePolicy})">
            <summary>
            Create an instance of a <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> from the provided
            <see cref="T:System.ClientModel.Primitives.ClientPipelineOptions" /> and <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            collections.
            </summary>
            <param name="options"> The <see cref="T:System.ClientModel.Primitives.ClientPipelineOptions" /> to use to
            construct the <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.</param>
            <param name="perCallPolicies">A collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances to add to the default pipeline before the pipeline's retry
            policy.</param>
            <param name="perTryPolicies">A collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances to add to the default pipeline after the pipeline's retry
            policy.</param>
            <param name="beforeTransportPolicies">A collection of
            <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> instances to add to the default pipeline
            before the pipeline's transport.</param>
            <returns>The created <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> instance.</returns>
            <remarks>Policies provided in <paramref name="options" /> are intended
            to come from the end-user of a client who has passed the
            <see cref="T:System.ClientModel.Primitives.ClientPipelineOptions" /> instance to the client's
            constructor. The client constructor implementation is intended to pass
            client-specific policies using the <paramref name="perCallPolicies" />,
            <paramref name="perTryPolicies" />, and
            <paramref name="beforeTransportPolicies" /> parameters and should not
            modify the <see cref="T:System.ClientModel.Primitives.ClientPipelineOptions" /> provided by the client
            user.
            </remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientPipeline.CreateMessage">
            <summary>
            Creates a <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> that can be sent using this
            pipeline instance.
            </summary>
            <returns>The created <see cref="T:System.ClientModel.Primitives.PipelineMessage" />.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            Send the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to send.</param>
            <exception cref="T:System.ClientModel.ClientResultException">Thrown if an error other than
            the service responding with an error response occurred while sending
            the HTTP request.</exception>
            <remarks>
            All necessary values on <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> should be
            set prior to calling <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />.  After the
            method returns, <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" /> will be populated
            with the details of the service response.
            </remarks>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientPipeline.SendAsync(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            Send the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to send.</param>
            <exception cref="T:System.ClientModel.ClientResultException">Thrown if an error other than
            the service responding with an error response occurred while sending
            the HTTP request.</exception>
            <remarks>
            All necessary values on <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> should be
            set prior to calling <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />.  After the
            method returns, <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" /> will be populated
            with the details of the service response.
            </remarks>
        </member>
        <member name="T:System.ClientModel.Primitives.ClientPipeline.RequestOptionsProcessor">
            <summary>
            Pipeline processor to advance through policies for pipeline customized
            per-request by passing RequestOptions to a protocol method.
            </summary>
        </member>
        <member name="T:System.ClientModel.Primitives.ClientRetryPolicy">
            <summary>
            A <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> used by a <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> to
            decide whether or not to retry a <see cref="T:System.ClientModel.Primitives.PipelineRequest" />.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.ClientRetryPolicy.Default">
            <summary>
            The <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> instance used by a default
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.#ctor(System.Int32)">
            <summary>
            Creates a new instance of the <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> class.
            </summary>
            <param name="maxRetries">The maximum number of retries to attempt.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.#ctor(System.Int32,System.Boolean,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Creates a new instance of the <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> class.
            </summary>
            <param name="maxRetries">The maximum number of retries to attempt.</param>
            <param name="enableLogging">If client-wide logging is enabled for this pipeline.</param>
            <param name="loggerFactory">The <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory" /> to use to create an <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance for logging.
            If one is not provided, logs are written to Event Source by default.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Process the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> according to the
            intended purpose of this <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />instance.
            Derived types must pass control to the next
            <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> in the pipeline by calling
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to process.</param>
            <param name="pipeline">The collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> instance whose
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" /> method was called to invoke
            this method.</param>
            <param name="currentIndex">The index of this policy in the
            <paramref name="pipeline" /> policy list. This value should be passed to
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            to pass control to the next policy in the pipeline.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.ProcessAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Process the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> according to the
            intended purpose of this <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />instance.
            Derived types must pass control to the next
            <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> in the pipeline by calling
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNextAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to process.</param>
            <param name="pipeline">The collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> instance whose
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.SendAsync(System.ClientModel.Primitives.PipelineMessage)" /> method was called to invoke
            this method.</param>
            <param name="currentIndex">The index of this policy in the
            <paramref name="pipeline" /> policy list. This value should be passed to
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNextAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            to pass control to the next policy in the pipeline.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.OnSendingRequest(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            A method that can be overridden by derived types to extend the default
            <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> logic.  It is called from
            <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            prior to passing control to the next policy in the pipeline.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            <see cref="T:System.ClientModel.Primitives.PipelineRequest" /> to be sent to the service.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.OnSendingRequestAsync(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            A method that can be overridden by derived types to extend the default
            <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> logic.  It is called from
            <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            prior to passing control to the next policy in the pipeline.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            <see cref="T:System.ClientModel.Primitives.PipelineRequest" /> to be sent to the service.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.OnRequestSent(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            A method that can be overridden by derived types to extend the default
            <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> logic.  It is called from
            <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            just after control has been returned from the policy at the position
            after the retry policy in the pipeline.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> that was received from the service.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.OnRequestSentAsync(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            A method that can be overridden by derived types to extend the default
            <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> logic.  It is called from
            <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            just after control has been returned from the policy at the position
            after the retry policy in the pipeline.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> that was received from the service.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.OnTryComplete(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            A method that can be overridden by derived types to extend the default
            <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> logic.  It is called from
            <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            after the time interval for the current retry attempt has passed.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> for this
            pipeline invocation.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.ShouldRetry(System.ClientModel.Primitives.PipelineMessage,System.Exception)">
            <summary>
            A method that can be overridden by derived types to customize the default
            <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> logic. It is called from
            <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            after control has been returned from the policy at the position
            after the retry policy in the pipeline.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> for this
            pipeline invocation.</param>
            <param name="exception">The exception, if any, that was thrown from
            a policy after the retry policy in the pipeline.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.ShouldRetryAsync(System.ClientModel.Primitives.PipelineMessage,System.Exception)">
            <summary>
            A method that can be overridden by derived types to customize the default
            <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> logic. It is called from
            <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            after control has been returned from the policy at the position
            after the retry policy in the pipeline.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> for this
            pipeline invocation.</param>
            <param name="exception">The exception, if any, that was thrown from
            a policy after the retry policy in the pipeline.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.GetNextDelay(System.ClientModel.Primitives.PipelineMessage,System.Int32)">
            <summary>
            A method that can be overridden by derived types to customize the default
            <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> logic. It is called from
            <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            to determine how long the policy should wait before re-sending the request.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> for this
            pipeline invocation.</param>
            <param name="tryCount">A number indicating how many times the policy has
            tried to send the request.</param>
            <returns>The amount of time to wait before the next retry attempt.
            </returns>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.WaitAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            A method that can be overridden by derived types to customize the default
            <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> logic. It is called from
            <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            to wait the time interval returned by <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.GetNextDelay(System.ClientModel.Primitives.PipelineMessage,System.Int32)" />.
            </summary>
            <param name="time">The amount of time to wait before trying to send the
            request again.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> used
            to cancel the wait if needed.</param>
            <returns>A task that can be awaited to asynchronously delay before the
            next retry attempt.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientRetryPolicy.Wait(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            A method that can be overridden by derived types to customize the default
            <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> logic. It is called from
            <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            to wait the time interval returned by <see cref="M:System.ClientModel.Primitives.ClientRetryPolicy.GetNextDelay(System.ClientModel.Primitives.PipelineMessage,System.Int32)" />.
            </summary>
            <param name="time">The amount of time to wait before trying to send the
            request again.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> used
            to cancel the wait if needed.</param>
        </member>
        <member name="T:System.ClientModel.Primitives.HttpClientPipelineTransport">
            <summary>
            An implementation of <see cref="T:System.ClientModel.Primitives.PipelineTransport" /> that uses a
            <see cref="T:System.Net.Http.HttpClient" /> to send and receive HTTP requests and responses.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.HttpClientPipelineTransport.Shared">
            <summary>
            A default instance of <see cref="T:System.ClientModel.Primitives.HttpClientPipelineTransport" /> that can
            be shared across pipelines and clients.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.HttpClientPipelineTransport.#ctor">
            <summary>
            Create a new instance of <see cref="T:System.ClientModel.Primitives.HttpClientPipelineTransport" /> that
            uses a shared default instance of <see cref="T:System.Net.Http.HttpClient" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.HttpClientPipelineTransport.#ctor(System.Net.Http.HttpClient)">
            <summary>
            Create a new instance of <see cref="T:System.ClientModel.Primitives.HttpClientPipelineTransport" /> that
            uses the provided <see cref="T:System.Net.Http.HttpClient" />.
            </summary>
            <param name="client">The <see cref="T:System.Net.Http.HttpClient" /> that this transport
            instance will use to send and receive HTTP requests and responses.
            </param>
        </member>
        <member name="M:System.ClientModel.Primitives.HttpClientPipelineTransport.#ctor(System.Net.Http.HttpClient,System.Boolean,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Create a new instance of <see cref="T:System.ClientModel.Primitives.HttpClientPipelineTransport" /> that
            uses the provided <see cref="T:System.Net.Http.HttpClient" />.
            </summary>
            <param name="client">The <see cref="T:System.Net.Http.HttpClient" /> that this transport
            instance will use to send and receive HTTP requests and responses. If no <see cref="T:System.Net.Http.HttpClient" />
            is passed, a default shared client will be used.
            </param>
            <param name="enableLogging">If client-wide logging is enabled for this pipeline.</param>
            <param name="loggerFactory">The <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory" /> to use to create an <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance for logging.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.HttpClientPipelineTransport.CreateMessageCore">
            <summary>
            Creates a new transport-specific instance of <see cref="T:System.ClientModel.Primitives.PipelineMessage" />.
            Types that derive from <see cref="T:System.ClientModel.Primitives.PipelineTransport" /> must implement this
            method to provide transport-specific functionality.
            </summary>
            <returns>A <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> that can be passed to
            <see cref="M:System.ClientModel.Primitives.PipelineTransport.Process(System.ClientModel.Primitives.PipelineMessage)" />.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.HttpClientPipelineTransport.ProcessCore(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            Transport-specific implementation used to sends the HTTP request
            contained by <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> and set the
            value of <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />. Types that derive
            from <see cref="T:System.ClientModel.Primitives.PipelineTransport" /> must implement this method to
            provide transport-specific functionality.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            request that was sent and response that was received by the transport.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.HttpClientPipelineTransport.ProcessCoreAsync(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            Transport-specific implementation used to sends the HTTP request
            contained by <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> and set the
            value of <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />. Types that derive
            from <see cref="T:System.ClientModel.Primitives.PipelineTransport" /> must implement this method to
            provide transport-specific functionality.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            request that was sent and response that was received by the transport.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.HttpClientPipelineTransport.OnSendingRequest(System.ClientModel.Primitives.PipelineMessage,System.Net.Http.HttpRequestMessage)">
            <summary>
            A method that can be overridden by derived types to extend the default
            <see cref="T:System.ClientModel.Primitives.HttpClientPipelineTransport" /> logic.  It is called from
            <see cref="M:System.ClientModel.Primitives.HttpClientPipelineTransport.ProcessCore(System.ClientModel.Primitives.PipelineMessage)" /> prior to sending the HTTP
            request.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            <see cref="T:System.ClientModel.Primitives.PipelineRequest" /> resulting from the processing of the
            policies in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> containing this transport.
            </param>
            <param name="httpRequest">The <see cref="T:System.Net.Http.HttpRequestMessage" /> created
            by the transport that will be sent to the service using this transport's
            <see cref="T:System.Net.Http.HttpClient" /> instance.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.HttpClientPipelineTransport.OnReceivedResponse(System.ClientModel.Primitives.PipelineMessage,System.Net.Http.HttpResponseMessage)">
            <summary>
            A method that can be overridden by derived types to extend the default
            <see cref="T:System.ClientModel.Primitives.HttpClientPipelineTransport" /> logic.  It is called from
            <see cref="M:System.ClientModel.Primitives.HttpClientPipelineTransport.ProcessCore(System.ClientModel.Primitives.PipelineMessage)" /> after the transport has
            created the <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> and set it on
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> created by the transport.</param>
            <param name="httpResponse">The <see cref="T:System.Net.Http.HttpResponseMessage" />
            returned by from the call to Send on <see cref="T:System.Net.Http.HttpClient" /> that the
            transport used to create <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />.
            </param>
        </member>
        <member name="M:System.ClientModel.Primitives.HttpClientPipelineTransport.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
        </member>
        <member name="M:System.ClientModel.Primitives.HttpClientPipelineTransport.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the
            <see cref="T:System.ClientModel.Primitives.HttpClientPipelineTransport" /> and optionally disposes of
            the managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and
            unmanaged resources; <c>false</c> to release only unmanaged resources.
            </param>
        </member>
        <member name="T:System.ClientModel.Primitives.MessageLoggingPolicy">
            <summary>
            A <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> used by a <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> to
            log request and response information.
            </summary>
        </member>
        <member name="P:System.ClientModel.Primitives.MessageLoggingPolicy.Default">
            <summary>
            The <see cref="T:System.ClientModel.Primitives.ClientRetryPolicy" /> instance used by a default
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.MessageLoggingPolicy.#ctor(System.ClientModel.Primitives.ClientLoggingOptions)">
            <summary>
            Creates a new instance of the <see cref="T:System.ClientModel.Primitives.MessageLoggingPolicy" /> class.
            </summary>
            <param name="options">The user-provided logging options object.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.MessageLoggingPolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Process the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> according to the
            intended purpose of this <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />instance.
            Derived types must pass control to the next
            <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> in the pipeline by calling
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to process.</param>
            <param name="pipeline">The collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> instance whose
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" /> method was called to invoke
            this method.</param>
            <param name="currentIndex">The index of this policy in the
            <paramref name="pipeline" /> policy list. This value should be passed to
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            to pass control to the next policy in the pipeline.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.MessageLoggingPolicy.ProcessAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Process the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> according to the
            intended purpose of this <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />instance.
            Derived types must pass control to the next
            <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> in the pipeline by calling
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNextAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to process.</param>
            <param name="pipeline">The collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> instance whose
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.SendAsync(System.ClientModel.Primitives.PipelineMessage)" /> method was called to invoke
            this method.</param>
            <param name="currentIndex">The index of this policy in the
            <paramref name="pipeline" /> policy list. This value should be passed to
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNextAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            to pass control to the next policy in the pipeline.</param>
        </member>
        <member name="T:System.ClientModel.Primitives.PipelinePolicy">
            <summary>
            A policy that can be added to a <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> to process a
            <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> during a call to
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />. Types deriving from
            <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> can read or modify the
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" />, implement functionality based on
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />, and must pass control to the next
            <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> in the pipeline by calling
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelinePolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Process the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> according to the
            intended purpose of this <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />instance.
            Derived types must pass control to the next
            <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> in the pipeline by calling
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to process.</param>
            <param name="pipeline">The collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> instance whose
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" /> method was called to invoke
            this method.</param>
            <param name="currentIndex">The index of this policy in the
            <paramref name="pipeline" /> policy list. This value should be passed to
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            to pass control to the next policy in the pipeline.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelinePolicy.ProcessAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Process the provided <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> according to the
            intended purpose of this <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />instance.
            Derived types must pass control to the next
            <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> in the pipeline by calling
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNextAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to process.</param>
            <param name="pipeline">The collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> instance whose
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.SendAsync(System.ClientModel.Primitives.PipelineMessage)" /> method was called to invoke
            this method.</param>
            <param name="currentIndex">The index of this policy in the
            <paramref name="pipeline" /> policy list. This value should be passed to
            <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNextAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            to pass control to the next policy in the pipeline.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Passes control to the next <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> in the
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to process.</param>
            <param name="pipeline">The collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.</param>
            <param name="currentIndex">The index of this policy in the
            <paramref name="pipeline" /> policy list. The derived-type implementation
            of <see cref="M:System.ClientModel.Primitives.PipelinePolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            should pass the value of <paramref name="currentIndex" /> it received
            without modifying it.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNextAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Passes control to the next <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> in the
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to process.</param>
            <param name="pipeline">The collection of <see cref="T:System.ClientModel.Primitives.PipelinePolicy" />
            instances in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" />.</param>
            <param name="currentIndex">The index of this policy in the
            <paramref name="pipeline" /> policy list. The derived-type implementation
            of <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            should pass the value of <paramref name="currentIndex" /> it received
            without modifying it.</param>
        </member>
        <member name="T:System.ClientModel.Primitives.PipelinePosition">
            <summary>
            The position at which to insert a <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> into the
            default <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> policy collection.
            </summary>
        </member>
        <member name="F:System.ClientModel.Primitives.PipelinePosition.PerCall">
            <summary>
            Insert the <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> before the pipeline's
            <see cref="P:System.ClientModel.Primitives.ClientPipelineOptions.RetryPolicy" />. Policies added to a
            pipeline in this position will run once per invocation of
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.Send(System.ClientModel.Primitives.PipelineMessage)" />.
            </summary>
        </member>
        <member name="F:System.ClientModel.Primitives.PipelinePosition.PerTry">
            <summary>
            Insert the <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> after the pipeline's
            <see cref="P:System.ClientModel.Primitives.ClientPipelineOptions.RetryPolicy" />. Policies added to a
            pipeline in this position will run each time the pipeline tries to send
            the <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" />.
            </summary>
        </member>
        <member name="F:System.ClientModel.Primitives.PipelinePosition.BeforeTransport">
            <summary>
            Insert the <see cref="T:System.ClientModel.Primitives.PipelinePolicy" /> just before the pipeline's
            <see cref="P:System.ClientModel.Primitives.ClientPipelineOptions.Transport" />. Policies added to a
            pipeline in this position will run after all other polices in the
            <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> have viewed the
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> and before they view the
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />.  Adding policies at this
            position should be done with care since changes made to the
            <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> by a before-transport policy
            will not be visible to any logging policies that come before it in the
            pipeline.
            </summary>
        </member>
        <member name="T:System.ClientModel.Primitives.PipelineTransport">
            <summary>
            Represents an HTTP pipeline transport used to send and receive HTTP requests
            and responses.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineTransport.#ctor">
            <summary>
            Creates a new instance of the <see cref="T:System.ClientModel.Primitives.PipelineTransport" /> class.
            </summary>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineTransport.#ctor(System.Boolean,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Creates a new instance of the <see cref="T:System.ClientModel.Primitives.PipelineTransport" /> class.
            </summary>
            <param name="enableLogging">If client-wide logging is enabled for this pipeline.</param>
            <param name="loggerFactory">The <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory" /> to use to create an <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance for logging.
            If one is not provided, logs are written to Event Source by default.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineTransport.CreateMessage">
            <summary>
            Create an instance of <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> that can be sent
            using this transport instance. This method will rarely be called directly;
            <see cref="M:System.ClientModel.Primitives.ClientPipeline.CreateMessage" /> should be called instead.
            </summary>
            <returns>A <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> that can be passed to
            <see cref="M:System.ClientModel.Primitives.PipelineTransport.Process(System.ClientModel.Primitives.PipelineMessage)" />.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineTransport.CreateMessageCore">
            <summary>
            Creates a new transport-specific instance of <see cref="T:System.ClientModel.Primitives.PipelineMessage" />.
            Types that derive from <see cref="T:System.ClientModel.Primitives.PipelineTransport" /> must implement this
            method to provide transport-specific functionality.
            </summary>
            <returns>A <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> that can be passed to
            <see cref="M:System.ClientModel.Primitives.PipelineTransport.Process(System.ClientModel.Primitives.PipelineMessage)" />.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineTransport.Process(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            Sends the HTTP request contained by <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" />
            and sets the value of <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            request that was sent and response that was received by the transport.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineTransport.ProcessAsync(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            Sends the HTTP request contained by <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" />
            and sets the value of <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            request that was sent and response that was received by the transport.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineTransport.ProcessCore(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            Transport-specific implementation used to sends the HTTP request
            contained by <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> and set the
            value of <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />. Types that derive
            from <see cref="T:System.ClientModel.Primitives.PipelineTransport" /> must implement this method to
            provide transport-specific functionality.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            request that was sent and response that was received by the transport.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineTransport.ProcessCoreAsync(System.ClientModel.Primitives.PipelineMessage)">
            <summary>
            Transport-specific implementation used to sends the HTTP request
            contained by <see cref="P:System.ClientModel.Primitives.PipelineMessage.Request" /> and set the
            value of <see cref="P:System.ClientModel.Primitives.PipelineMessage.Response" />. Types that derive
            from <see cref="T:System.ClientModel.Primitives.PipelineTransport" /> must implement this method to
            provide transport-specific functionality.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> containing the
            request that was sent and response that was received by the transport.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineTransport.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Implementation of <see cref="M:System.ClientModel.Primitives.PipelinePolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />.
            Since the transport is the last policy in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> policy
            chain, this method does not call <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            as other policy implementations do.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to pass to <see cref="M:System.ClientModel.Primitives.PipelineTransport.Process(System.ClientModel.Primitives.PipelineMessage)" />.</param>
            <param name="pipeline">The collection of policies in the pipeline.</param>
            <param name="currentIndex">The index of the current policy being processed
            in the pipeline invocation.</param>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineTransport.ProcessAsync(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)">
            <summary>
            Implementation of <see cref="M:System.ClientModel.Primitives.PipelinePolicy.Process(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />.
            Since the transport is the last policy in the <see cref="T:System.ClientModel.Primitives.ClientPipeline" /> policy
            chain, this method does not call <see cref="M:System.ClientModel.Primitives.PipelinePolicy.ProcessNext(System.ClientModel.Primitives.PipelineMessage,System.Collections.Generic.IReadOnlyList{System.ClientModel.Primitives.PipelinePolicy},System.Int32)" />
            as other policy implementations do.
            </summary>
            <param name="message">The <see cref="T:System.ClientModel.Primitives.PipelineMessage" /> to pass to <see cref="M:System.ClientModel.Primitives.PipelineTransport.Process(System.ClientModel.Primitives.PipelineMessage)" />.</param>
            <param name="pipeline">The collection of policies in the pipeline.</param>
            <param name="currentIndex">The index of the current policy being processed
            in the pipeline invocation.</param>
        </member>
        <member name="T:System.ClientModel.AsyncCollectionResult`1">
            <summary>
            Represents a collection of values returned from a cloud service operation.
            The collection values may be delivered over one or more service responses.
            </summary>
        </member>
        <member name="M:System.ClientModel.AsyncCollectionResult`1.#ctor">
            <summary>
            Creates a new instance of <see cref="T:System.ClientModel.AsyncCollectionResult`1" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(System.Threading.CancellationToken)">
            <summary>Returns an enumerator that iterates asynchronously through the collection.</summary><param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken" /> that may be used to cancel the asynchronous iteration.</param><returns>An enumerator that can be used to iterate asynchronously through the collection.</returns>
        </member>
        <member name="M:System.ClientModel.AsyncCollectionResult`1.GetValuesFromPageAsync(System.ClientModel.ClientResult)">
             <summary>
             Gets a collection of the values returned in a page response.
             </summary>
             <param name="page">The service response to obtain the values from.
             </param>
             <returns>A collection of <typeparamref name="T" /> values read from the
            response content in <paramref name="page" />.</returns>
             <remarks>This method does not take a <see cref="T:System.Threading.CancellationToken" />
             parameter.  <see cref="T:System.ClientModel.AsyncCollectionResult`1" /> implementations must
             store the <see cref="T:System.Threading.CancellationToken" /> passed to the service method
             that creates them and pass that token to any <c>async</c> methods
             called from this method.</remarks>
        </member>
        <member name="T:System.ClientModel.ClientResult">
            <summary>
            Represents the result of a cloud service operation.
            </summary>
        </member>
        <member name="M:System.ClientModel.ClientResult.#ctor(System.ClientModel.Primitives.PipelineResponse)">
            <summary>
            Creates a new instance of <see cref="T:System.ClientModel.ClientResult" /> from a service
            response.
            </summary>
            <param name="response">The <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> received
            from the service.</param>
        </member>
        <member name="M:System.ClientModel.ClientResult.GetRawResponse">
            <summary>
            Gets the <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> received from the service.
            </summary>
            <returns>The <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> received from the service.
            </returns>
        </member>
        <member name="M:System.ClientModel.ClientResult.FromResponse(System.ClientModel.Primitives.PipelineResponse)">
            <summary>
            Creates a new instance of <see cref="T:System.ClientModel.ClientResult" /> that holds the
            <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> received from the service.
            </summary>
            <param name="response">The response received from the service.</param>
            <returns>A new instance of <see cref="T:System.ClientModel.ClientResult`1" /> holding the
            provided <paramref name="response" />.
            </returns>
        </member>
        <member name="M:System.ClientModel.ClientResult.FromValue``1(``0,System.ClientModel.Primitives.PipelineResponse)">
            <summary>
            Creates a new instance of <see cref="T:System.ClientModel.ClientResult`1" /> that holds the
            provided model value and <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> received from
            the service.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="value">The strongly-typed representation of the service
            response payload value.</param>
            <param name="response">The response received from the service.</param>
            <returns>A new instance of <see cref="T:System.ClientModel.ClientResult`1" /> holding the
            provided <paramref name="value" /> and <paramref name="response" />.
            </returns>
        </member>
        <member name="M:System.ClientModel.ClientResult.FromOptionalValue``1(``0,System.ClientModel.Primitives.PipelineResponse)">
            <summary>
            Creates a new instance of <see cref="T:System.ClientModel.ClientResult`1" /> that holds the
            provided model value, if any, and the <see cref="T:System.ClientModel.Primitives.PipelineResponse" />
            received from the service. This method is used to create a return value
            for a service method representing a service operation that may or may not
            contain a payload. Callers of the client's service method must check
            whether <see cref="P:System.ClientModel.ClientResult`1.Value" /> is null to determine whether
            the service provided a value in its response. Nullable annotations
            indicate to the end-user the need to check whether
            <see cref="P:System.ClientModel.ClientResult`1.Value" /> is null.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="value">The strongly-typed representation of the service
            response payload value.</param>
            <param name="response">The response received from the service.</param>
            <returns>A new instance of <see cref="T:System.ClientModel.ClientResult`1" /> holding the
            provided <paramref name="value" /> and <paramref name="response" />.
            </returns>
        </member>
        <member name="T:System.ClientModel.ClientResultException">
            <summary>
            The exception that is thrown when the processing of a client request failed.
            </summary>
        </member>
        <member name="M:System.ClientModel.ClientResultException.CreateAsync(System.ClientModel.Primitives.PipelineResponse,System.Exception)">
            <summary>
            Asynchronously create an instance of <see cref="T:System.ClientModel.ClientResultException" />
            from the <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> containing the details of the
            service's error response.
            </summary>
            <param name="response">The service's error response.</param>
            <param name="innerException">The <see cref="P:System.Exception.InnerException" />,
            if any, that threw the current exception.</param>
            <returns>The <see cref="T:System.ClientModel.ClientResultException" /> instance that was
            created.</returns>
        </member>
        <member name="P:System.ClientModel.ClientResultException.Status">
            <summary>
            Gets the HTTP status code of the response. Returns. <code>0</code> if response was not received.
            </summary>
        </member>
        <member name="M:System.ClientModel.ClientResultException.#ctor(System.ClientModel.Primitives.PipelineResponse,System.Exception)">
            <summary>
            Initializes a new instance of <see cref="T:System.ClientModel.ClientResultException" /> from a
            <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> containing the details of the service's
            error response.  The <see cref="P:System.Exception.Message" /> is created from the
            provided <paramref name="response" />.
            </summary>
            <param name="response">The service's error response.</param>
            <param name="innerException">The <see cref="P:System.Exception.InnerException" />,
            if any, that threw the current exception.</param>
        </member>
        <member name="M:System.ClientModel.ClientResultException.#ctor(System.String,System.ClientModel.Primitives.PipelineResponse,System.Exception)">
            <summary>
            Initializes a new instance of <see cref="T:System.ClientModel.ClientResultException" /> with a
            custom exception message and an optional <see cref="T:System.ClientModel.Primitives.PipelineResponse" />.
            The <see cref="P:System.Exception.Message" /> is set to <paramref name="response" />
            and if <paramref name="response" /> is provided, it will be returned from
            calls to this exception instance's <see cref="M:System.ClientModel.ClientResultException.GetRawResponse" /> method.
            </summary>
            <param name="message">The message to set on <see cref="P:System.Exception.Message" />.
            </param>
            <param name="response">The response, if any, to return from
            <see cref="M:System.ClientModel.ClientResultException.GetRawResponse" />.</param>
            <param name="innerException">The <see cref="P:System.Exception.InnerException" />,
            if any, that threw the current exception.</param>
        </member>
        <member name="M:System.ClientModel.ClientResultException.GetRawResponse">
            <summary>
            Gets the <see cref="T:System.ClientModel.Primitives.PipelineResponse" />, if any, that led to the exception.
            </summary>
            <returns>The <see cref="T:System.ClientModel.Primitives.PipelineResponse" />, if any, that led to the exception.</returns>
        </member>
        <member name="T:System.ClientModel.ClientResult`1">
            <summary>
            Represents the result of a cloud service operation, and provides a
            strongly-typed representation of the service response value.
            </summary>
            <typeparam name="T">The type of the value returned in the service response.
            </typeparam>
        </member>
        <member name="M:System.ClientModel.ClientResult`1.#ctor(`0,System.ClientModel.Primitives.PipelineResponse)">
            <summary>
            Creates a new instance of <see cref="T:System.ClientModel.ClientResult`1" /> that holds the
            provided model value and <see cref="T:System.ClientModel.Primitives.PipelineResponse" /> received from
            the service.
            </summary>
            <param name="value">The strongly-typed representation of the service
            response payload value.</param>
            <param name="response">The response received from the service.</param>
        </member>
        <member name="P:System.ClientModel.ClientResult`1.Value">
            <summary>
            Gets the value received from the service.
            </summary>
        </member>
        <member name="M:System.ClientModel.ClientResult`1.op_Implicit(System.ClientModel.ClientResult{`0})~`0">
            <summary>
            Returns the value of this <see cref="T:System.ClientModel.ClientResult`1" /> object.
            </summary>
            <param name="result">The <see cref="T:System.ClientModel.ClientResult`1" /> instance.</param>
        </member>
        <member name="T:System.ClientModel.CollectionResult`1">
            <summary>
            Represents a collection of values returned from a cloud service operation.
            The collection values may be delivered over one or more service responses.
            </summary>
        </member>
        <member name="M:System.ClientModel.CollectionResult`1.#ctor">
            <summary>
            Creates a new instance of <see cref="T:System.ClientModel.CollectionResult`1" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.CollectionResult`1.GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:System.ClientModel.CollectionResult`1.GetValuesFromPage(System.ClientModel.ClientResult)">
             <summary>
             Gets a collection of the values returned in a page response.
             </summary>
             <param name="page">The service response to obtain the values from.
             </param>
             <returns>A collection of <typeparamref name="T" /> values read from the
            response content in <paramref name="page" />.</returns>
             <remarks><see cref="T:System.ClientModel.CollectionResult`1" /> implementations are expected
             to store the <see cref="T:System.Threading.CancellationToken" /> passed to the service
             method that creates them and pass that token to any methods making
             service calls that are called from this method.</remarks>
        </member>
        <member name="T:System.ClientModel.ContinuationToken">
            <summary>
            A token that can be passed to a client method to request or rehydrate a
            subclient from the state represented by the token.
            </summary>
        </member>
        <member name="M:System.ClientModel.ContinuationToken.#ctor">
            <summary>
            Create a new instance of <see cref="T:System.ClientModel.ContinuationToken" />.
            </summary>
            <remarks>This constructor is used by derived types to create an
            instance from values held by the client.</remarks>
        </member>
        <member name="M:System.ClientModel.ContinuationToken.#ctor(System.BinaryData)">
            <summary>
            Create a new instance of <see cref="T:System.ClientModel.ContinuationToken" />.
            </summary>
            <param name="bytes">Bytes that can be deserialized into a subtype of
            <see cref="T:System.ClientModel.ContinuationToken" />.</param>
        </member>
        <member name="M:System.ClientModel.ContinuationToken.FromBytes(System.BinaryData)">
            <summary>
            Create a new instance of <see cref="T:System.ClientModel.ContinuationToken" /> from the
            provided <paramref name="bytes" />.
            </summary>
            <param name="bytes">Bytes obtained from calling <see cref="M:System.ClientModel.ContinuationToken.ToBytes" />
            on a <see cref="T:System.ClientModel.ContinuationToken" />.</param>
            <returns>A <see cref="T:System.ClientModel.ContinuationToken" /> that can be passed to a
            client method to request or rehydrate a subclient equivalent to the one
            from which the original <see cref="T:System.ClientModel.ContinuationToken" /> bytes were
            obtained.
            </returns>
        </member>
        <member name="M:System.ClientModel.ContinuationToken.ToBytes">
            <summary>
            Write the bytes of this <see cref="T:System.ClientModel.ContinuationToken" />.
            </summary>
            <returns>The bytes of this <see cref="T:System.ClientModel.ContinuationToken" />.</returns>
            <exception cref="T:System.InvalidOperationException">Thrown if not implemented
            in a derived type.</exception>
        </member>
        <member name="M:System.ClientModel.Internal.Argument.AssertInRange``1(``0,``0,``0,System.String)">
            <summary>
            Throws if <paramref name="value" /> is less than the <paramref name="minimum" /> or greater than the <paramref name="maximum" />.
            </summary>
            <typeparam name="T">The type of to validate which implements <see cref="T:System.IComparable`1" />.</typeparam>
            <param name="value">The value to validate.</param>
            <param name="minimum">The minimum value to compare.</param>
            <param name="maximum">The maximum value to compare.</param>
            <param name="name">The name of the parameter.</param>
        </member>
        <member name="T:System.ClientModel.Internal.ArrayBackedPropertyBag`2">
            <summary>
            A property bag which is optimized for storage of a small number of items.
            If the item count is less than 2, there are no allocations. Any additional items are stored in an array which will grow as needed.
            MUST be passed by ref only.
            </summary>
        </member>
        <member name="T:System.ClientModel.Internal.BitVector640">
             <summary>
             This type effectively stores 640 bool values, but compresses their storage
             into ten ulongs, where each bit of the ulong represents a single bool value.
            
             It exposes a public indexer so that the bool values can be accessed as a
             standard .NET collection API.
            
             It is used in System.ClientModel and Azure.Core to implement response
             classifiers, where each true bit represents a status code that the
             classifier considers a success code.
             </summary>
        </member>
        <member name="M:System.ClientModel.Internal.CancellationHelper.ShouldWrapInOperationCanceledException(System.Exception,System.Threading.CancellationToken)">
            <summary>Determines whether to wrap an <see cref="T:System.Exception" /> in a cancellation exception.</summary>
            <param name="exception">The exception.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that may have triggered the exception.</param>
            <returns><c>true</c> if the exception should be wrapped; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:System.ClientModel.Internal.CancellationHelper.CreateOperationCanceledException(System.Exception,System.Threading.CancellationToken,System.String)">
            <summary>Creates a cancellation exception.</summary>
            <param name="innerException">The inner exception to wrap. May be null.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that triggered the cancellation.</param>
            <param name="message">The custom message to use.</param>
            <returns>The cancellation exception.</returns>
        </member>
        <member name="M:System.ClientModel.Internal.CancellationHelper.ThrowIfCancellationRequested(System.Threading.CancellationToken,System.Exception)">
            <summary>Throws a cancellation exception if cancellation has been requested via <paramref name="cancellationToken" />.</summary>
            <param name="cancellationToken">The token to check for a cancellation request.</param>
            <param name="innerException">The inner exception to wrap. May be null.</param>
        </member>
        <member name="M:System.ClientModel.Internal.CancellationHelper.ThrowIfCancellationRequestedOrTimeout(System.Threading.CancellationToken,System.Threading.CancellationToken,System.Exception,System.TimeSpan)">
            <summary>Throws a cancellation exception if cancellation has been requested via <paramref name="messageToken" /> or <paramref name="timeoutToken" />.</summary>
            <param name="messageToken">The user-provided token.</param>
            <param name="timeoutToken">The linked token that is cancelled on timeout provided token.</param>
            <param name="innerException">The inner exception to use.</param>
            <param name="timeout">The timeout used for the operation.</param>
        </member>
        <member name="M:System.ClientModel.Internal.PipelineMessageLogger.IsEnabled(Microsoft.Extensions.Logging.LogLevel,System.Diagnostics.Tracing.EventLevel)">
            <summary>
             Whether the given log level or event level is enabled, depending
             on whether this handler logs to ILogger or Event Source. Should be
             used to guard expensive operations.
            </summary>
            <param name="logLevel">The LogLevel to log to for ILogger. If an ILogger WAS NOT provided to the constructor, this value will be ignored.</param>
            <param name="eventLevel">The EventLevel to log to for EventSource. If an ILogger WAS provided to the constructor, this value will be ignored.</param>
        </member>
        <member name="T:System.ClientModel.Internal.ReadTimeoutStream">
            <summary>
            Read-only Stream that will throw a <see cref="T:System.OperationCanceledException" /> if it has to wait longer than a configurable timeout to read more data
            </summary>
        </member>
        <member name="T:System.ClientModel.Internal.TaskExtensions.Enumerable`1">
            <summary>
            Both <see cref="T:System.ClientModel.Internal.TaskExtensions.Enumerable`1" /> and <see cref="T:System.ClientModel.Internal.TaskExtensions.Enumerator`1" /> are defined as public structs so that foreach can use duck typing
            to call <see cref="M:System.ClientModel.Internal.TaskExtensions.Enumerable`1.GetEnumerator" /> and avoid heap memory allocation.
            Please don't delete this method and don't make these types private.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:System.ClientModel.Internal.ModelWriter`1">
            <summary>
            Provides an efficient way to write <see cref="T:System.ClientModel.Primitives.IJsonModel`1" /> into a <see cref="T:System.BinaryData" /> using multiple pooled buffers.
            </summary>
        </member>
        <member name="M:System.ClientModel.Internal.ModelWriter`1.#ctor(System.ClientModel.Primitives.IJsonModel{`0},System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Initializes a new instance of <see cref="T:System.ClientModel.Internal.ModelWriter`1" />.
            </summary>
            <param name="model">The model to write.</param>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" /> to use.</param>
            <exception cref="T:System.NotSupportedException">If the model does not support the requested <see cref="P:System.ClientModel.Primitives.ModelReaderWriterOptions.Format" />.</exception>
        </member>
        <member name="T:System.ClientModel.Internal.UnsafeBufferSequence">
            <summary>
            This class is a helper to write to a <see cref="T:System.Buffers.IBufferWriter`1" /> in a thread safe manner.
            It uses the shared pool to allocate buffers and returns them to the pool when disposed.
            Since there is no way to ensure someone didn't keep a reference to one of the buffers
            it must be disposed of in the same context it was created and its referenced should not be stored or shared.
            </summary>
            <summary>
            Provides a way to read a <see cref="T:System.ClientModel.Internal.UnsafeBufferSequence" /> without exposing the underlying buffers.
            This class is not thread safe and should only be used by one thread at a time.
            If you dispose while another thread is copying you will end up with a partial copy.
            </summary>
        </member>
        <member name="M:System.ClientModel.Internal.UnsafeBufferSequence.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:System.ClientModel.Internal.UnsafeBufferSequence" />.
            </summary>
            <param name="segmentSize">The size of each buffer segment.</param>
        </member>
        <member name="M:System.ClientModel.Internal.UnsafeBufferSequence.Advance(System.Int32)">
            <summary>
            Notifies the <see cref="T:System.ClientModel.Internal.UnsafeBufferSequence" /> that bytes bytes were written to the output <see cref="T:System.Span`1" /> or <see cref="T:System.Memory`1" />.
            You must request a new buffer after calling <see cref="M:System.ClientModel.Internal.UnsafeBufferSequence.Advance(System.Int32)" /> to continue writing more data; you cannot write to a previously acquired buffer.
            </summary>
            <param name="bytesWritten">The number of bytes written to the <see cref="T:System.Span`1" /> or <see cref="T:System.Memory`1" />.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"></exception>
        </member>
        <member name="M:System.ClientModel.Internal.UnsafeBufferSequence.GetMemory(System.Int32)">
            <summary>
            Returns a <see cref="T:System.Memory`1" /> to write to that is at least the requested size, as specified by the <paramref name="sizeHint" /> parameter.
            </summary>
            <param name="sizeHint">The minimum length of the returned <see cref="T:System.Memory`1" />. If less than 256, a buffer of size 256 will be returned.</param>
            <returns>A memory buffer of at least <paramref name="sizeHint" /> bytes. If <paramref name="sizeHint" /> is less than 256, a buffer of size 256 will be returned.</returns>
        </member>
        <member name="M:System.ClientModel.Internal.UnsafeBufferSequence.GetSpan(System.Int32)">
            <summary>
            Returns a <see cref="T:System.Span`1" /> to write to that is at least the requested size, as specified by the <paramref name="sizeHint" /> parameter.
            </summary>
            <param name="sizeHint">The minimum length of the returned <see cref="T:System.Span`1" />. If less than 256, a buffer of size 256 will be returned.</param>
            <returns>A buffer of at least <paramref name="sizeHint" /> bytes. If <paramref name="sizeHint" /> is less than 256, a buffer of size 256 will be returned.</returns>
        </member>
        <member name="M:System.ClientModel.Internal.UnsafeBufferSequence.Dispose">
            <summary>
            Disposes the SequenceWriter and returns the underlying buffers to the pool.
            </summary>
        </member>
        <member name="T:System.ClientModel.BinaryContent">
            <summary>
            Represents binary content that can be sent to a cloud service as part of
            a <see cref="T:System.ClientModel.Primitives.PipelineRequest" />.
            </summary>
        </member>
        <member name="M:System.ClientModel.BinaryContent.Create(System.BinaryData)">
            <summary>
            Creates an instance of <see cref="T:System.ClientModel.BinaryContent" /> that contains the
            bytes held in the provided <see cref="T:System.BinaryData" /> instance.
            </summary>
            <param name="value">The <see cref="T:System.BinaryData" /> containing the bytes
            this <see cref="T:System.ClientModel.BinaryContent" /> will hold.</param>
            <returns>An an instance of <see cref="T:System.ClientModel.BinaryContent" /> that contains the
            bytes held in the provided <see cref="T:System.BinaryData" /> instance.</returns>
        </member>
        <member name="M:System.ClientModel.BinaryContent.Create``1(``0,System.ClientModel.Primitives.ModelReaderWriterOptions)">
            <summary>
            Creates an instance of <see cref="T:System.ClientModel.BinaryContent" /> that contains the
            bytes resulting from writing the value of the provided
            <see cref="T:System.ClientModel.Primitives.IPersistableModel`1" />.
            </summary>
            <param name="model">The <see cref="T:System.ClientModel.Primitives.IPersistableModel`1" /> to write.</param>
            <param name="options">The <see cref="T:System.ClientModel.Primitives.ModelReaderWriterOptions" />, if any,
            that indicates what format the <paramref name="model" /> will be written in.
            </param>
            <returns>An instance of <see cref="T:System.ClientModel.BinaryContent" /> that wraps a <see cref="T:System.ClientModel.Primitives.IPersistableModel`1" />.</returns>
        </member>
        <member name="M:System.ClientModel.BinaryContent.Create(System.IO.Stream)">
            <summary>
            Creates an instance of <see cref="T:System.ClientModel.BinaryContent" /> that contains the
            bytes held in the provided <see cref="T:System.IO.Stream" /> instance.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream" /> containing the bytes
            this <see cref="T:System.ClientModel.BinaryContent" /> will hold.</param>
            <returns>An an instance of <see cref="T:System.ClientModel.BinaryContent" /> that contains the
            bytes held in the provided <see cref="T:System.IO.Stream" /> instance.</returns>
        </member>
        <member name="M:System.ClientModel.BinaryContent.TryComputeLength(System.Int64@)">
            <summary>
            Attempts to compute the length of the underlying body content, if available.
            </summary>
            <param name="length">The length of the underlying data.</param>
        </member>
        <member name="M:System.ClientModel.BinaryContent.WriteToAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Writes contents of this <see cref="T:System.ClientModel.BinaryContent" /> instance to the
            provided <see cref="T:System.IO.Stream" />.
            </summary>
            <param name="stream">The stream to write the binary content to.</param>
            <param name="cancellationToken">To <see cref="T:System.Threading.CancellationToken" /> to
            use for the write operation.</param>
        </member>
        <member name="M:System.ClientModel.BinaryContent.WriteTo(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Writes contents of this <see cref="T:System.ClientModel.BinaryContent" /> instance to the
            provided <see cref="T:System.IO.Stream" />.
            </summary>
            <param name="stream">The stream to write the binary content to.</param>
            <param name="cancellationToken">To <see cref="T:System.Threading.CancellationToken" /> to
            use for the write operation.</param>
        </member>
        <member name="M:System.ClientModel.BinaryContent.Dispose">
            <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue" />, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue" />, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute">
            <summary>
            Indicates that the specified method requires the ability to generate new code at runtime,
            for example through <see cref="N:System.Reflection" />.
            </summary>
            <remarks>
            This allows tools to understand which methods are unsafe to call when compiling ahead of time.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute" /> class
            with the specified message.
            </summary>
            <param name="message">
            A message that contains information about the usage of dynamic code.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.Message">
            <summary>
            Gets a message that contains information about the usage of dynamic code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the method,
            why it requires dynamic code, and what options a consumer has to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute">
            <summary>
            Indicates that the specified method requires dynamic access to code that is not referenced
            statically, for example through <see cref="N:System.Reflection" />.
            </summary>
            <remarks>
            This allows tools to understand which methods are unsafe to call when removing unreferenced
            code from an application.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute" /> class
            with the specified message.
            </summary>
            <param name="message">
            A message that contains information about the usage of unreferenced code.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Message">
            <summary>
            Gets a message that contains information about the usage of unreferenced code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the method,
            why it requires unreferenced code, and what options a consumer has to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute">
            <summary>
            Suppresses reporting of a specific rule violation, allowing multiple suppressions on a
            single code artifact.
            </summary>
            <remarks>
            <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute" /> is different than
            <see cref="T:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute" /> in that it doesn't have a
            <see cref="T:System.Diagnostics.ConditionalAttribute" />. So it is always preserved in the compiled assembly.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute" />
            class, specifying the category of the tool and the identifier for an analysis rule.
            </summary>
            <param name="category">The category for the attribute.</param>
            <param name="checkId">The identifier of the analysis rule the attribute applies to.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category">
            <summary>
            Gets the category identifying the classification of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category" /> property describes the tool or tool analysis category
            for which a message suppression attribute applies.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId">
            <summary>
            Gets the identifier of the analysis tool rule to be suppressed.
            </summary>
            <remarks>
            Concatenated together, the <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category" /> and <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId" />
            properties form a unique check identifier.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Scope">
            <summary>
            Gets or sets the scope of the code that is relevant for the attribute.
            </summary>
            <remarks>
            The Scope property is an optional argument that specifies the metadata scope for which
            the attribute is relevant.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target">
            <summary>
            Gets or sets a fully qualified path that represents the target of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target" /> property is an optional argument identifying the analysis target
            of the attribute. An example value is "System.IO.Stream.ctor():System.Void".
            Because it is fully qualified, it can be long, particularly for targets such as parameters.
            The analysis tool user interface should be capable of automatically formatting the parameter.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId">
            <summary>
            Gets or sets an optional argument expanding on exclusion criteria.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId" /> property is an optional argument that specifies additional
            exclusion where the literal metadata target is not sufficiently precise. For example,
            the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute" /> cannot be applied within a method,
            and it may be desirable to suppress a violation against a statement in the method that will
            give a rule violation, but not against all statements in the method.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Justification">
            <summary>
            Gets or sets the justification for suppressing the code analysis message.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute">
            <summary>
            States a dependency that one member has on another.
            </summary>
            <remarks>
            This can be used to inform tooling of a dependency that is otherwise not evident purely from
            metadata and IL, for example a member relied on via reflection.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute" /> class
            with the specified signature of a member on the same type as the consumer.
            </summary>
            <param name="memberSignature">The signature of the member depended on.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute" /> class
            with the specified signature of a member on a <see cref="T:System.Type" />.
            </summary>
            <param name="memberSignature">The signature of the member depended on.</param>
            <param name="type">The <see cref="T:System.Type" /> containing <paramref name="memberSignature" />.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute" /> class
            with the specified signature of a member on a type in an assembly.
            </summary>
            <param name="memberSignature">The signature of the member depended on.</param>
            <param name="typeName">The full name of the type containing the specified member.</param>
            <param name="assemblyName">The assembly name of the type containing the specified member.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute" /> class
            with the specified types of members on a <see cref="T:System.Type" />.
            </summary>
            <param name="memberTypes">The types of members depended on.</param>
            <param name="type">The <see cref="T:System.Type" /> containing the specified members.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute" /> class
            with the specified types of members on a type in an assembly.
            </summary>
            <param name="memberTypes">The types of members depended on.</param>
            <param name="typeName">The full name of the type containing the specified members.</param>
            <param name="assemblyName">The assembly name of the type containing the specified members.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberSignature">
            <summary>
            Gets the signature of the member depended on.
            </summary>
            <remarks>
            Either <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberSignature" /> must be a valid string or <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberTypes" />
            must not equal <see cref="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None" />, but not both.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberTypes">
            <summary>
            Gets the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes" /> which specifies the type
            of members depended on.
            </summary>
            <remarks>
            Either <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberSignature" /> must be a valid string or <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberTypes" />
            must not equal <see cref="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None" />, but not both.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Type">
            <summary>
            Gets the <see cref="T:System.Type" /> containing the specified member.
            </summary>
            <remarks>
            If neither <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Type" /> nor <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName" /> are specified,
            the type of the consumer is assumed.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName">
            <summary>
            Gets the full name of the type containing the specified member.
            </summary>
            <remarks>
            If neither <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Type" /> nor <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName" /> are specified,
            the type of the consumer is assumed.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.AssemblyName">
            <summary>
            Gets the assembly name of the specified type.
            </summary>
            <remarks>
            <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.AssemblyName" /> is only valid when <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName" /> is specified.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Condition">
            <summary>
            Gets or sets the condition in which the dependency is applicable, e.g. "DEBUG".
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute">
             <summary>
             Indicates that certain members on a specified <see cref="T:System.Type" /> are accessed dynamically,
             for example through <see cref="N:System.Reflection" />.
             </summary>
             <remarks>
             This allows tools to understand which members are being accessed during the execution
             of a program.
            
             This attribute is valid on members whose type is <see cref="T:System.Type" /> or <see cref="T:System.String" />.
            
             When this attribute is applied to a location of type <see cref="T:System.String" />, the assumption is
             that the string represents a fully qualified type name.
            
             When this attribute is applied to a class, interface, or struct, the members specified
             can be accessed dynamically on <see cref="T:System.Type" /> instances returned from calling
             <see cref="M:System.Object.GetType" /> on instances of that class, interface, or struct.
            
             If the attribute is applied to a method it's treated as a special case and it implies
             the attribute should be applied to the "this" parameter of the method. As such the attribute
             should only be used on instance methods of types assignable to System.Type (or string, but no methods
             will use it there).
             </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute" /> class
            with the specified member types.
            </summary>
            <param name="memberTypes">The types of members dynamically accessed.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.MemberTypes">
            <summary>
            Gets the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes" /> which specifies the type
            of members dynamically accessed.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes">
             <summary>
             Specifies the types of members that are dynamically accessed.
            
             This enumeration has a <see cref="T:System.FlagsAttribute" /> attribute that allows a
             bitwise combination of its member values.
             </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None">
            <summary>
            Specifies no members.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicParameterlessConstructor">
            <summary>
            Specifies the default, parameterless public constructor.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicConstructors">
            <summary>
            Specifies all public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicConstructors">
            <summary>
            Specifies all non-public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicMethods">
            <summary>
            Specifies all public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicMethods">
            <summary>
            Specifies all non-public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicFields">
            <summary>
            Specifies all public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicFields">
            <summary>
            Specifies all non-public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicNestedTypes">
            <summary>
            Specifies all public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicNestedTypes">
            <summary>
            Specifies all non-public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicProperties">
            <summary>
            Specifies all public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicProperties">
            <summary>
            Specifies all non-public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicEvents">
            <summary>
            Specifies all public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicEvents">
            <summary>
            Specifies all non-public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.Interfaces">
            <summary>
            Specifies all interfaces implemented by the type.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.All">
            <summary>
            Specifies all members.
            </summary>
        </member>
        <member name="M:System.ClientModel.CollectionResult`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:System.ClientModel.Internal.ChangeTrackingStringList.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:System.ClientModel.Internal.PipelineMessageHeadersLogValue.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineRequestHeaders.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.PipelineResponseHeaders.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:System.ClientModel.Internal.TaskExtensions.Enumerable`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary><returns>An enumerator that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:System.ClientModel.Internal.TaskExtensions.Enumerable`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="P:System.ClientModel.Internal.TaskExtensions.Enumerator`1.System#Collections#IEnumerator#Current">
            <summary>Gets the element in the collection at the current position of the enumerator.</summary><returns>The element in the collection at the current position of the enumerator.</returns>
        </member>
        <member name="M:System.ClientModel.Primitives.ClientPipeline.RequestOptionsProcessor.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary><returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
    </members>
</doc>
