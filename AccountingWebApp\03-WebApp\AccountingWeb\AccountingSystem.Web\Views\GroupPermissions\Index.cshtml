@using AccountingSystem.Web.Controllers
@model GroupPermissionsViewModel
@{
    ViewData["Title"] = "إدارة صلاحيات المجموعات";
    Layout = "_Layout";
}

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-shield-alt"></i>
                    إدارة صلاحيات المجموعات
                </h1>
                <div class="page-options">
                    <button type="button" class="btn btn-success" onclick="checkAllPermissions()">
                        <i class="fas fa-check-double"></i>
                        تحديد الكل
                    </button>
                    <button type="button" class="btn btn-warning" onclick="uncheckAllPermissions()">
                        <i class="fas fa-times"></i>
                        إلغاء تحديد الكل
                    </button>
                    <button type="button" class="btn btn-primary" onclick="savePermissions()" id="saveBtn" disabled>
                        <i class="fas fa-save"></i>
                        حفظ الصلاحيات
                    </button>
                    <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#createGroupModal">
                        <i class="fas fa-plus"></i>
                        إضافة مجموعة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            @TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="row">
        <!-- Groups Panel -->
        <div class="col-lg-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users"></i>
                        مجموعات المستخدمين
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="groupsList">
                        @foreach (var group in Model.Groups)
                        {
                            <div class="list-group-item list-group-item-action group-item" 
                                 data-group-id="@group.GroupID" 
                                 onclick="selectGroup(@group.GroupID, '@group.GroupName')">
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1 fw-bold">@group.GroupName</h6>
                                        <small class="text-muted">
                                            <i class="fas fa-user"></i>
                                            @group.UserCount مستخدم
                                        </small>
                                    </div>
                                    @if (group.GroupID != 1)
                                    {
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteGroup(@group.GroupID, '@group.GroupName', event)" 
                                                title="حذف المجموعة">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions Panel -->
        <div class="col-lg-9">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-key"></i>
                            صلاحيات المجموعة: <span id="selectedGroupName" class="text-primary fw-bold">اختر مجموعة</span>
                        </h5>
                        <div class="btn-group" id="permissionActions" style="display: none;">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="expandAllNodes()">
                                <i class="fas fa-expand"></i>
                                توسيع الكل
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAllNodes()">
                                <i class="fas fa-compress"></i>
                                طي الكل
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="permissionsContainer">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-hand-pointer fa-3x mb-3"></i>
                            <p>اختر مجموعة من القائمة لعرض وتعديل صلاحياتها</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Group Modal -->
<div class="modal fade" id="createGroupModal" tabindex="-1" aria-labelledby="createGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="createGroupForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="createGroupModalLabel">إضافة مجموعة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" dir="rtl">
                    <div class="mb-3">
                        <label for="groupName" class="form-label">اسم المجموعة</label>
                        <input type="text" class="form-control" id="groupName" name="groupName" required />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        إنشاء المجموعة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let currentGroupId = null;
        let allForms = @Html.Raw(Json.Serialize(Model.Forms));
        let originalPermissions = [];

        // Select group and load permissions
        function selectGroup(groupId, groupName) {
            currentGroupId = groupId;
            
            // Update UI
            $('.group-item').removeClass('active');
            $(`.group-item[data-group-id="${groupId}"]`).addClass('active');
            $('#selectedGroupName').text(groupName);
            $('#permissionActions').show();
            $('#saveBtn').prop('disabled', false);

            // Load permissions
            loadGroupPermissions(groupId);
        }

        // Load group permissions
        function loadGroupPermissions(groupId) {
            $('#permissionsContainer').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>');

            $.get(`/GroupPermissions/GetGroupPermissions?groupId=${groupId}`)
                .done(function(data) {
                    if (data.success) {
                        originalPermissions = data.permissions;
                        buildPermissionsTree(data.permissions);
                    } else {
                        showAlert('danger', data.error || 'حدث خطأ أثناء جلب الصلاحيات');
                    }
                })
                .fail(function() {
                    showAlert('danger', 'حدث خطأ أثناء جلب الصلاحيات');
                });
        }

        // Build permissions tree
        function buildPermissionsTree(selectedPermissions) {
            let html = '<div class="permissions-tree" id="permissionsTree">';
            
            function buildNode(form, level = 0) {
                const isChecked = selectedPermissions.includes(form.formID);
                const indent = level * 25;
                const isContainer = form.isContainer;
                
                html += `
                    <div class="permission-node" style="margin-right: ${indent}px;">
                        <div class="form-check">
                            <input class="form-check-input permission-checkbox" 
                                   type="checkbox" 
                                   id="form_${form.formID}" 
                                   data-form-id="${form.formID}"
                                   data-parent-id="${form.parentFormID || ''}"
                                   data-is-container="${isContainer}"
                                   ${isChecked ? 'checked' : ''}
                                   onchange="handlePermissionChange(this)">
                            <label class="form-check-label ${isContainer ? 'fw-bold text-primary' : ''}" for="form_${form.formID}">
                                ${isContainer ? '<i class="fas fa-folder text-warning"></i>' : '<i class="fas fa-file text-info"></i>'}
                                ${form.displayText || form.formName}
                            </label>
                        </div>
                    </div>
                `;
                
                if (form.children && form.children.length > 0) {
                    form.children.forEach(child => buildNode(child, level + 1));
                }
            }
            
            allForms.forEach(form => buildNode(form));
            html += '</div>';
            
            $('#permissionsContainer').html(html);
        }

        // Handle permission checkbox change
        function handlePermissionChange(checkbox) {
            const formId = $(checkbox).data('form-id');
            const isChecked = checkbox.checked;
            const isContainer = $(checkbox).data('is-container') === 'true';
            
            // Auto-check/uncheck children
            if (isContainer) {
                checkUncheckChildren(formId, isChecked);
            }
            
            // Auto-check parents if child is checked
            if (isChecked) {
                checkParents(formId);
            } else {
                // Uncheck parents if no siblings are checked
                uncheckParentsIfNoSiblingsChecked(formId);
            }
        }

        // Check/uncheck children recursively
        function checkUncheckChildren(parentFormId, isChecked) {
            $(`.permission-checkbox[data-parent-id="${parentFormId}"]`).each(function() {
                this.checked = isChecked;
                const childFormId = $(this).data('form-id');
                const isChildContainer = $(this).data('is-container') === 'true';
                
                if (isChildContainer) {
                    checkUncheckChildren(childFormId, isChecked);
                }
            });
        }

        // Check parents if any child is checked
        function checkParents(formId) {
            const parentId = $(`.permission-checkbox[data-form-id="${formId}"]`).data('parent-id');
            if (parentId) {
                const parentCheckbox = $(`.permission-checkbox[data-form-id="${parentId}"]`)[0];
                if (parentCheckbox && !parentCheckbox.checked) {
                    parentCheckbox.checked = true;
                    checkParents(parentId);
                }
            }
        }

        // Uncheck parents if no siblings are checked
        function uncheckParentsIfNoSiblingsChecked(formId) {
            const parentId = $(`.permission-checkbox[data-form-id="${formId}"]`).data('parent-id');
            if (parentId) {
                const parentCheckbox = $(`.permission-checkbox[data-form-id="${parentId}"]`)[0];
                if (parentCheckbox) {
                    // Check if any sibling is checked
                    let anyChildChecked = false;
                    $(`.permission-checkbox[data-parent-id="${parentId}"]`).each(function() {
                        if (this.checked) {
                            anyChildChecked = true;
                            return false; // break loop
                        }
                    });

                    if (!anyChildChecked && parentCheckbox.checked) {
                        parentCheckbox.checked = false;
                        uncheckParentsIfNoSiblingsChecked(parentId);
                    }
                }
            }
        }

        // Check all permissions
        function checkAllPermissions() {
            $('.permission-checkbox').prop('checked', true);
        }

        // Uncheck all permissions
        function uncheckAllPermissions() {
            $('.permission-checkbox').prop('checked', false);
        }

        // Expand all nodes
        function expandAllNodes() {
            // This would be implemented if we had collapsible sections
            showAlert('info', 'تم توسيع جميع العناصر');
        }

        // Collapse all nodes
        function collapseAllNodes() {
            // This would be implemented if we had collapsible sections
            showAlert('info', 'تم طي جميع العناصر');
        }

        // Save permissions
        function savePermissions() {
            if (!currentGroupId) {
                showAlert('warning', 'يرجى اختيار مجموعة أولاً');
                return;
            }

            const selectedFormIds = [];
            $('.permission-checkbox:checked').each(function() {
                selectedFormIds.push($(this).data('form-id'));
            });

            const data = {
                groupId: currentGroupId,
                formIds: selectedFormIds
            };

            // Show loading
            $('#saveBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');

            $.ajax({
                url: '/GroupPermissions/SavePermissions',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                headers: {
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('success', response.message);
                        originalPermissions = selectedFormIds;
                    } else {
                        showAlert('danger', response.message);
                    }
                },
                error: function() {
                    showAlert('danger', 'حدث خطأ أثناء حفظ الصلاحيات');
                },
                complete: function() {
                    $('#saveBtn').prop('disabled', false).html('<i class="fas fa-save"></i> حفظ الصلاحيات');
                }
            });
        }

        // Create new group
        $('#createGroupForm').on('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('groupName', $('#groupName').val());
            formData.append('__RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());

            $.ajax({
                url: '/GroupPermissions/CreateGroup',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        showAlert('success', response.message);
                        $('#createGroupModal').modal('hide');
                        location.reload(); // Refresh to show new group
                    } else {
                        showAlert('danger', response.message);
                    }
                },
                error: function() {
                    showAlert('danger', 'حدث خطأ أثناء إنشاء المجموعة');
                }
            });
        });

        // Delete group
        function deleteGroup(groupId, groupName, event) {
            event.stopPropagation(); // Prevent group selection
            
            if (confirm(`هل أنت متأكد من حذف المجموعة "${groupName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                const formData = new FormData();
                formData.append('groupId', groupId);
                formData.append('__RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());

                $.ajax({
                    url: '/GroupPermissions/DeleteGroup',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            showAlert('success', response.message);
                            location.reload(); // Refresh to remove deleted group
                        } else {
                            showAlert('danger', response.message);
                        }
                    },
                    error: function() {
                        showAlert('danger', 'حدث خطأ أثناء حذف المجموعة');
                    }
                });
            }
        }

        // Show alert message
        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'info' ? 'info-circle' : 'exclamation-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            $('.container-fluid').prepend(alertHtml);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                $('.alert').first().fadeOut('slow', function() {
                    $(this).remove();
                });
            }, 5000);
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Add CSRF token to all AJAX requests
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (settings.type !== 'GET' && settings.type !== 'HEAD') {
                    xhr.setRequestHeader('RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());
                }
            }
        });
    </script>

    <style>
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #dee2e6;
        }

        .page-title {
            margin: 0;
            color: #2c3e50;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: none;
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0;
        }

        .group-item {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .group-item:hover {
            background-color: #f8f9fa;
        }

        .group-item.active {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }

        .permissions-tree {
            max-height: 600px;
            overflow-y: auto;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .permission-node {
            margin-bottom: 0.5rem;
        }

        .permission-node .form-check {
            margin-bottom: 0;
        }

        .permission-node .form-check-label {
            cursor: pointer;
            user-select: none;
            padding: 0.25rem 0;
        }

        .permission-node .form-check-label i {
            margin-left: 0.5rem;
            width: 16px;
        }

        .permission-node .form-check-input {
            margin-top: 0.25rem;
        }

        .alert {
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .btn {
            border-radius: 8px;
        }

        .modal-content {
            border-radius: 10px;
        }

        .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0;
        }

        /* Custom scrollbar for permissions tree */
        .permissions-tree::-webkit-scrollbar {
            width: 8px;
        }

        .permissions-tree::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .permissions-tree::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .permissions-tree::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
}

@Html.AntiForgeryToken()
