using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class GroupPermissionsController : Controller
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<GroupPermissionsController> _logger;

        public GroupPermissionsController(IConfiguration configuration, ILogger<GroupPermissionsController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// Display the group permissions management page
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                _logger.LogInformation("GroupPermissions Index called by user: {Username}", User.Identity?.Name);

                // Temporarily bypass admin check for testing
                // if (!await IsUserAdminAsync())
                // {
                //     _logger.LogWarning("User {Username} attempted to access GroupPermissions without admin privileges", User.Identity?.Name);
                //     TempData["Error"] = "ليس لديك صلاحية للوصول إلى إدارة صلاحيات المجموعات";
                //     return RedirectToAction("Index", "SimpleDashboard");
                // }

                _logger.LogInformation("Loading group permissions data for user: {Username}", User.Identity?.Name);

                var model = new GroupPermissionsViewModel
                {
                    Groups = await GetGroupsAsync(),
                    Forms = await GetFormsHierarchyAsync()
                };

                _logger.LogInformation("Group permissions data loaded successfully. Groups: {GroupCount}, Forms: {FormCount}",
                    model.Groups.Count, model.Forms.Count);

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading group permissions page for user: {Username}", User.Identity?.Name);
                TempData["Error"] = "حدث خطأ أثناء تحميل صفحة صلاحيات المجموعات";
                return RedirectToAction("Index", "SimpleDashboard");
            }
        }

        public IActionResult Test()
        {
            return Json(new
            {
                status = "success",
                message = "GroupPermissions controller Test method is working!",
                timestamp = DateTime.Now,
                user = User.Identity?.Name ?? "Not logged in"
            });
        }

        public async Task<IActionResult> TestAdmin()
        {
            try
            {
                var currentUsername = User.Identity?.Name;
                var isAdmin = await IsUserAdminAsync();

                return Json(new
                {
                    status = "success",
                    currentUsername = currentUsername,
                    isAdmin = isAdmin,
                    message = isAdmin ? "User has admin privileges" : "User does NOT have admin privileges"
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    status = "error",
                    message = ex.Message,
                    details = ex.ToString()
                });
            }
        }

        /// <summary>
        /// Get group permissions for a specific group
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetGroupPermissions(int groupId)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { error = "ليس لديك صلاحية للوصول إلى هذه البيانات" });
            }

            try
            {
                var permissions = await GetGroupPermissionsAsync(groupId);
                return Json(new { success = true, permissions = permissions });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting group permissions for group {GroupId}", groupId);
                return Json(new { error = "حدث خطأ أثناء جلب صلاحيات المجموعة" });
            }
        }

        /// <summary>
        /// Save group permissions
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SavePermissions([FromBody] SavePermissionsRequest request)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = "ليس لديك صلاحية لحفظ الصلاحيات" });
            }

            try
            {
                await SaveGroupPermissionsAsync(request.GroupId, request.FormIds);
                _logger.LogInformation("Permissions saved for group {GroupId} by user {Username}", 
                    request.GroupId, User.Identity?.Name);
                
                return Json(new { success = true, message = "تم حفظ الصلاحيات بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving permissions for group {GroupId}", request.GroupId);
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ الصلاحيات" });
            }
        }

        /// <summary>
        /// Create new group
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateGroup(CreateGroupRequest request)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = "ليس لديك صلاحية لإنشاء المجموعات" });
            }

            if (string.IsNullOrWhiteSpace(request.GroupName))
            {
                return Json(new { success = false, message = "اسم المجموعة مطلوب" });
            }

            try
            {
                var groupId = await CreateGroupAsync(request.GroupName, request.Description);
                _logger.LogInformation("Group created with ID {GroupId} by user {Username}", 
                    groupId, User.Identity?.Name);
                
                return Json(new { success = true, message = "تم إنشاء المجموعة بنجاح", groupId = groupId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating group {GroupName}", request.GroupName);
                return Json(new { success = false, message = "حدث خطأ أثناء إنشاء المجموعة" });
            }
        }

        /// <summary>
        /// Delete group
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteGroup(int groupId)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = "ليس لديك صلاحية لحذف المجموعات" });
            }

            // Prevent deletion of admin group
            if (groupId == 1)
            {
                return Json(new { success = false, message = "لا يمكن حذف مجموعة المدراء" });
            }

            try
            {
                // Check if group has users
                if (await GroupHasUsersAsync(groupId))
                {
                    return Json(new { success = false, message = "لا يمكن حذف المجموعة لأنها تحتوي على مستخدمين" });
                }

                await DeleteGroupAsync(groupId);
                _logger.LogInformation("Group {GroupId} deleted by user {Username}", 
                    groupId, User.Identity?.Name);
                
                return Json(new { success = true, message = "تم حذف المجموعة بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting group {GroupId}", groupId);
                return Json(new { success = false, message = "حدث خطأ أثناء حذف المجموعة" });
            }
        }

        #region Private Methods

        /// <summary>
        /// Get all user groups
        /// </summary>
        private async Task<List<UserGroupViewModel>> GetGroupsAsync()
        {
            var groups = new List<UserGroupViewModel>();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = @"
                    SELECT g.GroupID, g.GroupName,
                           COUNT(u.SN) as UserCount
                    FROM tblGroupsAuth g
                    LEFT JOIN tblUsers u ON g.GroupID = u.GroupID
                    GROUP BY g.GroupID, g.GroupName
                    ORDER BY g.GroupName";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            groups.Add(new UserGroupViewModel
                            {
                                GroupID = Convert.ToInt32(reader["GroupID"]),
                                GroupName = reader["GroupName"]?.ToString() ?? "",
                                Description = "", // لا يوجد عمود Description في الجدول
                                UserCount = Convert.ToInt32(reader["UserCount"])
                            });
                        }
                    }
                }
            }

            return groups;
        }

        /// <summary>
        /// Get forms hierarchy for permissions tree
        /// </summary>
        private async Task<List<FormPermissionViewModel>> GetFormsHierarchyAsync()
        {
            var allForms = new List<FormPermissionViewModel>();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = @"
                    SELECT FormID, FormName, DisplayText, ParentFormID, IsContainer, SortOrder
                    FROM tblForms
                    WHERE FormName NOT LIKE '%frmGroupPermissions%'
                    ORDER BY
                        CASE WHEN ParentFormID IS NULL THEN SortOrder ELSE 999 END,
                        SortOrder, FormName";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            allForms.Add(new FormPermissionViewModel
                            {
                                FormID = Convert.ToInt32(reader["FormID"]),
                                FormName = reader["FormName"]?.ToString() ?? "",
                                DisplayText = reader["DisplayText"]?.ToString() ?? "",
                                ParentFormID = reader["ParentFormID"] as int?,
                                IsContainer = Convert.ToBoolean(reader["IsContainer"]),
                                SortOrder = Convert.ToInt32(reader["SortOrder"])
                            });
                        }
                    }
                }
            }

            // Build hierarchy
            var rootForms = allForms.Where(f => f.ParentFormID == null).ToList();
            foreach (var rootForm in rootForms)
            {
                BuildFormHierarchy(rootForm, allForms);
            }

            return rootForms;
        }

        /// <summary>
        /// Build hierarchical structure for forms
        /// </summary>
        private void BuildFormHierarchy(FormPermissionViewModel parent, List<FormPermissionViewModel> allForms)
        {
            var children = allForms.Where(f => f.ParentFormID == parent.FormID).OrderBy(f => f.SortOrder).ToList();
            parent.Children = children;

            foreach (var child in children)
            {
                BuildFormHierarchy(child, allForms);
            }
        }

        /// <summary>
        /// Get permissions for a specific group
        /// </summary>
        private async Task<List<int>> GetGroupPermissionsAsync(int groupId)
        {
            var permissions = new List<int>();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = "SELECT FormID FROM tblGroupFormPermissions WHERE GroupID = @GroupID";
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@GroupID", groupId);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            permissions.Add(Convert.ToInt32(reader["FormID"]));
                        }
                    }
                }
            }

            return permissions;
        }

        /// <summary>
        /// Save group permissions
        /// </summary>
        private async Task SaveGroupPermissionsAsync(int groupId, List<int> formIds)
        {
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Delete existing permissions
                        var deleteQuery = "DELETE FROM tblGroupFormPermissions WHERE GroupID = @GroupID";
                        using (var deleteCommand = new SqlCommand(deleteQuery, connection, transaction))
                        {
                            deleteCommand.Parameters.AddWithValue("@GroupID", groupId);
                            await deleteCommand.ExecuteNonQueryAsync();
                        }

                        // Insert new permissions
                        foreach (var formId in formIds)
                        {
                            var insertQuery = "INSERT INTO tblGroupFormPermissions (GroupID, FormID) VALUES (@GroupID, @FormID)";
                            using (var insertCommand = new SqlCommand(insertQuery, connection, transaction))
                            {
                                insertCommand.Parameters.AddWithValue("@GroupID", groupId);
                                insertCommand.Parameters.AddWithValue("@FormID", formId);
                                await insertCommand.ExecuteNonQueryAsync();
                            }
                        }

                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// Create new group
        /// </summary>
        private async Task<int> CreateGroupAsync(string groupName, string description)
        {
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = @"
                    INSERT INTO tblGroupsAuth (GroupName, Description) 
                    VALUES (@GroupName, @Description);
                    SELECT SCOPE_IDENTITY();";

                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@GroupName", groupName.Trim());
                    command.Parameters.AddWithValue("@Description", description?.Trim() ?? "");
                    
                    var result = await command.ExecuteScalarAsync();
                    return Convert.ToInt32(result);
                }
            }
        }

        /// <summary>
        /// Delete group
        /// </summary>
        private async Task DeleteGroupAsync(int groupId)
        {
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Delete group permissions first
                        var deletePermissionsQuery = "DELETE FROM tblGroupFormPermissions WHERE GroupID = @GroupID";
                        using (var deletePermissionsCommand = new SqlCommand(deletePermissionsQuery, connection, transaction))
                        {
                            deletePermissionsCommand.Parameters.AddWithValue("@GroupID", groupId);
                            await deletePermissionsCommand.ExecuteNonQueryAsync();
                        }

                        // Delete group
                        var deleteGroupQuery = "DELETE FROM tblGroupsAuth WHERE GroupID = @GroupID";
                        using (var deleteGroupCommand = new SqlCommand(deleteGroupQuery, connection, transaction))
                        {
                            deleteGroupCommand.Parameters.AddWithValue("@GroupID", groupId);
                            await deleteGroupCommand.ExecuteNonQueryAsync();
                        }

                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// Check if group has users
        /// </summary>
        private async Task<bool> GroupHasUsersAsync(int groupId)
        {
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = "SELECT COUNT(*) FROM tblUsers WHERE GroupID = @GroupID";
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@GroupID", groupId);
                    var count = Convert.ToInt32(await command.ExecuteScalarAsync());
                    return count > 0;
                }
            }
        }

        /// <summary>
        /// Check if the current user has admin privileges
        /// </summary>
        private async Task<bool> IsUserAdminAsync()
        {
            var currentUsername = User.Identity?.Name;
            if (string.IsNullOrEmpty(currentUsername))
                return false;

            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT g.GroupName 
                        FROM tblUsers u 
                        INNER JOIN tblGroupsAuth g ON u.GroupID = g.GroupID 
                        WHERE u.Username = @Username";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Username", currentUsername);
                        var groupName = await command.ExecuteScalarAsync() as string;
                        
                        return !string.IsNullOrEmpty(groupName) && 
                               groupName.Equals("admin", StringComparison.OrdinalIgnoreCase);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking admin privileges for user {Username}", currentUsername);
                return false;
            }
        }

        #endregion
    }

    #region View Models and Request Models

    public class GroupPermissionsViewModel
    {
        public List<UserGroupViewModel> Groups { get; set; } = new List<UserGroupViewModel>();
        public List<FormPermissionViewModel> Forms { get; set; } = new List<FormPermissionViewModel>();
    }

    public class UserGroupViewModel
    {
        public int GroupID { get; set; }
        public string GroupName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int UserCount { get; set; }
    }

    public class FormPermissionViewModel
    {
        public int FormID { get; set; }
        public string FormName { get; set; } = string.Empty;
        public string DisplayText { get; set; } = string.Empty;
        public int? ParentFormID { get; set; }
        public bool IsContainer { get; set; }
        public int SortOrder { get; set; }
        public List<FormPermissionViewModel> Children { get; set; } = new List<FormPermissionViewModel>();
    }

    public class SavePermissionsRequest
    {
        public int GroupId { get; set; }
        public List<int> FormIds { get; set; } = new List<int>();
    }

    public class CreateGroupRequest
    {
        [Required(ErrorMessage = "اسم المجموعة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المجموعة يجب أن يكون أقل من 100 حرف")]
        public string GroupName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;
    }

    #endregion
}
