using AccountingSystem.Models;
using AccountingSystem.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using System.Security.Cryptography;
using System.Text;
using System.Net;

namespace AccountingSystem.Services
{
    public interface IAuthenticationService
    {
        Task<AuthenticationResult> AuthenticateAsync(string username, string password, string ipAddress, string userAgent, string machineName, string machineUser);
        Task<bool> EndSessionAsync(string sessionToken);
        Task<User?> GetUserByUsernameAsync(string username);
        Task<bool> IsUserLockedAsync(string username);
        Task<SessionLog?> GetActiveSessionAsync(string sessionToken);
        Task<bool> ValidateSessionAsync(string sessionToken);
    }

    public class AuthenticationService : IAuthenticationService
    {
        private readonly AccountingDbContext _context;
        private const int MaxFailedAttempts = 5;
        private const int LockoutDurationMinutes = 30;

        public AuthenticationService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<AuthenticationResult> AuthenticateAsync(string username, string password, string ipAddress, string userAgent, string machineName, string machineUser)
        {
            var result = new AuthenticationResult();

            try
            {
                // Check if user exists
                var user = await GetUserByUsernameAsync(username);
                
                if (user == null)
                {
                    await LogSessionAttemptAsync(username, "FAILED", ipAddress, userAgent, machineName, machineUser, "User not found");
                    result.IsSuccess = false;
                    result.ErrorMessage = "اسم المستخدم او كلمة المرور خطأ";
                    return result;
                }

                // Verify password (using GetHashCode like in VB.NET version)
                var hashedPassword = password.GetHashCode().ToString();
                
                if (user.Password == hashedPassword)
                {
                    // Login successful
                    var sessionToken = Guid.NewGuid().ToString();
                    
                    // Log successful login
                    var sessionLog = await LogSessionAttemptAsync(username, "SUCCESS", ipAddress, userAgent, machineName, machineUser, "", sessionToken);

                    await _context.SaveChangesAsync();

                    result.IsSuccess = true;
                    result.User = user;
                    result.SessionToken = sessionToken;
                    result.SessionId = sessionLog?.SessionID ?? 0;

                    return result;
                }
                else
                {
                    // Login failed
                    await LogSessionAttemptAsync(username, "FAILED", ipAddress, userAgent, machineName, machineUser, "Invalid password");
                    await _context.SaveChangesAsync();

                    result.IsSuccess = false;
                    result.ErrorMessage = "اسم المستخدم او كلمة المرور خطأ";
                    return result;
                }
            }
            catch (Exception ex)
            {
                await LogSessionAttemptAsync(username, "FAILED", ipAddress, userAgent, machineName, machineUser, $"System error: {ex.Message}");
                result.IsSuccess = false;
                result.ErrorMessage = "حدث خطأ في النظام";
                return result;
            }
        }

        public async Task<bool> EndSessionAsync(string sessionToken)
        {
            try
            {
                var session = await _context.SessionLogs
                    .FirstOrDefaultAsync(s => s.SessionToken == sessionToken && s.LogoutTime == null);

                if (session != null)
                {
                    session.LogoutTime = DateTime.Now;
                    await _context.SaveChangesAsync();
                    return true;
                }
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error ending session: {ex.Message}");
            }

            return false;
        }

        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            return await _context.Users
                .Include(u => u.Group)
                .FirstOrDefaultAsync(u => u.Username == username);
        }

        public async Task<bool> IsUserLockedAsync(string username)
        {
            // This functionality is disabled as the required database columns
            // (IsLocked, FailedLoginAttempts) do not exist in the current schema.
            return await Task.FromResult(false);
        }

        public async Task<SessionLog?> GetActiveSessionAsync(string sessionToken)
        {
            return await _context.SessionLogs
                .Include(s => s.User)
                .FirstOrDefaultAsync(s => s.SessionToken == sessionToken && s.LogoutTime == null);
        }

        public async Task<bool> ValidateSessionAsync(string sessionToken)
        {
            var session = await GetActiveSessionAsync(sessionToken);
            return session != null;
        }

        private async Task<SessionLog?> LogSessionAttemptAsync(string username, string loginStatus, string ipAddress, string userAgent, string machineName, string machineUser, string failureReason = "", string sessionToken = "")
        {
            try
            {
                var sessionLog = new SessionLog
                {
                    Username = username,
                    LoginStatus = loginStatus,
                    MachineName = machineName,
                    MachineUser = machineUser,
                    SystemUsername = username,
                    IPAddress = ipAddress,
                    UserAgent = userAgent,
                    SessionToken = string.IsNullOrEmpty(sessionToken) ? null : sessionToken,
                    LoginTime = DateTime.Now,
                    FailureReason = string.IsNullOrEmpty(failureReason) ? null : failureReason
                };

                _context.SessionLogs.Add(sessionLog);
                await _context.SaveChangesAsync();

                return sessionLog;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error logging session attempt: {ex.Message}");
                return null;
            }
        }
    }

    public class AuthenticationResult
    {
        public bool IsSuccess { get; set; }
        public User? User { get; set; }
        public string? SessionToken { get; set; }
        public int SessionId { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Machine information helper class
    /// </summary>
    public static class MachineInfoHelper
    {
        public static string GetLocalIPAddress(HttpContext httpContext)
        {
            try
            {
                // Try to get real IP from headers (for reverse proxy scenarios)
                var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
                if (!string.IsNullOrEmpty(forwardedFor))
                {
                    return forwardedFor.Split(',')[0].Trim();
                }

                var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
                if (!string.IsNullOrEmpty(realIp))
                {
                    return realIp;
                }

                // Fallback to connection remote IP
                return httpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        public static string GetUserAgent(HttpContext httpContext)
        {
            return httpContext.Request.Headers["User-Agent"].FirstOrDefault() ?? "Unknown";
        }

        public static string GetMachineName()
        {
            return Environment.MachineName;
        }

        public static string GetMachineUser()
        {
            return $"{Environment.UserDomainName}\\{Environment.UserName}";
        }
    }
}
