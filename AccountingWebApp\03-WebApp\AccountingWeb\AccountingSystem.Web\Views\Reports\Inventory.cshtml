@{
    ViewData["Title"] = ViewBag.PageTitle ?? "تقارير المخزون";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-warehouse me-2"></i>
                        @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        @(ViewBag.Message ?? "صفحة تقارير المخزون - قيد التطوير")
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-boxes fa-3x text-warning mb-3"></i>
                                    <h5>رصيد المخزون</h5>
                                    <p class="text-muted">عرض رصيد المخزون الحالي</p>
                                    <a href="#" class="btn btn-warning">
                                        <i class="fas fa-eye me-2"></i>
                                        عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                                    <h5>الأصناف المنخفضة</h5>
                                    <p class="text-muted">الأصناف التي وصلت للحد الأدنى</p>
                                    <a href="#" class="btn btn-danger">
                                        <i class="fas fa-eye me-2"></i>
                                        عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-exchange-alt fa-3x text-info mb-3"></i>
                                    <h5>حركة المخزون</h5>
                                    <p class="text-muted">تقرير حركة المخزون</p>
                                    <a href="#" class="btn btn-info">
                                        <i class="fas fa-eye me-2"></i>
                                        عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
