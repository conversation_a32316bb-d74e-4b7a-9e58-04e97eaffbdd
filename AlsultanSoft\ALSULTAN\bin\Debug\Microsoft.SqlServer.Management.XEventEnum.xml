<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.SqlServer.Management.XEventEnum</name>
    </assembly>
    <members>
        <member name="T:Microsoft.SqlServer.Management.XEvent.XEventObject">
            <summary>
            This is the Enumerator object for XEvnet object model. It derived from SqlObject, the
            base class for all enumerator in SFC enabled object model.Override the ResourceAssembly
            to provide the correct assembly that contains the resources.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.XEvent.XEventObject.ResourceAssembly">
            <summary>
            Return the assebmly that contains the resources.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.XEvent.XEventObject.GetServerVersion(System.Object)">
            <summary>
            Return the server version for the given connection.
            </summary>
            <param name="conn">connetion to the server we want to know the version</param>
            <returns>server version on the connection</returns>
        </member>
    </members>
</doc>
