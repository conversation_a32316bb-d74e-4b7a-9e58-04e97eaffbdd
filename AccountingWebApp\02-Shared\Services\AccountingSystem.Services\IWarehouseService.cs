using AccountingSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public interface IWarehouseService
    {
        Task<List<Warehouse>> GetWarehousesAsync();
        Task<Warehouse?> GetWarehouseByIdAsync(int id);
        Task<(bool success, Warehouse? createdWarehouse)> CreateWarehouseAsync(Warehouse warehouse, string currentUser);
        Task<bool> UpdateWarehouseAsync(Warehouse warehouse, string currentUser);
        Task<(bool success, string? errorMessage)> DeleteWarehouseAsync(int id);
        Task<bool> WarehouseExistsAsync(int id);
    }
}
