{"ConnectionStrings": {"DefaultConnection": "Server=***************;Database=SULTDBTest;User Id=AppUser;Password=StrongP@ss123;TrustServerCertificate=true;", "AccountingDatabase": "Server=***************;Database=SULTDBTest;User Id=AppUser;Password=StrongP@ss123;TrustServerCertificate=true;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "AllowedHosts": "*", "ApplicationSettings": {"ApplicationName": "Accounting System Web", "Version": "1.0.0", "EnableAuditLogging": true, "SessionTimeoutMinutes": 30, "MaxLoginAttempts": 5}}