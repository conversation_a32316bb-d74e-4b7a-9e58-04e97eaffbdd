using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using AccountingSystem.Models;
using AccountingSystem.Data;
using AccountingSystem.Services;
using Microsoft.AspNetCore.Authorization;
using System;

namespace AccountingSystem.Web.Controllers
{
    [Authorize] // تم تفعيل التفويض
    public class VendorsController : Controller
    {
        private readonly AccountingDbContext _context;
        private readonly IVendorService _vendorService;

        public VendorsController(AccountingDbContext context, IVendorService vendorService)
        {
            _context = context;
            _vendorService = vendorService;
        }

        // GET: Vendors
        public async Task<IActionResult> Index(string searchTerm)
        {
            ViewBag.SearchTerm = searchTerm;
            var vendors = await _vendorService.GetVendorsAsync(searchTerm);
            return View(vendors);
        }

        // GET: Vendors/Create
        public async Task<IActionResult> Create()
        {
            var vendor = new Vendor
            {
                VendorNo = await _vendorService.GetNextVendorNoAsync()
            };
            LoadViewData();
            return View(vendor);
        }

        // POST: Vendors/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("VendorNo,VendorName,FirstName,LastName,Mobile,Phone,Email,StreetAddress1,StreetAddress2,City,Region,PostalCode,PaymentMethod,CreditLimit,PaymentTerm,ContactPerson,CR,VATRegNo,Shop,Status,LocalVendor,Notes")] Vendor vendor)
        {
            if (ModelState.IsValid)
            {
                var currentUser = User.Identity?.Name ?? "System";
                var (success, accountCode) = await _vendorService.CreateVendorAsync(vendor, currentUser);

                if (success)
                {
                    TempData["SuccessMessage"] = $"تم إنشاء المورد بنجاح - كود الحساب: {accountCode}";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    ModelState.AddModelError("", "فشل في إنشاء المورد أو رقم المورد موجود مسبقاً");
                }
            }

            LoadViewData();
            return View(vendor);
        }

        // GET: Vendors/Edit/5
        public async Task<IActionResult> Edit(long? vendorNo)
        {
            if (vendorNo == null)
            {
                return NotFound();
            }

            var vendor = await _vendorService.GetVendorByNoAsync(vendorNo.Value);
            if (vendor == null)
            {
                return NotFound();
            }

            LoadViewData();
            return View(vendor);
        }

        // POST: Vendors/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(long vendorNo, [Bind("VendorNo,VendorName,FirstName,LastName,Mobile,Phone,Email,StreetAddress1,StreetAddress2,City,Region,PostalCode,PaymentMethod,CreditLimit,PaymentTerm,ContactPerson,CR,VATRegNo,Shop,Status,LocalVendor,Notes,CreatedBy,CreatedOn,ModifiedBy,ModifiedOn")] Vendor vendor)
        {
            if (vendorNo != vendor.VendorNo)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var currentUser = User.Identity?.Name ?? "System";
                var success = await _vendorService.UpdateVendorAsync(vendor, currentUser);

                if (success)
                {
                    TempData["SuccessMessage"] = "تم تحديث المورد بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    ModelState.AddModelError("", "فشل في تحديث المورد");
                }
            }

            LoadViewData();
            return View(vendor);
        }

        // GET: Vendors/Delete/5
        public async Task<IActionResult> Delete(long? vendorNo)
        {
            if (vendorNo == null)
            {
                return NotFound();
            }

            var vendor = await _vendorService.GetVendorByNoAsync(vendorNo.Value);
            if (vendor == null)
            {
                return NotFound();
            }

            return View(vendor);
        }

        // POST: Vendors/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(long vendorNo)
        {
            var success = await _vendorService.DeleteVendorAsync(vendorNo);
            if (success)
            {
                TempData["SuccessMessage"] = "تم حذف المورد بنجاح";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في حذف المورد";
            }

            return RedirectToAction(nameof(Index));
        }

        private void LoadViewData()
        {
            try
            {
                // Load employees for dropdown
                var employees = _context.Employees
                    .Select(e => new { EmployeeNo = e.Id, EmployeeName = e.Name })
                    .OrderBy(e => e.EmployeeName)
                    .ToList();
                ViewBag.Employees = employees;

                // Load shops for dropdown
                var shops = _context.Shops
                    .Select(s => new { s.SN, StoreName = s.StoreName })
                    .OrderBy(s => s.StoreName)
                    .ToList();
                ViewBag.Shops = shops;
            }
            catch (Exception)
            {
                // Log the error and provide empty lists to prevent null reference exceptions
                ViewBag.Employees = new List<object>();
                ViewBag.Shops = new List<object>();
                // You might want to log this error: _logger.LogError(ex, "Error loading view data");
            }

            // Static data for dropdowns
            ViewBag.PaymentMethods = new List<string> { "نقدي", "آجل", "شيك", "تحويل بنكي" };
            ViewBag.StatusOptions = new List<string> { "نشط", "غير نشط", "معلق" };
            ViewBag.LocalVendorOptions = new List<string> { "محلي", "خارجي" };
        }
    }
}
