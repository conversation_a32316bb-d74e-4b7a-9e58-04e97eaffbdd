using AccountingSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public interface IVendorService
    {
        Task<List<Vendor>> GetVendorsAsync(string searchTerm = "");
        Task<Vendor?> GetVendorByNoAsync(long vendorNo);
        Task<(bool success, string? accountCode)> CreateVendorAsync(Vendor vendor, string currentUser);
        Task<bool> UpdateVendorAsync(Vendor vendor, string currentUser);
        Task<bool> DeleteVendorAsync(long vendorNo);
        Task<long> GetNextVendorNoAsync();
        Task<string> GenerateNextVendorAccountCodeAsync(string parentCode);
        Task<string?> GetVendorParentAccountCodeAsync();
    }
}
