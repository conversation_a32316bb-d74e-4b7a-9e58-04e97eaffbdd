﻿COLUMN STATS,dbo,__EFMigrationsHistory,MigrationId,nvarchar,150,NULL,NULL,NO,,NULL,"Table: __EFMigrationsHistory, Column: MigrationId, Type: nvarchar(150), Nullable: NO"
COLUMN STATS,dbo,__EFMigrationsHistory,ProductVersion,nvarchar,32,NULL,NULL,NO,,NULL,"Table: __EFMigrationsHistory, Column: ProductVersion, Type: nvarchar(32), Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_Accounts,AccountCode,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tbl_Acc_Accounts, Column: AccountCode, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_Accounts,SegmentCode,nvarchar,10,NULL,NULL,NO,,NULL,"Table: tbl_Acc_Accounts, Column: SegmentCode, Type: nvarchar(10), Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_Accounts,AccountName,nvarchar,255,NULL,NULL,NO,,NULL,"Table: tbl_Acc_Accounts, Column: AccountName, Type: nvarchar(255), Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_Accounts,ParentAccountCode,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tbl_Acc_Accounts, Column: ParentAccountCode, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_Accounts,AccountLevel,int,NULL,10,0,NO,,NULL,"Table: tbl_Acc_Accounts, Column: AccountLevel, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_Accounts,IsPosting,bit,NULL,NULL,NULL,NO,,NULL,"Table: tbl_Acc_Accounts, Column: IsPosting, Type: bit, Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_Accounts,AccountType,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tbl_Acc_Accounts, Column: AccountType, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_Accounts,AccountNature,nvarchar,20,NULL,NULL,YES,,NULL,"Table: tbl_Acc_Accounts, Column: AccountNature, Type: nvarchar(20), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_Accounts,OpeningBalance,decimal,NULL,18,2,YES,,((0)),"Table: tbl_Acc_Accounts, Column: OpeningBalance, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_Accounts,Notes,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tbl_Acc_Accounts, Column: Notes, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_Accounts,CreatedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tbl_Acc_Accounts, Column: CreatedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_Accounts,CreatedOn,datetime,NULL,NULL,NULL,YES,,(getdate()),"Table: tbl_Acc_Accounts, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_Accounts,ModifiedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tbl_Acc_Accounts, Column: ModifiedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_Accounts,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tbl_Acc_Accounts, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalHeader,DeletedID,int,NULL,10,0,NO,,NULL,"Table: tbl_Acc_DeletedJournalHeader, Column: DeletedID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalHeader,JEHeaderID,int,NULL,10,0,YES,,NULL,"Table: tbl_Acc_DeletedJournalHeader, Column: JEHeaderID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalHeader,EntryDate,date,NULL,NULL,NULL,YES,,NULL,"Table: tbl_Acc_DeletedJournalHeader, Column: EntryDate, Type: date, Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalHeader,EntryReference,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tbl_Acc_DeletedJournalHeader, Column: EntryReference, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalHeader,EntryType,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tbl_Acc_DeletedJournalHeader, Column: EntryType, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalHeader,CreatedBy,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tbl_Acc_DeletedJournalHeader, Column: CreatedBy, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalHeader,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tbl_Acc_DeletedJournalHeader, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalHeader,DeletedBy,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tbl_Acc_DeletedJournalHeader, Column: DeletedBy, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalHeader,DeletedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tbl_Acc_DeletedJournalHeader, Column: DeletedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalHeader,DeleteReason,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tbl_Acc_DeletedJournalHeader, Column: DeleteReason, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalLines,DeletedLineID,int,NULL,10,0,NO,,NULL,"Table: tbl_Acc_DeletedJournalLines, Column: DeletedLineID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalLines,DeletedID,int,NULL,10,0,YES,,NULL,"Table: tbl_Acc_DeletedJournalLines, Column: DeletedID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalLines,JEHeaderID,int,NULL,10,0,YES,,NULL,"Table: tbl_Acc_DeletedJournalLines, Column: JEHeaderID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalLines,AccountCode,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tbl_Acc_DeletedJournalLines, Column: AccountCode, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalLines,Debit,decimal,NULL,18,2,YES,,NULL,"Table: tbl_Acc_DeletedJournalLines, Column: Debit, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalLines,Credit,decimal,NULL,18,2,YES,,NULL,"Table: tbl_Acc_DeletedJournalLines, Column: Credit, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_DeletedJournalLines,Description,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tbl_Acc_DeletedJournalLines, Column: Description, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_JournalHeader,JEHeaderID,int,NULL,10,0,NO,,NULL,"Table: tbl_Acc_JournalHeader, Column: JEHeaderID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_JournalHeader,EntryDate,date,NULL,NULL,NULL,NO,,NULL,"Table: tbl_Acc_JournalHeader, Column: EntryDate, Type: date, Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_JournalHeader,EntryReference,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tbl_Acc_JournalHeader, Column: EntryReference, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_JournalHeader,EntryType,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tbl_Acc_JournalHeader, Column: EntryType, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_JournalHeader,CreatedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tbl_Acc_JournalHeader, Column: CreatedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_JournalHeader,CreatedOn,datetime,NULL,NULL,NULL,YES,,(getdate()),"Table: tbl_Acc_JournalHeader, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_JournalHeader,EntryReferenceTrxNo,int,NULL,10,0,YES,,NULL,"Table: tbl_Acc_JournalHeader, Column: EntryReferenceTrxNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_JournalHeader,ModifiedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tbl_Acc_JournalHeader, Column: ModifiedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_JournalHeader,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tbl_Acc_JournalHeader, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_JournalLines,LineID,int,NULL,10,0,NO,,NULL,"Table: tbl_Acc_JournalLines, Column: LineID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_JournalLines,JEHeaderID,int,NULL,10,0,NO,,NULL,"Table: tbl_Acc_JournalLines, Column: JEHeaderID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_JournalLines,AccountCode,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tbl_Acc_JournalLines, Column: AccountCode, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tbl_Acc_JournalLines,Debit,decimal,NULL,18,2,YES,,((0)),"Table: tbl_Acc_JournalLines, Column: Debit, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_JournalLines,Credit,decimal,NULL,18,2,YES,,((0)),"Table: tbl_Acc_JournalLines, Column: Credit, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tbl_Acc_JournalLines,Description,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tbl_Acc_JournalLines, Column: Description, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,ID,int,NULL,10,0,NO,,NULL,"Table: tblBarcodeSettings, Column: ID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblBarcodeSettings,Shop,nvarchar,50,NULL,NULL,NO,,NULL,"Table: tblBarcodeSettings, Column: Shop, Type: nvarchar(50), Nullable: NO"
COLUMN STATS,dbo,tblBarcodeSettings,BarcodeType,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblBarcodeSettings, Column: BarcodeType, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,EnableEmbeddedWeight,bit,NULL,NULL,NULL,YES,,NULL,"Table: tblBarcodeSettings, Column: EnableEmbeddedWeight, Type: bit, Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,EmbeddedFormat,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblBarcodeSettings, Column: EmbeddedFormat, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,WeightDivisor,int,NULL,10,0,YES,,NULL,"Table: tblBarcodeSettings, Column: WeightDivisor, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,CurrencyDivisor,int,NULL,10,0,YES,,NULL,"Table: tblBarcodeSettings, Column: CurrencyDivisor, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,Notes,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblBarcodeSettings, Column: Notes, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,CreatedBy,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblBarcodeSettings, Column: CreatedBy, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,CreatedOn,datetime,NULL,NULL,NULL,YES,,(getdate()),"Table: tblBarcodeSettings, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,ModifiedBy,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblBarcodeSettings, Column: ModifiedBy, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblBarcodeSettings, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,Barcode,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblBarcodeSettings, Column: Barcode, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,Weight,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblBarcodeSettings, Column: Weight, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblBarcodeSettings,FixedCode,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblBarcodeSettings, Column: FixedCode, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblBrands,Brand_Code,int,NULL,10,0,NO,,NULL,"Table: tblBrands, Column: Brand_Code, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblBrands,Brand_Text,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblBrands, Column: Brand_Text, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblBrands,CreatedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblBrands, Column: CreatedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblBrands,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblBrands, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblBrands,ModifiedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblBrands, Column: ModifiedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblBrands,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblBrands, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,DeleteSN,int,NULL,10,0,YES,,((0)),"Table: tblCashTrxDelete, Column: DeleteSN, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,DeleteReason,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCashTrxDelete, Column: DeleteReason, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,SN,int,NULL,10,0,YES,,((0)),"Table: tblCashTrxDelete, Column: SN, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,DocDate,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblCashTrxDelete, Column: DocDate, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,Cashier,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCashTrxDelete, Column: Cashier, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,PersNo,int,NULL,10,0,YES,,NULL,"Table: tblCashTrxDelete, Column: PersNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,PersName,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCashTrxDelete, Column: PersName, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,Recipient,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCashTrxDelete, Column: Recipient, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,DocType,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCashTrxDelete, Column: DocType, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,Amount,money,NULL,19,4,YES,,NULL,"Table: tblCashTrxDelete, Column: Amount, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,Note,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblCashTrxDelete, Column: Note, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,CreatedBy,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCashTrxDelete, Column: CreatedBy, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,CreatedOn,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblCashTrxDelete, Column: CreatedOn, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,DeletedBy,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCashTrxDelete, Column: DeletedBy, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCashTrxDelete,DeletedOn,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblCashTrxDelete, Column: DeletedOn, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblChartOfAccounts,AccountID,int,NULL,10,0,NO,,NULL,"Table: tblChartOfAccounts, Column: AccountID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblChartOfAccounts,AccountCode,nvarchar,50,NULL,NULL,NO,,NULL,"Table: tblChartOfAccounts, Column: AccountCode, Type: nvarchar(50), Nullable: NO"
COLUMN STATS,dbo,tblChartOfAccounts,AccountName,nvarchar,255,NULL,NULL,NO,,NULL,"Table: tblChartOfAccounts, Column: AccountName, Type: nvarchar(255), Nullable: NO"
COLUMN STATS,dbo,tblChartOfAccounts,ParentCode,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblChartOfAccounts, Column: ParentCode, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblChartOfAccounts,AccountLevel,int,NULL,10,0,NO,,((1)),"Table: tblChartOfAccounts, Column: AccountLevel, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblChartOfAccounts,IsFinal,bit,NULL,NULL,NULL,NO,,((0)),"Table: tblChartOfAccounts, Column: IsFinal, Type: bit, Nullable: NO"
COLUMN STATS,dbo,tblChartOfAccounts,AccountType,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblChartOfAccounts, Column: AccountType, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblChartOfAccounts,CreatedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblChartOfAccounts, Column: CreatedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblChartOfAccounts,CreatedOn,datetime,NULL,NULL,NULL,YES,,(getdate()),"Table: tblChartOfAccounts, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblChartOfAccounts,ModifiedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblChartOfAccounts, Column: ModifiedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblChartOfAccounts,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblChartOfAccounts, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblCompanies1,ID,int,NULL,10,0,NO,,NULL,"Table: tblCompanies1, Column: ID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblCompanies1,Company,nvarchar,255,NULL,NULL,NO,,NULL,"Table: tblCompanies1, Column: Company, Type: nvarchar(255), Nullable: NO"
COLUMN STATS,dbo,tblCompanies1,CreatedBy,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCompanies1, Column: CreatedBy, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCompanies1,CreatedOn,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblCompanies1, Column: CreatedOn, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblConfig,StoreName,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: StoreName, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblConfig,BackupLocation,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: BackupLocation, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblConfig,LastLoginBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: LastLoginBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblConfig,LastLoginOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: LastLoginOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblConfig,PrintPurchaseInvoice,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: PrintPurchaseInvoice, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,PrintPurchaseAccesInvoice,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: PrintPurchaseAccesInvoice, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,PrintSalesInvoice,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: PrintSalesInvoice, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,Password,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: Password, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblConfig,CashWithStore,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: CashWithStore, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,AddressFooter,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: AddressFooter, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblConfig,AllowSalesLess,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: AllowSalesLess, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,WarningSalesLess,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: WarningSalesLess, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,PrinterInvoice,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: PrinterInvoice, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblConfig,PrinterLabel,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: PrinterLabel, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblConfig,PrinterReports,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: PrinterReports, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblConfig,DefNegItem,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: DefNegItem, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,AllowNegItem,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: AllowNegItem, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,DefItemSN,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: DefItemSN, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,VATAccount,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: VATAccount, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,VATRegNo,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: VATRegNo, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,DefVatIncPurch,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: DefVatIncPurch, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,DefVatIncSales,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: DefVatIncSales, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,AllowVatIncPurch,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: AllowVatIncPurch, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,AllowVatIncSales,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: AllowVatIncSales, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,ItemLevel1,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: ItemLevel1, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,ItemLevel2,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: ItemLevel2, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,ItemLevel3,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: ItemLevel3, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,ItemLevel4,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: ItemLevel4, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,ItemLevel5,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: ItemLevel5, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,ItemDescription1,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: ItemDescription1, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,ItemDescription2,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: ItemDescription2, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,RPTHeight,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: RPTHeight, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,RPTWidth,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: RPTWidth, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,frmHeight,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: frmHeight, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,frmWidth,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: frmWidth, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblConfig,CashPayPrint,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: CashPayPrint, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,CashRecPrint,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: CashRecPrint, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,ModifiedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: ModifiedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblConfig,LogoFile,varbinary,-1,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: LogoFile, Type: varbinary(-1), Nullable: YES"
COLUMN STATS,dbo,tblConfig,IBAN,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: IBAN, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,IBAN_Bank,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: IBAN_Bank, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,IBAN_Invoice,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblConfig, Column: IBAN_Invoice, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblConfig,isTestDeploy,int,NULL,10,0,YES,,NULL,"Table: tblConfig, Column: isTestDeploy, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,CustomerNo,bigint,NULL,19,0,NO,,NULL,"Table: tblCustomers, Column: CustomerNo, Type: bigint(19,0), Nullable: NO"
COLUMN STATS,dbo,tblCustomers,CustomerName,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: CustomerName, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,FirstName,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: FirstName, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,LastName,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: LastName, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,Mobile,int,NULL,10,0,YES,,NULL,"Table: tblCustomers, Column: Mobile, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,Phone,int,NULL,10,0,YES,,NULL,"Table: tblCustomers, Column: Phone, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,Email,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: Email, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,StreetAddress1,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: StreetAddress1, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,StreetAddress2,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: StreetAddress2, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,City,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: City, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,Region,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: Region, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,PostalCode,int,NULL,10,0,YES,,NULL,"Table: tblCustomers, Column: PostalCode, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,PaymentMethod,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: PaymentMethod, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,CreditLimit,float,NULL,53,NULL,YES,,NULL,"Table: tblCustomers, Column: CreditLimit, Type: float(53,), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,PaymentTerm,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: PaymentTerm, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,ContactPerson,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: ContactPerson, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,CR,nvarchar,10,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: CR, Type: nvarchar(10), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,VATRegNo,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: VATRegNo, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,Shop,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: Shop, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,Status,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: Status, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,LocalCustomer,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: LocalCustomer, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,EmployeeNo,int,NULL,10,0,YES,,NULL,"Table: tblCustomers, Column: EmployeeNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,Notes,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: Notes, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,CreatedBy,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: CreatedBy, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblCustomers,ModifiedBy,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: ModifiedBy, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblCustomers,OldCode,int,NULL,10,0,YES,,NULL,"Table: tblCustomers, Column: OldCode, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,BuildingNo,int,NULL,10,0,YES,,NULL,"Table: tblCustomers, Column: BuildingNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,AdditionalNo,int,NULL,10,0,YES,,NULL,"Table: tblCustomers, Column: AdditionalNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblCustomers,District,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblCustomers, Column: District, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblCustomerStatus,SN,int,NULL,10,0,NO,,NULL,"Table: tblCustomerStatus, Column: SN, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblCustomerStatus,Customer_Status,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomerStatus, Column: Customer_Status, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomerStatus,CreatedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomerStatus, Column: CreatedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomerStatus,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblCustomerStatus, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblCustomerStatus,ModifiedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblCustomerStatus, Column: ModifiedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblCustomerStatus,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblCustomerStatus, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblEmployees,EmployeeNo,int,NULL,10,0,NO,,NULL,"Table: tblEmployees, Column: EmployeeNo, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblEmployees,Emp_Name,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblEmployees, Column: Emp_Name, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblEmployees,Emp_Salary,money,NULL,19,4,YES,,NULL,"Table: tblEmployees, Column: Emp_Salary, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblEmployees,CreatedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblEmployees, Column: CreatedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblEmployees,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblEmployees, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblEmployees,ModifiedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblEmployees, Column: ModifiedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblEmployees,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblEmployees, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblEmpNotes,ID,int,NULL,10,0,NO,,NULL,"Table: tblEmpNotes, Column: ID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblEmpNotes,Emp_Name,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblEmpNotes, Column: Emp_Name, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblEmpNotes,Emp_Note,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblEmpNotes, Column: Emp_Note, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblExpenseClass,SN,int,NULL,10,0,NO,,NULL,"Table: tblExpenseClass, Column: SN, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblExpenseClass,ClassExpense,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblExpenseClass, Column: ClassExpense, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,Invoice_ID,int,NULL,10,0,NO,,NULL,"Table: tblExpenseInvoices, Column: Invoice_ID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblExpenseInvoices,Invoice_Date,date,NULL,NULL,NULL,NO,,(getdate()),"Table: tblExpenseInvoices, Column: Invoice_Date, Type: date, Nullable: NO"
COLUMN STATS,dbo,tblExpenseInvoices,CreditCashier,nvarchar,100,NULL,NULL,YES,,(N'حساب الموردين'),"Table: tblExpenseInvoices, Column: CreditCashier, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,DebitExpense,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblExpenseInvoices, Column: DebitExpense, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,Invoice_amount,decimal,NULL,18,2,NO,,((0)),"Table: tblExpenseInvoices, Column: Invoice_amount, Type: decimal(18,2), Nullable: NO"
COLUMN STATS,dbo,tblExpenseInvoices,Tax_amount,decimal,NULL,18,2,YES,,((0)),"Table: tblExpenseInvoices, Column: Tax_amount, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,VendorNo,bigint,NULL,19,0,YES,,NULL,"Table: tblExpenseInvoices, Column: VendorNo, Type: bigint(19,0), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,VendorInvoiceNo,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblExpenseInvoices, Column: VendorInvoiceNo, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,VendorName,nvarchar,200,NULL,NULL,YES,,NULL,"Table: tblExpenseInvoices, Column: VendorName, Type: nvarchar(200), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,VendorVATRN,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblExpenseInvoices, Column: VendorVATRN, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,EmployeeBuyer,int,NULL,10,0,YES,,NULL,"Table: tblExpenseInvoices, Column: EmployeeBuyer, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,Notes,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblExpenseInvoices, Column: Notes, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,Attached_photo,varbinary,-1,NULL,NULL,YES,,NULL,"Table: tblExpenseInvoices, Column: Attached_photo, Type: varbinary(-1), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblExpenseInvoices, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblExpenseInvoices, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblExpenseInvoices, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblExpenseInvoices, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblExpenseInvoices,ReadyForUse,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblExpenseInvoices, Column: ReadyForUse, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblExpenses,ID,int,NULL,10,0,NO,,NULL,"Table: tblExpenses, Column: ID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblExpenses,ClassID,int,NULL,10,0,YES,,NULL,"Table: tblExpenses, Column: ClassID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblExpenses,Expens,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblExpenses, Column: Expens, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblExpenses,Excluded,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblExpenses, Column: Excluded, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblExpenses,Limited,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblExpenses, Column: Limited, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblExpenses,CreatedBy,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblExpenses, Column: CreatedBy, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblExpenses,CreatedOn,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblExpenses, Column: CreatedOn, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblForms,FormID,int,NULL,10,0,NO,,NULL,"Table: tblForms, Column: FormID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblForms,FormName,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tblForms, Column: FormName, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tblForms,DisplayText,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblForms, Column: DisplayText, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblForms,ParentFormID,int,NULL,10,0,YES,,NULL,"Table: tblForms, Column: ParentFormID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblForms,IsContainer,bit,NULL,NULL,NULL,NO,,((0)),"Table: tblForms, Column: IsContainer, Type: bit, Nullable: NO"
COLUMN STATS,dbo,tblForms,SortOrder,int,NULL,10,0,NO,,((0)),"Table: tblForms, Column: SortOrder, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblGLConfig,EntryReferenceModule,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblGLConfig, Column: EntryReferenceModule, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblGLConfig,AccountNo,int,NULL,10,0,YES,,NULL,"Table: tblGLConfig, Column: AccountNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblGLConfig,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblGLConfig, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblGLConfig,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblGLConfig, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblGLConfig,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblGLConfig, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblGLConfig,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblGLConfig, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblGLTrx,JESN,int,NULL,10,0,YES,,NULL,"Table: tblGLTrx, Column: JESN, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblGLTrx,AccountNo,bigint,NULL,19,0,YES,,NULL,"Table: tblGLTrx, Column: AccountNo, Type: bigint(19,0), Nullable: YES"
COLUMN STATS,dbo,tblGLTrx,Amount,numeric,NULL,19,2,YES,,NULL,"Table: tblGLTrx, Column: Amount, Type: numeric(19,2), Nullable: YES"
COLUMN STATS,dbo,tblGLTrx,DRCR,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblGLTrx, Column: DRCR, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblGroupFormPermissions,PermissionId,int,NULL,10,0,NO,,NULL,"Table: tblGroupFormPermissions, Column: PermissionId, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblGroupFormPermissions,GroupID,int,NULL,10,0,NO,,NULL,"Table: tblGroupFormPermissions, Column: GroupID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblGroupFormPermissions,FormID,int,NULL,10,0,NO,,NULL,"Table: tblGroupFormPermissions, Column: FormID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblGroupsAuth,GroupID,int,NULL,10,0,NO,,NULL,"Table: tblGroupsAuth, Column: GroupID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblGroupsAuth,GroupName,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tblGroupsAuth, Column: GroupName, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tblItems,SN,bigint,NULL,19,0,NO,,NULL,"Table: tblItems, Column: SN, Type: bigint(19,0), Nullable: NO"
COLUMN STATS,dbo,tblItems,ItemNo,bigint,NULL,19,0,YES,,NULL,"Table: tblItems, Column: ItemNo, Type: bigint(19,0), Nullable: YES"
COLUMN STATS,dbo,tblItems,ItemDescription,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblItems, Column: ItemDescription, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblItems,ItemDescription2,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblItems, Column: ItemDescription2, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblItems,Barcode,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblItems, Column: Barcode, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblItems,ItemCategory,int,NULL,10,0,YES,,NULL,"Table: tblItems, Column: ItemCategory, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblItems,UofM,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblItems, Column: UofM, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblItems,SNEnable,tinyint,NULL,3,0,YES,,NULL,"Table: tblItems, Column: SNEnable, Type: tinyint(3,0), Nullable: YES"
COLUMN STATS,dbo,tblItems,NegativeStock,tinyint,NULL,3,0,YES,,NULL,"Table: tblItems, Column: NegativeStock, Type: tinyint(3,0), Nullable: YES"
COLUMN STATS,dbo,tblItems,UnitSalesPrice,decimal,NULL,10,2,YES,,NULL,"Table: tblItems, Column: UnitSalesPrice, Type: decimal(10,2), Nullable: YES"
COLUMN STATS,dbo,tblItems,SalesUofM,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblItems, Column: SalesUofM, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblItems,UnitPurchasePrice,decimal,NULL,10,2,YES,,NULL,"Table: tblItems, Column: UnitPurchasePrice, Type: decimal(10,2), Nullable: YES"
COLUMN STATS,dbo,tblItems,ItemType,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblItems, Column: ItemType, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblItems,AUofM,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblItems, Column: AUofM, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblItems,AUofMX,decimal,NULL,10,4,YES,,NULL,"Table: tblItems, Column: AUofMX, Type: decimal(10,4), Nullable: YES"
COLUMN STATS,dbo,tblItems,AUofM_Price,decimal,NULL,10,2,YES,,NULL,"Table: tblItems, Column: AUofM_Price, Type: decimal(10,2), Nullable: YES"
COLUMN STATS,dbo,tblItems,AUofM2,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblItems, Column: AUofM2, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblItems,AUofMX2,decimal,NULL,10,4,YES,,NULL,"Table: tblItems, Column: AUofMX2, Type: decimal(10,4), Nullable: YES"
COLUMN STATS,dbo,tblItems,AUofM2_Price,decimal,NULL,10,2,YES,,NULL,"Table: tblItems, Column: AUofM2_Price, Type: decimal(10,2), Nullable: YES"
COLUMN STATS,dbo,tblItems,AUofM3,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblItems, Column: AUofM3, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblItems,AUofMX3,decimal,NULL,10,4,YES,,NULL,"Table: tblItems, Column: AUofMX3, Type: decimal(10,4), Nullable: YES"
COLUMN STATS,dbo,tblItems,AUofM3_Price,decimal,NULL,10,2,YES,,NULL,"Table: tblItems, Column: AUofM3_Price, Type: decimal(10,2), Nullable: YES"
COLUMN STATS,dbo,tblItems,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblItems, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblItems,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblItems, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblItems,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblItems, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblItems,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblItems, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblItems,Tax_Percent,decimal,NULL,5,2,YES,,NULL,"Table: tblItems, Column: Tax_Percent, Type: decimal(5,2), Nullable: YES"
COLUMN STATS,dbo,tblItems,Brand,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblItems, Column: Brand, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblItems,Status,tinyint,NULL,3,0,YES,,NULL,"Table: tblItems, Column: Status, Type: tinyint(3,0), Nullable: YES"
COLUMN STATS,dbo,tblItems,Item_Photo,varbinary,-1,NULL,NULL,YES,,NULL,"Table: tblItems, Column: Item_Photo, Type: varbinary(-1), Nullable: YES"
COLUMN STATS,dbo,tblItems,Notes,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblItems, Column: Notes, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblItems,Shop,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblItems, Column: Shop, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblItems,UnitAveragePrice,decimal,NULL,10,0,YES,,NULL,"Table: tblItems, Column: UnitAveragePrice, Type: decimal(10,0), Nullable: YES"
COLUMN STATS,dbo,tblItems,OldCode,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblItems, Column: OldCode, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblItemsCategory,RootID,int,NULL,10,0,YES,,NULL,"Table: tblItemsCategory, Column: RootID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblItemsCategory,RootName,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblItemsCategory, Column: RootName, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblItemsCategory,ParentID,int,NULL,10,0,YES,,NULL,"Table: tblItemsCategory, Column: ParentID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblItemsCategory,RootLevel,int,NULL,10,0,YES,,NULL,"Table: tblItemsCategory, Column: RootLevel, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblItemsCategory,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblItemsCategory, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblItemsCategory,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblItemsCategory, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblItemsCategory,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblItemsCategory, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblItemsCategory,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblItemsCategory, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblJEHeader,JESN,int,NULL,10,0,NO,,NULL,"Table: tblJEHeader, Column: JESN, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblJEHeader,TrxDate,date,NULL,NULL,NULL,YES,,NULL,"Table: tblJEHeader, Column: TrxDate, Type: date, Nullable: YES"
COLUMN STATS,dbo,tblJEHeader,Amount,numeric,NULL,19,2,YES,,NULL,"Table: tblJEHeader, Column: Amount, Type: numeric(19,2), Nullable: YES"
COLUMN STATS,dbo,tblJEHeader,Reference,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblJEHeader, Column: Reference, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblJEHeader,ReferenceTrx,int,NULL,10,0,YES,,NULL,"Table: tblJEHeader, Column: ReferenceTrx, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblJEHeader,ReferenceType,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblJEHeader, Column: ReferenceType, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblJEHeader,TrxNote,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblJEHeader, Column: TrxNote, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblJEHeader,PostStatus,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblJEHeader, Column: PostStatus, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblJEHeader,PostingBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblJEHeader, Column: PostingBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblJEHeader,PostingOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblJEHeader, Column: PostingOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblJEHeader,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblJEHeader, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblJEHeader,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblJEHeader, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblLocalKSA,SN,int,NULL,10,0,NO,,NULL,"Table: tblLocalKSA, Column: SN, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblLocalKSA,Local_KSA,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblLocalKSA, Column: Local_KSA, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblLocalKSA,CreatedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblLocalKSA, Column: CreatedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblLocalKSA,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblLocalKSA, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblLocalKSA,ModifiedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblLocalKSA, Column: ModifiedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblLocalKSA,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblLocalKSA, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblMachineLicenses,ID,int,NULL,10,0,NO,,NULL,"Table: tblMachineLicenses, Column: ID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblMachineLicenses,Username,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tblMachineLicenses, Column: Username, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tblMachineLicenses,ActivationCode,nvarchar,255,NULL,NULL,NO,,NULL,"Table: tblMachineLicenses, Column: ActivationCode, Type: nvarchar(255), Nullable: NO"
COLUMN STATS,dbo,tblMachineLicenses,MachineName,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tblMachineLicenses, Column: MachineName, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tblMachineLicenses,MachineID,nvarchar,500,NULL,NULL,NO,,NULL,"Table: tblMachineLicenses, Column: MachineID, Type: nvarchar(500), Nullable: NO"
COLUMN STATS,dbo,tblMachineLicenses,WindowsUser,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tblMachineLicenses, Column: WindowsUser, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tblMachineLicenses,ProcessorCount,int,NULL,10,0,NO,,NULL,"Table: tblMachineLicenses, Column: ProcessorCount, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblMachineLicenses,OSVersion,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblMachineLicenses, Column: OSVersion, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblMachineLicenses,IPAddress,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblMachineLicenses, Column: IPAddress, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblMachineLicenses,ActivationDate,datetime,NULL,NULL,NULL,NO,,(getdate()),"Table: tblMachineLicenses, Column: ActivationDate, Type: datetime, Nullable: NO"
COLUMN STATS,dbo,tblMachineLicenses,ExpiryDate,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblMachineLicenses, Column: ExpiryDate, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblMachineLicenses,IsActive,bit,NULL,NULL,NULL,NO,,((1)),"Table: tblMachineLicenses, Column: IsActive, Type: bit, Nullable: NO"
COLUMN STATS,dbo,tblMachineLicenses,Notes,nvarchar,500,NULL,NULL,YES,,NULL,"Table: tblMachineLicenses, Column: Notes, Type: nvarchar(500), Nullable: YES"
COLUMN STATS,dbo,tblMachineLicenses,CreatedDate,datetime,NULL,NULL,NULL,NO,,(getdate()),"Table: tblMachineLicenses, Column: CreatedDate, Type: datetime, Nullable: NO"
COLUMN STATS,dbo,tblMachineLicenses,LastAccessDate,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblMachineLicenses, Column: LastAccessDate, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPaymentMethod,SN,int,NULL,10,0,NO,,NULL,"Table: tblPaymentMethod, Column: SN, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblPaymentMethod,Payment_Method,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblPaymentMethod, Column: Payment_Method, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblPaymentMethod,CreatedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblPaymentMethod, Column: CreatedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblPaymentMethod,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblPaymentMethod, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPaymentMethod,ModifiedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblPaymentMethod, Column: ModifiedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblPaymentMethod,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblPaymentMethod, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPayMethod,Pay_mthd,int,NULL,10,0,NO,,NULL,"Table: tblPayMethod, Column: Pay_mthd, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblPayMethod,Pay_mthd_Text,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblPayMethod, Column: Pay_mthd_Text, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblPayMethod,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblPayMethod, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblPayMethod,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblPayMethod, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPayMethod,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblPayMethod, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblPayMethod,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblPayMethod, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPayMethodTrx,TrxNo,int,NULL,10,0,YES,,NULL,"Table: tblPayMethodTrx, Column: TrxNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblPayMethodTrx,Pay_mthd,int,NULL,10,0,YES,,NULL,"Table: tblPayMethodTrx, Column: Pay_mthd, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblPayMethodTrx,Pay_amnt,money,NULL,19,4,YES,,NULL,"Table: tblPayMethodTrx, Column: Pay_amnt, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblPayMethodTrx,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblPayMethodTrx, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblPayMethodTrx,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblPayMethodTrx, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPayMethodTrx,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblPayMethodTrx, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblPayMethodTrx,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblPayMethodTrx, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPOSCashier,SN,int,NULL,10,0,NO,,NULL,"Table: tblPOSCashier, Column: SN, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblPOSCashier,Store,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblPOSCashier, Column: Store, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblPOSCashier,CashierAccount,bigint,NULL,19,0,YES,,NULL,"Table: tblPOSCashier, Column: CashierAccount, Type: bigint(19,0), Nullable: YES"
COLUMN STATS,dbo,tblPOSCashier,Createdby,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblPOSCashier, Column: Createdby, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblPOSCashier,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblPOSCashier, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPOSDevices,DeviceID,int,NULL,10,0,NO,,NULL,"Table: tblPOSDevices, Column: DeviceID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblPOSDevices,DeviceName,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblPOSDevices, Column: DeviceName, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblPOSDevices,ShopID,int,NULL,10,0,YES,,NULL,"Table: tblPOSDevices, Column: ShopID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblPOSDevices,Description,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblPOSDevices, Column: Description, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblPOSDevices,IsActive,bit,NULL,NULL,NULL,YES,,((1)),"Table: tblPOSDevices, Column: IsActive, Type: bit, Nullable: YES"
COLUMN STATS,dbo,tblPOSDevices,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblPOSDevices, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblPOSDevices,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblPOSDevices, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblPOSDevices,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblPOSDevices, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPOSDevices,CreatedOn,datetime,NULL,NULL,NULL,YES,,(getdate()),"Table: tblPOSDevices, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPOSSessions,SessionID,int,NULL,10,0,NO,,NULL,"Table: tblPOSSessions, Column: SessionID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblPOSSessions,SessionSN,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tblPOSSessions, Column: SessionSN, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tblPOSSessions,ShopID,int,NULL,10,0,NO,,NULL,"Table: tblPOSSessions, Column: ShopID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblPOSSessions,DeviceID,int,NULL,10,0,NO,,NULL,"Table: tblPOSSessions, Column: DeviceID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblPOSSessions,ShiftID,int,NULL,10,0,NO,,NULL,"Table: tblPOSSessions, Column: ShiftID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblPOSSessions,OpenTime,datetime,NULL,NULL,NULL,NO,,NULL,"Table: tblPOSSessions, Column: OpenTime, Type: datetime, Nullable: NO"
COLUMN STATS,dbo,tblPOSSessions,OpenedBy,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tblPOSSessions, Column: OpenedBy, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tblPOSSessions,CloseTime,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblPOSSessions, Column: CloseTime, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPOSSessions,ClosedBy,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblPOSSessions, Column: ClosedBy, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblPOSSessions,Status,nvarchar,20,NULL,NULL,YES,,('Open'),"Table: tblPOSSessions, Column: Status, Type: nvarchar(20), Nullable: YES"
COLUMN STATS,dbo,tblPOSSessions,OpeningCash,decimal,NULL,18,2,YES,,((0)),"Table: tblPOSSessions, Column: OpeningCash, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblPOSSessions,ClosingCash,decimal,NULL,18,2,YES,,NULL,"Table: tblPOSSessions, Column: ClosingCash, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblPOSSessions,TotalSales,decimal,NULL,18,2,YES,,NULL,"Table: tblPOSSessions, Column: TotalSales, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblPOSSessions,Note,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblPOSSessions, Column: Note, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,POSID,int,NULL,10,0,NO,,NULL,"Table: tblPOSSettings, Column: POSID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblPOSSettings,ShopID,int,NULL,10,0,YES,,NULL,"Table: tblPOSSettings, Column: ShopID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,DefaultCustomerID,int,NULL,10,0,YES,,NULL,"Table: tblPOSSettings, Column: DefaultCustomerID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,DefaultTemplate,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblPOSSettings, Column: DefaultTemplate, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,DefaultPaymentMethodID,int,NULL,10,0,YES,,NULL,"Table: tblPOSSettings, Column: DefaultPaymentMethodID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,EnabledPaymentMethods,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblPOSSettings, Column: EnabledPaymentMethods, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,AllowedCategories,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblPOSSettings, Column: AllowedCategories, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,EnableNumericKeyboard,bit,NULL,NULL,NULL,YES,,NULL,"Table: tblPOSSettings, Column: EnableNumericKeyboard, Type: bit, Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,ShowProductImages,bit,NULL,NULL,NULL,YES,,NULL,"Table: tblPOSSettings, Column: ShowProductImages, Type: bit, Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,ShowPrintDialog,bit,NULL,NULL,NULL,YES,,NULL,"Table: tblPOSSettings, Column: ShowPrintDialog, Type: bit, Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,AllowPartialPayment,bit,NULL,NULL,NULL,YES,,NULL,"Table: tblPOSSettings, Column: AllowPartialPayment, Type: bit, Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,CreatedOn,datetime,NULL,NULL,NULL,YES,,(getdate()),"Table: tblPOSSettings, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,CreatedBy,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblPOSSettings, Column: CreatedBy, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblPOSSettings, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblPOSSettings,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblPOSSettings, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblPOSShifts,ShiftID,int,NULL,10,0,NO,,NULL,"Table: tblPOSShifts, Column: ShiftID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblPOSShifts,ShiftName,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblPOSShifts, Column: ShiftName, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblPOSShifts,StartTime,time,NULL,NULL,NULL,YES,,NULL,"Table: tblPOSShifts, Column: StartTime, Type: time, Nullable: YES"
COLUMN STATS,dbo,tblPOSShifts,EndTime,time,NULL,NULL,NULL,YES,,NULL,"Table: tblPOSShifts, Column: EndTime, Type: time, Nullable: YES"
COLUMN STATS,dbo,tblPOSShifts,IsActive,bit,NULL,NULL,NULL,YES,,((1)),"Table: tblPOSShifts, Column: IsActive, Type: bit, Nullable: YES"
COLUMN STATS,dbo,tblRoots,RootID,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblRoots, Column: RootID, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblRoots,RootName,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblRoots, Column: RootName, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblRoots,ParentID,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblRoots, Column: ParentID, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblRoots,RootLevel,int,NULL,10,0,YES,,NULL,"Table: tblRoots, Column: RootLevel, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblRoots,AccountFinal,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblRoots, Column: AccountFinal, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblRoots,AccountType,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblRoots, Column: AccountType, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblRoots,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblRoots, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblRoots,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblRoots, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblRoots,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblRoots, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblRoots,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblRoots, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblSessionLogs,SessionID,int,NULL,10,0,NO,,NULL,"Table: tblSessionLogs, Column: SessionID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblSessionLogs,Username,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tblSessionLogs, Column: Username, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tblSessionLogs,LoginAttemptTime,datetime2,NULL,NULL,NULL,NO,,(getdate()),"Table: tblSessionLogs, Column: LoginAttemptTime, Type: datetime2, Nullable: NO"
COLUMN STATS,dbo,tblSessionLogs,LoginStatus,nvarchar,20,NULL,NULL,NO,,NULL,"Table: tblSessionLogs, Column: LoginStatus, Type: nvarchar(20), Nullable: NO"
COLUMN STATS,dbo,tblSessionLogs,MachineName,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tblSessionLogs, Column: MachineName, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tblSessionLogs,MachineUser,nvarchar,100,NULL,NULL,NO,,NULL,"Table: tblSessionLogs, Column: MachineUser, Type: nvarchar(100), Nullable: NO"
COLUMN STATS,dbo,tblSessionLogs,SystemUsername,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblSessionLogs, Column: SystemUsername, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblSessionLogs,IPAddress,nvarchar,45,NULL,NULL,YES,,NULL,"Table: tblSessionLogs, Column: IPAddress, Type: nvarchar(45), Nullable: YES"
COLUMN STATS,dbo,tblSessionLogs,UserAgent,nvarchar,500,NULL,NULL,YES,,NULL,"Table: tblSessionLogs, Column: UserAgent, Type: nvarchar(500), Nullable: YES"
COLUMN STATS,dbo,tblSessionLogs,SessionToken,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblSessionLogs, Column: SessionToken, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblSessionLogs,LogoutTime,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblSessionLogs, Column: LogoutTime, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblSessionLogs,SessionDuration,int,NULL,10,0,YES,,NULL,"Table: tblSessionLogs, Column: SessionDuration, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblSessionLogs,FailureReason,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblSessionLogs, Column: FailureReason, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblSessionLogs,IsActive,bit,NULL,NULL,NULL,YES,,((1)),"Table: tblSessionLogs, Column: IsActive, Type: bit, Nullable: YES"
COLUMN STATS,dbo,tblSessionLogs,CreatedDate,datetime2,NULL,NULL,NULL,NO,,(getdate()),"Table: tblSessionLogs, Column: CreatedDate, Type: datetime2, Nullable: NO"
COLUMN STATS,dbo,tblShops,SN,int,NULL,10,0,NO,,NULL,"Table: tblShops, Column: SN, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblShops,Shop_Text,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblShops, Column: Shop_Text, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblShops,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblShops, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblShops,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblShops, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblShops,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblShops, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblShops,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblShops, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,DocNo,int,NULL,10,0,YES,,NULL,"Table: tblStockMovement, Column: DocNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,ItemNo,bigint,NULL,19,0,YES,,NULL,"Table: tblStockMovement, Column: ItemNo, Type: bigint(19,0), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,LineSN,int,NULL,10,0,YES,,NULL,"Table: tblStockMovement, Column: LineSN, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,TrxType,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovement, Column: TrxType, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,TrxDate,date,NULL,NULL,NULL,YES,,NULL,"Table: tblStockMovement, Column: TrxDate, Type: date, Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,TrxQTY,decimal,NULL,10,3,YES,,NULL,"Table: tblStockMovement, Column: TrxQTY, Type: decimal(10,3), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,UnitPrice,decimal,NULL,18,2,YES,,NULL,"Table: tblStockMovement, Column: UnitPrice, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,LineAmount,decimal,NULL,18,2,YES,,NULL,"Table: tblStockMovement, Column: LineAmount, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,VATAmount,decimal,NULL,18,2,YES,,NULL,"Table: tblStockMovement, Column: VATAmount, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,UnitCost,decimal,NULL,18,2,YES,,NULL,"Table: tblStockMovement, Column: UnitCost, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovement, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblStockMovement, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovement, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblStockMovement, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,Store,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovement, Column: Store, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,UofM,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblStockMovement, Column: UofM, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,UofMConversion,decimal,NULL,10,4,YES,,NULL,"Table: tblStockMovement, Column: UofMConversion, Type: decimal(10,4), Nullable: YES"
COLUMN STATS,dbo,tblStockMovement,VATIncluded,tinyint,NULL,3,0,YES,,NULL,"Table: tblStockMovement, Column: VATIncluded, Type: tinyint(3,0), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,TrxNo,int,NULL,10,0,NO,,NULL,"Table: tblStockMovHeader, Column: TrxNo, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblStockMovHeader,TrxDate,date,NULL,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: TrxDate, Type: date, Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,TrxType,nvarchar,50,NULL,NULL,NO,,NULL,"Table: tblStockMovHeader, Column: TrxType, Type: nvarchar(50), Nullable: NO"
COLUMN STATS,dbo,tblStockMovHeader,ReferenceInvoice,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: ReferenceInvoice, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,PartnerNo,bigint,NULL,19,0,YES,,NULL,"Table: tblStockMovHeader, Column: PartnerNo, Type: bigint(19,0), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,PartnerName,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: PartnerName, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,PartnerPhoneNo,nvarchar,10,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: PartnerPhoneNo, Type: nvarchar(10), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,PaymentMethod,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: PaymentMethod, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,PaymentStatus,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: PaymentStatus, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,PartnerReference,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: PartnerReference, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,TrxNote,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: TrxNote, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,Store,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: Store, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,Cashier,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: Cashier, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,TrxVAT,decimal,NULL,18,2,YES,,NULL,"Table: tblStockMovHeader, Column: TrxVAT, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,TrxTotal,decimal,NULL,18,2,YES,,NULL,"Table: tblStockMovHeader, Column: TrxTotal, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,TrxDiscount,decimal,NULL,5,2,YES,,NULL,"Table: tblStockMovHeader, Column: TrxDiscount, Type: decimal(5,2), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,TrxDiscountValue,decimal,NULL,18,2,YES,,NULL,"Table: tblStockMovHeader, Column: TrxDiscountValue, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,TrxNetAmount,decimal,NULL,18,2,YES,,NULL,"Table: tblStockMovHeader, Column: TrxNetAmount, Type: decimal(18,2), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,VOIDSTTS,int,NULL,10,0,YES,,NULL,"Table: tblStockMovHeader, Column: VOIDSTTS, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,ReadyForUse,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: ReadyForUse, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,QRCodeImage,varbinary,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: QRCodeImage, Type: varbinary(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,StoreTo,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: StoreTo, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,VATIncluded,tinyint,NULL,3,0,YES,,NULL,"Table: tblStockMovHeader, Column: VATIncluded, Type: tinyint(3,0), Nullable: YES"
COLUMN STATS,dbo,tblStockMovHeader,EmployeeName,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblStockMovHeader, Column: EmployeeName, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,InInvoiceType,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblStockTrx, Column: InInvoiceType, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,InInvoiceNo,int,NULL,10,0,YES,,NULL,"Table: tblStockTrx, Column: InInvoiceNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,InInvoiceItemSN,int,NULL,10,0,YES,,NULL,"Table: tblStockTrx, Column: InInvoiceItemSN, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,DeviceSN,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblStockTrx, Column: DeviceSN, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,UnitCost,money,NULL,19,4,YES,,NULL,"Table: tblStockTrx, Column: UnitCost, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,UnitPrice,money,NULL,19,4,YES,,NULL,"Table: tblStockTrx, Column: UnitPrice, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,OutInvoiceType,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblStockTrx, Column: OutInvoiceType, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,OutInvoiceNo,int,NULL,10,0,YES,,NULL,"Table: tblStockTrx, Column: OutInvoiceNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,OutInvoiceItemSN,int,NULL,10,0,YES,,NULL,"Table: tblStockTrx, Column: OutInvoiceItemSN, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,InStock,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblStockTrx, Column: InStock, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,CreatedBy,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblStockTrx, Column: CreatedBy, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,CreatedOn,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblStockTrx, Column: CreatedOn, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,OutCreatedBy,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblStockTrx, Column: OutCreatedBy, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,OutCreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblStockTrx, Column: OutCreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblStockTrx,Store,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStockTrx, Column: Store, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStores,SN,int,NULL,10,0,NO,,NULL,"Table: tblStores, Column: SN, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblStores,Store,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStores, Column: Store, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStores,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStores, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStores,CreatedOn,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblStores, Column: CreatedOn, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblStores,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblStores, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblStores,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblStores, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblSummary,DateFrom,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblSummary, Column: DateFrom, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblSummary,المعرف,int,NULL,10,0,NO,,NULL,"Table: tblSummary, Column: المعرف, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblSummary,DateTo,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblSummary, Column: DateTo, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblSummary,SalesUsed,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: SalesUsed, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,SalesNew,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: SalesNew, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,CostNew,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: CostNew, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,CostUsed,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: CostUsed, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,LossNew,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: LossNew, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,LossUsed,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: LossUsed, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,PurchasingNew,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: PurchasingNew, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,PurchasingUsed,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: PurchasingUsed, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,NetNew,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: NetNew, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,NetUsed,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: NetUsed, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,StockNew,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: StockNew, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,StockUsed,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: StockUsed, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,CashNew,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: CashNew, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,CashUsed,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: CashUsed, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,CashMain,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: CashMain, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,LossNew2,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: LossNew2, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,LossUsed2,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: LossUsed2, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,LossUsed3,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: LossUsed3, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblSummary,LossNew3,money,NULL,19,4,YES,,NULL,"Table: tblSummary, Column: LossNew3, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblTax,TaxCode,int,NULL,10,0,NO,,NULL,"Table: tblTax, Column: TaxCode, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblTax,Tax_Text,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblTax, Column: Tax_Text, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblTax,Tax_Percent,decimal,NULL,5,2,YES,,NULL,"Table: tblTax, Column: Tax_Percent, Type: decimal(5,2), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,InvoiceType,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: InvoiceType, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,VATIncludedChangeable,varchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: VATIncludedChangeable, Type: varchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,NonVatInvoiceChangeable,varchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: NonVatInvoiceChangeable, Type: varchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,ReferenceMandatory,varchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: ReferenceMandatory, Type: varchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,MaterialAccountNo,int,NULL,10,0,YES,,NULL,"Table: tblToolsInvoice, Column: MaterialAccountNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,DiscountAccountNo,int,NULL,10,0,YES,,NULL,"Table: tblToolsInvoice, Column: DiscountAccountNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,InvoicePrinter,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: InvoicePrinter, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,PrintOption,int,NULL,10,0,YES,,NULL,"Table: tblToolsInvoice, Column: PrintOption, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,MandatoryVendorVATReg,varchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: MandatoryVendorVATReg, Type: varchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,DefAccountNo,int,NULL,10,0,YES,,NULL,"Table: tblToolsInvoice, Column: DefAccountNo, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,DefPaymentType,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: DefPaymentType, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,DefCashier,int,NULL,10,0,YES,,NULL,"Table: tblToolsInvoice, Column: DefCashier, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,DefStores,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: DefStores, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,DefPriceIncludeVAT,varchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: DefPriceIncludeVAT, Type: varchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,DefNonVATInvoice,varchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: DefNonVATInvoice, Type: varchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblToolsInvoice,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblToolsInvoice, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblUofM,SN,int,NULL,10,0,NO,,NULL,"Table: tblUofM, Column: SN, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblUofM,UofM,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUofM, Column: UofM, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUofM,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUofM, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUofM,CreatedOn,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblUofM, Column: CreatedOn, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblUpdateReason,UpdateSN,int,NULL,10,0,YES,,((0)),"Table: tblUpdateReason, Column: UpdateSN, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUpdateReason,StockTrxSN,int,NULL,10,0,YES,,((0)),"Table: tblUpdateReason, Column: StockTrxSN, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUpdateReason,ActionTaken,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateReason, Column: ActionTaken, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateReason,ReasonText,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateReason, Column: ReasonText, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateReason,CreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateReason, Column: CreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateReason,CreatedOn,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblUpdateReason, Column: CreatedOn, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,UpdateSN,int,NULL,10,0,YES,,((0)),"Table: tblUpdateStock, Column: UpdateSN, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,SN,int,NULL,10,0,YES,,((0)),"Table: tblUpdateStock, Column: SN, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,Store,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: Store, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,Company,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: Company, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,ItemNo,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: ItemNo, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,DeviceSN,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: DeviceSN, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,Unit,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: Unit, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,InDate,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: InDate, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,InPaid,int,NULL,10,0,YES,,NULL,"Table: tblUpdateStock, Column: InPaid, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,InAmount,money,NULL,19,4,YES,,NULL,"Table: tblUpdateStock, Column: InAmount, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,InVendor,int,NULL,10,0,YES,,NULL,"Table: tblUpdateStock, Column: InVendor, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,InVenInvNo,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: InVenInvNo, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,OutDate,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: OutDate, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,OutAmount,money,NULL,19,4,YES,,NULL,"Table: tblUpdateStock, Column: OutAmount, Type: money(19,4), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,OutPaid,int,NULL,10,0,YES,,NULL,"Table: tblUpdateStock, Column: OutPaid, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,OutCustomer,int,NULL,10,0,YES,,NULL,"Table: tblUpdateStock, Column: OutCustomer, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,Notes,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: Notes, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,InCreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: InCreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,InCreatedOn,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: InCreatedOn, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,OutCreatedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: OutCreatedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,OutCreatedOn,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: OutCreatedOn, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,OutStatus,int,NULL,10,0,YES,,NULL,"Table: tblUpdateStock, Column: OutStatus, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUpdateStock,OutNote,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUpdateStock, Column: OutNote, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUserPOSItems,Username,nvarchar,50,NULL,NULL,NO,,NULL,"Table: tblUserPOSItems, Column: Username, Type: nvarchar(50), Nullable: NO"
COLUMN STATS,dbo,tblUserPOSItems,ItemNo,bigint,NULL,19,0,NO,,NULL,"Table: tblUserPOSItems, Column: ItemNo, Type: bigint(19,0), Nullable: NO"
COLUMN STATS,dbo,tblUserPOSItems,CreatedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblUserPOSItems, Column: CreatedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblUserPOSItems,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblUserPOSItems, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblUserPOSItems,ModifiedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblUserPOSItems, Column: ModifiedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblUserPOSItems,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblUserPOSItems, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblUsers,SN,int,NULL,10,0,NO,,NULL,"Table: tblUsers, Column: SN, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblUsers,Username,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblUsers, Column: Username, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblUsers,Password,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblUsers, Column: Password, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblUsers,GroupID,int,NULL,10,0,YES,,NULL,"Table: tblUsers, Column: GroupID, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUsers,DefaultStore,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblUsers, Column: DefaultStore, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblUsers,StoreChange,tinyint,NULL,3,0,YES,,NULL,"Table: tblUsers, Column: StoreChange, Type: tinyint(3,0), Nullable: YES"
COLUMN STATS,dbo,tblUsers,DefaultCustomer,int,NULL,10,0,YES,,NULL,"Table: tblUsers, Column: DefaultCustomer, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUsers,CustomerChange,tinyint,NULL,3,0,YES,,NULL,"Table: tblUsers, Column: CustomerChange, Type: tinyint(3,0), Nullable: YES"
COLUMN STATS,dbo,tblUsers,DefaultCashier,int,NULL,10,0,YES,,NULL,"Table: tblUsers, Column: DefaultCashier, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblUsers,CashierChange,tinyint,NULL,3,0,YES,,NULL,"Table: tblUsers, Column: CashierChange, Type: tinyint(3,0), Nullable: YES"
COLUMN STATS,dbo,tblUsers,ChangeInvoicePrice,tinyint,NULL,3,0,YES,,NULL,"Table: tblUsers, Column: ChangeInvoicePrice, Type: tinyint(3,0), Nullable: YES"
COLUMN STATS,dbo,tblUsers,MaxDiscountPercent,decimal,NULL,5,2,YES,,NULL,"Table: tblUsers, Column: MaxDiscountPercent, Type: decimal(5,2), Nullable: YES"
COLUMN STATS,dbo,tblUsers,CreatedBy,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblUsers, Column: CreatedBy, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblUsers,CreatedOn,datetime2,NULL,NULL,NULL,YES,,NULL,"Table: tblUsers, Column: CreatedOn, Type: datetime2, Nullable: YES"
COLUMN STATS,dbo,tblUsers,ModifiedBy,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblUsers, Column: ModifiedBy, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblUsers,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblUsers, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblVendors,VendorNo,bigint,NULL,19,0,NO,,NULL,"Table: tblVendors, Column: VendorNo, Type: bigint(19,0), Nullable: NO"
COLUMN STATS,dbo,tblVendors,VendorName,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: VendorName, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblVendors,FirstName,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: FirstName, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendors,LastName,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: LastName, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendors,Mobile,int,NULL,10,0,YES,,NULL,"Table: tblVendors, Column: Mobile, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblVendors,Phone,int,NULL,10,0,YES,,NULL,"Table: tblVendors, Column: Phone, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblVendors,Email,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: Email, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblVendors,StreetAddress1,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: StreetAddress1, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendors,StreetAddress2,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: StreetAddress2, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendors,City,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: City, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendors,Region,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: Region, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendors,PostalCode,int,NULL,10,0,YES,,NULL,"Table: tblVendors, Column: PostalCode, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblVendors,PaymentMethod,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: PaymentMethod, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendors,CreditLimit,float,NULL,53,NULL,YES,,NULL,"Table: tblVendors, Column: CreditLimit, Type: float(53,), Nullable: YES"
COLUMN STATS,dbo,tblVendors,PaymentTerm,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: PaymentTerm, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblVendors,ContactPerson,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: ContactPerson, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblVendors,CR,nvarchar,10,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: CR, Type: nvarchar(10), Nullable: YES"
COLUMN STATS,dbo,tblVendors,VATRegNo,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: VATRegNo, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendors,Shop,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: Shop, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendors,Status,nvarchar,-1,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: Status, Type: nvarchar(-1), Nullable: YES"
COLUMN STATS,dbo,tblVendors,LocalVendor,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: LocalVendor, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendors,Notes,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: Notes, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblVendors,CreatedBy,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: CreatedBy, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblVendors,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblVendors,ModifiedBy,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: ModifiedBy, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblVendors,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblVendors, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblVendors,OldCode,int,NULL,10,0,YES,,NULL,"Table: tblVendors, Column: OldCode, Type: int(10,0), Nullable: YES"
COLUMN STATS,dbo,tblVendorStatus,SN,int,NULL,10,0,NO,,NULL,"Table: tblVendorStatus, Column: SN, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblVendorStatus,Vendor_Status,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendorStatus, Column: Vendor_Status, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendorStatus,CreatedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendorStatus, Column: CreatedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendorStatus,CreatedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblVendorStatus, Column: CreatedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblVendorStatus,ModifiedBy,nvarchar,50,NULL,NULL,YES,,NULL,"Table: tblVendorStatus, Column: ModifiedBy, Type: nvarchar(50), Nullable: YES"
COLUMN STATS,dbo,tblVendorStatus,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblVendorStatus, Column: ModifiedOn, Type: datetime, Nullable: YES"
COLUMN STATS,dbo,tblZATCACompany,CompanyID,int,NULL,10,0,NO,,NULL,"Table: tblZATCACompany, Column: CompanyID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblZATCACompany,CompanyName,varchar,255,NULL,NULL,YES,,NULL,"Table: tblZATCACompany, Column: CompanyName, Type: varchar(255), Nullable: YES"
COLUMN STATS,dbo,tblZATCACompany,VATNumber,varchar,15,NULL,NULL,YES,,NULL,"Table: tblZATCACompany, Column: VATNumber, Type: varchar(15), Nullable: YES"
COLUMN STATS,dbo,tblZATCACompany,CSID,varchar,50,NULL,NULL,YES,,NULL,"Table: tblZATCACompany, Column: CSID, Type: varchar(50), Nullable: YES"
COLUMN STATS,dbo,tblZATCACompany,PrivateKeyPath,varchar,500,NULL,NULL,YES,,NULL,"Table: tblZATCACompany, Column: PrivateKeyPath, Type: varchar(500), Nullable: YES"
COLUMN STATS,dbo,tblZATCACompany,APIToken,varchar,500,NULL,NULL,YES,,NULL,"Table: tblZATCACompany, Column: APIToken, Type: varchar(500), Nullable: YES"
COLUMN STATS,dbo,tblZATCACompany,APIBaseURL,varchar,255,NULL,NULL,YES,,NULL,"Table: tblZATCACompany, Column: APIBaseURL, Type: varchar(255), Nullable: YES"
COLUMN STATS,dbo,tblZATCACompany,IsSandbox,bit,NULL,NULL,NULL,YES,,NULL,"Table: tblZATCACompany, Column: IsSandbox, Type: bit, Nullable: YES"
COLUMN STATS,dbo,tblZATCACompany,InvoicePrefix,varchar,20,NULL,NULL,YES,,NULL,"Table: tblZATCACompany, Column: InvoicePrefix, Type: varchar(20), Nullable: YES"
COLUMN STATS,dbo,tblZATCACustomerInfo,ZATCACustomerInfoID,int,NULL,10,0,NO,,NULL,"Table: tblZATCACustomerInfo, Column: ZATCACustomerInfoID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblZATCACustomerInfo,CustomerID,int,NULL,10,0,NO,,NULL,"Table: tblZATCACustomerInfo, Column: CustomerID, Type: int(10,0), Nullable: NO"
COLUMN STATS,dbo,tblZATCACustomerInfo,CustomerName,nvarchar,255,NULL,NULL,NO,,NULL,"Table: tblZATCACustomerInfo, Column: CustomerName, Type: nvarchar(255), Nullable: NO"
COLUMN STATS,dbo,tblZATCACustomerInfo,CRNumber,nvarchar,15,NULL,NULL,YES,,NULL,"Table: tblZATCACustomerInfo, Column: CRNumber, Type: nvarchar(15), Nullable: YES"
COLUMN STATS,dbo,tblZATCACustomerInfo,VATNumber,nvarchar,15,NULL,NULL,NO,,NULL,"Table: tblZATCACustomerInfo, Column: VATNumber, Type: nvarchar(15), Nullable: NO"
COLUMN STATS,dbo,tblZATCACustomerInfo,CountryCode,char,2,NULL,NULL,NO,,('SA'),"Table: tblZATCACustomerInfo, Column: CountryCode, Type: char(2), Nullable: NO"
COLUMN STATS,dbo,tblZATCACustomerInfo,BuildingNumber,nvarchar,10,NULL,NULL,YES,,NULL,"Table: tblZATCACustomerInfo, Column: BuildingNumber, Type: nvarchar(10), Nullable: YES"
COLUMN STATS,dbo,tblZATCACustomerInfo,StreetName,nvarchar,255,NULL,NULL,YES,,NULL,"Table: tblZATCACustomerInfo, Column: StreetName, Type: nvarchar(255), Nullable: YES"
COLUMN STATS,dbo,tblZATCACustomerInfo,CityName,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblZATCACustomerInfo, Column: CityName, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblZATCACustomerInfo,PostalCode,nvarchar,10,NULL,NULL,YES,,NULL,"Table: tblZATCACustomerInfo, Column: PostalCode, Type: nvarchar(10), Nullable: YES"
COLUMN STATS,dbo,tblZATCACustomerInfo,Email,nvarchar,150,NULL,NULL,YES,,NULL,"Table: tblZATCACustomerInfo, Column: Email, Type: nvarchar(150), Nullable: YES"
COLUMN STATS,dbo,tblZATCACustomerInfo,PhoneNumber,nvarchar,20,NULL,NULL,YES,,NULL,"Table: tblZATCACustomerInfo, Column: PhoneNumber, Type: nvarchar(20), Nullable: YES"
COLUMN STATS,dbo,tblZATCACustomerInfo,CreatedBy,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblZATCACustomerInfo, Column: CreatedBy, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblZATCACustomerInfo,CreatedOn,datetime,NULL,NULL,NULL,NO,,(getdate()),"Table: tblZATCACustomerInfo, Column: CreatedOn, Type: datetime, Nullable: NO"
COLUMN STATS,dbo,tblZATCACustomerInfo,ModifiedBy,nvarchar,100,NULL,NULL,YES,,NULL,"Table: tblZATCACustomerInfo, Column: ModifiedBy, Type: nvarchar(100), Nullable: YES"
COLUMN STATS,dbo,tblZATCACustomerInfo,ModifiedOn,datetime,NULL,NULL,NULL,YES,,NULL,"Table: tblZATCACustomerInfo, Column: ModifiedOn, Type: datetime, Nullable: YES"
