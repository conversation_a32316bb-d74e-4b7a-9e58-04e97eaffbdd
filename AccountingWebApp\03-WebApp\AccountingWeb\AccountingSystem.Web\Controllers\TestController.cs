using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using AccountingSystem.Web.Utilities;
using AccountingSystem.Data;

namespace AccountingSystem.Web.Controllers
{
    public class TestController : Controller
    {
        private readonly ILogger<TestController> _logger;
        private readonly IConfiguration _configuration;
        private readonly AccountingDbContext _context;

        public TestController(ILogger<TestController> logger, IConfiguration configuration, AccountingDbContext context)
        {
            _logger = logger;
            _configuration = configuration;
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            ViewBag.Message = "Application is running successfully!";

            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                ViewBag.ConnectionString = connectionString;

                // Test database connectivity
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    ViewBag.DatabaseStatus = "✅ Database Connected Successfully";

                    // Test basic queries
                    var userCountCmd = new SqlCommand("SELECT COUNT(*) FROM tblUsers", connection);
                    var userCount = (int)await userCountCmd.ExecuteScalarAsync();

                    var groupCountCmd = new SqlCommand("SELECT COUNT(*) FROM tblGroupsAuth", connection);
                    var groupCount = (int)await groupCountCmd.ExecuteScalarAsync();

                    ViewBag.UserCount = userCount;
                    ViewBag.GroupCount = groupCount;
                    ViewBag.DatabaseInfo = $"Users: {userCount}, Groups: {groupCount}";
                }
            }
            catch (Exception ex)
            {
                ViewBag.DatabaseStatus = $"❌ Database Error: {ex.Message}";
                ViewBag.ConnectionString = _configuration.GetConnectionString("DefaultConnection");
                ViewBag.ErrorDetails = ex.ToString();
                _logger.LogError(ex, "Database connection test failed");
            }

            return View();
        }

        public async Task<IActionResult> CheckTables()
        {
            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                var requiredTables = new[] { "tblUsers", "tblGroupsAuth", "tblConfig", "tblForms", "tblGroupFormPermissions" };
                var existingTables = new List<string>();
                var missingTables = new List<string>();

                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    foreach (var table in requiredTables)
                    {
                        try
                        {
                            var checkCmd = new SqlCommand($"SELECT COUNT(*) FROM {table}", connection);
                            var count = (int)await checkCmd.ExecuteScalarAsync();
                            existingTables.Add($"{table} ({count} rows)");
                        }
                        catch (Exception)
                        {
                            missingTables.Add(table);
                        }
                    }
                }

                return Json(new
                {
                    status = "success",
                    existingTables = existingTables,
                    missingTables = missingTables,
                    message = missingTables.Count > 0 ?
                        $"Missing tables: {string.Join(", ", missingTables)}" :
                        "All required tables exist"
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    status = "error",
                    message = ex.Message,
                    details = ex.ToString()
                });
            }
        }

        public async Task<IActionResult> CheckAuth()
        {
            try
            {
                var isAuthenticated = User.Identity?.IsAuthenticated ?? false;
                var username = User.Identity?.Name ?? "Not logged in";

                if (!isAuthenticated)
                {
                    return Json(new
                    {
                        status = "not_authenticated",
                        message = "User is not logged in",
                        username = username
                    });
                }

                // Check admin status
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT g.GroupName
                        FROM tblUsers u
                        INNER JOIN tblGroupsAuth g ON u.GroupID = g.GroupID
                        WHERE u.Username = @Username";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Username", username);
                        var groupName = await command.ExecuteScalarAsync() as string;

                        var isAdmin = !string.IsNullOrEmpty(groupName) &&
                                     groupName.Equals("admin", StringComparison.OrdinalIgnoreCase);

                        return Json(new
                        {
                            status = "authenticated",
                            username = username,
                            groupName = groupName ?? "No group found",
                            isAdmin = isAdmin,
                            message = isAdmin ? "User has admin privileges" : "User does not have admin privileges"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    status = "error",
                    message = ex.Message,
                    details = ex.ToString()
                });
            }
        }

        public async Task<IActionResult> TestSystemSetup()
        {
            try
            {
                var isAuthenticated = User.Identity?.IsAuthenticated ?? false;
                var username = User.Identity?.Name ?? "Not logged in";

                if (!isAuthenticated)
                {
                    return Json(new
                    {
                        status = "error",
                        message = "Not authenticated - please log in first"
                    });
                }

                // Test if we can access tblConfig
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Test the exact query that SystemSetup uses
                    var query = "SELECT * FROM tblConfig";
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            var configExists = await reader.ReadAsync();
                            var configData = new Dictionary<string, object>();

                            if (configExists)
                            {
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    var fieldName = reader.GetName(i);
                                    var fieldValue = reader.IsDBNull(i) ? null : reader.GetValue(i);
                                    configData[fieldName] = fieldValue?.ToString() ?? "NULL";
                                }
                            }

                            return Json(new
                            {
                                status = "success",
                                username = username,
                                configExists = configExists,
                                configData = configData,
                                message = configExists ? "tblConfig accessible" : "tblConfig is empty"
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    status = "error",
                    message = ex.Message,
                    details = ex.ToString()
                });
            }
        }

        public async Task<IActionResult> TestControllerRoutes()
        {
            try
            {
                var routes = new List<object>();

                // Test if controllers are accessible
                var controllerTests = new[]
                {
                    new { Controller = "SystemSetup", Action = "Index", Url = "/SystemSetup/Index" },
                    new { Controller = "GroupPermissions", Action = "Index", Url = "/GroupPermissions/Index" },
                    new { Controller = "UserManagement", Action = "Index", Url = "/UserManagement/Index" },
                    new { Controller = "SimpleDashboard", Action = "Index", Url = "/SimpleDashboard/Index" }
                };

                foreach (var test in controllerTests)
                {
                    routes.Add(new
                    {
                        controller = test.Controller,
                        action = test.Action,
                        url = test.Url,
                        status = "Should be accessible if logged in as admin"
                    });
                }

                return Json(new
                {
                    status = "success",
                    message = "Controller route test",
                    routes = routes,
                    currentUser = User.Identity?.Name ?? "Not logged in",
                    isAuthenticated = User.Identity?.IsAuthenticated ?? false
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    status = "error",
                    message = ex.Message,
                    details = ex.ToString()
                });
            }
        }

        public async Task<IActionResult> TestSystemSetupDirect()
        {
            try
            {
                // Test the SystemSetup controller logic directly
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                var model = new SystemSetupViewModel();

                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var query = "SELECT * FROM tblConfig";
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                model.StoreName = reader["StoreName"]?.ToString() ?? "";
                                model.VATRegNo = reader["VATRegNo"]?.ToString() ?? "";
                                model.AddressFooter = reader["AddressFooter"]?.ToString() ?? "";
                                model.ItemDescription1 = reader["ItemDescription1"]?.ToString() ?? "";
                                model.ItemDescription2 = reader["ItemDescription2"]?.ToString() ?? "";
                                model.ItemLevel1 = reader["ItemLevel1"]?.ToString() ?? "";
                                model.ItemLevel2 = reader["ItemLevel2"]?.ToString() ?? "";
                                model.ItemLevel3 = reader["ItemLevel3"]?.ToString() ?? "";
                                model.ItemLevel4 = reader["ItemLevel4"]?.ToString() ?? "";
                                model.ItemLevel5 = reader["ItemLevel5"]?.ToString() ?? "";
                                model.HasLogo = reader["LogoFile"] != DBNull.Value;
                            }
                        }
                    }
                }

                return Json(new
                {
                    status = "success",
                    message = "SystemSetup model loaded successfully",
                    model = new
                    {
                        storeName = model.StoreName,
                        vatRegNo = model.VATRegNo,
                        addressFooter = model.AddressFooter,
                        hasLogo = model.HasLogo
                    }
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    status = "error",
                    message = ex.Message,
                    details = ex.ToString()
                });
            }
        }

        // Simple ViewModel for testing
        public class SystemSetupViewModel
        {
            public string StoreName { get; set; } = string.Empty;
            public string VATRegNo { get; set; } = string.Empty;
            public string AddressFooter { get; set; } = string.Empty;
            public string ItemDescription1 { get; set; } = string.Empty;
            public string ItemDescription2 { get; set; } = string.Empty;
            public string ItemLevel1 { get; set; } = string.Empty;
            public string ItemLevel2 { get; set; } = string.Empty;
            public string ItemLevel3 { get; set; } = string.Empty;
            public string ItemLevel4 { get; set; } = string.Empty;
            public string ItemLevel5 { get; set; } = string.Empty;
            public bool HasLogo { get; set; }
        }

        public async Task<IActionResult> DatabaseTest()
        {
            try
            {
                var connectionString = _configuration.GetConnectionString("AccountingDatabase");

                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Test basic queries
                    var userCountCmd = new SqlCommand("SELECT COUNT(*) FROM tblUsers", connection);
                    var userCount = (int)await userCountCmd.ExecuteScalarAsync();

                    var groupCountCmd = new SqlCommand("SELECT COUNT(*) FROM tblGroupsAuth", connection);
                    var groupCount = (int)await groupCountCmd.ExecuteScalarAsync();

                    return Json(new
                    {
                        status = "success",
                        message = "Database connection and queries successful",
                        connectionString = connectionString,
                        server = "174.138.185.119",
                        database = "SULTDB",
                        userCount = userCount,
                        groupCount = groupCount,
                        timestamp = DateTime.Now
                    });
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    status = "error",
                    message = "Database test failed",
                    error = ex.Message,
                    connectionString = _configuration.GetConnectionString("AccountingDatabase"),
                    timestamp = DateTime.Now
                });
            }
        }

        public async Task<IActionResult> Users()
        {
            try
            {
                var connectionString = _configuration.GetConnectionString("AccountingDatabase");
                var users = new List<dynamic>();

                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT TOP 10 u.SN, u.Username, u.Password, u.GroupID, g.GroupName
                        FROM tblUsers u
                        LEFT JOIN tblGroupsAuth g ON u.GroupID = g.GroupID
                        ORDER BY u.SN";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                users.Add(new
                                {
                                    SN = reader["SN"]?.ToString() ?? "",
                                    Username = reader["Username"]?.ToString() ?? "",
                                    Password = reader["Password"]?.ToString() ?? "",
                                    GroupID = reader["GroupID"]?.ToString() ?? "",
                                    GroupName = reader["GroupName"]?.ToString() ?? ""
                                });
                            }
                        }
                    }
                }

                return Json(new
                {
                    status = "success",
                    message = "Users retrieved successfully",
                    users = users,
                    connectionString = connectionString,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    status = "error",
                    message = "Failed to retrieve users",
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        public IActionResult PasswordHash(string password = "654321")
        {
            var hashCode = password.Trim().GetHashCode().ToString();

            // Try different approaches to match VB.NET behavior
            var vbNetStyle = password.Trim().GetHashCode();
            var withoutToString = password.Trim().GetHashCode();

            var result = new
            {
                password = password,
                hashCode = hashCode,
                vbNetStyleHash = vbNetStyle,
                withoutToString = withoutToString,
                expectedForAbubaker = "1724376655",
                expectedForAdmin = "1266883811",
                timestamp = DateTime.Now
            };

            return Json(result);
        }

        public IActionResult TestVBNetHashes()
        {
            // Known password-hash pairs from the database
            var knownPairs = new Dictionary<string, string>
            {
                { "654321", "1724376655" }, // abubaker's password
                { "847268", "1266883811" }  // Admin's password (assuming this is correct)
            };

            // Test our VB.NET hash compatibility
            var analysis = VBNetHashCompatibility.AnalyzeHashPatterns(knownPairs);

            // Also test individual passwords
            var individualTests = new List<object>();
            foreach (var pair in knownPairs)
            {
                var (isMatch, hashes) = VBNetHashCompatibility.TestHashCompatibility(pair.Key, pair.Value);
                individualTests.Add(new
                {
                    password = pair.Key,
                    expectedHash = pair.Value,
                    isMatch = isMatch,
                    computedHashes = hashes
                });
            }

            return Json(new
            {
                analysis = analysis,
                individualTests = individualTests,
                timestamp = DateTime.Now
            });
        }

        public async Task<IActionResult> DatabaseUsers()
        {
            var connectionString = _configuration.GetConnectionString("DefaultConnection");
            var users = new List<object>();

            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    var query = "SELECT Username, Password FROM tblUsers";

                    using (var command = new SqlCommand(query, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            users.Add(new
                            {
                                username = reader["Username"]?.ToString(),
                                passwordHash = reader["Password"]?.ToString()
                            });
                        }
                    }
                }

                return Json(new
                {
                    users = users,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    error = ex.Message,
                    timestamp = DateTime.Now
                });
            }
        }

        public IActionResult TestPasswordCompatibility(string password = "654321", string expectedHash = "1724376655")
        {
            var result = VBNetHashCompatibility.VerifyVBNetPassword(password, expectedHash);
            var (isMatch, hashes) = VBNetHashCompatibility.TestHashCompatibility(password, expectedHash);

            return Json(new
            {
                password = password,
                expectedHash = expectedHash,
                isCompatible = result,
                isMatch = isMatch,
                computedHashes = hashes,
                vbNetHash = VBNetHashCompatibility.HashPasswordForVBNet(password),
                timestamp = DateTime.Now
            });
        }

        public IActionResult TestSpecificHash(string password = "123456", string hash = "-582751338")
        {
            try
            {
                var isMatch = VBNetHashCompatibility.VerifyVBNetPassword(password, hash);
                var (testMatch, hashes) = VBNetHashCompatibility.TestHashCompatibility(password, hash);

                return Json(new
                {
                    password = password,
                    hash = hash,
                    isMatch = isMatch,
                    testMatch = testMatch,
                    computedHashes = hashes,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    error = ex.Message,
                    stackTrace = ex.StackTrace,
                    timestamp = DateTime.Now
                });
            }
        }

        public IActionResult QuickPasswordTest()
        {
            // Test known working cases first
            var knownTests = new[]
            {
                new { password = "654321", hash = "1724376655", user = "abubaker" },
                new { password = "847268", hash = "1266883811", user = "Admin" }
            };

            var results = new List<object>();

            foreach (var test in knownTests)
            {
                var isMatch = VBNetHashCompatibility.VerifyVBNetPassword(test.password, test.hash);
                results.Add(new
                {
                    user = test.user,
                    password = test.password,
                    hash = test.hash,
                    isMatch = isMatch,
                    status = isMatch ? "✅ Working" : "❌ Failed"
                });
            }

            // Test unknown users with common passwords
            var unknownUsers = new[]
            {
                new { user = "كنج كاندي", hash = "-582751338" },
                new { user = "باربع", hash = "-38304474" },
                new { user = "عبدالله", hash = "-898575486" }
            };

            var commonPasswords = new[] { "123456", "password", "admin", "1234", "123", "12345" };

            foreach (var user in unknownUsers)
            {
                var foundPassword = "";
                foreach (var pwd in commonPasswords)
                {
                    if (VBNetHashCompatibility.VerifyVBNetPassword(pwd, user.hash))
                    {
                        foundPassword = pwd;
                        break;
                    }
                }

                results.Add(new
                {
                    user = user.user,
                    hash = user.hash,
                    foundPassword = foundPassword,
                    status = !string.IsNullOrEmpty(foundPassword) ? $"✅ Found: {foundPassword}" : "❌ Not found"
                });
            }

            return Json(new
            {
                results = results,
                timestamp = DateTime.Now
            });
        }

        public IActionResult TestVBNetHash(string password = "654321")
        {
            // Try to replicate exact VB.NET behavior
            var trimmed = password.Trim();
            var hash = trimmed.GetHashCode();

            // Check if this matches known values
            var isAbubakerMatch = hash.ToString() == "1724376655";
            var isAdminMatch = hash.ToString() == "1266883811";

            var result = new
            {
                password = password,
                trimmed = trimmed,
                hash = hash,
                hashString = hash.ToString(),
                isAbubakerMatch = isAbubakerMatch,
                isAdminMatch = isAdminMatch,
                timestamp = DateTime.Now
            };

            return Json(result);
        }

        public async Task<IActionResult> FindPasswords()
        {
            var results = new List<object>();
            var commonPasswords = new string[]
            {
                "123456", "password", "123456789", "12345678", "12345", "1234567", "1234567890",
                "qwerty", "abc123", "111111", "123123", "admin", "letmein", "welcome", "monkey",
                "1234", "password123", "123", "654321", "superman", "qazwsx", "michael", "football",
                "shadow", "master", "jennifer", "111111", "jordan", "michelle", "daniel", "iloveyou",
                "admin123", "administrator", "root", "toor", "pass", "test", "guest", "info",
                "user", "login", "service", "office", "internet", "computer", "system", "windows"
            };

            try
            {
                var connectionString = _configuration.GetConnectionString("AccountingDatabase");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT u.Username, u.Password, u.GroupID, g.GroupName, u.SN
                        FROM tblUsers u
                        LEFT JOIN tblGroupsAuth g ON u.GroupID = g.GroupID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var username = reader["Username"]?.ToString() ?? "";
                                var storedHash = reader["Password"]?.ToString() ?? "";
                                var groupName = reader["GroupName"]?.ToString() ?? "";

                                var userResult = new
                                {
                                    username = username,
                                    storedHash = storedHash,
                                    groupName = groupName,
                                    possiblePasswords = new List<object>()
                                };

                                // Test each common password
                                foreach (var testPassword in commonPasswords)
                                {
                                    var computedHash = testPassword.Trim().GetHashCode().ToString();
                                    if (computedHash == storedHash)
                                    {
                                        ((List<object>)userResult.possiblePasswords).Add(new
                                        {
                                            password = testPassword,
                                            hashMatch = true,
                                            method = "C# GetHashCode"
                                        });
                                    }
                                }

                                results.Add(userResult);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                results.Add(new { error = ex.Message });
            }

            return Json(new {
                message = "Password analysis complete",
                users = results,
                timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// Test GLConfig table access
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> TestGLConfig()
        {
            try
            {
                var results = new List<object>();
                
                // Test 1: Check if we can connect to database
                var canConnect = await _context.Database.CanConnectAsync();
                results.Add(new { Test = "Database Connection", Result = canConnect });
                
                if (canConnect)
                {
                    // Test 2: Check if table exists using direct SQL
                    var connectionString = _configuration.GetConnectionString("DefaultConnection");
                    using (var connection = new SqlConnection(connectionString))
                    {
                        await connection.OpenAsync();
                        
                        // Check if table exists
                        var tableExistsCmd = new SqlCommand("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'tblGLConfig'", connection);
                        var tableExists = (int)await tableExistsCmd.ExecuteScalarAsync();
                        results.Add(new { Test = "Table Exists", Result = tableExists > 0 });
                        
                        if (tableExists > 0)
                        {
                            // Get table structure
                            var columnsCmd = new SqlCommand("SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tblGLConfig' ORDER BY ORDINAL_POSITION", connection);
                            var columns = new List<object>();
                            using (var reader = await columnsCmd.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    columns.Add(new
                                    {
                                        ColumnName = reader["COLUMN_NAME"],
                                        DataType = reader["DATA_TYPE"],
                                        IsNullable = reader["IS_NULLABLE"]
                                    });
                                }
                            }
                            results.Add(new { Test = "Table Structure", Result = columns });
                            
                            // Count records
                            var recordCountCmd = new SqlCommand("SELECT COUNT(*) FROM tblGLConfig", connection);
                            var recordCount = (int)await recordCountCmd.ExecuteScalarAsync();
                            results.Add(new { Test = "Record Count", Result = recordCount });
                            
                            // Get sample records
                            if (recordCount > 0)
                            {
                                var recordsCmd = new SqlCommand("SELECT TOP 5 * FROM tblGLConfig", connection);
                                var records = new List<object>();
                                using (var reader = await recordsCmd.ExecuteReaderAsync())
                                {
                                    while (await reader.ReadAsync())
                                    {
                                        var record = new Dictionary<string, object>();
                                        for (int i = 0; i < reader.FieldCount; i++)
                                        {
                                            record[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
                                        }
                                        records.Add(record);
                                    }
                                }
                                results.Add(new { Test = "Sample Records", Result = records });
                            }
                            else
                            {
                                results.Add(new { Test = "Sample Records", Result = "No records found" });
                            }
                        }
                        else
                        {
                            results.Add(new { Test = "Table Structure", Result = "Table does not exist" });
                            results.Add(new { Test = "Record Count", Result = "N/A" });
                            results.Add(new { Test = "Sample Records", Result = "N/A" });
                        }
                    }
                    
                    // Test Entity Framework query
                    try
                    {
                        var efRecords = await _context.GLConfigs.Take(5).ToListAsync();
                        results.Add(new { Test = "EF Query Success", Result = true, Count = efRecords.Count });
                    }
                    catch (Exception efEx)
                    {
                        results.Add(new { Test = "EF Query Error", Result = efEx.Message });
                    }
                }
                
                return Json(new { success = true, results = results });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message, stackTrace = ex.StackTrace });
            }
        }
    }
}
