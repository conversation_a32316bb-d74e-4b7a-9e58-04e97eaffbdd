<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.SqlServer.Management.Utility</name>
    </assembly>
    <members>
        <member name="T:Microsoft.SqlServer.Management.Utility.ActionSequence">
            <summary>
            ActionSequence that holds a list of all actions. Actions could be either for validation or execution purposes
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionSequence.#ctor">
            <summary>
            Initializes a new instance of the ActionSequence class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionSequence.#ctor(Microsoft.SqlServer.Management.Utility.UtilityActionManager)">
            <summary>
            Initializes a new instance of the ActionSequence class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionSequence.Execute(Microsoft.SqlServer.Management.Utility.FailureBehavior)">
            <summary>
            Executes all the steps by calling the manager's ExecuteSteps function
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionSequence.OnNotifyPropertyChanged(System.String)">
            <summary>
            OnNotifyPropertyChanged
            </summary>
            <param name="propertyName"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionSequence.Steps">
            <summary>
            Steps
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionSequence.Result">
            <summary>
            Result
            </summary>
        </member>
        <member name="E:Microsoft.SqlServer.Management.Utility.ActionSequence.PropertyChanged">
            <summary>
            PropertyChangedEventHandler
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ActionStep">
            <summary>
            Represents a step in an action sequence.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String)">
            <summary>
            Initializes a new instance of the ActionStep class with the specified <see cref="P:Microsoft.SqlServer.Management.Utility.ActionStep.ActionSequence"/> and step name.
            </summary>
            <param name="validation">The sequence that holds this action step</param>
            <param name="name">The name of the action step</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionStep.Execute(Microsoft.SqlServer.Management.Utility.FailureBehavior)">
            <summary>
            Executes the action step for the children actions
            </summary>
            <param name="failureBehavior">Indicates whether the step should throw when a failure is encountered.</param>
            <returns>An <see cref="T:Microsoft.SqlServer.Management.Utility.ActionStepResult"/> representing the result of the step.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionStep.Skip(System.String)">
            <summary>
            Skip the action step and set its result to NotRun with the specified message.
            </summary>
            <param name="message">A message indicating why the step was skipped.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionStep.ExecuteCore">
            <summary>
            Override this function in all actions to perform the actual execution
            </summary>
            <returns>The action's execution result</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionStep.OnPropertyChanged(System.String)">
            <summary>
            Raises the PropertyChanged event.
            </summary>
            <param name="propertyName">The name of the property that changed.</param>
        </member>
        <member name="E:Microsoft.SqlServer.Management.Utility.ActionStep.PropertyChanged">
            <summary>
            Occurs after the value of a property is changed.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStep.ActionSequence">
            <summary>
            Gets the containing ActionSequence.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStep.Name">
            <summary>
            Gets the name of the step.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStep.State">
            <summary>
            Gets the state of the step.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStep.Result">
            <summary>
            Gets or sets the result of the step.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStep.Exception">
            <summary>
            Gets the exception caught while executing the step.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ActionStepCollection">
            <summary>
            ActionStepCollection class that holds a collection of ActionStep steps. that it is a list is an implementation detail
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionStepCollection.#ctor">
            <summary>
            Initializes a new instance of the ActionStepCollection class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionStepCollection.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence)">
            <summary>
            Initializes a new instance of the ActionStepCollection class.
            </summary>
            <param name="actionSequence">An ActionSequence to use as this collection's parent.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ActionStepResult">
            <summary>
            Represents the result of an ActionStep.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionStepResult.#ctor(Microsoft.SqlServer.Management.Utility.StepResult,System.String,System.String)">
            <summary>
            Initializes a new instance of the ActionStepResult class.
            </summary>
            <param name="result">A value representing the action step result.</param>
            <param name="message">A string value indicating the reason for the result.</param>
            <param name="helpLink">A URL to a help topic providing more detail.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionStepResult.#ctor(Microsoft.SqlServer.Management.Utility.StepResult,System.String)">
            <summary>
            Initializes a new instance of the ActionStepResult class.
            </summary>
            <param name="result">A value representing the action step result.</param>
            <param name="message">A string value indicating the reason for the result.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.Result">
            <summary>
            Gets a value representing the result of the ActionStep.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.Message">
            <summary>
            Gets a string indicating the reason for the result.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.HelpLink">
            <summary>
            Gets a URL to a help topic providing more information.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.Unknown">
            <summary>
            Unknown Result
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.Success">
            <summary>
            Success Result
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.Failure">
            <summary>
            Failure Result
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.Warning">
            <summary>
            Warning Result
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.NotRun">
            <summary>
            Not run Result
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.IsFailure">
            <summary>
            Returns true if the ActionStepResult represents a failure.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.IsNotRun">
            <summary>
            Returns true if the ActionStepResult represents the step was not run.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.IsSuccess">
            <summary>
            Returns true if the ActionStepResult represents success.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.IsPending">
            <summary>
            Returns true if the ActionStepResult represents a pending result.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResult.IsWarning">
            <summary>
            Returns true if the ActionStepResult represents a warning.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ActionStepResultFactory">
            <summary>
            Helper factory class used for ActionStepResult creation.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionStepResultFactory.Create(System.String,System.String)">
            <summary>
            Create a new ActionStepResult with message and help link
            </summary>
            <param name="message"></param>
            <param name="helpLink"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ActionStepResultFactory.Create(System.String)">
            <summary>
            Create a new ActionStepResult with message
            </summary>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResultFactory.Unknown">
            <summary>
            Unknown status
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResultFactory.Success">
            <summary>
            Success status
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResultFactory.Failure">
            <summary>
            Failure status
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResultFactory.Warning">
            <summary>
            Warning status
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ActionStepResultFactory.NotRun">
            <summary>
            NotRun status
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ConfigureUtilityActionStep">
            <summary>
            Configure the Utility.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.SqlActionStep">
            <summary>
            Base class for action steps who run against SQL backend
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.SqlActionStep.WellKnownSidTypes">
            <summary>
            Well known Sid to be used by all SQL Action steps
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.SqlActionStep.smoServer">
            <summary>
            The SMO Server representing the SQL backend
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.SqlActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the SqlActionStep class
            </summary>
            <param name="validation">The action sequence that this step belongs to</param>
            <param name="name">The name of the step</param>
            <param name="smoServer">The SMO Server representing the SQL backend</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.SqlActionStep.InstanceAgentServiceAccountIsWellKnownAccount(Microsoft.SqlServer.Management.Smo.Server,System.String@)">
            <summary>
            Determines if the agent service for the specified instance is running as a well known account.
            </summary>
            <param name="instance">The instance to check.</param>
            <param name="agentServiceAccount">The agent service account name</param>
            <returns>True if the instance's agent service account is a well known account</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.SqlActionStep.IsWellKnownAccount(System.String)">
            <summary>
            Checks whether the user account is a well known account
            </summary>
            <param name="account">The account name</param>
            <returns>True if well known, otherwise false</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.SqlActionStep.ValidateAgentProxyAccountIsNotWellKnown(System.String)">
            <summary>
            Checks whether the agent's proxy account is a well known account
            </summary>
            <param name="accountName">The proxy account name</param>
            <returns>True if well known, otherwise false</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.SqlActionStep.ValidateLoginIsDomainAccount(System.String,System.String)">
            <summary>
            Validate that an account is a domain account on a specified SmoServer.
            </summary>
            <param name="account">The account name to check.</param>
            <param name="validationStep">The validation step name.</param>
            <remarks>This method throws if the account is not a valid domain account.</remarks>
            <returns>The validation result</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.SqlActionStep.ValidateAgentProxyAccount(System.String,Microsoft.SqlServer.Common.SqlSecureString,System.String,System.String)">
            <summary>
            Validate the agent proxy account for the given instance with the given credentials
            </summary>
            <param name="agentProxyAccount">The agent proxy account</param>
            <param name="password">The agent proxy account password</param>
            <param name="managedInstanceCredential">The managed instance credentials</param>
            <param name="managedInstanceProxy">The managed instance proxy account</param>
            <returns>The validation result</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.SqlActionStep.GetAgentServiceAccount(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Gets the agent service account
            </summary>
            <param name="connection">The sql store connection</param>
            <returns>The agent service account name</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.SqlActionStep.AgentServiceAccount">
            <summary>
            Gets the agent service account from the local smo server
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ConfigureUtilityActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,System.String,System.String)">
            <summary>
            Initializes a new instance of the ConfigureUtilityActionStep class that takes dependency information
            </summary>
            <param name="executionSequence">The execution section this step belongs to</param>
            <param name="name">The step name</param>
            <param name="controlPoint">The control point instance</param>
            /// <param name="utilityName">The name of the utility</param>
            <param name="description">Description for the action step</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ConfigureUtilityActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step execution result</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ConfigureUtilityActionStep.Utility">
            <summary>
            Gets the newly created utility
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.CreateCacheDirectoryActionStep">
            <summary>
            Action step for creating the cache directory for DC
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateCacheDirectoryActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,System.String)">
            <summary>
            Initializes a new instance of the CreateCacheDirectoryActionStep class
            </summary>
            <param name="executionSequence">The execution section this step belongs to</param>
            <param name="name">The step name</param>
            <param name="managedInstance">The managed instance this step is executing on</param>
            <param name="agentProxyAccount">The agent proxy account for this step</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateCacheDirectoryActionStep.ExecuteCore">
            <summary>
            Executes the step's action
            </summary>
            <returns>The action step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.CreateControlPointActionStep">
            <summary>
            Updates the current instance to be a Utility Control Point.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateControlPointActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the CreateControlPointActionStep class
            </summary>
            <param name="executionSequence">The execution sequence</param>
            <param name="name">The step's name</param>
            <param name="controlPoint">The control point</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateControlPointActionStep.ExecuteCore">
            <summary>
            Execute the actual step
            </summary>
            <returns>Returns ActionStepResult</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.CreateMdwDatabaseActionStep">
            <summary>
            Create the MDW database.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateMdwDatabaseActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the CreateMdwDatabaseActionStep class
            </summary>
            <param name="executionSequence">The execution sequence</param>
            <param name="name">The name of the action step</param>
            <param name="controlPoint">The control point</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateMdwDatabaseActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.DeleteManagedInstanceFromUcpActionStep">
            <summary>
            Deletes the managed instance from UCP
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeleteManagedInstanceFromUcpActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Utility.ManagedInstance)">
            <summary>
            Initializes a new instance of the DeleteManagedInstanceFromUcpActionStep class
            </summary>
            <param name="executionSequence">The execution sequence</param>
            <param name="name">The action name</param>
            <param name="managedInstanceServer">The managed instance server</param>
            <param name="managedInstance">The managed instance</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeleteManagedInstanceFromUcpActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.InitializeMdwDatabaseActionStep">
            <summary>
            Initialize the MDW database.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.InitializeMdwDatabaseActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the InitializeMdwDatabaseActionStep class
            </summary>
            <param name="executionSequence">The execution sequence</param>
            <param name="name">The name of the step</param>
            <param name="controlPoint">The control point</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.InitializeMdwDatabaseActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.MakeInstanceManagedActionStep">
            <summary>
            Class to encapsulate all the actions needed to make an instance managed
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MakeInstanceManagedActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Utility.Utility,System.String,System.String,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Initializes a new instance of the MakeInstanceManagedActionStep class
            </summary>
            <param name="executionSequence">The execution sequence</param>
            <param name="name">The step name</param>
            <param name="managedInstance">The managed instance</param>
            <param name="utility">The utility instance</param>
            <param name="mdwDatabaseName">Mdw database name</param>
            <param name="agentProxyAccount">Agent proxy account</param>
            <param name="utilityInstanceName">Utility instance name</param>
            <param name="password">Password for the proxy account</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MakeInstanceManagedActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Utility.Utility,System.String,System.String)">
            <summary>
            Initializes a new instance of the MakeInstanceManagedActionStep class
            </summary>
            <param name="executionSequence">The execution sequence</param>
            <param name="name">The step name</param>
            <param name="managedInstance">The managed instance</param>
            <param name="utility">The utility instance</param>
            <param name="mdwDatabaseName">Mdw database name</param>
            <param name="utilityInstanceName">Utility instance name</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MakeInstanceManagedActionStep.ExecuteCore">
            <summary>
            Execute the actual step
            </summary>
            <returns>The result</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MakeInstanceManagedActionStep.GetCacheDirectory">
            <summary>
            Get the Utility Temp Cache Directory path.
            </summary>
            <returns>The cache directory</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.MakeInstanceManagedActionStep.ManagedInstance">
            <summary>
            Gets the actual managed instance if all the steps here are succeeded
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.MakeUtilityManagedActionStep">
            <summary>
            Enroll the utility as a managed instance to itself
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MakeUtilityManagedActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Utility.ConfigureUtilityActionStep)">
            <summary>
            Initializes a new instance of the MakeUtilityManagedActionStep class
            </summary>
            <param name="executionSequence">The execution sequence</param>
            <param name="name">The action name</param>
            <param name="utilityInstance">The utility instance</param>
            <param name="configureUtilityActionStep">The configureUtilityActionStep object</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MakeUtilityManagedActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Utility.ConfigureUtilityActionStep,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Initializes a new instance of the MakeUtilityManagedActionStep class
            </summary>
            <param name="executionSequence">The execution sequence</param>
            <param name="name">The action name</param>
            <param name="utilityInstance">The utility instance</param>
            <param name="configureUtilityActionStep">The configureUtilityActionStep object</param>
            <param name="agentProxyAccount">The agent proxy account</param>
            <param name="password">The proxy account password</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MakeUtilityManagedActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.PrepareInstanceEnrollmentActionStep">
            <summary>
            Prepare an instance for being enrolled as a managed instance
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.PrepareInstanceEnrollmentActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the PrepareInstanceEnrollmentActionStep class
            </summary>
            <param name="executionSequence">The execution sequence</param>
            <param name="name">The step name</param>
            <param name="managedInstance">The managed instance</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.PrepareInstanceEnrollmentActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.RemoveManagedInstanceFromUtilityActionStep">
            <summary>
            Remove the managed instance from utility
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.RemoveManagedInstanceFromUtilityActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the RemoveManagedInstanceFromUtilityActionStep class
            </summary>
            <param name="executionSequence">The execution sequence</param>
            <param name="name">The step name</param>
            <param name="managedInstance">The managed instance</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.RemoveManagedInstanceFromUtilityActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountIsDomainAccountActionStep">
            <summary>
            Validates whether agent's proxy account is a domain account or not
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountIsDomainAccountActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Initializes a new instance of the ValidateAgentProxyAccountIsDomainAccountActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="smoServer">The smo server</param>
            <param name="agentProxyAccount">The agent proxy account</param>
            <param name="password">The agent proxy account password</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountIsDomainAccountActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountIsNotWellKnownActionStep">
            <summary>
            Validated whether agent's proxy account is a well known account or not
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountIsNotWellKnownActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,System.String)">
            <summary>
            Initializes a new instance of the ValidateAgentProxyAccountIsNotWellKnownActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="smoServer">The smo server</param>
            <param name="agentProxyAccount">The agent proxy account</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountIsNotWellKnownActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountOnMIActionStep">
            <summary>
            Class representing validation step to validate the agent proxy account on a given Managed Instance
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountOnMIActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Initializes a new instance of the ValidateAgentProxyAccountOnMIActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="managedInstance">The managed instance</param>
            <param name="agentProxyAccount">The agent proxy account</param>
            <param name="password">The agent proxy account password</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountOnMIActionStep.ExecuteCore">
            <summary>
            Execute the validation step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountOnUcpActionStep">
            <summary>
            Validate the agent's proxy account on the UCP side.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountOnUcpActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Initializes a new instance of the ValidateAgentProxyAccountOnUcpActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="ucpInstance">The ucp instance</param>
            <param name="agentProxyAccount">The agent proxy account</param>
            <param name="password">The agent proxy account password</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentProxyAccountOnUcpActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceAccountIsDomainOnMIActionStep">
            <summary>
            Validates whether the agent's service account is a domain account on the MI side
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceAccountIsDomainOnMIActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateAgentServiceAccountIsDomainOnMIActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="smoServer">The smo server</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceAccountIsDomainOnMIActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceAccountIsDomainOnUcpActionStep">
            <summary>
            Validates whether the agent's service account is a domain account on the UCP side
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceAccountIsDomainOnUcpActionStep.miSmoServer">
            <summary>
            The SMO Server representing the SQL backend on the MI side
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceAccountIsDomainOnUcpActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateAgentServiceAccountIsDomainOnUcpActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="ucpSmoServer">The UCP smo server</param>
            <param name="miSmoServer">The MI smo server</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceAccountIsDomainOnUcpActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceAccountIsNotWellKnownActionStep">
            <summary>
            Validates whether the agent's service account is a well known account
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceAccountIsNotWellKnownActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateAgentServiceAccountIsNotWellKnownActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="smoServer">The smo server</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceAccountIsNotWellKnownActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceStateActionStep">
            <summary>
            Validates the agent's service state (running state and start-up mode)
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceStateActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateAgentServiceStateActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="smoServer">The smo server</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateAgentServiceStateActionStep.ExecuteCore">
            <summary>
            Execute the actial step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateCanMakeManagedActionStep">
            <summary>
            Validates whether a specific SQL instance can be made managed (i.e. it's not already managed)
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateCanMakeManagedActionStep.#ctor(Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Utility.ActionSequence,System.String)">
            <summary>
            Initializes a new instance of the ValidateCanMakeManagedActionStep class
            </summary>
            <param name="managedInstance">The managed instance</param>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateCanMakeManagedActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateCollectionNotConfiguredForNonMDWActionStep">
            <summary>
            Validates whether the DC standard collection sets are already enabled or not.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateCollectionNotConfiguredForNonMDWActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateCollectionNotConfiguredForNonMDWActionStep class
            </summary>
            <param name="actionSequence">The action sequence</param>
            <param name="name">The step name</param>
            <param name="managedInstance">The managed instance</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateCollectionNotConfiguredForNonMDWActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateCompatibilityActionStep">
            <summary>
            Validates the instance's compatibility
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateCompatibilityActionStep.#ctor(Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Initializes a new instance of the ValidateCompatibilityActionStep class
            </summary>
            <param name="smoServer">The smo server</param>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="utilityConnection">The utility connection</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateCompatibilityActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateControlPointActionStep">
            <summary>
            Checks whether a specific instance can be a control point
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateControlPointActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateControlPointActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="smoServer">The smo server</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateControlPointActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateInstanceIsNotAlreadyManagedActionStep">
            <summary>
            Validates whether the instance is already managed or not.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateInstanceIsNotAlreadyManagedActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateInstanceIsNotAlreadyManagedActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="smoServer">The smo server</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateInstanceIsNotAlreadyManagedActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateInstanceIsNotUcpActionStep">
            <summary>
            Validated if the instances is an UCP machine or not
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateInstanceIsNotUcpActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Initializes a new instance of the ValidateInstanceIsNotUcpActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="connection">The sql store connection</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateInstanceIsNotUcpActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateTcpEnabledOnUcpActionStep">
            <summary>
            Validate whether TCP is enabled on the UCP or not
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateTcpEnabledOnUcpActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Initializes a new instance of the ValidateTcpEnabledOnUcpActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="utilityConnection">The utility connection</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateTcpEnabledOnUcpActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateUserHasRequiredAdminCredentialsActionStep">
            <summary>
            Validate whether the logged in user has the required admin credentials or not.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateUserHasRequiredAdminCredentialsActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateUserHasRequiredAdminCredentialsActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="smoServer">The smo server</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateUserHasRequiredAdminCredentialsActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateUtilityCollectionSetExistsActionStep">
            <summary>
            Validates if the utility collection sets exist or not.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateUtilityCollectionSetExistsActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateUtilityCollectionSetExistsActionStep class
            </summary>
            <param name="actionSequence">The action sequence</param>
            <param name="name">The step name</param>
            <param name="managedInstance">The managed instance</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateUtilityCollectionSetExistsActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateUtilityMdwDatabaseNotExistsActionStep">
            <summary>
            Checks whether MDW database already exists on the instance or not
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateUtilityMdwDatabaseNotExistsActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateUtilityMdwDatabaseNotExistsActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="smoServer">The smo server</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateUtilityMdwDatabaseNotExistsActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateVersionActionStep">
            <summary>
            Checks whether the instance version is supported or not.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateVersionActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateVersionActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="smoServer">The smo server</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateVersionActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ValidateWmiQueriesSucceedActionStep">
            <summary>
            Validates whether Wmi queries can succeed or not.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateWmiQueriesSucceedActionStep.#ctor(Microsoft.SqlServer.Management.Utility.ActionSequence,System.String,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the ValidateWmiQueriesSucceedActionStep class
            </summary>
            <param name="validationSequence">The validation sequence</param>
            <param name="name">The step name</param>
            <param name="smoServer">The smo server</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ValidateWmiQueriesSucceedActionStep.ExecuteCore">
            <summary>
            Executes the actual step
            </summary>
            <returns>The step result</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.CompositeSpaceUtilizationData">
            <summary>
            Class for holding the space utilization parameters of the Utility object
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.CompositeUtilizationData">
            <summary>
            Class for holding the utilization parameters of a Utility object
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CompositeUtilizationData.CreateUtilizationData(System.String,System.Data.DataRow,Microsoft.SqlServer.Management.Utility.UtilizationDataType)">
            <summary>
            Creates the CompositeUtilizationData object from the passed datarow
            </summary>
            <param name="name"></param>
            <param name="datarow"></param>
            <param name="utilizationDataType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CompositeUtilizationData.#ctor(System.String,System.Double,System.Double,System.Double)">
            <summary>
            Constructor that initializes all the utilization parameters
            </summary>
            <param name="name"></param>
            <param name="underUtilizationThreshold"></param>
            <param name="currentUtilization"></param>
            <param name="overUtilizationThreshold"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.CompositeUtilizationData.Name">
            <summary>
            Gets the name of the Utility object whose utilization is collected
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.CompositeUtilizationData.UnderUtilizationThreshold">
            <summary>
            Gets the under utilization limit of the Utility object
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.CompositeUtilizationData.CurrentUtilization">
            <summary>
            Gets the current utilization of the Utility object
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.CompositeUtilizationData.OverUtilizationThreshold">
            <summary>
            Gets the over utilization limit of the Utility object
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CompositeSpaceUtilizationData.#ctor(System.String,System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Constructor that initializes all the space utilization parameters
            </summary>
            <param name="name"></param>
            <param name="underUtilizationThreshold"></param>
            <param name="currentUtilization"></param>
            <param name="overUtilizationThreshold"></param>
            <param name="usedSpace"></param>
            <param name="availableSpace"></param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.CompositeSpaceUtilizationData.UsedSpace">
            <summary>
            Gets the used space of the Utility object
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.CompositeSpaceUtilizationData.AvailableSpace">
            <summary>
            Gets the available space of the Utility object
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.Computer">
            <summary>
            This is the non-generated part of the Computer class.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.IProcessorUtilizationProvider">
            <summary>
            Defines a method to retrieve processor utilization history data.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.IProcessorUtilizationProvider.GetProcessorUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            Gets processor utilization history data.
            </summary>
            <param name="startTime">A DateTimeOffset value representing  the beginning of the historical period.</param>
            <param name="endTime">A DateTimeOffset value representing the end of the historical period.</param>
            <param name="interval">An AggregationInterval that represents the data aggregation interval.</param>
            <returns>An IEnumerable of type IProcessorUtilization with process utilization history data.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.#ctor">
            <summary>
            SFC needs a default constructor. Don't use this unless you remember to set parent.        
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.#ctor(Microsoft.SqlServer.Management.Utility.Utility,System.String)">
            <summary>
            Instantiates a new Computer object.
            </summary>
            <param name="parent"></param>
            <param name="name"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.SetName(System.String)">
            <summary>
            Setter for Name property - This is private to avoid
            the SfcKey to be set externally.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.CreateIdentityKey">
            <summary>
            SfcInstance implementation - creates a new key.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.GetChildCollection(System.String)">
            <summary>
            SfcInstance implementation - Gets the SfcCollection for
            the children of Computer.
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.GetObjectFactory">
            <summary>
            Gets the Factory to create a new instance of Computer.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Microsoft#SqlServer#Management#Utility#IProcessorUtilizationProvider#GetProcessorUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            This function returns the processor utilization history for the computer over the UTC time range and aggregation interval
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="interval"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Computer.Name">
            <summary>
            Key property of the type.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Computer.VirtualName">
            <summary>
            Virtual-server-name. 
            Differs from "Name" for a failover cluster. In that case, the VirtualName 
            refers to the logical name for the cluster, while Name refers to 
            the actual computer
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Computer.ID">
            <summary>
            Identification number
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Computer.IsClustered">
            <summary>
            Property to indicate if the Computer is Clustered
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Computer.ProcessorUtilization">
            <summary>
            Gets the processor utilization (%) of the computer
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Computer.CpuName">
            <summary>
            Gets the name of the CPU.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Computer.CpuMaxClockSpeed">
            <summary>
            Gets the Maximum clock speed of the CPU in MHz.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Computer.IdentityKey">
            <summary>
            returns the identity key
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Computer.Parent">
            <summary>
            Computer's parent is set as Utility
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Computer.Volumes">
            <summary>
            Property to get the children collection of volumes.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.Computer.Key">
            Internal key class
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Computer.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.#ctor(Microsoft.SqlServer.Management.Utility.Computer.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Utility.Computer.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.op_Equality(Microsoft.SqlServer.Management.Utility.Computer.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.op_Equality(Microsoft.SqlServer.Management.Utility.Computer.Key,Microsoft.SqlServer.Management.Utility.Computer.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Utility.Computer.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.op_Inequality(Microsoft.SqlServer.Management.Utility.Computer.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.op_Inequality(Microsoft.SqlServer.Management.Utility.Computer.Key,Microsoft.SqlServer.Management.Utility.Computer.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Computer.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Computer.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ComputerCollection">
            <summary>
            This is the collection for Computers.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ComputerCollection.#ctor(Microsoft.SqlServer.Management.Utility.Utility)">
            <summary>
            This constructor sets the parent.
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ComputerCollection.#ctor(Microsoft.SqlServer.Management.Utility.Utility,System.Collections.Generic.IComparer{System.String})">
            <summary>
            This constructor sets the parent and passes on a comparer too.
            </summary>
            <param name="parent"></param>
            <param name="customComparer"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ComputerCollection.Contains(System.String)">
            <summary>
            This checks if the collection contains a child with the given name.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ComputerCollection.GetElementFactoryImpl">
            <summary>
            Returns the Factory that helps instantiate the children objects.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ComputerCollection.Item(System.String)">
            <summary>
            Indexer to get the child object.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ConnectionHelper">
            <summary>
            A class that contains methods for connecting to a utility control point.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ConnectionHelper.EncryptConnectionString(System.String,Microsoft.SqlServer.Management.Common.SqlOlapConnectionInfoBase)">
            <summary>
            Ensures that the connection string specifies an encrypted connection, and modifies 
            the connection string if it doesn.t
            </summary>
            <param name="connectionString">A connection string.</param>
            <param name="connectionInfoBase">A SqlOlapConnectionInfoBase that contains information for encrypting the connection.</param>
            <returns>A connection string that specifies an encrypted connection.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ConnectionHelper.GetAsEncryptedConnection(System.Data.IDbConnection@,Microsoft.SqlServer.Management.Common.SqlOlapConnectionInfoBase)">
            <summary>
            Reopen a connection if it is not already encrypted.
            </summary>
            <param name="databaseConnection">A IDbConnection that should be encrypted.</param>
            <param name="connectionInfoBase">A SqlOlapConnectionInfoBase that contains information for encrypting the connection.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ConnectionHelper.ValidateUcpConnection(System.Data.SqlClient.SqlConnection)">
            <summary>
            Ensure a SqlConnection is valid for connecting to a utility control point.
            </summary>
            <remarks>Throws if the connection is invalid</remarks>
            <exception cref="T:Microsoft.SqlServer.Management.Utility.UtilityException">Throws UtilityException if the server version does not support being a utility control point,
             if the server is not a utility control point, or if the connection login is not a member
            of the utility CMR reader role.</exception>
            <param name="connection">A SqlConnection to validate.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.Constants">
            <summary>
            The constants used the Utility object model.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Constants.ComponentName">
            <summary>
            Used for STrace.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Constants.DefaultMdwCollectionsPerHour">
            <summary>
            Default value for MDW collection frequency per hour
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Constants.DefaultOverUtilizationTrailingWindow">
            <summary>
            Default value for over utilization trailing window
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Constants.DefaultOverUtilizationOccurrenceFrequency">
            <summary>
            Default value for over utilization occurrency frequency
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Constants.DefaultUnderUtilizationTrailingWindow">
            <summary>
            Default value for under utilization trailing window
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Constants.DefaultUnderUtilizationOccurrenceFrequency">
            <summary>
            Default value for under utilization occurrency frequency
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Constants.DefaultOverUtilizationThreshold">
            <summary>
            Default value for over utilization resource health policy threshold
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Constants.DefaultUnderUtilizationThreshold">
            <summary>
            Default value for under utilization resource health policy threshold
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.CreateUcpActionManager">
            <summary>
            Create control point steps
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UtilityActionManager">
            <summary>
            Class to maintain all the actions related to utility
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityActionManager.ExecuteSteps(Microsoft.SqlServer.Management.Utility.FailureBehavior)">
            <summary>
            Execute all the action steps in the sequence
            </summary>
            <param name="failureBehavior">Indicates what to do when the step fails.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityActionManager.BuildActionSequence">
            <summary>
            Builds the action sequence representing the actions to be executed
            </summary>
            <returns>A list of all action steps with their dependencies</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityActionManager.LocateNode(Microsoft.SqlServer.Management.Utility.ActionStep)">
            <summary>
            Locates the dependency node for a specific step
            </summary>
            <param name="step">The step to search for</param>
            <returns>The dependency node if exists</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityActionManager.RearrangeWithDependency">
            <summary>
            Re-arrange the whole action sequence based on the dependency between items.
            i.e. if the sequence was like this: 1,2,3,4,5,6,7. And 2 depends on 1, 5 depends on 6, and 7 depends on both 5,6
            Then the final sequence would look like this: 1,2,3,4,6,5,7
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityActionManager.GetNextStepWithNoDependency(System.Collections.Generic.List{Microsoft.SqlServer.Management.Utility.UtilityActionManager.DependencyNode})">
            <summary>
            Locates the next step which has no dependencies
            </summary>
            <param name="steps">The steps list to search inside</param>
            <returns>The next step with no dependencies</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityActionManager.ActionSteps">
            <summary>
            Gets the list of steps to be executed
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityActionManager.ActionSequence">
            <summary>
            Gets or sets the action sequence representing the actions to be executed
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UtilityActionManager.DependencyNode">
            <summary>
            Internal structure to represent all the actions and their dependencies
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityActionManager.DependencyNode.#ctor(Microsoft.SqlServer.Management.Utility.ActionStep,System.Collections.Generic.List{Microsoft.SqlServer.Management.Utility.ActionStep})">
            <summary>
            Initializes a new instance of the DependencyNode class
            </summary>
            <param name="step">Step value representing the action step</param>
            <param name="dependencies">Step's dependencies list</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityActionManager.DependencyNode.Clone">
            <summary>
            Clone the DependencyNode
            </summary>
            <returns>The cloned version</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityActionManager.DependencyNode.Step">
            <summary>
            Gets the action step
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityActionManager.DependencyNode.Dependencies">
            <summary>
            Gets the dependencies for the action step
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateUcpActionManager.#ctor(Microsoft.SqlServer.Management.Smo.Server,System.String,System.String)">
            <summary>
            Initializes a new instance of the CreateUcpActionManager class
            </summary>
            <param name="utilityServer">The utility server</param>
            <param name="utilityName">The utility name</param>
            <param name="description">The utility description</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateUcpActionManager.#ctor(Microsoft.SqlServer.Management.Smo.Server,System.String,System.String,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Initializes a new instance of the CreateUcpActionManager class with proxy information
            </summary>
            <param name="utilityServer">The utility server</param>
            <param name="utilityName">The utility name</param>
            <param name="description">The utility description</param>
            <param name="agentProxyAccount">The agent proxy account</param>
            <param name="password">The agent proxy password</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateUcpActionManager.BuildActionSequence">
            <summary>
            Get the actual execution steps
            <returns>A list of all action steps with their dependencies</returns>
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.CreateUcpValidationManager">
            <summary>
            Class to encapsulate creating a control point validation steps
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateUcpValidationManager.#ctor(Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the CreateUcpValidationManager class
            </summary>
            <param name="controlPoint">The control point</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateUcpValidationManager.#ctor(Microsoft.SqlServer.Management.Smo.Server,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Initializes a new instance of the CreateUcpValidationManager class with proxy information
            </summary>
            <param name="controlPoint">The control point</param>
            <param name="agentProxyAccount">The agent proxy account</param>
            <param name="password">The agent proxy account password</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.CreateUcpValidationManager.BuildActionSequence">
            <summary>
            Get the action steps to validate creating a new control point
            <returns>A list of all action steps with their dependencies</returns>
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DatabaseStorageUtilizationHierarchyBuilder.#ctor(Microsoft.SqlServer.Management.Utility.IManagedInstanceContext)">
            <summary>
            Constructor setting the Managed Instance's Context for DatabaseStorageUtilizationHierarchyBuilder
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DatabaseStorageUtilizationHierarchyBuilder.BuildDatabaseStorageUtilizationHierarchy(System.Collections.Generic.IEnumerable{System.Data.DataRow})">
            <summary>
            Builds the database storage utilization hierarchy
            </summary>
            <param name="databaseFileUtilizations"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DatabaseStorageUtilizationHierarchyBuilder.Databases">
            <summary>
            Collection of Databases in the Storage Utilization hierarchy
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DatabaseStorageUtilizationHierarchyBuilder.Context">
            <summary>
            Managed Instance's context for the Utility object whose storage utilization hierarchy needs to be built
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.DeployedDac">
            <summary>
            DeployedDac class representing a deployed DAC in Utility
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.IManagedInstanceContext">
            <summary>
            Interface for the managed instance's context needed to calculate the storage utilization history of utility objects
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.IManagedInstanceContext.SqlStoreConnection">
            <summary>
            Gets the SqlStoreConnection object for the utility
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.IManagedInstanceContext.ManagedComputerName">
            <summary>
            Gets the Computer's name corresponding to the ManagedInstance's context
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.IManagedInstanceContext.ManagedComputerVirtualName">
            <summary>
            Get the "virtual" name for the computer. This is typically the same as ManagedComputerName
            except in the case of failover clusters, where ManagedComputerVirtualName refers to the logical name
            for the cluster, while ManagedComputerName refers to the name of each individual computer in the cluster
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.IManagedInstanceContext.ManagedInstanceName">
            <summary>
            Gets the Managed Instance's name corresponding to the ManagedInstance's context
            This is the server-qualified instance name, typically of the form "server\instance"
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.#ctor">
            <summary>
            Default empty constructor required by SFC
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.Microsoft#SqlServer#Management#Utility#IProcessorUtilizationProvider#GetProcessorUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            This function returns the processor utilization history for the Deployed DAC over the UTC time range and aggregation interval
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.GetDatabaseFileUtilizations">
            <summary>
            Returns the DataRows containing the storage utilization related data for database files
            in the DeployedDac
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.GetVolumeUtilizations">
            <summary>
            Returns the DataRows containing the storage utilization related data for database files in the DeployedDac 
            and also the storage utilization related data for the volumes of the server on which the DeployedDac resides
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.GetChildCollection(System.String)">
            <summary>
            Returns the SfcCollection of the DeployedDac object children
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.CreateIdentityKey">
            <summary>
            Creates the Indentity key for the DeployedDac object
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.GetObjectFactory">
            <summary>
            Gets the Factory to create a new instance of DeployedDac
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Id">
            <summary>
            Gets the unique ID of the Deployed DAC on the Utility
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Name">
            <summary>
            Gets the name of the Deployed DAC
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.ServerInstanceName">
            <summary>
            Gets the SQL Server instance name of the Deployed DAC in the form of ServerName\InstanceName
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.DeployedDate">
            <summary>
            Returns a DateTime value representing the deployed date of the DAC.  The time is local 
            to the MI's time zone. 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Description">
            <summary>
            Gets the brief description of the Deployed DAC
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.ProcessorUtilization">
            <summary>
            Gets the Processor(CPU) Utilization(in %) of the Deployed DAC
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.DatabaseName">
            <summary>
            Gets the database name associated with the Deployed DAC
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.ComputerName">
            <summary>
            Gets the computer name on which the DAC is deployed
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.LastReportedTime">
            <summary>
            Returns a DateTimeOffset value representing the last upload time for deployed DAC object.  
            The time is local to the client machine's time zone. 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Collation">
            <summary>
            This property gives the collation for the dac database
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.CompatibilityLevel">
            <summary>
            This property gives the compatibility level for the dac database
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.EncryptionEnabled">
            <summary>
            This property gives the encryption state for the dac database
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.RecoveryModel">
            <summary>
            This property gives the recovery model for the dac database
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Trustworthy">
            <summary>
            This property gives the trustworthy state for the dac database
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.DacProcessorHealthState">
            <summary>
            Gets the server instance processor health state
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.ComputerProcessorHealthState">
            <summary>
            Gets the computer processor health state
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.FileSpaceHealthState">
            <summary>
            Gets the file space health state
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.VolumeSpaceHealthState">
            <summary>
            Gets the volume space health state
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.ContainsOverUtilizedVolumes">
            <summary>
            Indicates whether any volume on which the database files reside is over utilized
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.ContainsUnderUtilizedVolumes">
            <summary>
            Indicates whether any volume on which the database files reside is under utilized
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.ContainsOverUtilizedFileGroups">
            <summary>
            Indicates whether any underlying filegroup is over utilized in terms of file space
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.ContainsUnderUtilizedFileGroups">
            <summary>
            Indicates whether any underlying filegroup is under utilized in terms of file space
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.IsPolicyOverridden">
            <summary>
            Indicates whether any policy is overridden for this object
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.DatabaseState">
            <summary>
            Gets the state of the database associated with the Deployed DAC
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Parent">
            <summary>
            Gets the Parent of the Deployed DAC
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Microsoft#SqlServer#Management#Utility#IManagedInstanceContext#SqlStoreConnection">
            <summary>
            Gets the SqlStoreConnection object for the utility
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Microsoft#SqlServer#Management#Utility#IManagedInstanceContext#ManagedComputerName">
            <summary>
            Gets the Computer's name on which the DAC is deployed
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Microsoft#SqlServer#Management#Utility#IManagedInstanceContext#ManagedComputerVirtualName">
            <summary>
            Get the "virtual" name for this computer. For a failover cluster, this represents 
            the logical name. For a standalone computer, this is the same as ManagedComputerName
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Microsoft#SqlServer#Management#Utility#IManagedInstanceContext#ManagedInstanceName">
            <summary>
            Gets the Managed Instance's name on which the DAC database resides
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.DeployedDac.Key">
            <summary>
            Key class for the DeployedDac object
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.Key.#ctor(System.String,System.String)">
            <summary>
            Constructor taking the keyName and keyServerInstanceName
            </summary>
            <param name="keyName"></param>
            <param name="keyServerInstanceName"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.Key.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Returns the Key from the set of name-value pairs that represent Urn fragment
            </summary>
            <param name="fieldDictionary"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.Key.Equals(System.Object)">
            <summary>
            Checks for equality of Key objects
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Checks for equality of Key objects
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.Key.GetHashCode">
            <summary>
            Returns the HashCode of the Key class
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDac.Key.GetUrnFragment">
            <summary>
            Returns the Urn fragment of the DeployedDac object
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Key.Name">
            <summary>
            Gets the Key Name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDac.Key.ServerInstanceName">
            <summary>
            Gets the Key ServerInstanceName
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.DeployedDacCollection">
            <summary>
            This class represents the collection class of DeployedDac objects
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDacCollection.#ctor(Microsoft.SqlServer.Management.Utility.Utility)">
            <summary>
            This Constructor sets the parent of the DeployedDacCollection
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDacCollection.#ctor(Microsoft.SqlServer.Management.Utility.Utility,System.Collections.Generic.IComparer{System.String})">
            <summary>
            This Constructor sets the parent and customComparer for the DeployedDacCollection
            </summary>
            <param name="parent"></param>
            <param name="customComparer"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDacCollection.Contains(System.String,System.String)">
            <summary>
            This checks if the collection contains a DeployedDac object with the given name and server instance name
            </summary>
            <param name="name"></param>
            <param name="serverInstanceName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDacCollection.GetElementFactoryImpl">
            <summary>
            Returns the Factory that helps instantiate the DeployedDac objects
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DeployedDacCollection.Item(System.String,System.String)">
            <summary>
            Indexer to get the DeployedDac object
            </summary>
            <param name="name"></param>
            <param name="serverInstanceName"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.DeployedDacStorageUtilizationHierarchyBuilder">
            <summary>
            This class represents the Storage Utilization Hierarchy for a DeployedDac
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDacStorageUtilizationHierarchyBuilder.#ctor(Microsoft.SqlServer.Management.Utility.DeployedDac)">
            <summary>
            DeployedDacStorageUtilizationHierarchyBuilder's constructor taking the deployedDac object
            </summary>
            <param name="deployedDac"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDacStorageUtilizationHierarchyBuilder.GetFileGroupNodeHealthState(System.Data.DataRow)">
            <summary>
            Returns the FileGroup's HealthState from the passed Datarow
            </summary>
            <param name="dataRow"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDacStorageUtilizationHierarchyBuilder.GetLogFilesNodeHealthState(System.Data.DataRow)">
            <summary>
            Returns the LogFileGroup's HealthState from the passed Datarow
            </summary>
            <param name="dataRow"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DeployedDacStorageUtilizationHierarchyBuilder.GetDatabaseNodeHealthState(System.Data.DataRow)">
            <summary>
            Returns the Database's HealthState from the passed Datarow
            </summary>
            <param name="dataRow"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.EnrollInstanceActionManager">
            <summary>
            Class represent all the execution steps needed by the "Make Instance Managed" scenario
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.EnrollInstanceActionManager.#ctor(Microsoft.SqlServer.Management.Utility.Utility,Microsoft.SqlServer.Management.Smo.Server,System.String,System.String)">
            <summary>
            Initializes a new instance of the EnrollInstanceActionManager class
            </summary>
            <param name="utility">The utility instance</param>
            <param name="managedInstanceServer">The managed instance server</param>
            <param name="mdwDatabaseName">The mdw database</param>
            <param name="utilityInstanceName">The utility instance name</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.EnrollInstanceActionManager.#ctor(Microsoft.SqlServer.Management.Utility.Utility,Microsoft.SqlServer.Management.Smo.Server,System.String,Microsoft.SqlServer.Common.SqlSecureString,System.String,System.String)">
            <summary>
            Initializes a new instance of the EnrollInstanceActionManager class with proxy information
            </summary>
            <param name="utility">The utility instance</param>
            <param name="managedInstanceServer">The managed instance server</param>
            <param name="agentProxyAccount">The agent proxy account</param>
            <param name="password">The agent proxy account password</param>
            <param name="mdwDatabaseName">The mdw database</param>
            <param name="utilityInstanceName">The utility instance name</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.EnrollInstanceActionManager.BuildActionSequence">
            <summary>
            Get all the action steps
            <returns>A list of all action steps with their dependencies</returns>
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.EnrollInstanceValidationManager">
            <summary>
            Class to represent all the validation needed by the "Make Instance Managed" scenatio
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.EnrollInstanceValidationManager.#ctor(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Initializes a new instance of the EnrollInstanceValidationManager class
            </summary>
            <param name="utilityConnection">The utility connection</param>
            <param name="managedInstanceServer">The managed instance server</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.EnrollInstanceValidationManager.#ctor(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,Microsoft.SqlServer.Management.Smo.Server,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Initializes a new instance of the EnrollInstanceValidationManager class with proxy information
            </summary>
            <param name="utilityConnection">The utility connection</param>
            <param name="managedInstanceServer">The managed instance server</param>
            <param name="agentProxyAccount">The agent proxy account</param>
            <param name="password">The agent proxy account password</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.EnrollInstanceValidationManager.BuildActionSequence">
            <summary>
            Get all the action steps
            <returns>A list of all action steps with their dependencies</returns>
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.RollupObjectType">
            <summary>
            The rollup object type to which the health policy applies.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.RollupObjectType.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.RollupObjectType.DeployedDac">
            <summary>
            Rollup object type deployed DAC
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.RollupObjectType.ManagedInstance">
            <summary>
            Rollup object type managed instance
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.RollupObjectType.Computer">
            <summary>
            Rollup object type computer
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ResourceType">
            <summary>
            The resouce type to monitior the health state.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ResourceType.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ResourceType.StorageSpace">
            <summary>
            Resource of type storage space
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ResourceType.StorageIO">
            <summary>
            Resource of type storage IO
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ResourceType.Processor">
            <summary>
            Resource of type processor
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ResourceType.Memory">
            <summary>
            Resource of type memory 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ResourceType.NetworkIO">
            <summary>
            Resource of type network IO
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.TargetType">
            <summary>
            The target type against which the policy condition is applied.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.TargetType.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.TargetType.Computer">
            <summary>
            Target of type Computer
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.TargetType.DataFile">
            <summary>
            Target of type DataFile
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.TargetType.LogFile">
            <summary>
            Target of type LogFile
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.TargetType.Server">
            <summary>
            Target of type Server 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.TargetType.DeployedDac">
            <summary>
            Target of type DeployedDac
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.TargetType.Volume">
            <summary>
            Target of type Volume
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UtilizationType">
            <summary>
            The utilization type of a health policy
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationType.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationType.UnderUtilization">
            <summary>
            Resource under utilzation
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationType.OverUtilization">
            <summary>
            Resource over utilization
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.HealthState">
            <summary>
            The health state of a resource
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.HealthState.None">
            <summary>
            None state
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.HealthState.Steady">
            <summary>
            Steady state (green)
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.HealthState.Warning">
            <summary>
            Warning state (Yellow)
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.HealthState.Critical">
            <summary>
            Critical sate (Red)
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UtilizationDataType">
            <summary>
            Utilization Data Type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationDataType.None">
            <summary>
            None UtilizationData Type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationDataType.DacProcessor">
            <summary>
            DAC Processor Utilization Data
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationDataType.ManagedInstanceProcessor">
            <summary>
            ManagedInstance Processor Utilization Data
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationDataType.ComputerProcessor">
            <summary>
            Computer Processor Utilization Data
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationDataType.VolumeSpace">
            <summary>
            Volume Space Utilization Data
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationDataType.FileSpace">
            <summary>
            File Space Utilization Data
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ChartTimeInterval">
            <summary>
            This enumeration represents the valid chart time intervals
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ChartTimeInterval.None">
            <summary>
            None
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ChartTimeInterval.OneDay">
            <summary>
            One day
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ChartTimeInterval.OneWeek">
            <summary>
            One week
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ChartTimeInterval.OneMonth">
            <summary>
            One month
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ChartTimeInterval.OneYear">
            <summary>
            One year
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.AggregationInterval">
            <summary>
            Aggregation Interval currently used for performance history api
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.AggregationInterval.None">
            <summary>
            No aggregation
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.AggregationInterval.Hourly">
            <summary>
            hourly interval
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.AggregationInterval.Daily">
            <summary>
            daily interval
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UtilizationCalculationLevel">
            <summary>
            The utilization calculation level 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationCalculationLevel.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationCalculationLevel.Total">
            <summary>
            Total utilization
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilizationCalculationLevel.Allocated">
            <summary>
            Allocated utilization
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.StorageUtilizationLevel">
            <summary>
            Storage utilization Level currently used for performance history api
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StorageUtilizationLevel.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StorageUtilizationLevel.UtilityLevel">
            <summary>
            Overall utility
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StorageUtilizationLevel.ServerLevel">
            <summary>
            ServerLevel
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StorageUtilizationLevel.VolumeLevel">
            <summary>
            VolumeLevel
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StorageUtilizationLevel.InstanceLevel">
            <summary>
            InstanceLevel
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StorageUtilizationLevel.DatabaseLevel">
            <summary>
            DatabaseLevel
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StorageUtilizationLevel.FilegroupLevel">
            <summary>
            FilegroupLevel
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StorageUtilizationLevel.DataFileLevel">
            <summary>
            DatabaseFileLevel
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StorageUtilizationLevel.LogFileLevel">
            <summary>
            LogFile
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ProcessorUtilizationLevel">
            <summary>
            Processor utilization Level currently used for performance history api
            NOTE: The members of this enum are *deliberately* the same (albeit a subset) 
              of the StorageUtilizationLevel. 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ProcessorUtilizationLevel.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ProcessorUtilizationLevel.ServerLevel">
            <summary>
            ServerLevel
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ProcessorUtilizationLevel.InstanceLevel">
            <summary>
            InstanceLevel
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ProcessorUtilizationLevel.DatabaseLevel">
            <summary>
            DatabaseLevel (aka DAC in KJ)
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ActionStepState">
            <summary>
            ActionStepState
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ActionStepState.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ActionStepState.Pending">
            <summary>
            Pending
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ActionStepState.InProgress">
            <summary>
            InProgress
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ActionStepState.Completed">
            <summary>
            Completed
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagementState">
            <summary>
            Management State data type.  Primarily used to track ManagedInstance state
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ManagementState.Unknown">
            <summary>
            Unknown state
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ManagementState.Discovered">
            <summary>
            Instance has been discovered
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ManagementState.Manageable">
            <summary>
            Instance has been validated as manageable
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ManagementState.ManagementPending">
            <summary>
            Make managed has been initiated but is pending
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ManagementState.Managed">
            <summary>
            Management has been verified
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.DatabaseFileType">
            <summary>
            Database File Type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.DatabaseFileType.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.DatabaseFileType.DataFile">
            <summary>
            Data File
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.DatabaseFileType.LogFile">
            <summary>
            Log File
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.DatabaseState">
            <summary>
            Database State 
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.DatabaseState.Unknown">
            <summary>
            Unknown state
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.DatabaseState.Available">
            <summary>
            Online state
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.DatabaseState.Unavailable">
            <summary>
            Not online state
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.MdwRetentionLength">
            <summary>
            Constant values for MDW retention length.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.MinimumLengthInDaysForMinutesHistory">
            <summary>
            Minimum length (in days) for the 15-minute history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.DefaultLengthInDaysForMinutesHistory">
            <summary>
            Default length (in days) for the 15-minute history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.MaximumLengthInDaysForMinutesHistory">
            <summary>
            Maximum length (in days) for the 15-minute history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.MinimumLengthInDaysForHoursHistory">
            <summary>
            Minimum length (in days) for the hourly history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.DefaultLengthInDaysForHoursHistory">
            <summary>
            Default length (in days) for the hourly history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.MaximumLengthInDaysForHoursHistory">
            <summary>
            Maximum length (in days) for the hourly history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.MinimumLengthInDaysForDaysHistory">
            <summary>
            Minimum length (in days) for the daily history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.DefaultLengthInDaysForDaysHistory">
            <summary>
            Default length (in days) for the daily history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.MaximumLengthInDaysForDaysHistory">
            <summary>
            Maximum length (in days) for the daily history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.LengthForDaysHistory_OneMonth">
            <summary>
            One month length (in days) for the daily history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.LengthForDaysHistory_OneQuarter">
            <summary>
            One quarter length (in days) for the daily history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.LengthForDaysHistory_HalfYear">
            <summary>
            One half year length (in days) for the daily history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.LengthForDaysHistory_OneYear">
            <summary>
            One full year length (in days) for the daily history entries.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwRetentionLength.LengthForDaysHistory_TwoYears">
            <summary>
            Two full years length (in days) for the daily history entries.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.OverUtilizationTrailingWindowLength">
            <summary>
            The length of trailing window against which the over utilization is evaluated for a resource
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.OverUtilizationTrailingWindowLength.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.OverUtilizationTrailingWindowLength.OneHour">
            <summary>
            One hour of trailing window length
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.OverUtilizationTrailingWindowLength.QuarterDay">
            <summary>
            Quarter day (6 hours) of trailing window length
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.OverUtilizationTrailingWindowLength.HalfDay">
            <summary>
            Half day (12 hours) of trailing window length
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.OverUtilizationTrailingWindowLength.OneDay">
            <summary>
            One day of trailing window length
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.OverUtilizationTrailingWindowLength.OneWeek">
            <summary>
            One week of trailing window length
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UnderUtilizationTrailingWindowLength">
            <summary>
            The length of trailing window against which the under utilization is evaluated for a resource
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UnderUtilizationTrailingWindowLength.None">
            <summary>
            None type
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UnderUtilizationTrailingWindowLength.OneDay">
            <summary>
            One day of trailing window length
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UnderUtilizationTrailingWindowLength.OneWeek">
            <summary>
            One week of trailing window length
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UnderUtilizationTrailingWindowLength.TwoWeeks">
            <summary>
            Two weeks of trailing window length
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UnderUtilizationTrailingWindowLength.OneMonth">
            <summary>
            One month of trailing window length
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ActionTarget">
            <summary>
            Represents the target of an action such as a validation step
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ActionTarget.ManagedInstance">
            <summary>
            Represents a managed instance
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ActionTarget.UtilityControlPoint">
            <summary>
            Represents a Utility control point
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UploadSchemaCompatibility">
            <summary>
            
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UploadSchemaCompatibility.UpgradeMi">
            <summary>
            The MI is downlevel and needs upgraded to be able to upload to the UCP
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UploadSchemaCompatibility.IsCompatible">
            <summary>
            The MI and UCP are compatible
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UploadSchemaCompatibility.UpgradeUcp">
            <summary>
            The UCP is downlevel and needs upgraded to be able to enroll the MI in the UCP.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.FailureBehavior">
            <summary>
            Represents how an <see cref="T:Microsoft.SqlServer.Management.Utility.ActionStep"/> should behave
            when it fails.
            </summary>
            <remarks>Use Continue when executing actions in the UI and Throw
            when executing actions via a public API call.</remarks>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.FailureBehavior.Record">
            <summary>
            Record the exception but do not throw.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.FailureBehavior.Throw">
            <summary>
            Throw an exception.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.Utility">
            <summary>
            The Utility object is the root object for the hierarchy.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.IStorageUtilizationProvider">
            <summary>
            Defines a method to retrieve storage utilization history data.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.IStorageUtilizationProvider.GetStorageUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            Gets storage utilization history data.
            </summary>
            <param name="startTime">A DateTimeOffset value representing the beginning of the historical period.</param>
            <param name="endTime">A DateTimeOffset value representing the end of the historical period.</param>
            <param name="aggregationInterval">An AggregationInterval that represents the data aggregation interval.</param>
            <returns>An IEnumerable of type IStorageUtilization with storage utilization history data.</returns>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Utility.UtilityMdwDatabaseName">
            <summary>
            The Data warehouse associated with the Utility Control point.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Utility.UtilityCmrDatabaseName">
            <summary>
            The name of the database that hosts CMR objects
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Utility.CmrReaderRoleName">
            <summary>
            Name of the Utility Reader role in the CMR database
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Utility.MdwCacheReaderRoleName">
            <summary>
            Name of the Utility Reader role in the MDW/cache database
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.#cctor">
            <summary>
            The static constructor should be used to initialize the scriptAlterAction formatter.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.#ctor">
            <summary>
            The empty constructor should only be called when the SqlStoreConnection is set separately from the constructor.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.#ctor(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Creates a Utility object with a connection to the Server.
            </summary>
            <param name="connection">The connection to the Server that stores the Utility information</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Connect(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            This method returns a Utility object based on the connection passed in.  This method also validates that the connection is a valid ucp.
            </summary>
            <param name="connection">The connection to the Server that stores the Utility information</param>
            <returns>Utility object</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.CreateUtility(System.String,Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Creates a new Utility based on the provided server context
            </summary>
            <param name="utilityName">The name of the Utility</param>
            <param name="sqlStoreConnection">The Sql connection to the Utility</param>
            <returns>Utility object</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.CreateUtility(System.String,Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Creates a new Utility based on the provided server context and proxy account information
            </summary>
            <param name="utilityName">The name of the Utility</param>
            <param name="sqlStoreConnection">The sql connection to the Utility</param>
            <param name="agentProxyAccount">The agent proxy account</param>
            <param name="agentProxyPassword">The agent proxy account password</param>
            <returns>Utility object</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.EnrollInstance(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Enroll a Sql Server Instance in the Utility
            </summary>
            <param name="sqlStoreConnection">The Sql connection for the instance</param>
            <returns>Managed Instance object</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.EnrollInstance(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Enroll a Sql Server Instance in the Utility with provided proxy account information
            </summary>
            <param name="sqlStoreConnection">The Sql connection for the instance</param>
            <param name="agentProxyAccount">The agent proxy account</param>
            <param name="agentProxyPassword">The agent proxy account password</param>
            <returns>Managed Instance object</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.IsUtilityControlPoint(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            This method is used to validate that a server is a Utility Control Point
            </summary>
            <param name="storeConnection"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.IsLoginUtilityReader(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Validates whether the login used in the connection has utility reader role
            </summary>
            <param name="storeConnection">Connection to utility control point</param>
            <returns>Returns true if the login has reader role else false</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Microsoft#SqlServer#Management#Utility#IStorageUtilizationProvider#GetStorageUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            This returns the storage utilization history for the utility for the specified UTC time range and aggregation interval
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.ExecuteAndSelectTopRow(System.String)">
            <summary>
            This methods will return first(top) row if there are multiple rows and null if there are no rows, returned by running the query
            </summary>
            <param name="query"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.ExecuteAndReturnAllRows(System.String)">
            <summary>
            This method will return the DataRow collection got by running the query(which might be empty also)
            </summary>
            <param name="query"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.GetChildCollection(System.String)">
            <summary>
            Returns the collection that is associated with the element type.
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.CreateIdentityKey">
            <summary>
            Return the Identity Key that corresponds to this class.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Refresh">
            <summary>
             refreshes the object's properties by reading them from the server
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetType(System.String)">
            <summary>
            
            </summary>
            <param name="typeName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain#GetKey(Microsoft.SqlServer.Management.Sdk.Sfc.IUrnFragment)">
            <summary>
            returns the Key object given Urn fragment
            </summary>
            <param name="urnFragment"></param>
            <returns>SfcKey</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomainLite#GetLogicalVersion">
            <summary>
            Returns the logical version of the domain.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDomain2#GetUrnSkeletonsFromType(System.Type)">
            <summary>
            
            </summary>
            <param name="inputType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#GetConnection">
            <summary>
            Get the current connection to query on.
            Return a connection supporting a single serial query, so the query must end before another one may begin.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#SetConnection(Microsoft.SqlServer.Management.Common.ISfcConnection)">
            <summary>
            Sets the active connection.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#GetConnection(Microsoft.SqlServer.Management.Sdk.Sfc.SfcObjectQueryMode)">
            <summary>
            Get the current connection to query on.
            Return a connection supporting either a single serial query or multiple simultaneously open queries as requested.
            </summary>
            <param name="mode"></param>
            <returns>The connection to use, or null to use Cache mode. Cache mode avoids connection and open data reader issues.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Create">
            <summary>
            Create the Utility
            </summary>
            <returns></returns>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Utility.policyStore">
            <summary>
            The policy store.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.ScriptAlter">
            <summary>
            Produces ISfcScript ready to execute 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Alter">
            <summary>
            Persists all changes made to this object.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.GetUtilityLoginMappings">
            <summary>
            Get utility login mappings
            </summary>
            <returns>list of utility login mappings</returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.SqlStoreConnection">
            <summary>
            The connection used to retrive Utility information.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.Name">
            <summary>
            The name of the server connected to
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.UtilityName">
            <summary>
            The friendly Utility name.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.MdwDatabaseName">
            <summary>
            The MDW database name.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DateCreated">
            <summary>
            This property gives the creation time of the utility.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.CreatedBy">
            <summary>
            This gets the 'CreatedBy' information.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.Description">
            <summary>
            This property describes the utility. This is an optional property.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.TotalStorageCapacity">
            <summary>
            The total storage capacity of the utility
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.TotalStorageUtilization">
            <summary>
            The amount of storage currently utilized by the utility
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.OverUtilizationTrailingWindow">
            <summary>
            Gets / Sets the trailing window (in hours) used to compute over utilization health state for volatile resources (processor, I/O etc)
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.OverUtilizationOccurenceFrequency">
            <summary>
            Gets / Sets the occurence frequency (in %) used to compute over utilization health state for volatile resources (processor, I/O etc) over the trailing window specified
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.UnderUtilizationTrailingWindow">
            <summary>
            Gets / Sets the trailing window (in hours) used to compute under utilization health state for volatile resources (processor, I/O etc)
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.UnderUtilizationOccurenceFrequency">
            <summary>
            Gets / Sets the occurence frequency (in %) used to compute under utilization health state for volatile resources (processor, I/O etc) over the trailing window specified
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.MdwRetentionLengthInDaysForMinutesHistory">
            <summary>
            MDW retention length in number of days for the history table containing per-15-minute
            data entries.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.MdwRetentionLengthInDaysForHoursHistory">
            <summary>
            MDW retention length in number of days for the history table containing per-hour
            data entries.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.MdwRetentionLengthInDaysForDaysHistory">
            <summary>
            MDW retention length in number of days for the history table containing per-day
            data entries.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceCount">
            <summary>
            ManagedInstanceCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceHealthyCount">
            <summary>
            ManagedInstanceHealthyCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceUnhealthyCount">
            <summary>
            ManagedInstanceUnhealthyCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceOverutilizedCount">
            <summary>
            ManagedInstanceOverutilizedCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceUnderutilizedCount">
            <summary>
            ManagedInstanceUnderutilizedCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceWithOverutilizedProcessorCount">
            <summary>
            ManagedInstanceWithOverutilizedProcessorCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceWithUnderutilizedProcessorCount">
            <summary>
            ManagedInstanceWithUnderutilizedProcessorCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceWithOverutilizedFileCount">
            <summary>
            ManagedInstanceWithOverutilizedFileCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceWithUnderutilizedFileCount">
            <summary>
            ManagedInstanceWithUnderutilizedFileCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceOnOverutilizedComputerCount">
            <summary>
            ManagedInstanceOnOverutilizedComputerCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceOnUnderutilizedComputerCount">
            <summary>
            ManagedInstanceOnUnderutilizedComputerCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceWithFilesOnOverutilizedVolumeCount">
            <summary>
            ManagedInstanceWithFilesOnOverutilizedVolumeCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceWithFilesOnUnderutilizedVolumeCount">
            <summary>
            ManagedInstanceWithFilesOnUnderutilizedVolumeCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstanceManagementPendingCount">
            <summary>
            ManagedInstanceManagementPendingCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacCount">
            <summary>
            DeployedDacCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacHealthyCount">
            <summary>
            DeployedDacHealthyCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacUnhealthyCount">
            <summary>
            DeployedDacUnhealthyCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacOverutilizedCount">
            <summary>
            DeployedDacOverutilizedCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacUnderutilizedCount">
            <summary>
            DeployedDacUnderutilizedCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacWithOverutilizedProcessorCount">
            <summary>
            DeployedDacWithOverutilizedProcessorCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacWithUnderutilizedProcessorCount">
            <summary>
            DeployedDacWithUnderutilizedProcessorCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacWithOverutilizedFileCount">
            <summary>
            DeployedDacWithOverutilizedFileCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacWithUnderutilizedFileCount">
            <summary>
            DeployedDacWithUnderutilizedFileCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacOnOverutilizedComputerCount">
            <summary>
            DeployedDacOnOverutilizedComputerCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacOnUnderutilizedComputerCount">
            <summary>
            DeployedDacOnUnderutilizedComputerCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacWithFilesOnOverutilizedVolumeCount">
            <summary>
            DeployedDacWithFilesOnOverutilizedVolumeCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacWithFilesOnUnderutilizedVolumeCount">
            <summary>
            DeployedDacWithFilesOnUnderutilizedVolumeCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacManagementPendingCount">
            <summary>
            DeployedDacManagementPendingCount property
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcHasConnection#ConnectionContext">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.PolicyStore">
            <summary>
            The policy store.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.Computers">
            <summary>
            Property to get the children collection of Computers.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.DeployedDacs">
            <summary>
            The SFC collection of deployedDac objects
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ManagedInstances">
            <summary>
            Collection of ManagedInstance objects under Utility domain
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Utility.ResourceHealthPolicies">
            <summary>
            Property to get the collection resource health policies.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.Utility.Key">
            <summary>
            The Key class is used to identify the object within Sfc
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.GetHashCode">
            <summary>
            The hash code for this type
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.Equals(System.Object,System.Object)">
            <summary>
            Returns true if the two objects are equal
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            Returns true if the key equals this object.
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Utility.Utility.Key)">
            <summary>
            Returns true if the object is a key and equal to the right operand
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.op_Equality(Microsoft.SqlServer.Management.Utility.Utility.Key,System.Object)">
            <summary>
            Returns true if the object is a Key and it is equal to the left operand
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.op_Equality(Microsoft.SqlServer.Management.Utility.Utility.Key,Microsoft.SqlServer.Management.Utility.Utility.Key)">
            <summary>
            Returns true if the two operands are equal
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Utility.Utility.Key)">
            <summary>
            Returns true if the object is not equal to the right operand
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.op_Inequality(Microsoft.SqlServer.Management.Utility.Utility.Key,System.Object)">
            <summary>
            Returns false if the object is not equal to the left operand
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.op_Inequality(Microsoft.SqlServer.Management.Utility.Utility.Key,Microsoft.SqlServer.Management.Utility.Utility.Key)">
            <summary>
            Returns true if the left operand is not equal to the right operand
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Utility.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UtilityException">
            <summary>
            Base exception class for all Utility exception classes
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityException.#ctor">
            <summary>
            Base constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityException.#ctor(System.String)">
            <summary>
            Base constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityException.#ctor(System.String,System.Exception)">
            <summary>
            Base constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Base constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityException.Init">
            <summary>
            Initializes instance properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityException.SetHelpContext(System.String)">
            <summary>
            Sets Help Context
            </summary>
            <param name="resource"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityException.ProdVer">
            <summary>
            Product Version
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityException.HelpLink">
            <summary>
            will output a link to the help web site
            <!--http://www.microsoft.com/products/ee/transform.aspx?ProdName=Microsoft%20SQL%20Server&ProdVer=09.00.0000.00&EvtSrc=MSSQLServer&EvtID=15401-->
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping">
            <summary>
            The class represents one server login to Ucp role mapping entry.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.connection">
            <summary>
            Indicate where the mapping lives.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.login">
            <summary>
            The login. The login should be of win user of win group type.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.cmrUser">
            <summary>
            The login mapped user in CMR; if null, then there is no mapped user in CMR yet.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.mdwUser">
            <summary>
            The login mapped user in the MDW/cache database; if null, then there is no mapped user yet.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.isSysadmin">
            <summary>
            sysadmin flag, and why sysadmin can't be changed.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.isReaderRole">
            <summary>
            Reader role flag, readonly flag and the explanation string.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.commitException">
            <summary>
            Use to record the commit result. If null then it's a success.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.Save">
            <summary>
            Save the mapping back to database
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.#ctor(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,Microsoft.SqlServer.Management.Smo.Login,Microsoft.SqlServer.Management.Smo.User,Microsoft.SqlServer.Management.Smo.User,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.GetUtilityLoginMappings(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Static helper to enumerate(construct) the Utility login mapping entries.
            This method returns all the windows authentication type (including users and groups)
            server logins and SQL server logins and maps them to sysadmin and Utility reader role.
            </summary>
            <param name="connection">connection to utility control point</param>
            <returns>A list of UtilityLoginMapping that describes the server logins mapping to sysadmin and Utility reader roles</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.GetConnectionLoginMapping(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Static helper to get the mapping entries for the login used in the specified connection.
            This method extracts the logins from the connection and maps it to sysadmin and Utility reader role.
            </summary>
            <param name="connection">connection to utility control point</param>
            <returns>UtilityLoginMapping that describes the mapping to sysadmin and Utility reader role</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.GetUtilityLoginMapping(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,Microsoft.SqlServer.Management.Smo.Login)">
            <summary>
            Factory method to get the effective Utility permissions for the specified login.
            </summary>
            <param name="connection">connection to utility control point</param>
            <param name="login">Login for which the mapping enteries need to be extracted</param>
            <returns>UtilityLoginMapping that describes the login's effective Utility permissions</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.GetLoginCanonicalName(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String)">
            <summary>
            Static helper to convert the login name to canonical domain account name
            SQL server engine expects this in case of case sensitive server
            </summary>
            <param name="connection">Connection to target instance</param>
            <param name="loginName">Login name for lookup</param>
            <returns>String, representing the coorresponding canonical domain account name of the login name</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.SaveChangePerRole(Microsoft.SqlServer.Management.Smo.Server,System.String,System.String,Microsoft.SqlServer.Management.Smo.User,System.Boolean)">
            <summary>
            note 1: if login is sysadmin, then the operation is skipped;
            note 2: that a user is dbo, then the operation is skipped;
            note 3: for adding to role, if no mapped user exists, then we create a user in the database
            </summary>
            <param name="ucp">Ucp instance</param>
            <param name="databaseName">the database name</param>
            <param name="roleName">the role name in the database</param>
            <param name="user">the user that should be added to/removed from the role</param>
            <param name="addToRole">true, the the login will be added to the role; false the login will be removed from the role</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.LoginName">
            <summary>
            Return the login name of the entry.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.IsSysadmin">
            <summary>
            Whether the login is of sysadmin server role.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.IsReaderRole">
            <summary>
            Set/get the reader role flag.
            Note the setter will throw if the readonly flag is on for this property
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.IsReaderRoleReadOnly">
            <summary>
            If the reader role flag is readonly and not modifiable.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.ReaderRoleReadOnlyReason">
            <summary>
            Return the reader role flag readonly reason. Null if reader role isn't readonly.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UtilityLoginMapping.CommitException">
            <summary>
            The commit result in form of Exception.
            If the property is null, then the mapping save/commit
            has been carried out successfully. If not, the property
            is the exception thrown.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UtilityRegistration">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.SetJobHistoryLogRetentionLevel(Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            This sets the job history log retention size.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.MakeInstanceManaged(Microsoft.SqlServer.Management.Smo.Server,System.String,System.String,System.String,System.String,Microsoft.SqlServer.Common.SqlSecureString,System.Boolean)">
            <summary>
            Function make the instance managed (enroll instance). This clears the Utility Proxy
            from any non Utility collection set, Sets up the Utility Proxy if useProxy is true,
            adds registration info for the enrolled instance, initializes the Utility collection set
            and sets up job history retention level.
            </summary>
            <param name="targetInstance"></param>
            <param name="mdwDatabaseName"></param>
            <param name="ucpInstanceName"></param>
            <param name="cacheDirectory"></param>
            <param name="agentProxyAccount"></param>
            <param name="password"></param>
            <param name="useProxy"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.MakeInstanceManaged(Microsoft.SqlServer.Management.Smo.Server,System.String,System.String,System.String,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Function called to make the instance managed
            </summary>
            <param name="targetInstance"></param>
            <param name="mdwDatabaseName"></param>
            <param name="ucpInstanceName"></param>
            <param name="cacheDirectory"></param>
            <param name="agentProxyAccount"></param>
            <param name="password"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.MakeInstanceManaged(Microsoft.SqlServer.Management.Smo.Server,System.String,System.String)">
            <summary>
            Function called to make the instance managed
            </summary>
            <param name="targetInstance"></param>
            <param name="mdwDatabaseName"></param>
            <param name="ucpInstanceName"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.TryMakeInstanceManaged(Microsoft.SqlServer.Management.Smo.Server,System.String,System.String,System.String,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Function called to make the instance managed
            </summary>
            <param name="targetInstance"></param>
            <param name="ucpInstanceName"></param>
            <param name="proxyName"></param>
            <param name="password"></param>
            <param name="mdwDatabaseName"></param>
            <param name="cacheDirectory"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.GetUtilityCollectionSet(Microsoft.SqlServer.Management.Collector.CollectorConfigStore)">
            <summary>
            Function to get the Utility collection set object
            </summary>
            <param name="store"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.InstanceHasUtilityCollectionSetInstalledCheck(Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Function to check whether the instance has Utility collection set installed or not
            </summary>
            <param name="targetInstance"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.MakeInstanceUnmanaged(Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Function to make targetInstance unmanaged
            </summary>
            <param name="targetInstance"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.IsInstanceAgentStartModeSet(Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Smo.ServiceStartMode)">
            <summary>
            Checks if the agent service's start mode is set to the expected value.
            </summary>
            <param name="instance">A server that represents the instance to check.</param>
            <param name="startMode">A ServiceStartMode that represents the expected start mode.</param>
            <returns>True if the agent service is set to the startMode value; otherwise, false</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.ValidateAgentServiceState(Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Utility.ActionTarget)">
            <summary>
            Validate that the agent service for the specified instance is running and configured correctly
            </summary>
            <param name="instance">The instance to check.</param>
            <param name="target">A value indicating if this method should perform validation for a UCP or for a MI</param>
            <returns>An ActionStepResult; Success if agent is running and configured, Warning if running but not configured, Failure if agent is not running.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.GetValuesForAgentServiceCheck(System.Boolean,Microsoft.SqlServer.Management.Utility.ActionTarget,Microsoft.SqlServer.Management.Smo.ServiceStartMode@,System.String@,System.String@,System.String@)">
            <summary>
            Get values needed to perform the agent service check
            </summary>
            <param name="serverIsClustered">A value representing whether the target server is clustered or not.</param>
            <param name="target">A value representing whether the target server is going to be a UCP or not.</param>
            <param name="expectedStartMode">Returns the ServiceStartMode the target server should be set to.</param>
            <param name="validationText">Returns the validation text based on the ActionTarget.</param>
            <param name="warningText">Returns the warning text based on the ActionTarget.</param>
            <param name="errorText">Returns the error text based on the ActionTarget.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.IsAnyCollectionSetRunning(Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Will return true if any collection sets is running on a given instance.
            </summary>
            <param name="instance"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.IsUtilityControlPoint(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            This method is used to validate that a server is a Utility Control Point
            </summary>
            <param name="storeConnection"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.IsLoginUtilityReader(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Indicates if a connection's login is a member of the Utility Reader functional role. 
            </summary>
            <param name="storeConnection"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.InstanceUcpCheck(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Check whether the current targetInstance is an active Ucp or not
            </summary>
            <param name="storeConnection"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.InstanceManagedCheck(Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Check whether the current targetInstance is already managed or not
            </summary>
            <param name="targetInstance">
            </param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.InstanceCollectionSetConfiguredForNonMdwCheck(Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Check whether the current targetInstance has a running DC collection set or not.
            </summary>
            <param name="targetInstance"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.CurrentUserHasAdminRightsCheck(Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Check whether the current logged in user has admin rights on the provided targetInstance or not
            </summary>
            <param name="targetInstance">The targetInstance to check against</param>
            <returns>True if has admin rights, otherwise false</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.SetupProxy(Microsoft.SqlServer.Management.Smo.Server,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Setup the agent proxy.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.RunAgentJob(Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Smo.Agent.Job,System.String,System.String@)">
            <summary>
            Run the Agent job and report back the result.
            </summary>
            <param name="targetInstance">The targetInstance to run the job on</param>
            <param name="job">The job to run</param>
            <param name="jobQuery">The job query</param>
            <param name="errorMessage">The error message</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityRegistration.InstanceTcpCheck(Microsoft.SqlServer.Management.Smo.Server)">
            <summary>
            Check that TCP/IP is enabled for the specified server.
            </summary>
            <param name="server">A <see cref="T:Microsoft.SqlServer.Management.Smo.Server"/> to check.</param>
            <returns>true if TCP/IP protocol is enabled; otherwise, false</returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.IDataFilePerformanceFacet">
            <summary>
            Interface that describes the extendend properties implemented by data file adapter.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.IDataFilePerformanceFacet.SpaceUtilization">
            <summary>
            Total space Utilization (%) of the data file
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.DataFileAdapter">
            <summary>
            This is an adapter class to extend the properties of the DataFile class
            PBM uses this class to set and evaluate polices against the extended properties.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.DataFileAdapter.#ctor(Microsoft.SqlServer.Management.Smo.DataFile)">
            <summary>
            Parameterized constructor
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.DataFileAdapter.SpaceUtilization">
            <summary>
            Gets the space utilization (%)
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.FileAdapterHelper">
            <summary>
            This is helper class which implements functions that assist in compution space utilization of the file.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.FileAdapterHelper.GetTotalSpace(System.Double,System.Double,System.Double,Microsoft.SqlServer.Management.Smo.FileGrowthType,System.Int64)">
            <summary>
            Gets the total space a file could grow up to (considering maxsize / volume free space)
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.FileAdapterHelper.GetAbsoluteGrowthSize(System.Double,System.Double,System.Double)">
            <summary>
            Gets the total space a file could grow up to (considering maxsize with growth specified in KiloBytes)
            The algorithm used for compuation is as follows
            s = SMO.size (bytes)
            g = SMO.growth (KB, i.e., absolute size in bytes)
            v = projected growth (s + free size)(bytes)
            File size after auto-grow occurrences: (v-((v-s) mod g))
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.FileAdapterHelper.GetPercentageGrowthSize(System.Double,System.Double,System.Double)">
            <summary>
            Gets the total space a file could grow up to (considering maxsize with growth specified in %)
            The algorithm used for compuation is as follows
            s = SMO.size (bytes)
            g = SMO.growth (%, i.e., number between 0 and 100 inclusive)
            v = projected growth (s + free size)(bytes)
            n = # auto-grow occurrences (an integer)
            File size after n auto-grow occurrences: s*(1+g)^n
            We want the largest integer n such that:  s*(1+g)^n less-than or equal to v
            That is, n = floor ( log (1+v/s) / log (1+g) )
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ILogFilePerformanceFacet">
            <summary>
            Interface that describes the extendend properties implemented by Log File adapter.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ILogFilePerformanceFacet.SpaceUtilization">
            <summary>
            Total space Utilization (%) of the log File
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.LogFileAdapter">
            <summary>
            This is an adapter class to extend the properties of the LogFile class
            PBM uses this class to set and evaluate polices against the extended properties.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.LogFileAdapter.#ctor(Microsoft.SqlServer.Management.Smo.LogFile)">
            <summary>
            Parameterized constructor
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.LogFileAdapter.SpaceUtilization">
            <summary>
            Gets the space utilization (%)
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UcpFactory">
            <summary>
            The Utility Control point Factory. 
            </summary>
            <remarks>
            This factory can be instantiated to access an existing
            Utility control point or to create a new one. After instantiation if the property called
            Utility -
            1) Returns a valid (not null) object that implies that this instance is already an
               existing Utility control point. In this case the public properties UtilityName,
               MdwDatabaseName, Description will return the Utility object's name, MDW database name
               and description respectively. Consequently these properties are not allowed to be set (changed).
               Likewise the single step Utility creation functions CreateUtility() and
               CreateUtiltiy(string agentProxyAccount, SqlSecureString password) are not allowed to be
               invoked since the Utility has already been created.
            <para/>
            2) Returns null that implies that this instance is not a Utility control point and the
               factory shall allow the user to make this instance the Utility control point. The
               user has 2 choices in creating the Utility control point -
               a) Call the following validation functions -
            <para/>
                  Set the properties -
                  UtilityName and optionally the Description.
            <para/>
                  Call the following function -
                  CreateUtility()
                     (if agent service account is to be used), OR call -
                  CreateUtiltiy(string agentProxyAccount, SqlSecureString password)
                     (if agent proxy is to be used)
            <para/>
                  This shall create the Utility control point. The Utility object can now be
                  accessed by the Utility property.
            <para/>
               b) Set the properties -
                  UtilityName and optionally the Description.
            <para/>
                  Call Execute() off each of the ActionStep in factory.Validate().Steps
                     (if agent service account is to be used) OR
                  Call Execute() off each of the ActionStep in factory.Validate(agentProxyAccount, password).Steps
                     (if agent proxy account is to be used)
            <para/>         
                  AND
                  Call Execute() off each of the ActionStep in factory.Install().Steps
            <para/>
                  The Utility object can still be accessed by the Utility property.
            </remarks>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UcpFactory.#ctor(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Initializes a new instance of the UcpFactory class with the specified SqlStoreConnection.
            </summary>
            <param name="connection">A SqlStoreConnection to the target instance.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UcpFactory.Validate">
            <summary>
            Get the ActionSequence for validating that an instance
            can become a utility control point.
            </summary>
            <returns>An ActionSequence.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UcpFactory.Validate(System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Get the ActionSequence for validating that an instance
            can become a utility control point with the specified agent proxy account
            and agent proxy password.
            </summary>
            <param name="agentProxyAccount">The agent proxy account.</param>
            <param name="password">The agent proxy account password.</param>
            <returns>An ActionSequence.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UcpFactory.Install">
            <summary>
            Get the ActionSequence for creating the utility control point.
            </summary>
            <remarks>This method does not return the ActionSequence for
            validating that an instance can be a utility control point.</remarks>
            <returns>An ActionSequence.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UcpFactory.CreateUtility">
            <summary>
            Create the Utility control point. This is a one step function which runs the validations,
            creates the MDW database, initializes the MDW database, creates the Utility and finally enrolls it.
            This returns the created Utility object as well.
            This overload should be used if the agent service account option is desired.
            </summary>
            <returns>A Utility representing the created utility control point.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UcpFactory.CreateUtility(System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Create the Utility control point. This is a one step function which runs the validations,
            creates the MDW database, initializes the MDW database, creates the Utility and finally enrolls it.
            This returns the created Utility object as well.
            This overload should be used if the agent proxy account option is desired.
            </summary>
            <param name="agentProxyAccount">The agent proxy account.</param>
            <param name="password">The agent proxy password.</param>
            <returns>A Utility representing the created utility control point.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UcpFactory.RunValidationsAndInstall">
            <summary>
            Execute validation steps and then execute installation steps.
            </summary>
            <returns>A Utility representing the created utility control point.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UcpFactory.EnsureValidated">
            <summary>
            Ensure that all validation steps have finished running
            and that none of them failed.
            </summary>
            <exception cref="T:Microsoft.SqlServer.Management.Utility.UtilityException"></exception>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UcpFactory.Utility">
            <summary>
            Gets a Utility representing the utility control point created by this factory.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UcpFactory.MdwDatabaseName">
            <summary>
            Gets the name of the MDW database.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UcpFactory.UtilityName">
            <summary>
            Gets or sets the name of the utility control point.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UcpFactory.Description">
            <summary>
            Gets or sets the utility control point description.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UcpFactory.AgentServiceAccount">
            <summary>
            Gets the agent service account
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UcpFactory.AgentProxyAccount">
            <summary>
            Gets or sets the agent proxy account used when creating the utility control point.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.UcpFactory.Password">
            <summary>
            Gets the agent proxy password.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation">
            <summary>
            The class implements the health policy violation object functionality 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.#ctor">
            <summary>
            SFC needs a default constructor. Don't use this unless you remember to set parent.        
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.#ctor(Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy)">
            <summary>
            Instantiates a new HealthPolicyViolation object.
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.CreateIdentityKey">
            <summary>
            SfcInstance implementation - creates a new key.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.GetChildCollection(System.String)">
            <summary>
            SfcInstance implementation - Gets the SfcCollection for
            the children of HealthPolicyViolation.
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.GetObjectFactory">
            <summary>
            Gets the Factory to create a new instance of HealthPolicyViolation.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.ID">
            <summary>
            Property for identification
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Name">
            <summary>
            Gets the policy name 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.ExecutionDate">
            <summary>
            Gets the policy last run date-time as a UCP-local time
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.TargetQueryExpression">
            <summary>
            Gets the target query expression
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Result">
            <summary>
            Gets the policy execution result
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.IdentityKey">
            <summary>
            returns the identity key
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Parent">
            <summary>
            HealthPolicyViolation's parent is set as ResourceHealthPolicy
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key">
            Internal key class
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.keyId">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.#ctor(Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.#ctor(System.Int64)">
            <summary>
            
            </summary>
            <param name="id"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.op_Equality(Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.op_Equality(Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key,Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.op_Inequality(Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.op_Inequality(Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key,Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.HealthPolicyViolation.Key.Id">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.HealthPolicyViolationCollection">
            <summary>
            The class implements the health policy violation collection functionality 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolationCollection.#ctor(Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy)">
            <summary>
            This constructor sets the parent.
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolationCollection.#ctor(Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy,System.Collections.Generic.IComparer{System.String})">
            <summary>
            This constructor sets the parent and passes on a comparer too.
            </summary>
            <param name="parent"></param>
            <param name="customComparer"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolationCollection.Contains(System.Int64)">
            <summary>
            This checks if the collection contains a child with the given name.
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.HealthPolicyViolationCollection.GetElementFactoryImpl">
            <summary>
            Returns the Factory that helps instantiate the children objects.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.HealthPolicyViolationCollection.Item(System.Int64)">
            <summary>
            Indexer to get the child object.
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.IProcessorUtilization">
            <summary>
            Interface for exposing processor utilization data
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.IResourceUtilization">
            <summary>
            base interface for resource utilization
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.IResourceUtilization.SampleTime">
            <summary>
            A DateTimeOffset value (UCP-local time zone) recording the time that the sample was captured
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.IResourceUtilization.UtilizationPercentage">
            <summary>
            utilization percent (0-100)
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.IStorageUtilization">
            <summary>
            Interface for exposing processor utilization data
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.IStorageUtilization.CapacityInBytes">
            <summary>
            storage capacity in bytes
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.IStorageUtilization.UtilizationInBytes">
            <summary>
            storage utilization in bytes
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedDatabase">
            <summary>
            This class corresponds to the Database node in Storage Utilization hierarchy
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedDatabase.GetStorageUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            This function returns the storage utilization history for the database over the UTC time range and aggregation interval
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedDatabaseFile">
            <summary>
            This class corresponds to the DatabaseFile in Storage Utilization hierarchy
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedDatabaseFile.GetStorageUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            This function returns the storage utilization history for the database file over the UTC time range and aggregation interval
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedDataFile">
            <summary>
            This class corresponds to the DataFile in Storage Utilization hierarchy
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedDataFile.GetStorageUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            This function returns the storage utilization history for the datafile over the UTC time range and aggregation interval
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedDataFile.LogicalName">
            <summary>
            Logical Name of this datafile (i.e.) the name used in the database 
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedFileGroup">
            <summary>
            This class corresponds to the Filegroup node in Storage Utilization hierarchy
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedFileGroup.GetStorageUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            This function returns the storage utilization history for the filegroup over the UTC time range and aggregation interval
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedInstance">
            <summary>
            This class implements the managed instance. 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.#cctor">
            <summary>
            Static constructor. This builds the TSQL scripts - provides the stored procedure name
            and its parameter-values. The values are loaded from the property bag identified by
            the SFC property identifier.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.#ctor">
            <summary>
            Initializes a new instance of the ManagedInstance class.
            </summary>
            <remarks>Required by SFC.</remarks>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.#ctor(Microsoft.SqlServer.Management.Utility.Utility,System.String,System.String,System.String,System.String,Microsoft.SqlServer.Management.Utility.ManagementState)">
            <summary>
            Initializes a new instnace of the ManagedInstance class with the specified values.
            </summary>
            <param name="parent">The parent Utility.</param>
            <param name="instanceName">The name of the instance.</param>
            <param name="netName">The network name of the instance.</param>
            <param name="agentProxyAccount">The account name to use as the agent proxy.</param>
            <param name="cacheDirectory">The cache directory to use for data collector.</param>
            <param name="managementState">A ManagementState value representing the initial state.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.Remove(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Removes the managed instance from the Utility
            </summary>
            <param name="sqlStoreConnection">The instance to remove</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.RemoveInternal(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Remove Managed Instance from the Utility
            </summary>
            <param name="sqlConnection"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.IsInstanceStillManaged(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Validates whether the target instance is still in managed state as enrolled by the UCP
            
            A managed instance can undergo configuration changes w/o un-enrolling from UCP. For e.g.
            1. Re-install a non-utility version of SQL Server (e.g. Katmai - RTM)
            2. Re-install a utility version of SQL Server (e.g. KJ - R2) but the instance is not enrolled against any UCP.
            3. Re-install a utility version of SQL Server (e.g. KJ - R2) and the instance is enrolled against a different UCP
            
            In all the above mentioned scenarios, the desired behavior of the un-enroll functionality is to 
            silently remove the managed instance entry from the UCP.  
            
            </summary>
            <param name="sqlStoreConnection">Connection to the target instance</param>
            <returns>Boolean indicating whether the instance is still managed by this UCP</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.Microsoft#SqlServer#Management#Utility#IProcessorUtilizationProvider#GetProcessorUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            This function returns the processor utilization history for the managed instance over the UTC time range and aggregation interval
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.GetDatabaseFileUtilizations">
            <summary>
            Returns the DataRows containing the storage utilization related data for database files
            in the ManagedInstance
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.GetVolumeUtilizations">
            <summary>
            Returns the DataRows containing the storage utilization related data for database files in the ManagedInstance
            and also the storage utilization related data for the volumes of the server on which the ManagedInstance resides
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.CreateIdentityKey">
            <summary>
            SfcInstance implementation - creates a new key.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.GetChildCollection(System.String)">
            <summary>
            SfcInstance implementation - Gets the SfcCollection for
            the children of Managed Instance. 
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.GetObjectFactory">
            <summary>
            Gets the Factory to create a new instance of ManagedInstance.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcCreatable#ScriptCreate">
            <summary>
            Produces ISfcScript ready to execute 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.PostCreate(System.Object)">
            <summary>
            Perform post-create action
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.Microsoft#SqlServer#Management#Common#ICreatable#Create">
            <summary>
            creates the script for running create proc
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.Microsoft#SqlServer#Management#Common#IDroppable#Drop">
            <summary>
            
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.Microsoft#SqlServer#Management#Sdk#Sfc#ISfcDroppable#ScriptDrop">
            <summary>
            Scripts deletion of the object
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.Name">
            <summary>
            This property gets the managed instance name 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ID">
            <summary>
            The unique identifier - this is the primary key used to store the object at the backend table
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.AgentProxyAccount">
            <summary>
            This property gives the agent proxy account of the managed instance.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.CacheDirectory">
            <summary>
            This property gives the cache directory of the managed instance.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.DateCreated">
            <summary>
            Returns a DateTimeOffset value representing the creation time of the managed instance. 
            The time is local to the client machine's time zone. 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ManagementState">
            <summary>
            This currently read-only property gets the management state of the ManageInstance.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ServerProcessorHealthState">
            <summary>
            Gets the server instance processor health state
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ComputerProcessorHealthState">
            <summary>
            Gets the computer processor health state
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.FileSpaceHealthState">
            <summary>
            Gets the file space health state
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.VolumeSpaceHealthState">
            <summary>
            Gets the volume space health state
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ContainsOverUtilizedVolumes">
            <summary>
            Indicates whether any volume on which the database files reside is over utilized
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ContainsUnderUtilizedVolumes">
            <summary>
            Indicates whether any volume on which the database files reside is under utilized
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ContainsOverUtilizedDatabases">
            <summary>
            Indicates whether any underlying database is over utilized in terms of file space
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ContainsUnderUtilizedDatabases">
            <summary>
            Indicates whether any underlying database is under utilized in terms of file space
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.IsPolicyOverridden">
            <summary>
            Indicates whether any policy is overridden for this object
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.Version">
            <summary>
            This property gives the version string for the server instance
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.BackupDirectory">
            <summary>
            This property gives the backup directory for the server instance
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.Collation">
            <summary>
            This property gives the collation for the server instance
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.EngineEdition">
            <summary>
            This property gives the engine edition for the server instance
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.IsCaseSensitive">
            <summary>
            This property gives the case sensitivity for the server instance
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.IsClustered">
            <summary>
            This property gives the clustered state for the server instance
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ProductLevel">
            <summary>
            This property gives the product level for the server instance
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ServerType">
            <summary>
            This property gives the server type for the server instance
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.Edition">
            <summary>
            This property gives the sql edition for the server instance
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.InstanceName">
            <summary>
            This property gives the instance name for the server instance
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ComputerNamePhysicalNetBIOS">
            <summary>
            This property gives the Physical NetBIOS for the server instance
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.NetName">
            <summary>
            This property gives the net name for the server instance
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.ServerUrn">
            <summary>
            This property gives the urn for the server instance
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.LastReportedTime">
            <summary>
            Gets a DateTimeOffset value representing the most recent set of data returned from this managed 
            instance.  Time is local to the client machine's time zone. 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.Language">
            <summary>
            Gets the Language.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.OSVersion">
            <summary>
            Gets the OS Version.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.Processors">
            <summary>
            Gets the number of logical processors.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.PhysicalMemory">
            <summary>
            Gets the physical memory of the computer.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.CpuName">
            <summary>
            Gets the name of the CPU.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.CpuMaxClockSpeed">
            <summary>
            Gets the Maximum clock speed of the CPU in MHz.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.IdentityKey">
            <summary>
            returns the identity key
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.Parent">
            <summary>
            ManagedInstance's parent is set as the Utility.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.Microsoft#SqlServer#Management#Utility#IManagedInstanceContext#SqlStoreConnection">
            <summary>
            Gets the SqlStoreConnection object for the utility
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.Microsoft#SqlServer#Management#Utility#IManagedInstanceContext#ManagedComputerName">
            <summary>
            Gets the Computer's name on which the Managed Instance is installed
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.Microsoft#SqlServer#Management#Utility#IManagedInstanceContext#ManagedComputerVirtualName">
            <summary>
            Get the "virtual" name for this computer. For a failover cluster, this represents 
            the logical name. For a standalone computer, this is the same as ManagedComputerName
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstance.Microsoft#SqlServer#Management#Utility#IManagedInstanceContext#ManagedInstanceName">
            <summary>
            Gets the Managed Instance's name from the ManagedInstance object
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedInstance.Key">
            <summary>
            The key used to access the managed instance
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.NameKey">
            <summary>
            The NameKey class is used to identify the object within Utility which have "Name" property as the key
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.NameKey.#ctor(System.String)">
            <summary>
            Constructor taking the keyName
            </summary>
            <param name="keyName"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.NameKey.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.NameKey.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.NameKey.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.NameKey.op_Equality(System.Object,Microsoft.SqlServer.Management.Utility.NameKey)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.NameKey.op_Equality(Microsoft.SqlServer.Management.Utility.NameKey,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.NameKey.op_Equality(Microsoft.SqlServer.Management.Utility.NameKey,Microsoft.SqlServer.Management.Utility.NameKey)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.NameKey.op_Inequality(System.Object,Microsoft.SqlServer.Management.Utility.NameKey)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.NameKey.op_Inequality(Microsoft.SqlServer.Management.Utility.NameKey,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.NameKey.op_Inequality(Microsoft.SqlServer.Management.Utility.NameKey,Microsoft.SqlServer.Management.Utility.NameKey)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.NameKey.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.NameKey.Name">
            <summary>
            Property for getting the key Name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.Key.#ctor(System.String)">
            <summary>
            Constructor taking the key name
            </summary>
            <param name="managedInstanceName"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstance.Key.GetUrnFragment">
            <summary>
            Gets the UrnFragment for the SfcInstance type
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedInstanceCollection">
            <summary>
            ManagedInstanceCollection class 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceCollection.#ctor(Microsoft.SqlServer.Management.Utility.Utility)">
            <summary>
            This Constructor sets the parent of the ManagedInstanceCollection
            </summary>
            <param name="parent">parent of the ManagedInstanceCollection</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceCollection.#ctor(Microsoft.SqlServer.Management.Utility.Utility,System.Collections.Generic.IComparer{System.String})">
            <summary>
            This Constructor sets the parent and customComparer for the ManagedInstanceCollection
            </summary>
            <param name="parent">parent of the ManagedInstanceCollection</param>
            <param name="customComparer">customComparer of the ManagedInstanceCollection</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceCollection.Contains(System.String)">
            <summary>
            This checks if the collection contains a child with the given name.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceCollection.GetElementFactoryImpl">
            <summary>
            Returns the Factory that helps instantiate the children objects.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstanceCollection.Item(System.String)">
            <summary>
            Indexer to get the child object.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory">
            <summary>
            Class used for creation of ManagedInstance's in the Utility
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.#ctor(Microsoft.SqlServer.Management.Utility.Utility,Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Initializes a new instance of the ManagedInstanceFactory class using the specified control point,
            and connection.
            </summary>
            <param name="utility">A Utility object representing the control point to enroll the instance into.</param>
            <param name="connection">A SqlStoreConnection to the instance to enroll.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.Validate(Microsoft.SqlServer.Management.Utility.Utility,Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection)">
            <summary>
            Validate that a SQL Server instance can be enrolled.
            </summary>
            <param name="utility">The UCP the instance would be enrolled into.</param>
            <param name="targetConnection">A connection to the instance to be enrolled.</param>
            <exception cref="T:Microsoft.SqlServer.Management.Utility.UtilityException">Throws if the instance cannot be enrolled.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.Validate(Microsoft.SqlServer.Management.Utility.Utility,Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Validate that a SQL Server instance can be enrolled with the specified proxy account name and password.
            </summary>
            <param name="utility">The UCP the instance would be enrolled into.</param>
            <param name="targetConnection">A connection to the instance to be enrolled.</param>
            <param name="agentProxyAccount">The proxy account name to use.</param>
            <param name="agentProxyPassword">The proxy account password to use.</param>
            <exception cref="T:Microsoft.SqlServer.Management.Utility.UtilityException">Throws if the instance cannot be enrolled.</exception>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.MakeManaged">
            <summary>
            Make Managed Function validates the input parameters and returns the ActionSequence
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.EnrollInstance">
            <summary>
            Enrolls an instance in a utility control point.
            </summary>
            <returns>The new <see cref="P:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.ManagedInstance"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.EnrollInstance(System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            Enroll an instance in a utility control point with the specified
            agent proxy account and agent proxy password.
            </summary>
            <param name="agentProxyAccount">The agent proxy account.</param>
            <param name="agentProxyPassword">The agent proxy password.</param>
            <returns>The new <see cref="P:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.ManagedInstance"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.Validate(System.String,System.String,System.String,Microsoft.SqlServer.Common.SqlSecureString)">
            <summary>
            This will return the ActionSequence for validating that the instance is manageable
            </summary>
            <param name="mdwDatabaseName"></param>
            <param name="ucpInstanceName"></param>
            <param name="agentProxyAccount"></param>
            <param name="password"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.Validate(System.String,System.String)">
            <summary>
            This will return the ActionSequence for validating that the instance is manageable
            </summary>
            <param name="mdwDatabaseName"></param>
            <param name="ucpInstanceName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.OnNotifyPropertyChanged(System.String)">
            <summary>
            Handler for property change notifications
            </summary>
            <param name="propertyName"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.Validate">
            <summary>
            This will return the ActionSequence for validating that the instance is manageable
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.RecreateValidationSteps">
            <summary>
            Re-create validation steps.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.RunEnroll">
            <summary>
            Run the validation and enroll steps.
            </summary>
            <returns>The new <see cref="P:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.ManagedInstance"/>.</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.RecreateMakeManagedSteps">
            <summary>
            Re-create make managed steps.
            </summary>
        </member>
        <member name="E:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.PropertyChanged">
            <summary>
            PropertyChangedEventHandler
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.AgentProxyAccount">
            <summary>
            Gets or sets the agent proxy account.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.Password">
            <summary>
            Gets the agent proxy password.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.UtilityInstanceName">
            <summary>
            Get the utility control point instance name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.ManagedInstance">
            <summary>
            Gets the created managed instance.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.ManagementState">
            <summary>
            Gets the <see cref="P:Microsoft.SqlServer.Management.Utility.ManagedInstanceFactory.ManagementState"/> of the target managed instance.
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedInstanceStorageUtilizationHierarchyBuilder">
            <summary>
            This class represents the Storage Utilization Hierarchy for a ManagedInstance
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceStorageUtilizationHierarchyBuilder.#ctor(Microsoft.SqlServer.Management.Utility.ManagedInstance)">
            <summary>
            ManagedInstanceStorageUtilizationHierarchyBuilder's constructor taking the managedInstance object
            </summary>
            <param name="managedInstance"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceStorageUtilizationHierarchyBuilder.GetFileGroupNodeHealthState(System.Data.DataRow)">
            <summary>
            Returns the FileGroup's HealthState from the passed Datarow
            </summary>
            <param name="dataRow"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceStorageUtilizationHierarchyBuilder.GetLogFilesNodeHealthState(System.Data.DataRow)">
            <summary>
            Returns the LogFileGroup's HealthState from the passed Datarow
            </summary>
            <param name="dataRow"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedInstanceStorageUtilizationHierarchyBuilder.GetDatabaseNodeHealthState(System.Data.DataRow)">
            <summary>
            Returns the Database's HealthState from the passed Datarow
            </summary>
            <param name="dataRow"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedLogFile">
            <summary>
            This class corresponds to the LogFile in Storage Utilization hierarchy
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedLogFile.GetStorageUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            This function returns the storage utilization history for the logfile over the UTC time range and aggregation interval
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ManagedLogFile.LogicalName">
            <summary>
            Logical Name of this datafile (i.e.) the name used in the database 
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedLogFileGroup">
            <summary>
            This class corresponds to the Log File(s) node in Storage Utilization hierarchy
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedLogFileGroup.GetStorageUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            This function returns the storage utilization history for the LogFileGroup of the associated database over the UTC time range and aggregation interval
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ManagedVolume">
            <summary>
            This class corresponds to the Volume node in Storage Utilization hierarchy
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ManagedVolume.GetStorageUtilizationHistory(System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            This function returns the storage utilization history for the volume over the UTC time range and aggregation interval
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.MdwHelper">
            <summary>
            MDW helper.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwHelper.MdwAdmin">
            <summary>
            MdwAdmin
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwHelper.MdwReader">
            <summary>
            MdwReader
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwHelper.MdwWriter">
            <summary>
            MdwWriter
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwHelper.MdwAdminFlag">
            <summary>
            MdwAdminFlag
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwHelper.MdwReaderFlag">
            <summary>
            MdwReaderFlag
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwHelper.MdwWriterFlag">
            <summary>
            MdwWriterFlag
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MdwHelper.#ctor">
            <summary>
            Private constructor.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.MdwHelper.instance">
            <summary>
            private static instance.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MdwHelper.Instance">
            <summary>
            public static instance accessor.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MdwHelper.IsMdwDatabase(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String)">
            <summary>
            Is the database name provider a MDW database?
            </summary>
            <param name="connection">connection.</param>
            <param name="databaseName">database name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MdwHelper.GetDatabase(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String)">
            <summary>
            Get a database object.
            </summary>
            <param name="connection">connection.</param>
            <param name="databaseName">database name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MdwHelper.CreateDatabase(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String)">
            <summary>
            Create a management data warehouse database.
            </summary>
            <param name="connection">connection.</param>
            <param name="databaseName">database name.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MdwHelper.CreateDatabase(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String,System.String,System.String)">
            <summary>
            Create a management data warehouse database.
            </summary>
            <param name="connection">connection.</param>
            <param name="databaseName">database name.</param>
            <param name="databaseFileName">database data file name without the extension.</param>
            <param name="logFileName">database log file name without the extension.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MdwHelper.InitializeMdw(Microsoft.SqlServer.Management.Smo.Database,Microsoft.SqlServer.Management.Common.ServerConnection)">
            <summary>
            Setup MDW i.e. Run installation script on management data warehouse/
            </summary>
            <param name="database">database.</param>
            <param name="warehouseConnection">data-warehouse Connection.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.MdwHelper.CreateMdwViews(Microsoft.SqlServer.Management.Common.ServerConnection,System.String)">
            <summary>
            Create MDW views in msdb,
            </summary>
            <param name="connection">Connection.</param>
            <param name="mdwDatabaseName">mdwDatabaseName.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ObjectFactory`1">
            <summary>
            Singleton Factory class that helps to instantiate the SfcInstance
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ProcessorUtilization">
            <summary>
            Represents a processor utilization sample.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ProcessorUtilization.#ctor(System.Data.DataRow)">
            <summary>
            Initializes a new instance of the ProcessorUtilization class.
            </summary>
            <param name="dataRow">A DataRow containing values to extract.</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ProcessorUtilization.#ctor(System.DateTimeOffset,System.Single)">
            <summary>
            Initializes a new instance of the ProcessorUtilization class.
            </summary>
            <param name="sampleTime">A DateTimeOffset representing the time that the sample was taken (client local time zone).</param>
            <param name="utilizationPercentage">A value indicating the utilization percentage.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ProcessorUtilization.SampleTime">
            <summary>
            Returns a DateTimeOffset representing the time time the sample was taken.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ProcessorUtilization.UtilizationPercentage">
            <summary>
            Gets a value representing the utilization percentage.
            </summary>
            <remarks>The value returned will be between 0 and 1.</remarks>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.RemoveInstanceActionManager">
            <summary>
            Unenroll instance (make instance unmanaged)
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.RemoveInstanceActionManager.#ctor(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,Microsoft.SqlServer.Management.Smo.Server,Microsoft.SqlServer.Management.Utility.ManagedInstance)">
            <summary>
            Constructor
            </summary>
            <param name="managedInstanceConnection"></param>
            <param name="managedInstanceServer"></param>
            <param name="managedInstance"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.RemoveInstanceActionManager.BuildActionSequence">
            <summary>
            Get the actions steps to un-enroll a specific managed instance
            <returns>A list of all action steps with their dependencies</returns>
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy">
            <summary>
            The class implements the resource health policy object functionality 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.#cctor">
            <summary>
            Static constructor. This builds the TSQL scripts - provides the stored procedure name
            and its parameter-values. The values are loaded from the property bag identified by
            the SFC property identifier.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.#ctor">
            <summary>
            SFC needs a default constructor. Don't use this unless you remember to set parent.        
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.#ctor(Microsoft.SqlServer.Management.Utility.Utility)">
            <summary>
            Instantiates a new ResourceHealthPolicy object.
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.#ctor(Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy)">
            <summary>
            Copy constructor for ResourceHealthPolicy object.
            </summary>
            <param name="policy">Source ResourceHealthPolicy object</param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetApplicablePolicy(Microsoft.SqlServer.Management.Utility.Utility,Microsoft.SqlServer.Management.Sdk.Sfc.Urn,Microsoft.SqlServer.Management.Utility.RollupObjectType,Microsoft.SqlServer.Management.Utility.TargetType,Microsoft.SqlServer.Management.Utility.ResourceType,Microsoft.SqlServer.Management.Utility.UtilizationType)">
            <summary>
            Gets the applicable policy based on the input parameters
            </summary>
            <param name="utility">Utility object</param>
            <param name="rollupObjectUrn">rollup object urn </param>
            <param name="rollupObjectType">rollup object type (Dac / managed instance)</param>
            <param name="targetType">Facet type (e.g. Computer, Volume etc)</param>
            <param name="resourceType">Resource Type (e.g. Processor, Memory etc)</param>
            <param name="utilizationType">Utilization Type (e.g Over, Under etc)</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetGlobalPolicy(Microsoft.SqlServer.Management.Utility.Utility,Microsoft.SqlServer.Management.Utility.RollupObjectType,Microsoft.SqlServer.Management.Utility.TargetType,Microsoft.SqlServer.Management.Utility.ResourceType,Microsoft.SqlServer.Management.Utility.UtilizationType)">
            <summary>
            Gets the global policy based on the input parameters
            </summary>
            <param name="utility">Utility object</param>
            <param name="rollupObjectType">rollup object type (Dac / managed instance)</param>
            <param name="targetType">Facet type (e.g. Computer, Volume etc)</param>
            <param name="resourceType">Resource Type (e.g. Processor, Memory etc)</param>
            <param name="utilizationType">Utilization Type (e.g Over, Under etc)</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetPolicyViolations(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression)">
            <summary>
            Gets the rollup policy violations for the target query expression filter
            </summary>
            <param name="targetQueryExpression">Object urn or powershell path</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetPolicyViolationCount(Microsoft.SqlServer.Management.Sdk.Sfc.SfcQueryExpression)">
            <summary>
            Gets the rollup policy violation count for the target query expression filter
            </summary>
            <param name="targetQueryExpression">Object urn or powershell path</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.OverrideResourceHealthPolicy(Microsoft.SqlServer.Management.Utility.RollupObjectType,Microsoft.SqlServer.Management.Sdk.Sfc.Urn)">
            <summary>
            This method abstracts the policy overriding functionality for setting threshold values at a rollup object level
            Internally clones the current resource health policy for the specified rollup object
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.ScriptCreate">
            <summary>
            Produces ISfcScript ready to execute 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.PostCreate(System.Object)">
            <summary>
            Perform post-create action
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Create">
            <summary>
            creates the script for running create proc
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.ScriptDrop">
            <summary>
            Produces ISfcScript ready to execute 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Drop">
            <summary>
            creates the script for running delete proc
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.CreateIdentityKey">
            <summary>
            SfcInstance implementation - creates a new key.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetChildCollection(System.String)">
            <summary>
            SfcInstance implementation - Gets the SfcCollection for
            the children of ResourceHealthPolicy.
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetObjectFactory">
            <summary>
            Gets the Factory to create a new instance of ResourceHealthPolicy.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.EscapeLikePattern(System.String)">
            <summary>
            Escapes the pattern that is supplied into a t-sql query doing 
            pattern matching with the LIKE keyword.
            </summary>
            <param name="pattern"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetPolicyCheckConditions">
            <summary>
            Gets the check condition metatdata for the policy based on the target and resource type 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetPolicyTargetConditions(System.String)">
            <summary>
            Gets the target condition collection(data table) for the policy based on the rollup object, target and resource type 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetConditionAttributes(System.Data.DataRow)">
            <summary>
            Gets the condition attributes for the input data reader
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetPropertyValue(System.String)">
            <summary>
            Gets the property value based on the property name
            </summary>
            <param name="propertyName"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetRollupObjectFromUrn">
            <summary>
            Gets the rollup object from the urn
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetTargetCondtionName(System.String)">
            <summary>
            Gets the target set condition name based on the policy name and target type 
            Note: Health policies do not show up in SSMS, so no issue with with english suffix
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.CreatePolicy">
            <summary>
            Creates the PBM policy in the Utility policy store 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.CreateOrAlterCheckCondition(Microsoft.SqlServer.Management.Dmf.Condition)">
            <summary>
            Creates condtion filters based on the meta-data defined
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.CreateOrAlterTargetSetCondtions(Microsoft.SqlServer.Management.Dmf.PolicyStore,Microsoft.SqlServer.Management.Dmf.ObjectSet)">
            <summary>
            Enumerates the target set levels and creates condtions if defined in meta-data 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.ApplyConditionFilters(Microsoft.SqlServer.Management.Dmf.Condition,System.Data.DataTable)">
            <summary>
            Build the condtion by applying the specified filter
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.AppendConditionExpression(Microsoft.SqlServer.Management.Dmf.Condition,Microsoft.SqlServer.Management.Dmf.ExpressionNodeOperator)">
            <summary>
            Appends the operator to the condtion expression 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.DropPolicy">
            <summary>
            Drops the PBM policy from the Utility policy store 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.DropTargetSetConditions(System.Collections.Generic.IList{System.String})">
            <summary>
            Drops the associated targetSet level conditions
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.GetTargetSetConditions(Microsoft.SqlServer.Management.Dmf.ObjectSet)">
            <summary>
            Enumerates targetset levels and gets the associated conditions
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.AlterPolicy">
            <summary>
            Alters the PBM policy in the Utility policy store 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.ID">
            <summary>
            Property for identification
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Name">
            <summary>
            Gets / Sets the policy name 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.RollupObjectUrn">
            <summary>
            Gets / Sets the rollup object urn. 
            This is the URN of the top level object for which the health states are computed 
            (e.g. DeployedDac or ManagedInstance)
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.RollupObjectType">
            <summary>
            Gets / sets the rollup object type
            The types supported are the top level object for which the health states are computed 
            (e.g. DeployedDac or ManagedInstance)
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.TargetType">
            <summary>
            Gets / Sets the target type
            The target type indicates the object on which the resources are monitored.
            (e.g. DataFile, LogFile, Computer, Volume, Instance etc)
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.ResourceType">
            <summary>
            Gets / sets the resource type
            The resource type indicates the health aspect on which the policy are evaluated to determine their states
            (e.g. Processor, Storage space, Storage I/O, Memory, Network I/O etc)
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.UtilizationType">
            <summary>
            Gets / sets the utilization type
            The utilization type indicates the health of the resource on a given target
            (e.g. over, under or steady utilzation)
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.UtilizationThreshold">
            <summary>
            Gets / sets the utilization threshold (value between 0 to 100%)
            The utilization threshold is a value that represents the high or low condition 
            to the policy against which a resource is monitored for a given target.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.IsGlobalPolicy">
            <summary>
            Indicates whether the object is of system type. 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.IdentityKey">
            <summary>
            returns the identity key
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Parent">
            <summary>
            ResourceHealthPolicy's parent is set as Utility
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.HealthPolicyViolations">
            <summary>
            Property to get the violations for the resource health policy.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.DeployedDac">
            <summary>
            Gets the deployed dac object based on the rollup object urn
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Server">
            <summary>
            Gets the smo object based on the rollup object urn
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Computer">
            <summary>
            Gets the computer object based on the rollup object urn
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.ServerNetName">
            <summary>
            Gets the smo server computer name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.ComputerName">
            <summary>
            Gets the computer name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.ServerInstanceName">
            <summary>
            Gets the smo server instance name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.DacName">
            <summary>
            Gets the Deployed Dac name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.DacServerInstanceName">
            <summary>
            Gets the Deployed Dac server instance name 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.DacInstanceName">
            <summary>
            Gets the Deployed Dac instance name (truncating the machine name)
            Note: If the machine name exists, it is removed as the value is used as filter condition for Server.InstanceName
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.DacDatabaseName">
            <summary>
            Gets the Deployed Dac database name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.DacComputerName">
            <summary>
            Gets the Deployed Dac computer name
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.ConditionName">
            <summary>
            Gets the check condition name based on the policy name
            Note: Health policies do not show up in SSMS, so no issue with english suffix
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.ObjectsetName">
            <summary>
            Gets the objectset name based on the policy name
            Note: Health policies do not show up in SSMS, so no issue with with enlish suffix
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key">
            Internal key class
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.keyId">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.#ctor(Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.#ctor(System.Int32)">
            <summary>
            
            </summary>
            <param name="id"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.op_Equality(Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.op_Equality(Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key,Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.op_Inequality(Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.op_Inequality(Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key,Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicy.Key.Id">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ConditionAttributes">
            <summary>
            Condition properties helper struct used as a container to hold contion attributes 
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicyCollection">
            <summary>
            The class implements the resource health policy collection functionality 
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicyCollection.#ctor(Microsoft.SqlServer.Management.Utility.Utility)">
            <summary>
            This constructor sets the parent.
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicyCollection.#ctor(Microsoft.SqlServer.Management.Utility.Utility,System.Collections.Generic.IComparer{System.String})">
            <summary>
            This constructor sets the parent and passes on a comparer too.
            </summary>
            <param name="parent"></param>
            <param name="customComparer"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicyCollection.Contains(System.Int32)">
            <summary>
            This checks if the collection contains a child with the given name.
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicyCollection.GetElementFactoryImpl">
            <summary>
            Returns the Factory that helps instantiate the children objects.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.ResourceHealthPolicyCollection.Item(System.Int32)">
            <summary>
            Indexer to get the child object.
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.StepResult">
            <summary>
            A value representing the result of an ActionStep result
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StepResult.None">
            <summary>
            None state
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StepResult.Pending">
            <summary>
            Pending state
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StepResult.Success">
            <summary>
            Success state
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StepResult.Failure">
            <summary>
            Failure state
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StepResult.Warning">
            <summary>
            Warning state
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.StepResult.NotRun">
            <summary>
            The step was not run
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.StorageUtilization">
            <summary>
            Storage utilization implementation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.StorageUtilization.#ctor(System.DateTimeOffset,System.Single,System.Int64,System.Int64)">
            <summary>
            Initializes a new instance of the StorageUtilization class.
            </summary>
            <param name="sampleTime">The time the sample was taken.</param>
            <param name="utilizationPercentage">A value representing the utilization percentage.</param>
            <param name="capacityInBytes">A value representing the capacity in bytes.</param>
            <param name="utilizationInBytes">A value representing the utilization in bytes.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.StorageUtilization.SampleTime">
            <summary>
            Data point sample time as a DateTimeOffset value in the UCP server's time zone
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.StorageUtilization.UtilizationPercentage">
            <summary>
            storage utilization in percent
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.StorageUtilization.CapacityInBytes">
            <summary>
            storage capacity in bytes
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.StorageUtilization.UtilizationInBytes">
            <summary>
            storage utilization in bytes
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UserSecurity">
            <summary>
            User security utility class.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNtAuthoritySystem">
            <summary>
            Sid for "NT Authority\Local System"
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UserSecurity.LookupAccountName(System.String,System.String,System.IntPtr,System.Int32@,System.Text.StringBuilder,System.Int32@,System.Int32@)">
            <summary>
            Win32 API which looks up the sid for an account name
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UserSecurity.LookupAccountSid(System.String,System.IntPtr,System.Text.StringBuilder,System.UInt32@,System.Text.StringBuilder,System.UInt32@,Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse@)">
            <summary>
            Win32 API which looks up the acouunt for a sid
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UserSecurity.ConvertSidToStringSid(System.IntPtr,System.Text.StringBuilder@)">
            <summary>
            Win32 API to convert sid to string sid
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UserSecurity.ConvertStringSidToSid(System.String,System.IntPtr@)">
            <summary>
            Win32 API to convert string sid to sid
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UserSecurity.IsWellKnownSid(System.IntPtr,System.Int32)">
            <summary>
            Win32 API which compares the sid with a sid type
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UserSecurity.GetAccountFromSid(System.String,System.String@,Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse@)">
            <summary>
            Gets account (as return value), domain (as out parameter) and SidNameUse (as out paramter) for SID.
            </summary>
            <param name="sid">String SID</param>
            <param name="domain">Domain name</param>
            <param name="sidNameUse">SidNameUse</param>
            <returns>Returns user or user group name as string </returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UserSecurity.GetLocalSystemAccountName">
            <summary>
            Gets localized account name for NT AUTHORITY\LOCAL SYSTEM. Uses well-known SID and queries the OS for localized name.
            </summary>
            <returns>Retunrs NT style account name as string</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UserSecurity.IsWellKnownAccount(System.String,System.Collections.Generic.IEnumerable{Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType})">
            <summary>
            This function determines if the account specified by accountName parameter matches
            any sid from the sidTypeList parameter.
            </summary>
            <param name="accountName">account name.</param>
            <param name="sidTypeList">The list of SIDs from which to compare the account to.</param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType">
            <summary>
            Well known Sid Types
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinNullSid">
            <summary>
            Null
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinWorldSid">
            <summary>
            world
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinLocalSid">
            <summary>
            local
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinCreatorOwnerSid">
            <summary>
            creator owner
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinCreatorGroupSid">
            <summary>
            creator group
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinCreatorOwnerServerSid">
            <summary>
            creator owner server
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinCreatorGroupServerSid">
            <summary>
            creator group server
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinNtAuthoritySid">
            <summary>
            NT AUTHORITY
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinDialupSid">
            <summary>
            Dialup
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinNetworkSid">
            <summary>
            NETWORK
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBatchSid">
            <summary>
            Batch
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinInteractiveSid">
            <summary>
            Interactive
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinServiceSid">
            <summary>
            Service
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAnonymousSid">
            <summary>
            Anonymous
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinProxySid">
            <summary>
            Proxy
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinEnterpriseControllersSid">
            <summary>
            Enterprise Controllers
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinSelfSid">
            <summary>
            Self
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAuthenticatedUserSid">
            <summary>
            Authenticated User
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinRestrictedCodeSid">
            <summary>
            Restricted Code
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinTerminalServerSid">
            <summary>
            Terminal Server
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinRemoteLogonIdSid">
            <summary>
            Remote Logon Id
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinLogonIdsSid">
            <summary>
            Logon Ids
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinLocalSystemSid">
            <summary>
            LOCAL SYSTEM
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinLocalServiceSid">
            <summary>
            LOCAL SERVICE
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinNetworkServiceSid">
            <summary>
            NETWORK SERVICE
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinDomainSid">
            <summary>
            BUILTIN Domain
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinAdministratorsSid">
            <summary>
            BUILTIN\Administrators
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinUsersSid">
            <summary>
            BUILTIN Users
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinGuestsSid">
            <summary>
            BUILTIN Guests
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinPowerUsersSid">
            <summary>
            BUILTIN power users
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinAccountOperatorsSid">
            <summary>
            BUILTIN account operators
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinSystemOperatorsSid">
            <summary>
            BUILTIN system operators
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinPrintOperatorsSid">
            <summary>
            BUILTIN print operators
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinBackupOperatorsSid">
            <summary>
            BUILTIN  backup operators
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinReplicatorSid">
            <summary>
            BUILTIN replicator
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinPreWindows2000CompatibleAccessSid">
            <summary>
            BUILTIN Pre Windows 2000 Compatible Access
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinRemoteDesktopUsersSid">
            <summary>
            BUILTIN remote desktop users
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinNetworkConfigurationOperatorsSid">
            <summary>
            BUILTIN network configuration operators
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountAdministratorSid">
            <summary>
            account administrators
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountGuestSid">
            <summary>
            account guests
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountKrbtgtSid">
            <summary>
            account krbtgt
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountDomainAdminsSid">
            <summary>
            account domain admins
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountDomainUsersSid">
            <summary>
            account domain users
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountDomainGuestsSid">
            <summary>
            account domain guests
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountComputersSid">
            <summary>
            account computers
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountControllersSid">
            <summary>
            account controllers
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountCertAdminsSid">
            <summary>
            account cert admins
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountSchemaAdminsSid">
            <summary>
            account schema admins
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountEnterpriseAdminsSid">
            <summary>
            account enterprise admins
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountPolicyAdminsSid">
            <summary>
            account policy admins
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountRasAndIasServersSid">
            <summary>
            account RAS and IAS servers
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinNTLMAuthenticationSid">
            <summary>
            NT LM authentication
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinDigestAuthenticationSid">
            <summary>
            Digest authentication
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinSChannelAuthenticationSid">
            <summary>
            S Channel Authentication
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinThisOrganizationSid">
            <summary>
            This organization
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinOtherOrganizationSid">
            <summary>
            Other organization
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinIncomingForestTrustBuildersSid">
            <summary>
            BUILTIN incoming forest trust builders
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinPerfMonitoringUsersSid">
            <summary>
            BUILTIN perf monitoring users
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinPerfLoggingUsersSid">
            <summary>
            BUILTIN perf logging users
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinAuthorizationAccessSid">
            <summary>
            BUILTIN authorization access
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinTerminalServerLicenseServersSid">
            <summary>
            BUILTIN terminal server license servers
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinDCOMUsersSid">
            <summary>
            BUILTIN DCOM users
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinIUsersSid">
            <summary>
            BUILTIN IUsers
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinIUserSid">
            <summary>
            IUsers
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinCryptoOperatorsSid">
            <summary>
            BUILTIN crypto operators
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinUntrustedLabelSid">
            <summary>
            untrusted label
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinLowLabelSid">
            <summary>
            low label
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinMediumLabelSid">
            <summary>
            medium label
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinHighLabelSid">
            <summary>
            high label
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinSystemLabelSid">
            <summary>
            system label
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinWriteRestrictedCodeSid">
            <summary>
            write restricted code
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinCreatorOwnerRightsSid">
            <summary>
            creator owner rights
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinCacheablePrincipalsGroupSid">
            <summary>
            cacheable principals group
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinNonCacheablePrincipalsGroupSid">
            <summary>
            cacheable principals group
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinEnterpriseReadonlyControllersSid">
            <summary>
            enterprise readonly controllers
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinAccountReadonlyControllersSid">
            <summary>
            readonly controllers
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinEventLogReadersGroup">
            <summary>
            BUILTIN event log readers group
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinNewEnterpriseReadonlyControllersSid">
            <summary>
            new enterprise readonly controllers
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.WellKnownSidType.WinBuiltinCertSvcDComAccessGroup">
            <summary>
            BUILTIN Cert Service DCOM access group
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse">
            <summary>
            Flags returned by LookupAccountName and LookupAccountSid. Refer to Win32 LsaXxx API documentation for details on how to interpret this value.
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse.USER">
            <summary>
            Refer to Win32 LsaXxx documentation
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse.GROUP">
            <summary>
            Refer to Win32 LsaXxx documentation
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse.DOMAIN">
            <summary>
            Refer to Win32 LsaXxx documentation
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse.ALIAS">
            <summary>
            Refer to Win32 LsaXxx documentation
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse.WELL_KNOWN_GROUP">
            <summary>
            Refer to Win32 LsaXxx documentation
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse.DELETED_ACCOUNT">
            <summary>
            Refer to Win32 LsaXxx documentation
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse.INVALID">
            <summary>
            Refer to Win32 LsaXxx documentation
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse.UNKNOWN">
            <summary>
            Refer to Win32 LsaXxx documentation
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse.COMPUTER">
            <summary>
            Refer to Win32 LsaXxx documentation
            </summary>
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.UserSecurity.SidNameUse.LABEL">
            <summary>
            Refer to Win32 LsaXxx documentation
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper">
            <summary>
            Helper class for storage utilization queries
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.GetStorageUtilizationHistory(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,Microsoft.SqlServer.Management.Utility.StorageUtilizationLevel,System.String,System.String,System.String,System.String,System.String,System.String,Microsoft.SqlServer.Management.Utility.UtilizationCalculationLevel,System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            Returns Storage Utilization History series
            </summary>
            <param name="sqlStoreConnection"></param>
            <param name="storageType"></param>
            <param name="virtualServerName"></param>
            <param name="volumeDeviceId"></param>
            <param name="serverInstanceName"></param>
            <param name="databaseName"></param>
            <param name="filegroupName"></param>
            <param name="databaseFileName"></param>
            <param name="utilizationType"></param>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.GetProcessorUtilizationHistory(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,Microsoft.SqlServer.Management.Utility.ProcessorUtilizationLevel,System.String,System.String,System.String,Microsoft.SqlServer.Management.Utility.UtilizationCalculationLevel,System.DateTimeOffset,System.DateTimeOffset,Microsoft.SqlServer.Management.Utility.AggregationInterval)">
            <summary>
            Returns Processor Utilization History series
            </summary>
            <param name="sqlStoreConnection"></param>
            <param name="processorType"></param>
            <param name="physicalServerName"></param>
            <param name="serverInstanceName"></param>
            <param name="dacName"></param>
            <param name="utilizationType"></param>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="aggregationInterval"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.GetManagedInstanceDatabaseFileUtilizations(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String)">
            <summary>
            Returns the DataRows containing the storage utilization related data for database files
            in the ManagedInstance
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.GetManagedInstanceFileGroupHealthState(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String,System.String,System.String)">
            <summary>
            Gets the FileGroup/LogFileGroup health state for the given database
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.GetManagedInstanceVolumeUtilizations(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String)">
            <summary>
            Returns the DataRows containing the storage utilization related data for database files in the ManagedInstance
            and also the storage utilization related data for the volumes of the server on which the ManagedInstance resides
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.GetDeployedDacDatabaseFileUtilizations(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String,System.String)">
            <summary>
            Returns the DataRows containing the storage utilization related data for database files
            in the DeployedDac
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.GetDeployedDacVolumeUtilizations(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String,System.String)">
            <summary>
            Returns the DataRows containing the storage utilization related data for database files in the DeployedDac 
            and also the storage utilization related data for the volumes of the server on which the DeployedDac resides
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.QueryValueForUtilityConfigurationParameter(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.String)">
            <summary>
            Get the value of a Utility configuration setting.
            Caller can use one of the above defined string constants as the parameterName value.
            </summary>
            <param name="sqlStoreConnection">Connection to the instance we will query against</param>
            <param name="parameterName">Which Utility configuration setting we want to query.</param>
            <returns>The value of the setting</returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.GetInstanceUploadSchemaVersionComparison(Microsoft.SqlServer.Management.Sdk.Sfc.SqlStoreConnection,System.Int32)">
            <summary>
            
            </summary>
            <param name="sqlStoreConnection"></param>
            <param name="instanceUploadSchemaVersion"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.GetInstanceUploadSchemaVersion(System.Data.SqlClient.SqlConnection)">
            <summary>
            
            </summary>
            <param name="connection"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.ExecuteAndReturnAllRows(System.Data.SqlClient.SqlCommand)">
            <summary>
            Execute query
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.UtilityQueryHelper.AddParameter(System.Data.SqlClient.SqlCommand,System.String,System.Object)">
            <summary>
            Add a sql parameter to the sql command
            </summary>
            <param name="command"></param>
            <param name="parameterName"></param>
            <param name="parameterValue"></param>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.Volume">
            <summary>
            This is the non-generated part of the Volume class.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.#ctor">
            <summary>
            SFC needs a default constructor. Don't use this unless you remember to set parent.        
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.#ctor(Microsoft.SqlServer.Management.Utility.Computer,System.String)">
            <summary>
            Instantiates a new Volume object.
            </summary>
            <param name="parent"></param>
            <param name="name"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.SetName(System.String)">
            <summary>
            Setter for Name property - This is private to avoid
            the SfcKey to be set externally.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.CreateIdentityKey">
            <summary>
            SfcInstance implementation - creates a new key.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.GetChildCollection(System.String)">
            <summary>
            SfcInstance implementation - Gets the SfcCollection for
            the children of Volume.
            </summary>
            <param name="elementType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.GetObjectFactory">
            <summary>
            Gets the Factory to create a new instance of Volume.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Volume.Name">
            <summary>
            Key property of the type.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Volume.ID">
            <summary>
            Property for Guid identification
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Volume.DeviceId">
            <summary>
            Property to indicate the the device identifier for the volume
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Volume.TotalSpace">
            <summary>
            Property to indicate the Total Storage capacity (in MB)
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Volume.TotalSpaceUsed">
            <summary>
            Property to indicate the Total Storage Used (in MB)
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Volume.TotalSpaceUtilization">
            <summary>
            Property to indicate the Total Storage Utilization in percent
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Volume.IdentityKey">
            <summary>
            returns the identity key
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Volume.Parent">
            <summary>
            Volume's parent is set as Utility
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.Volume.Key">
            Internal key class
        </member>
        <member name="F:Microsoft.SqlServer.Management.Utility.Volume.Key.keyName">
            <summary>
            Properties
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.#ctor">
            <summary>
            Default constructor for generic Key generation
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.#ctor(Microsoft.SqlServer.Management.Utility.Volume.Key)">
            <summary>
            Constructors
            </summary>
            <param name="other"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.#ctor(System.String)">
            <summary>
            
            </summary>
            <param name="name"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.Equals(System.Object)">
            <summary>
            Equality and Hashing
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.Equals(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="obj1"></param>
            <param name="obj2"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.Equals(Microsoft.SqlServer.Management.Sdk.Sfc.SfcKey)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.op_Equality(System.Object,Microsoft.SqlServer.Management.Utility.Volume.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.op_Equality(Microsoft.SqlServer.Management.Utility.Volume.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.op_Equality(Microsoft.SqlServer.Management.Utility.Volume.Key,Microsoft.SqlServer.Management.Utility.Volume.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.op_Inequality(System.Object,Microsoft.SqlServer.Management.Utility.Volume.Key)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.op_Inequality(Microsoft.SqlServer.Management.Utility.Volume.Key,System.Object)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.op_Inequality(Microsoft.SqlServer.Management.Utility.Volume.Key,Microsoft.SqlServer.Management.Utility.Volume.Key)">
            <summary>
            
            </summary>
            <param name="leftOperand"></param>
            <param name="rightOperand"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.GetHashCode">
            <summary>
            Equality and Hashing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.Volume.Key.GetUrnFragment">
            <summary>
            Conversions
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.Volume.Key.Name">
            <summary>
            
            </summary>
        </member>
        <member name="T:Microsoft.SqlServer.Management.Utility.VolumeCollection">
            <summary>
            This is the collection for Computers.
            </summary>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.VolumeCollection.#ctor(Microsoft.SqlServer.Management.Utility.Computer)">
            <summary>
            This constructor sets the parent.
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.VolumeCollection.#ctor(Microsoft.SqlServer.Management.Utility.Computer,System.Collections.Generic.IComparer{System.String})">
            <summary>
            This constructor sets the parent and passes on a comparer too.
            </summary>
            <param name="parent"></param>
            <param name="customComparer"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.VolumeCollection.Contains(System.String)">
            <summary>
            This checks if the collection contains a child with the given name.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.VolumeCollection.GetElementFactoryImpl">
            <summary>
            Returns the Factory that helps instantiate the children objects.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.VolumeCollection.Item(System.String)">
            <summary>
            Indexer to get the child object.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.VolumeStorageUtilizationHierarchyBuilder.#ctor(Microsoft.SqlServer.Management.Utility.IManagedInstanceContext)">
            <summary>
            Constructor setting the Managed Instance's context for VolumeStorageUtilizationHierarchyBuilder
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.VolumeStorageUtilizationHierarchyBuilder.BuildVolumeStorageUtilizationHierarchy(System.Collections.Generic.IEnumerable{System.Data.DataRow})">
            <summary>
            Builds the volume storage utilization hierarchy
            </summary>
            <param name="volumeUtilizations"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.SqlServer.Management.Utility.VolumeStorageUtilizationHierarchyBuilder.ConvertToDouble(System.Data.DataRow,System.String)">
            <summary>
            Helper method for converting the passed column's value to double
            </summary>
            <param name="row"></param>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.VolumeStorageUtilizationHierarchyBuilder.Volumes">
            <summary>
            Collection of Volumes in the Storage Utilization hierarchy
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Management.Utility.VolumeStorageUtilizationHierarchyBuilder.Context">
            <summary>
            Managed Instance's Context corresponding to the Utility object whose storage utilization hierarchy needs to be built
            </summary>
        </member>
    </members>
</doc>
