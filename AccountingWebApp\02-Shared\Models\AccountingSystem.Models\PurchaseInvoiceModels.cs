using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.Generic; // Added for List

namespace AccountingSystem.Models
{
    /// <summary>
    /// Purchase Invoice - inherits from BaseInvoice for purchase transactions
    /// </summary>
    public class PurchaseInvoice : BaseInvoice
    {
        public PurchaseInvoice()
        {
            TrxType = "مشتريات";
        }

        [StringLength(100)]
        public string? ReferenceInvoice { get; set; } // Reference number

        [StringLength(50)]
        public string? PaymentStatus { get; set; } // "1" = "Paid", "0" = "Open", null = "Not Set"
    }

    /// <summary>
    /// Purchase Invoice Item - inherits from BaseInvoiceItem for purchase transactions
    /// </summary>
    public class PurchaseInvoiceItem : BaseInvoiceItem
    {
        public PurchaseInvoiceItem()
        {
            TrxType = "مشتريات";
        }
    }



    /// <summary>
    /// Purchase Invoice View Model for UI
    /// </summary>
    public class PurchaseInvoiceViewModel
    {
        public int TrxNo { get; set; }
        public DateTime? TrxDate { get; set; } = DateTime.Now;
        public string? Store { get; set; }
        public string? Cashier { get; set; }
        public long? PartnerNo { get; set; }
        public string? PartnerName { get; set; }
        public string? PaymentMethod { get; set; }
        public string? PartnerReference { get; set; }
        public string? ReferenceInvoice { get; set; }
        public string? TrxNote { get; set; }
        public decimal? TrxVAT { get; set; }
        public decimal? TrxTotal { get; set; }
        public decimal? TrxDiscount { get; set; }
        public decimal? TrxDiscountValue { get; set; }
        public decimal? TrxNetAmount { get; set; }
        public string? PaymentStatus { get; set; }
        public bool VATIncluded { get; set; }
        public List<PurchaseInvoiceItemViewModel> Items { get; set; } = new List<PurchaseInvoiceItemViewModel>();
        public List<ChartOfAccount> Vendors { get; set; } = new List<ChartOfAccount>();
        public List<ChartOfAccount> Cashiers { get; set; } = new List<ChartOfAccount>();
        public List<Store> Stores { get; set; } = new List<Store>();
        public InvoiceToolSetting Settings { get; set; } = new InvoiceToolSetting();
        public PurchaseUserAuthorization UserAuthorization { get; set; } = new PurchaseUserAuthorization();
    }

    /// <summary>
    /// Purchase Invoice Item View Model for UI
    /// </summary>
    public class PurchaseInvoiceItemViewModel
    {
        public int LineSN { get; set; }
        public long ItemNo { get; set; }
        public string? ItemDescription { get; set; }
        public decimal TrxQTY { get; set; }
        public string? UofM { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal LineTotal { get; set; }
        public decimal VATAmount { get; set; }
        public decimal UofMConversion { get; set; } = 1;
        public List<ItemUnit> AvailableUnits { get; set; } = new List<ItemUnit>();
    }

    /// <summary>
    /// Item Unit for purchase invoice
    /// </summary>
    public class ItemUnit
    {
        public string UnitName { get; set; } = string.Empty;
        public decimal ConversionFactor { get; set; } = 1;
    }

    /// <summary>
    /// Purchase Invoice Create/Edit View Model
    /// </summary>
    public class PurchaseInvoiceCreateViewModel
    {
        [Required(ErrorMessage = "يجب إدخال المورد")]
        public long? PartnerNo { get; set; }

        [Required(ErrorMessage = "يجب إدخال المخزن")]
        public string? Store { get; set; }

        [Required(ErrorMessage = "يجب إدخال طريقة الدفع")]
        public string? PaymentMethod { get; set; }

        public string? PartnerReference { get; set; }
        public string? ReferenceInvoice { get; set; }
        public string? TrxNote { get; set; }
        public decimal TrxDiscount { get; set; } = 0;
        public bool VATIncluded { get; set; } = false;
        public List<PurchaseInvoiceItemCreateViewModel> Items { get; set; } = new List<PurchaseInvoiceItemCreateViewModel>();
    }

    /// <summary>
    /// Purchase Invoice Item Create/Edit View Model
    /// </summary>
    public class PurchaseInvoiceItemCreateViewModel
    {
        [Required(ErrorMessage = "يجب إدخال الصنف")]
        public long ItemNo { get; set; }

        [Required(ErrorMessage = "يجب إدخال الكمية")]
        [Range(0.01, double.MaxValue, ErrorMessage = "يجب أن تكون الكمية أكبر من صفر")]
        public decimal TrxQTY { get; set; }

        [Required(ErrorMessage = "يجب إدخال الوحدة")]
        public string? UofM { get; set; }

        [Required(ErrorMessage = "يجب إدخال سعر الوحدة")]
        [Range(0.01, double.MaxValue, ErrorMessage = "يجب أن يكون سعر الوحدة أكبر من صفر")]
        public decimal UnitPrice { get; set; }

        public decimal UofMConversion { get; set; } = 1;
    }

    /// <summary>
    /// User authorization information for Purchase operations
    /// </summary>
    public class PurchaseUserAuthorization
    {
        public string Username { get; set; } = string.Empty;
        public int? GroupID { get; set; }
        public string? DefaultStore { get; set; }
        public bool StoreChange { get; set; }
        public long? DefaultCustomer { get; set; }
        public bool CustomerChange { get; set; }
        public int? DefaultCashier { get; set; }
        public bool CashierChange { get; set; }
        public bool ChangeInvoicePrice { get; set; }
        public decimal? MaxDiscountPercent { get; set; }
        public bool IsAdmin { get; set; }
    }
} 