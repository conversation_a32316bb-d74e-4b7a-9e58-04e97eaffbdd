using AccountingSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public interface IItemsCategoryService
    {
        Task<List<ItemsCategory>> GetCategoriesAsTreeAsync();
        Task<ItemsCategory> GetCategoryByIdAsync(int id);
        Task<bool> CreateCategoryAsync(ItemsCategory category, string currentUser);
        Task<bool> UpdateCategoryAsync(ItemsCategory category, string currentUser);
        Task<(bool success, string errorMessage)> DeleteCategoryAsync(int id);
        Task<int> GetNextIdAsync();
    }
} 