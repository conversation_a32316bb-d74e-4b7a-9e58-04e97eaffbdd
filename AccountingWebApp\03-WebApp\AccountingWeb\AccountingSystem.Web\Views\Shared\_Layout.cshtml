﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام السلطان المحاسبي</title>

    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic Support -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }

        body {
            background-color: #f8f9fa;
            direction: rtl;
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.3rem;
        }

        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        .sidebar {
            min-height: calc(100vh - 56px);
            max-height: calc(100vh - 56px);
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            color: white;
            position: fixed;
            top: 56px;
            right: 0;
            width: 250px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .sidebar.show {
            transform: translateX(0);
        }

        .sidebar-nav {
            padding: 1rem 0 2rem 0;
            height: 100%;
        }

        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .sidebar-nav .nav-link:hover {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }

        .sidebar-nav .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
        }

        .sidebar-nav .nav-link i {
            margin-left: 0.5rem;
            width: 20px;
        }

        .sidebar-section-toggle {
            background: none;
            border: none;
            width: 100%;
            text-align: right;
            padding: 0.75rem 1.5rem;
            color: rgba(255,255,255,0.8);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .sidebar-section-toggle:hover {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }

        .sidebar-section-toggle:focus {
            outline: none;
            box-shadow: none;
        }

        .sidebar-section-toggle .toggle-icon {
            transition: transform 0.3s ease;
        }

        .sidebar-section-toggle[aria-expanded="true"] .toggle-icon {
            transform: rotate(180deg);
        }

        .sidebar-section-toggle .fas {
            margin-left: 0.5rem;
            width: 20px;
        }

        .main-content {
            margin-right: 0;
            transition: margin-right 0.3s ease;
            padding: 1.5rem;
        }

        .main-content.sidebar-open {
            margin-right: 250px;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .user-dropdown .dropdown-toggle::after {
            margin-right: 0.5rem;
        }

        @@media (max-width: 768px) {
            .main-content.sidebar-open {
                margin-right: 0;
            }
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
        }

        .alert {
            border-radius: 8px;
            border: none;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <img id="storeLogo" src="/SystemSetup/GetLogo" alt="Logo" style="height: 40px; width: auto; margin-left: 10px; display: none;" />
                <a class="navbar-brand" href="/">
                    <i class="fas fa-calculator"></i>
                    <span id="appName">نظام السلطان المحاسبي</span>
                    <span id="storeName" class="ms-2 text-light" style="font-size: 0.9rem; opacity: 0.9;"></span>
                </a>
            </div>

            <div class="d-flex align-items-center">
                @if (User.Identity?.IsAuthenticated == true)
                {
                    <div class="dropdown user-dropdown me-3">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i>
                            @User.Identity.Name
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/Account/Profile">
                                <i class="fas fa-user-circle"></i> الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="/ChangeMyPassword/Index">
                                <i class="fas fa-key"></i> تغيير كلمة المرور
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form asp-controller="SimpleAccount" asp-action="Logout" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                    
                    <button class="btn btn-outline-light" type="button" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                        القائمة
                    </button>
                }
            </div>
        </div>
    </nav>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    @if (User.Identity?.IsAuthenticated == true)
    {
        <div class="sidebar" id="sidebar">
            <nav class="sidebar-nav">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="/SimpleDashboard/Index">
                            <i class="fas fa-home"></i>
                            الصفحة الرئيسية
                        </a>
                    </li>
                    
                    <!-- Sales Section -->
                    <li class="nav-item">
                        <hr class="text-white-50">
                        <button class="nav-link sidebar-section-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#salesSection">
                            <i class="fas fa-shopping-cart"></i>
                            المبيعات
                            <i class="fas fa-chevron-down float-end toggle-icon"></i>
                        </button>
                        <div class="collapse" id="salesSection">
                            <ul class="nav flex-column ms-3">
                                <li class="nav-item">
                                    <a class="nav-link" href="/Sales/Invoice">
                                        <i class="fas fa-file-invoice"></i>
                                        فواتير المبيعات
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/POS/Index">
                                        <i class="fas fa-cash-register"></i>
                                        نقاط البيع
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/POSSessions/Index">
                                        <i class="fas fa-clock"></i>
                                        جلسات نقاط البيع
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/Sales/Return">
                                        <i class="fas fa-undo"></i>
                                        مرتجع المبيعات
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/Sales/Search">
                                        <i class="fas fa-search"></i>
                                        بحث المبيعات
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    
                    <!-- Purchase Section -->
                    <li class="nav-item">
                        <button class="nav-link sidebar-section-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#purchaseSection">
                            <i class="fas fa-shopping-cart"></i>
                            المشتريات
                            <i class="fas fa-chevron-down float-end toggle-icon"></i>
                        </button>
                        <div class="collapse" id="purchaseSection">
                            <ul class="nav flex-column ms-3">
                                <li class="nav-item">
                                    <a class="nav-link" href="/Purchase/Create">
                                        <i class="fas fa-plus"></i>
                                        إنشاء فاتورة مشتريات
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/Purchase/Index">
                                        <i class="fas fa-list"></i>
                                        عرض فواتير المشتريات
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/Purchase/Return">
                                        <i class="fas fa-undo"></i>
                                        مرتجع المشتريات
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    
                    <!-- Cash Movements Section -->
                    <li class="nav-item">
                        <button class="nav-link sidebar-section-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#cashSection">
                            <i class="fas fa-money-bill-wave"></i>
                            الحركات النقدية
                            <i class="fas fa-chevron-down float-end toggle-icon"></i>
                        </button>
                        <div class="collapse" id="cashSection">
                            <ul class="nav flex-column ms-3">
                                <li class="nav-item">
                                    <a class="nav-link" href="/Cash/Receipt">
                                        <i class="fas fa-hand-holding-usd"></i>
                                        سندات القبض
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/Cash/Payment">
                                        <i class="fas fa-money-bill-wave"></i>
                                        سندات الصرف
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/Journal/Entry">
                                        <i class="fas fa-book"></i>
                                        القيود اليومية
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    
                    <!-- Master Data Section -->
                    <li class="nav-item">
                        <button class="nav-link sidebar-section-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#masterDataSection">
                            <i class="fas fa-database"></i>
                            البيانات الأساسية
                            <i class="fas fa-chevron-down float-end toggle-icon"></i>
                        </button>
                        <div class="collapse" id="masterDataSection">
                            <ul class="nav flex-column ms-3">
                                <li class="nav-item">
                                    <a class="nav-link" href="/Customers/Index">
                                        <i class="fas fa-users"></i>
                                        العملاء
                                    </a>
                                </li>

                                <li class="nav-item">
                                    <a class="nav-link" href="/Vendors/Index">
                                        <i class="fas fa-truck"></i>
                                        الموردين
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/Items/Index">
                                        <i class="fas fa-boxes"></i>
                                        الأصناف
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/ItemsCategories">
                                        <i class="fas fa-sitemap"></i>
                                        تصنيفات الأصناف
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/Employees/Index">
                                        <i class="fas fa-users"></i>
                                        الموظفين
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/ChartOfAccounts/Index">
                                        <i class="fas fa-sitemap"></i>
                                        دليل الحسابات
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/StoresV2/Index">
                                        <i class="fas fa-store"></i>
                                        إدارة المتاجر
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/Warehouses/Index">
                                        <i class="fas fa-warehouse"></i>
                                        إدارة المستودعات
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <!-- Reports Section -->
                    <li class="nav-item">
                        <button class="nav-link sidebar-section-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#reportsSection">
                            <i class="fas fa-chart-line"></i>
                            التقارير
                            <i class="fas fa-chevron-down float-end toggle-icon"></i>
                        </button>
                        <div class="collapse" id="reportsSection">
                            <ul class="nav flex-column ms-3">
                                <li class="nav-item">
                                    <a class="nav-link" href="/Reports/Financial">
                                        <i class="fas fa-chart-line"></i>
                                        التقارير المالية
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/Reports/Inventory">
                                        <i class="fas fa-warehouse"></i>
                                        تقارير المخزون
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    
                    <!-- Settings Section -->
                    <li class="nav-item">
                        <button class="nav-link sidebar-section-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#settingsSection">
                            <i class="fas fa-cogs"></i>
                            الإعدادات
                            <i class="fas fa-chevron-down float-end toggle-icon"></i>
                        </button>
                        <div class="collapse" id="settingsSection">
                            <ul class="nav flex-column ms-3">
                                <li class="nav-item">
                                    <a class="nav-link" href="/UserManagement/Index">
                                        <i class="fas fa-user-cog"></i>
                                        إدارة المستخدمين
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/GroupPermissions/Index">
                                        <i class="fas fa-shield-alt"></i>
                                        صلاحيات المجموعات
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/GLConfig/Index">
                                        <i class="fas fa-link"></i>
                                        ربط الحسابات
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/InvoiceToolSettings">
                                        <i class="fas fa-tools"></i>
                                        إعدادات الفواتير
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/SystemSetup/Index">
                                        <i class="fas fa-cogs"></i>
                                        إعدادات النظام
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/BarcodeSettings/Index">
                                        <i class="fas fa-barcode"></i>
                                        إعدادات الباركود
                                    </a>
                                </li>
                                <li class="nav-item">
                                    @if (User.IsInRole("admin"))
                                    {
                                        <a class="nav-link" href="/Admin/QuickActions">
                                            <i class="fas fa-bolt"></i>
                                            إدارة الإجراءات السريعة
                                        </a>
                                    }
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
    }

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        @RenderBody()
    </main>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            // Load store configuration
            loadStoreConfig();

            // Sidebar toggle functionality
            $('#sidebarToggle').click(function() {
                $('#sidebar').toggleClass('show');
                $('#sidebarOverlay').toggleClass('show');
                $('#mainContent').toggleClass('sidebar-open');
            });

            // Close sidebar when clicking overlay
            $('#sidebarOverlay').click(function() {
                $('#sidebar').removeClass('show');
                $('#sidebarOverlay').removeClass('show');
                $('#mainContent').removeClass('sidebar-open');
            });

            // Close sidebar on mobile when clicking a link
            $('.sidebar-nav .nav-link').click(function() {
                if ($(window).width() <= 768) {
                    $('#sidebar').removeClass('show');
                    $('#sidebarOverlay').removeClass('show');
                    $('#mainContent').removeClass('sidebar-open');
                }
            });

            // Handle window resize
            $(window).resize(function() {
                if ($(window).width() > 768) {
                    $('#mainContent').addClass('sidebar-open');
                } else {
                    $('#mainContent').removeClass('sidebar-open');
                }
            });

            // Set active nav item based on current URL
            var currentPath = window.location.pathname;
            $('.sidebar-nav .nav-link').each(function() {
                if ($(this).attr('href') === currentPath) {
                    $(this).addClass('active');
                    // Expand parent section if this is a sub-menu item
                    var parentCollapse = $(this).closest('.collapse');
                    if (parentCollapse.length > 0) {
                        parentCollapse.addClass('show');
                        var toggleButton = $('[data-bs-target="#' + parentCollapse.attr('id') + '"]');
                        toggleButton.attr('aria-expanded', 'true');
                    }
                }
            });

            // Handle sidebar section toggle
            $('.sidebar-section-toggle').on('click', function() {
                var target = $($(this).data('bs-target'));
                var icon = $(this).find('.toggle-icon');
                
                // Toggle the collapse
                target.collapse('toggle');
                
                // Update button state
                $(this).attr('aria-expanded', function(i, attr) {
                    return attr === 'true' ? 'false' : 'true';
                });
            });

            // Handle collapse events for smooth icon rotation
            $('.collapse').on('show.bs.collapse', function() {
                var toggleButton = $('[data-bs-target="#' + $(this).attr('id') + '"]');
                toggleButton.attr('aria-expanded', 'true');
            }).on('hide.bs.collapse', function() {
                var toggleButton = $('[data-bs-target="#' + $(this).attr('id') + '"]');
                toggleButton.attr('aria-expanded', 'false');
            });
        });

        // Load store configuration from server
        function loadStoreConfig() {
            $.ajax({
                url: '/SystemSetup/GetStoreConfig',
                type: 'GET',
                success: function(data) {
                    if (data.storeName) {
                        $('#storeName').text('- ' + data.storeName);
                    }
                    if (data.hasLogo) {
                        $('#storeLogo').show();
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Error loading store config:', error);
                }
            });
        }
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
