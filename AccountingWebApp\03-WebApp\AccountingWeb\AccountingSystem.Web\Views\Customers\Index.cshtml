@model IEnumerable<AccountingSystem.Models.Customer>

@{
    ViewData["Title"] = "العملاء";
}

<h1>@ViewData["Title"]</h1>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success">
        @TempData["SuccessMessage"]
    </div>
}

<p>
    <a asp-action="Create" class="btn btn-primary">
        <i class="fas fa-plus"></i>
        إنشاء عميل جديد
    </a>
</p>

<form asp-action="Index" method="get">
    <div class="form-actions no-color">
        <p>
            بحث بالاسم أو الرقم: <input type="text" name="SearchTerm" value="@ViewBag.SearchTerm" class="form-control d-inline-block w-auto" />
            <input type="submit" value="بحث" class="btn btn-default" /> |
            <a asp-action="Index">عرض الكل</a>
        </p>
    </div>
</form>

<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead class="table-dark">
            <tr>
                <th>
                    @Html.DisplayNameFor(model => model.CustomerNo)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.CustomerName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.FirstName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.LastName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Mobile)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Email)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Status)
                </th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                <tr>
                    <td>
                        @Html.DisplayFor(modelItem => item.CustomerNo)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.CustomerName)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.FirstName)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.LastName)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Mobile)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Email)
                    </td>
                    <td>
                        @if (item.Status == "نشط")
                        {
                            <span class="badge bg-success">@item.Status</span>
                        }
                        else if (item.Status == "غير نشط")
                        {
                            <span class="badge bg-danger">@item.Status</span>
                        }
                        else
                        {
                            <span class="badge bg-warning">@item.Status</span>
                        }
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <a asp-action="Edit" asp-route-customerNo="@item.CustomerNo" class="btn btn-sm btn-outline-primary" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a asp-action="Details" asp-route-customerNo="@item.CustomerNo" class="btn btn-sm btn-outline-info" title="تفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a asp-action="Delete" asp-route-customerNo="@item.CustomerNo" class="btn btn-sm btn-outline-danger" title="حذف">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>

@if (!Model.Any())
{
    <div class="alert alert-info text-center">
        <i class="fas fa-info-circle"></i>
        لا توجد عملاء لعرضها
    </div>
} 