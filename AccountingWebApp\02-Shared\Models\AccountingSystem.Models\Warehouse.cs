using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    /// <summary>
    /// Warehouse entity for warehouse management
    /// Maps to tblStores table (like frmStoresMaster in VB.NET)
    /// </summary>
    [Table("tblStores")]
    public class Warehouse
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        [Display(Name = "رقم المستودع")]
        public int SN { get; set; }

        [Required]
        [StringLength(100)]
        [Column("Store")]
        [Display(Name = "اسم المستودع")]
        public string WarehouseName { get; set; } = string.Empty;

        // Audit fields
        [StringLength(50)]
        [Display(Name = "أنشئ بواسطة")]
        public string? CreatedBy { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime? CreatedOn { get; set; }

        [StringLength(50)]
        [Display(Name = "عدل بواسطة")]
        public string? ModifiedBy { get; set; }

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedOn { get; set; }
    }
}
