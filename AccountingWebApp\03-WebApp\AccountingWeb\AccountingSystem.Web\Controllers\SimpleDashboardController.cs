using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Web.ViewModels;
using Microsoft.Data.SqlClient;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class SimpleDashboardController : Controller
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SimpleDashboardController> _logger;

        public SimpleDashboardController(IConfiguration configuration, ILogger<SimpleDashboardController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var username = User.Identity?.Name ?? "Unknown";
                var userGroup = User.FindFirst("UserGroup")?.Value ?? "";
                var fullName = User.FindFirst("FullName")?.Value ?? username;
                var storeName = "Alsultan Accounting System"; // Default store name

                // Create dashboard view model
                var viewModel = new DashboardViewModel
                {
                    Username = fullName,
                    UserGroup = userGroup,
                    StoreName = storeName,
                    LoginTime = DateTime.Now,
                    UserPermissions = await GetUserPermissionsAsync(),
                    DashboardStats = await GetDashboardStatisticsAsync(),
                    QuickActions = GetQuickActions()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard for user {Username}", User.Identity?.Name);
                ViewBag.ErrorMessage = "حدث خطأ أثناء تحميل لوحة التحكم";
                return View(new DashboardViewModel());
            }
        }

        private async Task<List<UserPermissionViewModel>> GetUserPermissionsAsync()
        {
            var permissions = new List<UserPermissionViewModel>();

            try
            {
                var username = User.Identity?.Name;
                var groupId = User.FindFirst("GroupID")?.Value;
                
                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(groupId))
                    return permissions;

                var connectionString = _configuration.GetConnectionString("AccountingDatabase");
                
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Simple query to get permissions (using DisplayText instead of FormTitle)
                    var query = @"
                        SELECT f.FormName, f.DisplayText as FormTitle, 'General' as ModuleName
                        FROM tblGroupFormPermissions gfp 
                        INNER JOIN tblForms f ON gfp.FormID = f.FormID 
                        WHERE gfp.GroupID = @GroupID";
                    
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@GroupID", groupId);
                        
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                permissions.Add(new UserPermissionViewModel
                                {
                                    FormName = reader["FormName"]?.ToString() ?? "",
                                    FormTitle = reader["FormTitle"]?.ToString() ?? "",
                                    ModuleName = reader["ModuleName"]?.ToString() ?? "General",
                                    CanView = true,
                                    CanAdd = true,
                                    CanEdit = true,
                                    CanDelete = true,
                                    CanPrint = true
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions for {Username}", User.Identity?.Name);
            }

            return permissions;
        }

        private async Task<DashboardStatisticsViewModel> GetDashboardStatisticsAsync()
        {
            var stats = new DashboardStatisticsViewModel();

            try
            {
                var connectionString = _configuration.GetConnectionString("AccountingDatabase");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Get today's total sales - handle null TrxDate values
                    var today = DateTime.Today;
                    var salesQuery = @"
                        SELECT ISNULL(SUM(TrxNetAmount), 0) 
                        FROM tblStockMovHeader 
                        WHERE TrxType = N'مبيعات' 
                        AND TrxDate IS NOT NULL 
                        AND CAST(TrxDate AS DATE) = @TodayDate 
                        AND ReadyForUse = 'True'";
                    using (var salesCmd = new SqlCommand(salesQuery, connection))
                    {
                        salesCmd.Parameters.AddWithValue("@TodayDate", today);
                        var todaySalesObj = await salesCmd.ExecuteScalarAsync();
                        stats.TodaySales = todaySalesObj != null ? Convert.ToDecimal(todaySalesObj) : 0;
                    }

                    // Get today's total purchases - handle null TrxDate values
                    var purchaseQuery = @"
                        SELECT ISNULL(SUM(TrxNetAmount), 0) 
                        FROM tblStockMovHeader 
                        WHERE TrxType = N'مشتريات' 
                        AND TrxDate IS NOT NULL 
                        AND CAST(TrxDate AS DATE) = @TodayDate 
                        AND ReadyForUse = 'True'";
                    using (var purchaseCmd = new SqlCommand(purchaseQuery, connection))
                    {
                        purchaseCmd.Parameters.AddWithValue("@TodayDate", today);
                        var todayPurchasesObj = await purchaseCmd.ExecuteScalarAsync();
                        stats.TodayPurchases = todayPurchasesObj != null ? Convert.ToDecimal(todayPurchasesObj) : 0;
                    }

                    // Get basic counts from available tables with error handling
                    var query = @"
                        SELECT 
                            (SELECT COUNT(*) FROM tblCustomers WHERE CustomerName IS NOT NULL) as ActiveCustomers,
                            (SELECT COUNT(*) FROM tblCustomers WHERE CustomerName IS NOT NULL) as TotalCustomers,
                            (SELECT COUNT(*) FROM tblGroupsAuth WHERE GroupName IS NOT NULL) as GroupCount";
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                stats.TotalCustomers = Convert.ToInt32(reader["ActiveCustomers"]);
                                stats.TotalVendors = Convert.ToInt32(reader["GroupCount"]);
                            }
                        }
                    }
                }

                // Set some default values for now
                stats.LowStockItems = 0;
                stats.PendingInvoices = 0;
                stats.CashBalance = 0;
                stats.TotalInventoryValue = 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard statistics");
                
                // Set safe default values on error
                stats.TodaySales = 0;
                stats.TodayPurchases = 0;
                stats.TotalCustomers = 0;
                stats.TotalVendors = 0;
                stats.LowStockItems = 0;
                stats.PendingInvoices = 0;
                stats.CashBalance = 0;
                stats.TotalInventoryValue = 0;
            }

            return stats;
        }

        private List<QuickActionViewModel> GetQuickActions()
        {
            return new List<QuickActionViewModel>
            {
                new QuickActionViewModel
                {
                    Title = "نقاط البيع",
                    Description = "نظام نقاط البيع",
                    Icon = "fas fa-cash-register",
                    Color = "success",
                    Action = "/POS/Index"
                },
                new QuickActionViewModel
                {
                    Title = "فاتورة مشتريات",
                    Description = "إنشاء فاتورة مشتريات جديدة",
                    Icon = "fas fa-file-invoice",
                    Color = "primary",
                    Action = "/Purchase/Create"
                },
                new QuickActionViewModel
                {
                    Title = "إدارة المستخدمين",
                    Description = "إضافة وتعديل المستخدمين",
                    Icon = "fas fa-users",
                    Color = "primary",
                    Action = "/UserManagement/Index"
                },
                new QuickActionViewModel
                {
                    Title = "إعدادات النظام",
                    Description = "تكوين إعدادات النظام",
                    Icon = "fas fa-cogs",
                    Color = "secondary",
                    Action = "/SystemSetup/Index"
                },
                new QuickActionViewModel
                {
                    Title = "صلاحيات المجموعات",
                    Description = "إدارة صلاحيات المجموعات",
                    Icon = "fas fa-shield-alt",
                    Color = "warning",
                    Action = "/GroupPermissions/Index"
                },
                new QuickActionViewModel
                {
                    Title = "التقارير",
                    Description = "عرض التقارير المالية",
                    Icon = "fas fa-chart-bar",
                    Color = "info",
                    Action = "/Reports/Index"
                },
                new QuickActionViewModel
                {
                    Title = "النسخ الاحتياطي",
                    Description = "إنشاء نسخة احتياطية",
                    Icon = "fas fa-database",
                    Color = "warning",
                    Action = "/Backup/Index"
                },
                new QuickActionViewModel
                {
                    Title = "إدارة العملاء",
                    Description = "إضافة وتعديل بيانات العملاء",
                    Icon = "fas fa-users",
                    Color = "info",
                    Action = "/Customers/Index"
                },
                new QuickActionViewModel
                {
                    Title = "إدارة الموردين",
                    Description = "إضافة وتعديل بيانات الموردين",
                    Icon = "fas fa-truck",
                    Color = "success",
                    Action = "/Vendors/Index"
                },
                new QuickActionViewModel
                {
                    Title = "إدارة الأصناف",
                    Description = "إضافة وتعديل بيانات الأصناف",
                    Icon = "fas fa-boxes",
                    Color = "warning",
                    Action = "/Items/Index"
                },
                new QuickActionViewModel
                {
                    Title = "دليل الحسابات",
                    Description = "إدارة دليل الحسابات",
                    Icon = "fas fa-list-alt",
                    Color = "secondary",
                    Action = "/ChartOfAccounts/Index"
                },
                new QuickActionViewModel
                {
                    Title = "اختبار قاعدة البيانات",
                    Description = "اختبار الاتصال بقاعدة البيانات",
                    Icon = "fas fa-database",
                    Color = "success",
                    Action = "/Test/Index"
                }
            };
        }
    }
}
