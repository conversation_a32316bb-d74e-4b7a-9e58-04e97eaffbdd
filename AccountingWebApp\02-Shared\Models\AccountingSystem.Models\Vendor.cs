using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    /// <summary>
    /// Vendor entity matching tblVendors table structure from legacy VB.NET application
    /// </summary>
    [Table("tblVendors")]
    public class Vendor
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        [Display(Name = "رقم المورد")]
        public long VendorNo { get; set; }

        [StringLength(255)]
        [Display(Name = "اسم المورد")]
        public string? VendorName { get; set; }

        [StringLength(50)]
        [Display(Name = "الاسم الأول")]
        public string? FirstName { get; set; }

        [StringLength(50)]
        [Display(Name = "الاسم الأخير")]
        public string? LastName { get; set; }

        [Display(Name = "الجوال")]
        public int? Mobile { get; set; }

        [Display(Name = "الهاتف")]
        public int? Phone { get; set; }

        [StringLength(255)]
        [Display(Name = "البريد الإلكتروني")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string? Email { get; set; }

        [StringLength(50)]
        [Display(Name = "العنوان")]
        public string? StreetAddress1 { get; set; }

        [StringLength(50)]
        [Display(Name = "العنوان 2")]
        public string? StreetAddress2 { get; set; }

        [StringLength(50)]
        [Display(Name = "المدينة")]
        public string? City { get; set; }

        [StringLength(50)]
        [Display(Name = "المنطقة")]
        public string? Region { get; set; }

        [Display(Name = "الرمز البريدي")]
        public int? PostalCode { get; set; }

        [StringLength(50)]
        [Display(Name = "طريقة الدفع")]
        public string? PaymentMethod { get; set; }

        [Display(Name = "الحد الائتماني")]
        [Column(TypeName = "money")]
        public decimal? CreditLimit { get; set; }

        [StringLength(255)]
        [Display(Name = "شروط الدفع")]
        public string? PaymentTerm { get; set; }

        [StringLength(255)]
        [Display(Name = "شخص التواصل")]
        public string? ContactPerson { get; set; }

        [StringLength(10)]
        [Display(Name = "السجل التجاري")]
        public string? CR { get; set; }

        [StringLength(50)]
        [Display(Name = "الرقم الضريبي")]
        public string? VATRegNo { get; set; }

        [StringLength(50)]
        [Display(Name = "المتجر")]
        public string? Shop { get; set; }

        [StringLength(50)]
        [Display(Name = "الحالة")]
        public string? Status { get; set; }

        [StringLength(50)]
        [Display(Name = "نوع المورد")]
        public string? LocalVendor { get; set; }

        [StringLength(255)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        // Audit fields
        [StringLength(255)]
        [Display(Name = "أنشئ بواسطة")]
        public string? CreatedBy { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime? CreatedOn { get; set; }

        [StringLength(255)]
        [Display(Name = "عدل بواسطة")]
        public string? ModifiedBy { get; set; }

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedOn { get; set; }
    }
}
