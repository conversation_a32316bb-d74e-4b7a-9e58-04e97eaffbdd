@{
    ViewData["Title"] = "نقطة البيع الجديدة";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var currentCulture = System.Globalization.CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
    var switchTo = currentCulture == "ar" ? "en" : "ar";
    var switchLabel = currentCulture == "ar" ? "EN" : "AR";
}

@using System.Globalization
@using System.Threading
@inject Microsoft.AspNetCore.Mvc.Localization.IViewLocalizer Localizer

<!-- POS New Main Layout -->
<div class="container-fluid pos-new" id="posNewRoot" dir="@((System.Threading.Thread.CurrentThread.CurrentUICulture.TextInfo.IsRightToLeft) ? "rtl" : "ltr")">
    <!-- Header: Invoice, Store, Customer, Barcode, Language Switch -->
    <header class="row align-items-center py-3 border-bottom">
        <div class="col-md-2 col-6 mb-2 mb-md-0">
            <label class="form-label">@Localizer["InvoiceNumber"]</label>
            <input type="text" class="form-control" id="txtInvoiceNo" readonly />
        </div>
        <div class="col-md-2 col-6 mb-2 mb-md-0">
            <label class="form-label">@Localizer["Store"]</label>
            <select class="form-select" id="ddlStore"></select>
        </div>
        <div class="col-md-2 col-6 mb-2 mb-md-0">
            <label class="form-label">@Localizer["Customer"]</label>
            <select class="form-select" id="ddlCustomer"></select>
        </div>
        <div class="col-md-3 col-12 mb-2 mb-md-0">
            <label class="form-label">@Localizer["Barcode"]</label>
            <input type="text" class="form-control" id="txtBarcode" autocomplete="off" />
        </div>
        <div class="col-md-1 col-6 mb-2 mb-md-0">
            <label class="form-label">@Localizer["Language"]</label>
            <form id="langForm" asp-action="SetLanguage" asp-controller="POSNew" method="post" class="d-inline">
                @Html.AntiForgeryToken()
                <input type="hidden" name="lang" value="@switchTo" />
                <button type="submit" class="btn btn-outline-secondary w-100" id="btnLangSwitch">@switchLabel</button>
            </form>
        </div>
        <div class="col-md-2 col-6 text-end">
            <button class="btn btn-danger" id="btnAbort"><i class="fa fa-times"></i> @Localizer["Abort"]</button>
        </div>
    </header>

    <!-- Main Area: Cart, Favorites, Search, Keyboard -->
    <main class="row mt-3">
        <!-- Cart Grid -->
        <section class="col-lg-7 col-12 mb-3">
            <div class="card h-100">
                <div class="card-header">@Localizer["CartItems"]</div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0" id="cartTable">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>@* Item *@ الصنف</th>
                                    <th>@* Qty *@ الكمية</th>
                                    <th>@* Price *@ السعر</th>
                                    <th>@* Discount *@ الخصم</th>
                                    <th>@* VAT *@ الضريبة</th>
                                    <th>@* Total *@ الإجمالي</th>
                                    <th>@* Actions *@ إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Cart items will be rendered here dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>
        <!-- Favorites & Search & Keyboard -->
        <aside class="col-lg-5 col-12 mb-3">
            <div class="row h-100">
                <div class="col-12 mb-3">
                    <div class="card h-100">
                        <div class="card-header">@Localizer["Favorites"]</div>
                        <div class="card-body" id="favoritesPanel">
                            <!-- Favorite items as quick buttons -->
                        </div>
                    </div>
                </div>
                <div class="col-12 mb-3">
                    <div class="card h-100">
                        <div class="card-header">@Localizer["ItemSearch"]</div>
                        <div class="card-body">
                            <input type="text" class="form-control mb-2" id="txtSearch" placeholder="بحث..." />
                            <div id="searchResults">
                                <!-- Search results will appear here -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <span>@Localizer["OnScreenKeyboard"]</span>
                            <button class="btn btn-sm btn-outline-secondary" id="btnToggleKeyboard">إخفاء/إظهار</button>
                        </div>
                        <div class="card-body" id="onScreenKeyboard">
                            <!-- On-screen keyboard will be rendered here -->
                        </div>
                    </div>
                </div>
            </div>
        </aside>
    </main>

    <!-- Footer: Totals, VAT, Discount, Payment, Save/Print -->
    <footer class="row align-items-center py-3 border-top bg-white sticky-bottom mt-3">
        <div class="col-md-2 col-6 mb-2 mb-md-0">
            <label class="form-label">@Localizer["Subtotal"]</label>
            <input type="text" class="form-control" id="txtSubtotal" readonly />
        </div>
        <div class="col-md-2 col-6 mb-2 mb-md-0">
            <label class="form-label">@Localizer["Discount"]</label>
            <input type="number" class="form-control" id="txtDiscount" min="0" max="100" />
        </div>
        <div class="col-md-2 col-6 mb-2 mb-md-0">
            <label class="form-label">@Localizer["VAT"]</label>
            <input type="text" class="form-control" id="txtVAT" readonly />
        </div>
        <div class="col-md-2 col-6 mb-2 mb-md-0">
            <label class="form-label">@Localizer["Total"]</label>
            <input type="text" class="form-control" id="txtTotal" readonly />
        </div>
        <div class="col-md-1 col-6 mb-2 mb-md-0">
            <label class="form-label">@Localizer["Cash"]</label>
            <input type="number" class="form-control" id="txtCash" min="0" />
        </div>
        <div class="col-md-1 col-6 mb-2 mb-md-0">
            <label class="form-label">@Localizer["Card"]</label>
            <input type="number" class="form-control" id="txtCard" min="0" />
        </div>
        <div class="col-md-2 col-12 text-end">
            <button class="btn btn-success me-2" id="btnSave"><i class="fa fa-save"></i> @Localizer["Save"]</button>
            <button class="btn btn-primary" id="btnPrint"><i class="fa fa-print"></i> @Localizer["Print"]</button>
        </div>
    </footer>
</div>

@section Scripts {
    <script src="/js/qz-tray.js"></script>
    <script>
    // --- POS New Frontend Logic ---
    const posApiBase = '/posNew';
    let currentInvoiceNo = null;
    let userName = '@User.Identity?.Name'; // Adjust as needed
    let storeLocked = false;
    let customerLocked = false;
    let lang = 'ar'; // Default, will be updated by session/lang switch

    // --- Utility Functions ---
    function fetchApi(url, options = {}) {
        return fetch(url, options).then(r => r.json());
    }

    // --- Helper: Populate Dropdown ---
    function populateDropdown(id, items, valueField = 'id', textField = 'name', locked = false, selectedValue = null) {
        const ddl = document.getElementById(id);
        ddl.innerHTML = '';
        items.forEach(item => {
            const opt = document.createElement('option');
            opt.value = item[valueField];
            opt.textContent = item[textField];
            if (selectedValue && item[valueField] == selectedValue) opt.selected = true;
            ddl.appendChild(opt);
        });
        ddl.disabled = locked;
    }

    // --- Helper: Render Favorites ---
    function renderFavorites(favorites) {
        const panel = document.getElementById('favoritesPanel');
        panel.innerHTML = '';
        favorites.forEach(item => {
            const btn = document.createElement('button');
            btn.className = 'btn btn-outline-primary m-1';
            btn.textContent = item.name;
            btn.onclick = () => addItemToCart(item.id, 1, item.price); // Default qty 1
            panel.appendChild(btn);
        });
    }

    // --- Helper: Render Cart Table ---
    function renderCart(cartItems) {
        const tbody = document.querySelector('#cartTable tbody');
        tbody.innerHTML = '';
        cartItems.forEach((item, idx) => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${idx + 1}</td>
                <td>${item.name}</td>
                <td><input type='number' min='1' value='${item.quantity}' class='form-control form-control-sm' onchange='updateCartItem(${item.lineSN}, this.value, ${item.unitPrice})' /></td>
                <td>${item.unitPrice.toFixed(2)}</td>
                <td>${item.discount?.toFixed(2) ?? ''}</td>
                <td>${item.vat?.toFixed(2) ?? ''}</td>
                <td>${item.total.toFixed(2)}</td>
                <td><button class='btn btn-sm btn-danger' onclick='deleteCartItem(${item.lineSN})'><i class='fa fa-trash'></i></button></td>
            `;
            tbody.appendChild(tr);
        });
        updateTotals(cartItems);
    }

    // --- Helper: Update Totals ---
    function updateTotals(cartItems) {
        let subtotal = 0, vat = 0, discount = 0, total = 0;
        cartItems.forEach(item => {
            subtotal += item.unitPrice * item.quantity;
            vat += item.vat ?? 0;
            discount += item.discount ?? 0;
            total += item.total;
        });
        document.getElementById('txtSubtotal').value = subtotal.toFixed(2);
        document.getElementById('txtVAT').value = vat.toFixed(2);
        document.getElementById('txtDiscount').value = discount.toFixed(2); // If discount is editable, handle separately
        document.getElementById('txtTotal').value = total.toFixed(2);
    }

    // --- Helper: Render Search Results ---
    function renderSearchResults(items) {
        const results = document.getElementById('searchResults');
        results.innerHTML = '';
        items.forEach(item => {
            const div = document.createElement('div');
            div.className = 'search-result-item p-2 border-bottom';
            div.textContent = item.name;
            div.onclick = () => addItemToCart(item.id, 1, item.price);
            results.appendChild(div);
        });
    }

    // --- Get Selected Store ---
    function getSelectedStore() {
        const ddl = document.getElementById('ddlStore');
        return ddl ? ddl.value : null;
    }

    // --- Initial Data Load ---
    async function loadInitData() {
        const data = await fetchApi(`${posApiBase}/init`);
        // Example data: { items, stores, favorites, userSettings }
        populateDropdown('ddlStore', data.stores, 'id', 'name', data.userSettings.storeLocked, data.userSettings.defaultStore);
        populateDropdown('ddlCustomer', data.customers, 'id', 'name', data.userSettings.customerLocked, data.userSettings.defaultCustomer);
        renderFavorites(data.favorites);
        // Load current invoice and cart
        const invoice = await fetchApi(`${posApiBase}/invoice/current`);
        if (invoice) {
            currentInvoiceNo = invoice.invoiceNo;
            document.getElementById('txtInvoiceNo').value = invoice.invoiceNo;
            const cartItems = await fetchApi(`${posApiBase}/invoice/items?invoiceNo=${currentInvoiceNo}`);
            renderCart(cartItems);
        }
    }

    // --- Cart Operations ---
    async function addItemToCart(itemNo, quantity, unitPrice) {
        if (!currentInvoiceNo) return;
        await fetchApi(`${posApiBase}/cart/add`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ invoiceNo: currentInvoiceNo, itemNo, quantity, unitPrice, username: userName, store: getSelectedStore() })
        });
        const cartItems = await fetchApi(`${posApiBase}/invoice/items?invoiceNo=${currentInvoiceNo}`);
        renderCart(cartItems);
    }

    async function updateCartItem(lineSN, quantity, unitPrice) {
        if (!currentInvoiceNo) return;
        await fetchApi(`${posApiBase}/cart/update`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ invoiceNo: currentInvoiceNo, lineSN, quantity, unitPrice, username: userName })
        });
        const cartItems = await fetchApi(`${posApiBase}/invoice/items?invoiceNo=${currentInvoiceNo}`);
        renderCart(cartItems);
    }

    async function deleteCartItem(lineSN) {
        if (!currentInvoiceNo) return;
        await fetchApi(`${posApiBase}/cart/delete`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ invoiceNo: currentInvoiceNo, lineSN, username: userName })
        });
        const cartItems = await fetchApi(`${posApiBase}/invoice/items?invoiceNo=${currentInvoiceNo}`);
        renderCart(cartItems);
    }

    // --- Barcode Processing ---
    async function processBarcode(barcode) {
        if (!currentInvoiceNo) return;
        await fetchApi(`${posApiBase}/barcode/process`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ barcode, invoiceNo: currentInvoiceNo, store: getSelectedStore(), username: userName })
        });
        const cartItems = await fetchApi(`${posApiBase}/invoice/items?invoiceNo=${currentInvoiceNo}`);
        renderCart(cartItems);
    }

    // --- Item Search ---
    async function searchItems(term) {
        const res = await fetchApi(`${posApiBase}/items/search?searchTerm=${encodeURIComponent(term)}`);
        // TODO: Render search results
    }

    // --- Payment Handling ---
    async function saveInvoice() {
        if (!currentInvoiceNo) return;
        const res = await fetchApi(`${posApiBase}/payment/save`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ invoiceNo: currentInvoiceNo, username: userName })
        });
        // TODO: Handle save result, show success/error
    }

    // --- QZ Tray Integration ---
    let invoicePrinter = null;

    // Fetch printer name from backend (add to GetInitData or invoice data)
    async function fetchPrinterName() {
        // Example: add printerName to your GetInitData endpoint result
        const data = await fetchApi(`${posApiBase}/init`);
        invoicePrinter = data.invoicePrinter || null;
    }

    // QZ Tray connection and print logic
    async function printReceipt(receiptContent) {
        if (typeof qz === 'undefined') {
            alert('QZ Tray is not installed or not running. Please download and install QZ Tray from https://qz.io/download/');
            return;
        }
        try {
            await qz.websocket.connect();
            const printers = await qz.printers.find(invoicePrinter);
            if (!printers) {
                alert('Invoice printer not found: ' + invoicePrinter);
                return;
            }
            const config = qz.configs.create(invoicePrinter);
            await qz.print(config, [{ type: 'raw', format: 'plain', data: receiptContent }]);
            qz.websocket.disconnect();
        } catch (err) {
            alert('Printing failed: ' + err);
        }
    }

    // Override completeInvoice to print after completion
    async function completeInvoice() {
        if (!currentInvoiceNo) return;
        const cash = parseFloat(document.getElementById('txtCash').value) || 0;
        const card = parseFloat(document.getElementById('txtCard').value) || 0;
        const res = await fetchApi(`${posApiBase}/payment/complete`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ invoiceNo: currentInvoiceNo, cashAmount: cash, cardAmount: card, username: userName })
        });
        // Fetch receipt content from backend (add endpoint if needed)
        const receipt = await fetchApi(`${posApiBase}/invoice/receipt?invoiceNo=${currentInvoiceNo}`);
        if (receipt && receipt.content) {
            printReceipt(receipt.content);
        } else {
            alert('Receipt content not available.');
        }
    }

    // --- Abort/Reset ---
    async function abortInvoice() {
        if (!currentInvoiceNo) return;
        const res = await fetchApi(`${posApiBase}/abort`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ invoiceNo: currentInvoiceNo })
        });
        // TODO: Reset UI
    }

    // --- Language Switching ---
    document.getElementById('btnLangSwitch').addEventListener('click', function() {
        // TODO: Implement session-based language switching and reload UI
    });

    // --- On-Screen Keyboard Toggle ---
    document.getElementById('btnToggleKeyboard').addEventListener('click', function() {
        const kb = document.getElementById('onScreenKeyboard');
        kb.style.display = (kb.style.display === 'none') ? 'block' : 'none';
    });

    // --- Barcode Input Handler ---
    document.getElementById('txtBarcode').addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            processBarcode(this.value);
            this.value = '';
        }
    });

    // --- Save/Print/Abort Buttons ---
    document.getElementById('btnSave').addEventListener('click', saveInvoice);
    document.getElementById('btnPrint').addEventListener('click', completeInvoice);
    document.getElementById('btnAbort').addEventListener('click', abortInvoice);

    // --- Overwrite loadInitData to bind UI ---
    document.addEventListener('DOMContentLoaded', function() {
        loadInitData();
        // TODO: Load current invoice, bind events, etc.
    });

    // --- Overwrite search box event ---
    document.getElementById('txtSearch').addEventListener('input', function() {
        if (this.value.length > 1) {
            searchItems(this.value).then(renderSearchResults);
        } else {
            renderSearchResults([]);
        }
    });

    // --- Update addItemToCart to refresh cart ---
    // This function is now called directly in renderFavorites and renderSearchResults
    // async function addItemToCart(itemNo, quantity, unitPrice) {
    //     if (!currentInvoiceNo) return;
    //     await fetchApi(`${posApiBase}/cart/add`, {
    //         method: 'POST',
    //         headers: { 'Content-Type': 'application/json' },
    //         body: JSON.stringify({ invoiceNo: currentInvoiceNo, itemNo, quantity, unitPrice, username: userName, store: getSelectedStore() })
    //     });
    //     const cartItems = await fetchApi(`${posApiBase}/invoice/items?invoiceNo=${currentInvoiceNo}`);
    //     renderCart(cartItems);
    // }

    // --- Update updateCartItem to refresh cart ---
    // This function is now called directly in renderCart
    // async function updateCartItem(lineSN, quantity, unitPrice) {
    //     if (!currentInvoiceNo) return;
    //     await fetchApi(`${posApiBase}/cart/update`, {
    //         method: 'POST',
    //         headers: { 'Content-Type': 'application/json' },
    //         body: JSON.stringify({ invoiceNo: currentInvoiceNo, lineSN, quantity, unitPrice, username: userName })
    //     });
    //     const cartItems = await fetchApi(`${posApiBase}/invoice/items?invoiceNo=${currentInvoiceNo}`);
    //     renderCart(cartItems);
    // }

    // --- Update deleteCartItem to refresh cart ---
    // This function is now called directly in renderCart
    // async function deleteCartItem(lineSN) {
    //     if (!currentInvoiceNo) return;
    //     await fetchApi(`${posApiBase}/cart/delete`, {
    //         method: 'POST',
    //         headers: { 'Content-Type': 'application/json' },
    //         body: JSON.stringify({ invoiceNo: currentInvoiceNo, lineSN, username: userName })
    //     });
    //     const cartItems = await fetchApi(`${posApiBase}/invoice/items?invoiceNo=${currentInvoiceNo}`);
    //     renderCart(cartItems);
    // }

    // --- Update processBarcode to add item and refresh cart ---
    // This function is now called directly in processBarcode
    // async function processBarcode(barcode) {
    //     if (!currentInvoiceNo) return;
    //     await fetchApi(`${posApiBase}/barcode/process`, {
    //         method: 'POST',
    //         headers: { 'Content-Type': 'application/json' },
    //         body: JSON.stringify({ barcode, invoiceNo: currentInvoiceNo, store: getSelectedStore(), username: userName })
    //     });
    //     const cartItems = await fetchApi(`${posApiBase}/invoice/items?invoiceNo=${currentInvoiceNo}`);
    //     renderCart(cartItems);
    // }

    // --- TODO: Implement cart rendering, dropdown population, favorites, search, accessibility, RTL/LTR switching, and localization ---
    </script>
} 