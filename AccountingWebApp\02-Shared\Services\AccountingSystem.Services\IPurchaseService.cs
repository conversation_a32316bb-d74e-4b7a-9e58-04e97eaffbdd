using AccountingSystem.Models;

namespace AccountingSystem.Services
{
    public interface IPurchaseService
    {
        // Invoice Management
        Task<int> GetNextInvoiceNumberAsync();
        Task<PurchaseInvoice?> GetCurrentInvoiceAsync();
        Task<PurchaseInvoice> CreateNewInvoiceAsync(string username);
        Task<PurchaseInvoice?> GetInvoiceByNumberAsync(int invoiceNo);
        Task<PurchaseInvoice> SaveInvoiceAsync(int invoiceNo, PurchaseInvoiceCreateViewModel model, string username);
        Task<bool> DeleteInvoiceAsync(int invoiceNo, string username);
        Task<List<PurchaseInvoice>> SearchInvoicesAsync(string? invoiceNo, string? vendor, string? user, string? store, DateTime? date);

        // Invoice Items Management
        Task<PurchaseInvoiceItem> AddItemToInvoiceAsync(int invoiceNo, long itemNo, decimal quantity, decimal unitPrice, string uofM, decimal uofMConversion, string username, string store);
        Task<bool> UpdateInvoiceItemAsync(int invoiceNo, int lineSN, decimal quantity, decimal unitPrice, string uofM, decimal uofMConversion, string username);
        Task<bool> DeleteInvoiceItemAsync(int invoiceNo, int lineSN, string username);
        Task<List<PurchaseInvoiceItem>> GetInvoiceItemsAsync(int invoiceNo);
        Task<bool> ClearInvoiceItemsAsync(int invoiceNo);

        // Data Loading
        Task<List<ChartOfAccount>> GetVendorsAsync();
        Task<List<ChartOfAccount>> GetCashiersAsync();
        Task<List<Store>> GetStoresAsync();
        Task<InvoiceToolSetting> GetPurchaseSettingsAsync();
        Task<PurchaseUserAuthorization> GetUserAuthorizationAsync(string username);

        // Item Management
        Task<Item?> GetItemByNumberAsync(long itemNo);
        Task<List<Item>> SearchItemsAsync(string searchTerm);
        Task<List<ItemUnit>> GetItemUnitsAsync(long itemNo);
        Task<decimal> GetItemPurchasePriceAsync(long itemNo);

        // Calculations
        Task<decimal> CalculateVATAsync(decimal amount, decimal vatPercentage, bool isPriceIncludeVAT);
        Task<decimal> CalculateDiscountAsync(decimal amount, decimal discountPercentage);
        Task<decimal> CalculateLineTotalAsync(decimal quantity, decimal unitPrice, decimal vatPercentage, bool isPriceIncludeVAT);

        // GL Integration
        Task<bool> CreatePurchaseInvoiceGLEntryAsync(int invoiceNo);
        Task<bool> CreateVendorPaymentGLEntryAsync(int invoiceNo);
        Task<bool> CreateAllGLEntriesAsync(int invoiceNo);

        // Validation
        Task<bool> ValidateInvoiceAsync(PurchaseInvoiceCreateViewModel model);
        Task<bool> CanUserChangeStoreAsync(string username);
        Task<bool> CanUserChangePriceAsync(string username);
        Task<decimal> GetUserMaxDiscountPercentAsync(string username);
    }
} 