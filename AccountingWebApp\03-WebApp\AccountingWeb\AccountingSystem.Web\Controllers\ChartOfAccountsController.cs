using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Services.ViewModels;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class ChartOfAccountsController : Controller
    {
        private readonly IChartOfAccountService _chartOfAccountService;
        private readonly ILogger<ChartOfAccountsController> _logger;

        public ChartOfAccountsController(
            IChartOfAccountService chartOfAccountService,
            ILogger<ChartOfAccountsController> logger)
        {
            _chartOfAccountService = chartOfAccountService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var accounts = await _chartOfAccountService.GetAllAccountsAsync();
                _logger.LogInformation($"Loaded {accounts.Count} accounts from database");
                return View(accounts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading chart of accounts: {Message}", ex.Message);
                TempData["Error"] = $"حدث خطأ أثناء تحميل دليل الحسابات: {ex.Message}";
                return View(new List<ChartOfAccountViewModel>());
            }
        }

        [HttpGet]
        public async Task<IActionResult> Create(string? parentCode = null)
        {
            try
            {
                var model = new ChartOfAccountCreateViewModel
                {
                    IsPosting = true,
                    OpeningBalance = 0
                };

                if (!string.IsNullOrEmpty(parentCode))
                {
                    var parentAccount = await _chartOfAccountService.GetAccountByCodeAsync(parentCode);
                    if (parentAccount != null)
                    {
                        model.ParentAccountCode = parentCode;
                        var segmentCode = await _chartOfAccountService.GenerateNextSegmentCodeAsync(parentCode);
                        model.SegmentCode = segmentCode;
                        model.AccountCode = parentCode + segmentCode;
                    }
                }

                ViewBag.AccountTypes = new[] { "Asset", "Liability", "Equity", "Revenue", "Expense" };
                ViewBag.AccountNatures = new[] { "Debit", "Credit" };
                ViewBag.ParentAccounts = await _chartOfAccountService.GetParentAccountsAsync();

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error preparing create account form: {Message}", ex.Message);
                TempData["Error"] = $"حدث خطأ أثناء تحضير نموذج إنشاء الحساب: {ex.Message} - {ex.StackTrace}";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(ChartOfAccountCreateViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    ViewBag.AccountTypes = new[] { "Asset", "Liability", "Equity", "Revenue", "Expense" };
                    ViewBag.AccountNatures = new[] { "Debit", "Credit" };
                    ViewBag.ParentAccounts = await _chartOfAccountService.GetParentAccountsAsync();
                    TempData["Error"] = "ModelState is invalid: " + string.Join("; ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                    return View(model);
                }

                // Check if account code already exists
                if (await _chartOfAccountService.AccountExistsAsync(model.AccountCode))
                {
                    ModelState.AddModelError("AccountCode", "كود الحساب موجود مسبقاً");
                    ViewBag.AccountTypes = new[] { "Asset", "Liability", "Equity", "Revenue", "Expense" };
                    ViewBag.AccountNatures = new[] { "Debit", "Credit" };
                    ViewBag.ParentAccounts = await _chartOfAccountService.GetParentAccountsAsync();
                    TempData["Error"] = "Account code already exists.";
                    return View(model);
                }

                var createdBy = User.Identity?.Name ?? "System";
                var success = await _chartOfAccountService.CreateAccountAsync(model, createdBy);

                if (success)
                {
                    TempData["Success"] = "تم إنشاء الحساب بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["Error"] = "حدث خطأ أثناء إنشاء الحساب (service returned false)";
                    ViewBag.AccountTypes = new[] { "Asset", "Liability", "Equity", "Revenue", "Expense" };
                    ViewBag.AccountNatures = new[] { "Debit", "Credit" };
                    ViewBag.ParentAccounts = await _chartOfAccountService.GetParentAccountsAsync();
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating account: {Message}", ex.Message);
                TempData["Error"] = $"حدث خطأ أثناء إنشاء الحساب: {ex.Message} - {ex.StackTrace}";
                ViewBag.AccountTypes = new[] { "Asset", "Liability", "Equity", "Revenue", "Expense" };
                ViewBag.AccountNatures = new[] { "Debit", "Credit" };
                ViewBag.ParentAccounts = await _chartOfAccountService.GetParentAccountsAsync();
                return View(model);
            }
        }

        [HttpGet]
        public async Task<IActionResult> Edit(string accountCode)
        {
            try
            {
                var account = await _chartOfAccountService.GetAccountByIdAsync(accountCode);
                if (account == null)
                {
                    TempData["Error"] = "الحساب غير موجود";
                    return RedirectToAction(nameof(Index));
                }

                var model = new ChartOfAccountEditViewModel
                {
                    AccountCode = account.AccountCode,
                    SegmentCode = account.SegmentCode,
                    AccountName = account.AccountName,
                    ParentAccountCode = account.ParentAccountCode,
                    AccountType = account.AccountType,
                    AccountNature = account.AccountNature,
                    IsPosting = account.IsPosting,
                    OpeningBalance = account.OpeningBalance,
                    Notes = account.Notes
                };

                ViewBag.AccountTypes = new[] { "Asset", "Liability", "Equity", "Revenue", "Expense" };
                ViewBag.AccountNatures = new[] { "Debit", "Credit" };
                ViewBag.ParentAccounts = await _chartOfAccountService.GetParentAccountsAsync();

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading account for edit: {Message}", ex.Message);
                TempData["Error"] = $"حدث خطأ أثناء تحميل بيانات الحساب: {ex.Message} - {ex.StackTrace}";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(ChartOfAccountEditViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    ViewBag.AccountTypes = new[] { "Asset", "Liability", "Equity", "Revenue", "Expense" };
                    ViewBag.AccountNatures = new[] { "Debit", "Credit" };
                    ViewBag.ParentAccounts = await _chartOfAccountService.GetParentAccountsAsync();
                    TempData["Error"] = "ModelState is invalid: " + string.Join("; ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                    return View(model);
                }

                var modifiedBy = User.Identity?.Name ?? "System";
                var success = await _chartOfAccountService.UpdateAccountAsync(model, modifiedBy);

                if (success)
                {
                    TempData["Success"] = "تم تحديث الحساب بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["Error"] = "حدث خطأ أثناء تحديث الحساب (service returned false)";
                    ViewBag.AccountTypes = new[] { "Asset", "Liability", "Equity", "Revenue", "Expense" };
                    ViewBag.AccountNatures = new[] { "Debit", "Credit" };
                    ViewBag.ParentAccounts = await _chartOfAccountService.GetParentAccountsAsync();
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating account: {Message}", ex.Message);
                TempData["Error"] = $"حدث خطأ أثناء تحديث الحساب: {ex.Message} - {ex.StackTrace}";
                ViewBag.AccountTypes = new[] { "Asset", "Liability", "Equity", "Revenue", "Expense" };
                ViewBag.AccountNatures = new[] { "Debit", "Credit" };
                ViewBag.ParentAccounts = await _chartOfAccountService.GetParentAccountsAsync();
                return View(model);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(string accountCode)
        {
            try
            {
                var account = await _chartOfAccountService.GetAccountByIdAsync(accountCode);
                if (account == null)
                {
                    return Json(new { success = false, message = "الحساب غير موجود" });
                }

                // Check if account has sub-accounts
                if (await _chartOfAccountService.HasSubAccountsAsync(account.AccountCode))
                {
                    return Json(new { success = false, message = "لا يمكن حذف الحساب لأنه يحتوي على حسابات فرعية" });
                }

                var success = await _chartOfAccountService.DeleteAccountAsync(accountCode);

                if (success)
                {
                    return Json(new { success = true, message = "تم حذف الحساب بنجاح" });
                }
                else
                {
                    return Json(new { success = false, message = "حدث خطأ أثناء حذف الحساب" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting account");
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الحساب" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAccountDetails(string accountCode)
        {
            try
            {
                var account = await _chartOfAccountService.GetAccountByIdAsync(accountCode);
                if (account == null)
                {
                    return Json(new { success = false, message = "الحساب غير موجود" });
                }

                return Json(new { success = true, account });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting account details");
                return Json(new { success = false, message = "حدث خطأ أثناء جلب بيانات الحساب" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GenerateSegmentCode(string parentCode)
        {
            try
            {
                if (string.IsNullOrEmpty(parentCode))
                {
                    return Json(new { success = false, message = "كود الحساب الأب مطلوب" });
                }

                var segmentCode = await _chartOfAccountService.GenerateNextSegmentCodeAsync(parentCode);
                var fullCode = parentCode + segmentCode;

                return Json(new { success = true, segmentCode, fullCode });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating segment code");
                return Json(new { success = false, message = "حدث خطأ أثناء توليد كود الجزء" });
            }
        }
    }
} 