using Microsoft.EntityFrameworkCore;
using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Data.SqlClient;
using System.Data;

namespace AccountingSystem.Services
{
    public class PurchaseService : IPurchaseService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<PurchaseService> _logger;

        public PurchaseService(AccountingDbContext context, ILogger<PurchaseService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<int> GetNextInvoiceNumberAsync()
        {
            try
            {
                // Check for unused invoice number first
                var unusedInvoice = await _context.PurchaseInvoices
                    .Where(p => p.ReadyForUse == "True" && p.TrxType == "مشتريات")
                    .OrderBy(p => p.TrxNo)
                    .FirstOrDefaultAsync();

                if (unusedInvoice != null)
                {
                    return unusedInvoice.TrxNo;
                }

                // Get next available number
                var maxInvoiceNo = await _context.PurchaseInvoices
                    .Where(p => p.TrxType == "مشتريات")
                    .MaxAsync(p => (int?)p.TrxNo) ?? 0;

                return maxInvoiceNo + 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting next invoice number");
                throw;
            }
        }

        public async Task<PurchaseInvoice?> GetCurrentInvoiceAsync()
        {
            try
            {
                return await _context.PurchaseInvoices
                    .Where(p => p.ReadyForUse == "False" && p.TrxType == "مشتريات")
                    .OrderByDescending(p => p.CreatedOn)
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current invoice");
                throw;
            }
        }

        public async Task<PurchaseInvoice> CreateNewInvoiceAsync(string username)
        {
            try
            {
                var invoiceNo = await GetNextInvoiceNumberAsync();
                var existingInvoice = await _context.PurchaseInvoices
                    .FirstOrDefaultAsync(p => p.TrxNo == invoiceNo && p.TrxType == "مشتريات");

                if (existingInvoice != null)
                {
                    // Mark existing invoice as not ready for use
                    existingInvoice.ReadyForUse = "False";
                    existingInvoice.ModifiedBy = username;
                    existingInvoice.ModifiedOn = DateTime.Now;
                }
                else
                {
                    // Create new invoice
                    existingInvoice = new PurchaseInvoice
                    {
                        TrxNo = invoiceNo,
                        TrxType = "مشتريات",
                        ReadyForUse = "False",
                        CreatedBy = username,
                        CreatedOn = DateTime.Now,
                        TrxDate = DateTime.Now
                    };
                    _context.PurchaseInvoices.Add(existingInvoice);
                }

                await _context.SaveChangesAsync();
                return existingInvoice;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating new invoice");
                throw;
            }
        }

        public async Task<PurchaseInvoice?> GetInvoiceByNumberAsync(int invoiceNo)
        {
            try
            {
                return await _context.PurchaseInvoices
                    .FirstOrDefaultAsync(p => p.TrxNo == invoiceNo && p.TrxType == "مشتريات");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice by number: {InvoiceNo}", invoiceNo);
                throw;
            }
        }

        public async Task<PurchaseInvoice> SaveInvoiceAsync(int invoiceNo, PurchaseInvoiceCreateViewModel model, string username)
        {
            try
            {
                var invoice = await GetInvoiceByNumberAsync(invoiceNo);
                if (invoice == null)
                {
                    throw new InvalidOperationException("Invoice not found");
                }

                // Update invoice header
                invoice.TrxDate = DateTime.Now;
                invoice.Store = model.Store;
                invoice.PartnerNo = model.PartnerNo;
                // Get partner name from ChartOfAccount
                if (model.PartnerNo.HasValue)
                {
                    var partner = await _context.ChartOfAccounts
                        .FirstOrDefaultAsync(c => c.AccountCode == model.PartnerNo.Value.ToString());
                    invoice.PartnerName = partner?.AccountName ?? "";
                }
                invoice.PaymentMethod = model.PaymentMethod;
                invoice.PartnerReference = model.PartnerReference;
                invoice.ReferenceInvoice = model.ReferenceInvoice;
                invoice.TrxNote = model.TrxNote;
                invoice.TrxDiscount = model.TrxDiscount;
                invoice.ModifiedBy = username;
                invoice.ModifiedOn = DateTime.Now;

                // Calculate totals
                var items = await GetInvoiceItemsAsync(invoice.TrxNo);
                var total = items.Sum(i => i.LineAmount);
                var discountAmount = (model.TrxDiscount / 100) * total;
                var netAmount = total - discountAmount;
                var vatAmount = await CalculateVATAsync(netAmount, 15, model.VATIncluded);

                invoice.TrxTotal = total;
                invoice.TrxDiscountValue = discountAmount;
                invoice.TrxNetAmount = netAmount;
                invoice.TrxVAT = vatAmount;
                invoice.PaymentStatus = model.PaymentMethod == "نقدي" ? "Paid" : "Open";

                await _context.SaveChangesAsync();

                // Create GL entries if payment is cash
                if (model.PaymentMethod == "نقدي")
                {
                    await CreateAllGLEntriesAsync(invoice.TrxNo);
                }

                return invoice;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving invoice");
                throw;
            }
        }

        public async Task<PurchaseInvoice> CompleteInvoiceAsync(int invoiceNo, string username)
        {
            try
            {
                var invoice = await GetInvoiceByNumberAsync(invoiceNo);
                if (invoice == null)
                {
                    throw new InvalidOperationException("Invoice not found");
                }

                // Mark invoice as completed
                invoice.ReadyForUse = "True";
                invoice.ModifiedBy = username;
                invoice.ModifiedOn = DateTime.Now;

                await _context.SaveChangesAsync();

                return invoice;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing invoice");
                throw;
            }
        }

        public async Task<bool> DeleteInvoiceAsync(int invoiceNo, string username)
        {
            try
            {
                var invoice = await GetInvoiceByNumberAsync(invoiceNo);
                if (invoice == null) return false;

                // Delete invoice items
                await ClearInvoiceItemsAsync(invoiceNo);

                // Mark invoice as ready for use (available for reuse)
                invoice.ReadyForUse = "True";
                invoice.ModifiedBy = username;
                invoice.ModifiedOn = DateTime.Now;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice: {InvoiceNo}", invoiceNo);
                throw;
            }
        }

        public async Task<List<PurchaseInvoice>> SearchInvoicesAsync(string? invoiceNo, string? vendor, string? user, string? store, DateTime? date)
        {
            try
            {
                var query = _context.PurchaseInvoices
                    .Where(p => p.TrxType == "مشتريات" && p.ReadyForUse == "False")
                    .AsQueryable();

                if (!string.IsNullOrEmpty(invoiceNo))
                    query = query.Where(p => p.TrxNo.ToString().Contains(invoiceNo));

                if (!string.IsNullOrEmpty(vendor))
                    query = query.Where(p => p.PartnerName != null && p.PartnerName.Contains(vendor));

                if (!string.IsNullOrEmpty(user))
                    query = query.Where(p => p.CreatedBy != null && p.CreatedBy.Contains(user));

                if (!string.IsNullOrEmpty(store))
                    query = query.Where(p => p.Store != null && p.Store.Contains(store));

                if (date.HasValue)
                    query = query.Where(p => p.TrxDate.HasValue && p.TrxDate.Value.Date == date.Value.Date);

                return await query
                    .OrderByDescending(p => p.TrxNo)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching invoices");
                throw;
            }
        }

        public async Task<PurchaseInvoiceItem> AddItemToInvoiceAsync(int invoiceNo, long itemNo, decimal quantity, decimal unitPrice, string uofM, decimal uofMConversion, string username, string store)
        {
            try
            {
                // Get next line number
                var maxLineSN = await _context.PurchaseInvoiceItems
                    .Where(p => p.DocNo == invoiceNo && p.TrxType == "مشتريات")
                    .MaxAsync(p => (int?)p.LineSN) ?? 0;

                var lineSN = maxLineSN + 1;

                // Get item details
                var item = await GetItemByNumberAsync(itemNo);
                if (item == null)
                    throw new InvalidOperationException("Item not found");

                // Calculate VAT
                var vatPercentage = item.Tax_Percent ?? 0;
                var lineTotal = quantity * unitPrice;
                var vatAmount = await CalculateVATAsync(lineTotal, vatPercentage, false);

                var invoiceItem = new PurchaseInvoiceItem
                {
                    DocNo = invoiceNo,
                    LineSN = lineSN,
                    TrxType = "مشتريات",
                    TrxDate = DateTime.Now,
                    ItemNo = itemNo,
                    Store = store,
                    TrxQTY = quantity,
                    UnitPrice = unitPrice,
                    UofM = uofM,
                    UofMConversion = uofMConversion,
                    VATAmount = vatAmount,
                    LineAmount = lineTotal + vatAmount,
                    CreatedBy = username,
                    CreatedOn = DateTime.Now
                };

                _context.PurchaseInvoiceItems.Add(invoiceItem);
                await _context.SaveChangesAsync();

                return invoiceItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding item to invoice");
                throw;
            }
        }

        public async Task<bool> UpdateInvoiceItemAsync(int invoiceNo, int lineSN, decimal quantity, decimal unitPrice, string uofM, decimal uofMConversion, string username)
        {
            try
            {
                var item = await _context.PurchaseInvoiceItems
                    .FirstOrDefaultAsync(p => p.DocNo == invoiceNo && p.LineSN == lineSN && p.TrxType == "مشتريات");

                if (item == null) return false;

                // Get item details for VAT calculation
                var itemDetails = await GetItemByNumberAsync(item.ItemNo);
                var vatPercentage = itemDetails?.Tax_Percent ?? 0;

                // Update item
                item.TrxQTY = quantity;
                item.UnitPrice = unitPrice;
                item.UofM = uofM;
                item.UofMConversion = uofMConversion;
                item.ModifiedBy = username;
                item.ModifiedOn = DateTime.Now;

                // Recalculate amounts
                var lineTotal = quantity * unitPrice;
                var vatAmount = await CalculateVATAsync(lineTotal, vatPercentage, false);
                item.VATAmount = vatAmount;
                item.LineAmount = lineTotal + vatAmount;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice item");
                throw;
            }
        }

        public async Task<bool> DeleteInvoiceItemAsync(int invoiceNo, int lineSN, string username)
        {
            try
            {
                var item = await _context.PurchaseInvoiceItems
                    .FirstOrDefaultAsync(p => p.DocNo == invoiceNo && p.LineSN == lineSN && p.TrxType == "مشتريات");

                if (item == null) return false;

                _context.PurchaseInvoiceItems.Remove(item);

                // Update line numbers for subsequent items
                var subsequentItems = await _context.PurchaseInvoiceItems
                    .Where(p => p.DocNo == invoiceNo && p.LineSN > lineSN && p.TrxType == "مشتريات")
                    .ToListAsync();

                foreach (var subsequentItem in subsequentItems)
                {
                    subsequentItem.LineSN--;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice item");
                throw;
            }
        }

        public async Task<List<PurchaseInvoiceItem>> GetInvoiceItemsAsync(int invoiceNo)
        {
            try
            {
                return await _context.PurchaseInvoiceItems
                    .Where(p => p.DocNo == invoiceNo && p.TrxType == "مشتريات")
                    .OrderBy(p => p.LineSN)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice items");
                throw;
            }
        }

        public async Task<bool> ClearInvoiceItemsAsync(int invoiceNo)
        {
            try
            {
                var items = await GetInvoiceItemsAsync(invoiceNo);
                _context.PurchaseInvoiceItems.RemoveRange(items);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing invoice items");
                throw;
            }
        }

        public async Task<List<ChartOfAccount>> GetVendorsAsync()
        {
            try
            {
                var vendorParentAccount = await _context.GLConfigs
                    .Where(g => g.EntryReferenceModule == "موردون")
                    .Select(g => g.AccountNo)
                    .FirstOrDefaultAsync();

                if (vendorParentAccount == 0) return new List<ChartOfAccount>();

                return await _context.ChartOfAccounts
                    .Where(c => c.ParentAccountCode == vendorParentAccount.ToString())
                    .OrderBy(c => c.AccountCode)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vendors");
                throw;
            }
        }

        public async Task<List<ChartOfAccount>> GetCashiersAsync()
        {
            try
            {
                var cashParentAccount = await _context.GLConfigs
                    .Where(g => g.EntryReferenceModule == "نقدية")
                    .Select(g => g.AccountNo)
                    .FirstOrDefaultAsync();

                if (cashParentAccount == 0) return new List<ChartOfAccount>();

                return await _context.ChartOfAccounts
                    .Where(c => c.ParentAccountCode == cashParentAccount.ToString())
                    .OrderBy(c => c.AccountCode)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cashiers");
                throw;
            }
        }

        public async Task<List<Store>> GetStoresAsync()
        {
            try
            {
                return await _context.Shops
                    .OrderBy(s => s.StoreName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stores");
                throw;
            }
        }

        public async Task<InvoiceToolSetting> GetPurchaseSettingsAsync()
        {
            try
            {
                var settings = await _context.InvoiceToolSettings
                    .FirstOrDefaultAsync(p => p.InvoiceType == "مشتريات");

                if (settings == null)
                {
                    // Create default settings
                    settings = new InvoiceToolSetting
                    {
                        InvoiceType = "مشتريات",
                        DefPaymentType = "نقدي",
                        DefPriceIncludeVAT = "False",
                        ReferenceMandatory = "False",
                        PrintOption = 0
                    };
                    _context.InvoiceToolSettings.Add(settings);
                    await _context.SaveChangesAsync();
                }

                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting purchase settings");
                throw;
            }
        }

        public async Task<PurchaseUserAuthorization> GetUserAuthorizationAsync(string username)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == username);

                if (user == null)
                    return new PurchaseUserAuthorization { Username = username };

                return new PurchaseUserAuthorization
                {
                    Username = username,
                    GroupID = user.GroupID,
                    DefaultStore = user.DefaultStore,
                    StoreChange = user.StoreChange,
                    DefaultCustomer = user.DefaultCustomer,
                    CustomerChange = user.CustomerChange,
                    DefaultCashier = user.DefaultCashier,
                    CashierChange = user.CashierChange,
                    ChangeInvoicePrice = user.ChangeInvoicePrice,
                    MaxDiscountPercent = user.MaxDiscountPercent,
                    IsAdmin = user.GroupID == 1
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user authorization");
                throw;
            }
        }

        public async Task<Item?> GetItemByNumberAsync(long itemNo)
        {
            try
            {
                return await _context.Items
                    .FirstOrDefaultAsync(i => i.ItemNo == itemNo && (i.Status == 0 || i.Status == null));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item by number: {ItemNo}", itemNo);
                throw;
            }
        }

        public async Task<List<Item>> SearchItemsAsync(string searchTerm)
        {
            try
            {
                return await _context.Items
                    .Where(i => (i.Status == 0 || i.Status == null) &&
                               (i.ItemDescription != null && i.ItemDescription.Contains(searchTerm)) ||
                               (i.Barcode != null && i.Barcode.Contains(searchTerm)) ||
                               i.ItemNo.ToString().Contains(searchTerm))
                    .OrderBy(i => i.ItemDescription)
                    .Take(50)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching items");
                throw;
            }
        }

        public async Task<List<ItemUnit>> GetItemUnitsAsync(long itemNo)
        {
            try
            {
                var item = await GetItemByNumberAsync(itemNo);
                if (item == null) return new List<ItemUnit>();

                var units = new List<ItemUnit>();

                if (!string.IsNullOrEmpty(item.UofM))
                    units.Add(new ItemUnit { UnitName = item.UofM, ConversionFactor = 1 });

                if (!string.IsNullOrEmpty(item.AUofM))
                    units.Add(new ItemUnit { UnitName = item.AUofM, ConversionFactor = item.AUofMX ?? 1 });

                if (!string.IsNullOrEmpty(item.AUofM2))
                    units.Add(new ItemUnit { UnitName = item.AUofM2, ConversionFactor = item.AUofMX2 ?? 1 });

                if (!string.IsNullOrEmpty(item.AUofM3))
                    units.Add(new ItemUnit { UnitName = item.AUofM3, ConversionFactor = item.AUofMX3 ?? 1 });

                return units;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item units");
                throw;
            }
        }

        public async Task<decimal> GetItemPurchasePriceAsync(long itemNo)
        {
            try
            {
                var item = await GetItemByNumberAsync(itemNo);
                return item?.UnitPurchasePrice ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item purchase price");
                throw;
            }
        }

        public async Task<decimal> CalculateVATAsync(decimal amount, decimal vatPercentage, bool isPriceIncludeVAT)
        {
            try
            {
                if (isPriceIncludeVAT)
                {
                    return Math.Round((amount * vatPercentage) / (100 + vatPercentage), 2);
                }
                else
                {
                    return Math.Round((amount * vatPercentage) / 100, 2);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating VAT");
                throw;
            }
        }

        public async Task<decimal> CalculateDiscountAsync(decimal amount, decimal discountPercentage)
        {
            try
            {
                return Math.Round((amount * discountPercentage) / 100, 2);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating discount");
                throw;
            }
        }

        public async Task<decimal> CalculateLineTotalAsync(decimal quantity, decimal unitPrice, decimal vatPercentage, bool isPriceIncludeVAT)
        {
            try
            {
                var lineTotal = quantity * unitPrice;
                var vatAmount = await CalculateVATAsync(lineTotal, vatPercentage, isPriceIncludeVAT);
                return lineTotal + vatAmount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating line total");
                throw;
            }
        }

        public async Task<bool> CreatePurchaseInvoiceGLEntryAsync(int invoiceNo)
        {
            try
            {
                // Implementation for GL entry creation
                // This would create the necessary GL entries for the purchase invoice
                _logger.LogInformation("Creating GL entry for purchase invoice: {InvoiceNo}", invoiceNo);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating purchase invoice GL entry");
                throw;
            }
        }

        public async Task<bool> CreateVendorPaymentGLEntryAsync(int invoiceNo)
        {
            try
            {
                // Implementation for vendor payment GL entry creation
                _logger.LogInformation("Creating vendor payment GL entry for invoice: {InvoiceNo}", invoiceNo);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating vendor payment GL entry");
                throw;
            }
        }

        public async Task<bool> CreateAllGLEntriesAsync(int invoiceNo)
        {
            try
            {
                await CreatePurchaseInvoiceGLEntryAsync(invoiceNo);
                await CreateVendorPaymentGLEntryAsync(invoiceNo);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating all GL entries");
                throw;
            }
        }

        public async Task<bool> ValidateInvoiceAsync(PurchaseInvoiceCreateViewModel model)
        {
            try
            {
                if (model.PartnerNo == null || model.Store == null || model.PaymentMethod == null)
                    return false;

                if (model.Items == null || !model.Items.Any())
                    return false;

                foreach (var item in model.Items)
                {
                    if (item.ItemNo == 0 || item.TrxQTY <= 0 || item.UnitPrice <= 0)
                        return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating invoice");
                throw;
            }
        }

        public async Task<bool> CanUserChangeStoreAsync(string username)
        {
            try
            {
                var auth = await GetUserAuthorizationAsync(username);
                return auth.IsAdmin || auth.StoreChange;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking user store change permission");
                throw;
            }
        }

        public async Task<bool> CanUserChangePriceAsync(string username)
        {
            try
            {
                var auth = await GetUserAuthorizationAsync(username);
                return auth.IsAdmin || auth.ChangeInvoicePrice;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking user price change permission");
                throw;
            }
        }

        public async Task<decimal> GetUserMaxDiscountPercentAsync(string username)
        {
            try
            {
                var auth = await GetUserAuthorizationAsync(username);
                return auth.MaxDiscountPercent ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user max discount percent");
                throw;
            }
        }
    }
} 