@model List<AccountingSystem.Services.ViewModels.ChartOfAccountViewModel>
@{
    ViewData["Title"] = "دليل الحسابات";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-sitemap me-2"></i>
                        دليل الحسابات
                    </h3>
                    <div>
                        <a href="@Url.Action("Create")" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة حساب جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["Success"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["Success"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["Error"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["Error"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-8">
                            <div class="tree-container">
                                <div class="tree-header">
                                    <h5><i class="fas fa-tree me-2"></i>دليل الحسابات</h5>
                                </div>
                                <div class="tree-body" id="accountsTree">
                                    @await Html.PartialAsync("_AccountsTree", Model)
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        تفاصيل الحساب
                                    </h5>
                                </div>
                                <div class="card-body" id="accountDetails">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-mouse-pointer fa-2x mb-2"></i>
                                        <p>اختر حساباً لعرض تفاصيله</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .tree-container {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        background: #fff;
    }

    .tree-header {
        background: #f8f9fa;
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .tree-body {
        padding: 1rem;
        max-height: 600px;
        overflow-y: auto;
    }

    .tree-node {
        margin: 0.25rem 0;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
    }

    .tree-node:hover {
        background-color: #f8f9fa;
    }

    .tree-node.selected {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }

    .tree-content {
        display: flex;
        align-items: center;
        padding: 0.5rem;
        cursor: pointer;
        border-radius: 0.375rem;
    }

    .tree-toggle {
        width: 20px;
        height: 20px;
        margin-left: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .tree-toggle:hover {
        background-color: #e9ecef;
    }

    .tree-icon {
        width: 24px;
        height: 24px;
        margin-left: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 0.75rem;
    }

    .tree-text {
        flex: 1;
        margin-left: 0.5rem;
    }

    .tree-actions {
        display: flex;
        gap: 0.25rem;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .tree-node:hover .tree-actions {
        opacity: 1;
    }

    .tree-children {
        margin-right: 2rem;
        border-right: 2px solid #e9ecef;
        padding-right: 1rem;
    }

    .account-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        margin-left: 0.5rem;
    }

    .account-type-debit { background-color: #d4edda; color: #155724; }
    .account-type-credit { background-color: #fff3cd; color: #856404; }
</style>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Tree node click handler
            $('.tree-content').click(function (e) {
                if (!$(e.target).hasClass('tree-toggle') && !$(e.target).hasClass('tree-action')) {
                    $('.tree-node').removeClass('selected');
                    $(this).closest('.tree-node').addClass('selected');

                    var accountCode = $(this).data('account-code');
                    loadAccountDetails(accountCode);
                }
            });

            // Tree toggle handler
            $('.tree-toggle').click(function (e) {
                e.stopPropagation();
                var node = $(this).closest('.tree-node');
                var children = node.find('> .tree-children');
                var icon = $(this).find('i');

                if (children.is(':visible')) {
                    children.slideUp(200);
                    icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
                } else {
                    children.slideDown(200);
                    icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
                }
            });

            // Delete account
            $('.tree-action-delete').click(function (e) {
                e.stopPropagation();
                var accountCode = $(this).data('account-code');
                var accountName = $(this).data('account-name');

                if (confirm('هل أنت متأكد من حذف الحساب "' + accountName + '"؟')) {
                    deleteAccount(accountCode);
                }
            });
        });

        function loadAccountDetails(accountCode) {
            $.get('@Url.Action("GetAccountDetails")', { accountCode: accountCode })
                .done(function (response) {
                    if (response.success) {
                        var account = response.account;
                        var detailsHtml = `
                            <div class="account-details">
                                <h6 class="text-primary">${account.accountName}</h6>
                                <hr>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>كود الحساب:</strong>
                                    </div>
                                    <div class="col-6">
                                        ${account.accountCode}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>كود الجزء:</strong>
                                    </div>
                                    <div class="col-6">
                                        ${account.segmentCode || '-'}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>الحساب الأب:</strong>
                                    </div>
                                    <div class="col-6">
                                        ${account.parentAccountCode || '-'}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>مستوى الحساب:</strong>
                                    </div>
                                    <div class="col-6">
                                        ${account.accountLevel}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>نوع الحساب:</strong>
                                    </div>
                                    <div class="col-6">
                                        <span class="badge bg-info">${account.accountType || '-'}</span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>طبيعة الحساب:</strong>
                                    </div>
                                    <div class="col-6">
                                        <span class="badge ${account.accountNature === 'Debit' ? 'bg-success' : 'bg-warning'}">${account.accountNature || '-'}</span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>الرصيد الافتتاحي:</strong>
                                    </div>
                                    <div class="col-6">
                                        ${parseFloat(account.openingBalance).toLocaleString('ar-SA', { minimumFractionDigits: 2 })}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>حساب ترحيل:</strong>
                                    </div>
                                    <div class="col-6">
                                        <span class="badge ${account.isPosting ? 'bg-success' : 'bg-secondary'}">${account.isPosting ? 'نعم' : 'لا'}</span>
                                    </div>
                                </div>
                                ${account.notes ? `
                                <div class="row">
                                    <div class="col-12">
                                        <strong>ملاحظات:</strong>
                                        <p class="mt-1">${account.notes}</p>
                                    </div>
                                </div>
                                ` : ''}
                                <hr>
                                <div class="d-grid gap-2">
                                    <a href="@Url.Action("Edit")?accountCode=${account.accountCode}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit me-1"></i>تعديل الحساب
                                    </a>
                                    <a href="@Url.Action("Create")?parentCode=${account.accountCode}" class="btn btn-success btn-sm">
                                        <i class="fas fa-plus me-1"></i>إضافة حساب فرعي
                                    </a>
                                </div>
                            </div>
                        `;
                        $('#accountDetails').html(detailsHtml);
                    } else {
                        $('#accountDetails').html('<div class="alert alert-danger">' + response.message + '</div>');
                    }
                })
                .fail(function () {
                    $('#accountDetails').html('<div class="alert alert-danger">حدث خطأ أثناء جلب بيانات الحساب</div>');
                });
        }

        function deleteAccount(accountCode) {
            $.post('@Url.Action("Delete")', { accountCode: accountCode })
                .done(function (response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                })
                .fail(function () {
                    alert('حدث خطأ أثناء حذف الحساب');
                });
        }
    </script>
} 