using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using AccountingSystem.Models;
using AccountingSystem.Data;
using AccountingSystem.Services;
using Microsoft.AspNetCore.Authorization;

namespace AccountingSystem.Web.Controllers
{
    [Authorize] // تم تفعيل التفويض
    public class CustomersController : Controller
    {
        private readonly AccountingDbContext _context;
        private readonly ICustomerService _customerService;

        public CustomersController(AccountingDbContext context, ICustomerService customerService)
        {
            _context = context;
            _customerService = customerService;
        }

        // GET: Customers
        public async Task<IActionResult> Index(string searchTerm)
        {
            ViewBag.SearchTerm = searchTerm;
            var customers = await _customerService.GetCustomersAsync(searchTerm);
            return View(customers);
        }

        // GET: Customers/Details/5
        public async Task<IActionResult> Details(long? customerNo)
        {
            if (customerNo == null)
            {
                return NotFound();
            }

            var customer = await _context.Customers
                .Include(c => c.Employee)
                .FirstOrDefaultAsync(m => m.CustomerNo == customerNo);

            if (customer == null)
            {
                return NotFound();
            }

            return View(customer);
        }

        // GET: Customers/Create
        public async Task<IActionResult> Create()
        {
            var customer = new Customer
            {
                CustomerNo = await _customerService.GetNextCustomerNoAsync()
            };
            LoadViewData();
            return View(customer);
        }

        // POST: Customers/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("CustomerNo,CustomerName,FirstName,LastName,Mobile,Phone,Email,StreetAddress1,StreetAddress2,City,Region,PostalCode,PaymentMethod,CreditLimit,PaymentTerm,ContactPerson,CR,VATRegNo,Shop,Status,LocalCustomer,EmployeeNo,Notes,OldCode,BuildingNo,AdditionalNo,District")] Customer customer)
        {
            if (ModelState.IsValid)
            {
                var currentUser = User.Identity?.Name ?? "System";
                var (success, accountCode) = await _customerService.CreateCustomerAsync(customer, currentUser);

                if (success)
                {
                    TempData["SuccessMessage"] = $"تم إنشاء العميل بنجاح - كود الحساب: {accountCode}";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    ModelState.AddModelError("", "فشل في إنشاء العميل أو رقم العميل موجود مسبقاً");
                }
            }

            LoadViewData();
            return View(customer);
        }

        // GET: Customers/Edit/5
        public async Task<IActionResult> Edit(long? customerNo)
        {
            if (customerNo == null)
            {
                return NotFound();
            }

            var customer = await _customerService.GetCustomerByNoAsync(customerNo.Value);
            if (customer == null)
            {
                return NotFound();
            }

            LoadViewData();
            return View(customer);
        }

        // POST: Customers/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(long customerNo, [Bind("CustomerNo,CustomerName,FirstName,LastName,Mobile,Phone,Email,StreetAddress1,StreetAddress2,City,Region,PostalCode,PaymentMethod,CreditLimit,PaymentTerm,ContactPerson,CR,VATRegNo,Shop,Status,LocalCustomer,EmployeeNo,Notes,OldCode,BuildingNo,AdditionalNo,District,CreatedBy,CreatedOn,ModifiedBy,ModifiedOn")] Customer customer)
        {
            if (customerNo != customer.CustomerNo)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var currentUser = User.Identity?.Name ?? "System";
                var success = await _customerService.UpdateCustomerAsync(customer, currentUser);

                if (success)
                {
                    TempData["SuccessMessage"] = "تم تحديث العميل بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    ModelState.AddModelError("", "فشل في تحديث العميل");
                }
            }

            LoadViewData();
            return View(customer);
        }

        // GET: Customers/Delete/5
        public async Task<IActionResult> Delete(long? customerNo)
        {
            if (customerNo == null)
            {
                return NotFound();
            }

            var customer = await _customerService.GetCustomerByNoAsync(customerNo.Value);

            if (customer == null)
            {
                return NotFound();
            }

            return View(customer);
        }

        // POST: Customers/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(long customerNo)
        {
            var success = await _customerService.DeleteCustomerAsync(customerNo);
            if (success)
            {
                TempData["SuccessMessage"] = "تم حذف العميل بنجاح";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في حذف العميل";
            }

            return RedirectToAction(nameof(Index));
        }



        private void LoadViewData()
        {
            // Payment Methods
            ViewBag.PaymentMethods = new List<string>
            {
                "نقداً",
                "شيك",
                "تحويل بنكي",
                "بطاقة ائتمان",
                "آجل"
            };

            // Status Options
            ViewBag.StatusOptions = new List<string>
            {
                "نشط",
                "غير نشط",
                "معلق"
            };

            // Local Customer Options
            ViewBag.LocalCustomerOptions = new List<string>
            {
                "داخل المملكة",
                "خارج المملكة"
            };

            // Employees for dropdown
            ViewBag.Employees = _context.Employees
                .OrderBy(e => e.Name)
                .Select(e => new { EmployeeNo = e.Id, EmployeeName = e.Name })
                .ToList();

            // Shops for dropdown
            ViewBag.Shops = _context.Shops
                .OrderBy(s => s.StoreName)
                .Select(s => new { s.SN, s.StoreName })
                .ToList();
        }
    }
} 