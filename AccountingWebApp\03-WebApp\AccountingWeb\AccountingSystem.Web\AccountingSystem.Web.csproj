<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="QRCoder" Version="1.6.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\02-Shared\Core\AccountingSystem.Core\AccountingSystem.Core.csproj" />
    <ProjectReference Include="..\..\..\02-Shared\Data\AccountingSystem.Data\AccountingSystem.Data.csproj" />
    <ProjectReference Include="..\..\..\02-Shared\Models\AccountingSystem.Models\AccountingSystem.Models.csproj" />
    <ProjectReference Include="..\..\..\02-Shared\Services\AccountingSystem.Services\AccountingSystem.Services.csproj" />
  </ItemGroup>

</Project>
